/* eslint-disable */
/* tslint:disable */
// @ts-nocheck
/* prettier-ignore-start */

/** @jsxRuntime classic */
/** @jsx createPlasmicElementProxy */
/** @jsxFrag React.Fragment */

// This class is auto-generated by Plasmic; please do not edit!
// Plasmic Project: nZeWKcn21KijvC7eT8B3c7
// Component: msZSdug4VrvS

"use client";

import * as React from "react";

import Head from "next/head";
import Link, { LinkProps } from "next/link";
import { useRouter } from "next/navigation";

import {
  Flex as Flex__,
  MultiChoiceArg,
  PlasmicDataSourceContextProvider as PlasmicDataSourceContextProvider__,
  PlasmicIcon as PlasmicIcon__,
  PlasmicImg as PlasmicImg__,
  PlasmicLink as PlasmicLink__,
  PlasmicPageGuard as PlasmicPageGuard__,
  SingleBooleanChoiceArg,
  SingleChoiceArg,
  Stack as Stack__,
  StrictProps,
  Trans as Trans__,
  classNames,
  createPlasmicElementProxy,
  deriveRenderOpts,
  ensureGlobalVariants,
  generateOnMutateForSpec,
  generateStateOnChangeProp,
  generateStateOnChangePropForCodeComponents,
  generateStateValueProp,
  get as $stateGet,
  hasVariant,
  initializeCodeComponentStates,
  initializePlasmicStates,
  makeFragment,
  omit,
  pick,
  renderPlasmicSlot,
  set as $stateSet,
  useCurrentUser,
  useDollarState,
  usePlasmicTranslator,
  useTrigger,
  wrapWithClassName
} from "@plasmicapp/react-web";
import {
  DataCtxReader as DataCtxReader__,
  useDataEnv,
  useGlobalActions
} from "@plasmicapp/host";

import SubcomponentSkillsBadgeForCaseStudyTiles from "../../SubcomponentSkillsBadgeForCaseStudyTiles"; // plasmic-import: vjtGF3glVSRj/component

import "@plasmicapp/react-web/lib/plasmic.css";

import projectcss from "./plasmic.module.css"; // plasmic-import: nZeWKcn21KijvC7eT8B3c7/projectcss
import sty from "./PlasmicProfileTileCaseStudyTile.module.css"; // plasmic-import: msZSdug4VrvS/css

createPlasmicElementProxy;

export type PlasmicProfileTileCaseStudyTile__VariantMembers = {
  content: "photo" | "text" | "photoWText";
  modals: "popoutModal" | "popped";
  hoverAndClick: "hover" | "click";
  comingSoon: "comingSoon";
};
export type PlasmicProfileTileCaseStudyTile__VariantsArgs = {
  content?: SingleChoiceArg<"photo" | "text" | "photoWText">;
  modals?: SingleChoiceArg<"popoutModal" | "popped">;
  hoverAndClick?: SingleChoiceArg<"hover" | "click">;
  comingSoon?: SingleBooleanChoiceArg<"comingSoon">;
};
type VariantPropType = keyof PlasmicProfileTileCaseStudyTile__VariantsArgs;
export const PlasmicProfileTileCaseStudyTile__VariantProps =
  new Array<VariantPropType>(
    "content",
    "modals",
    "hoverAndClick",
    "comingSoon"
  );

export type PlasmicProfileTileCaseStudyTile__ArgsType = {};
type ArgPropType = keyof PlasmicProfileTileCaseStudyTile__ArgsType;
export const PlasmicProfileTileCaseStudyTile__ArgProps =
  new Array<ArgPropType>();

export type PlasmicProfileTileCaseStudyTile__OverridesType = {
  caseStudyFormatting?: Flex__<"div">;
  glassOverlay?: Flex__<"section">;
  h3?: Flex__<"h3">;
  headerSection?: Flex__<"section">;
  heading2?: Flex__<"div">;
  backgroundBaseColor?: Flex__<"section">;
  displayImage?: Flex__<typeof PlasmicImg__>;
  freeBox?: Flex__<"div">;
  summaryText?: Flex__<"div">;
  img?: Flex__<typeof PlasmicImg__>;
  skillsBadgeBar?: Flex__<"section">;
  subcomponentSkillsBadgeForCaseStudyTiles?: Flex__<
    typeof SubcomponentSkillsBadgeForCaseStudyTiles
  >;
};

export interface DefaultProfileTileCaseStudyTileProps {
  content?: SingleChoiceArg<"photo" | "text" | "photoWText">;
  modals?: SingleChoiceArg<"popoutModal" | "popped">;
  hoverAndClick?: SingleChoiceArg<"hover" | "click">;
  comingSoon?: SingleBooleanChoiceArg<"comingSoon">;
  className?: string;
}

const $$ = {};

function useNextRouter() {
  try {
    return useRouter();
  } catch {}
  return undefined;
}

function PlasmicProfileTileCaseStudyTile__RenderFunc(props: {
  variants: PlasmicProfileTileCaseStudyTile__VariantsArgs;
  args: PlasmicProfileTileCaseStudyTile__ArgsType;
  overrides: PlasmicProfileTileCaseStudyTile__OverridesType;
  forNode?: string;
}) {
  const { variants, overrides, forNode } = props;

  const args = React.useMemo(
    () =>
      Object.assign(
        {},
        Object.fromEntries(
          Object.entries(props.args).filter(([_, v]) => v !== undefined)
        )
      ),
    [props.args]
  );

  const $props = {
    ...args,
    ...variants
  };

  const __nextRouter = useNextRouter();
  const $ctx = useDataEnv?.() || {};
  const refsRef = React.useRef({});
  const $refs = refsRef.current;

  let [$queries, setDollarQueries] = React.useState<
    Record<string, ReturnType<typeof usePlasmicDataOp>>
  >({});
  const stateSpecs: Parameters<typeof useDollarState>[0] = React.useMemo(
    () => [
      {
        path: "hoverAndClick",
        type: "private",
        variableType: "variant",
        initFunc: ({ $props, $state, $queries, $ctx }) => $props.hoverAndClick
      },
      {
        path: "content",
        type: "private",
        variableType: "variant",
        initFunc: ({ $props, $state, $queries, $ctx }) => $props.content
      },
      {
        path: "modals",
        type: "private",
        variableType: "variant",
        initFunc: ({ $props, $state, $queries, $ctx }) => $props.modals
      },
      {
        path: "comingSoon",
        type: "private",
        variableType: "variant",
        initFunc: ({ $props, $state, $queries, $ctx }) => $props.comingSoon
      }
    ],
    [$props, $ctx, $refs]
  );
  const $state = useDollarState(stateSpecs, {
    $props,
    $ctx,
    $queries: $queries,
    $refs
  });

  const new$Queries: Record<string, ReturnType<typeof usePlasmicDataOp>> = {};
  if (Object.keys(new$Queries).some(k => new$Queries[k] !== $queries[k])) {
    setDollarQueries(new$Queries);

    $queries = new$Queries;
  }

  return (
    <div
      data-plasmic-name={"caseStudyFormatting"}
      data-plasmic-override={overrides.caseStudyFormatting}
      data-plasmic-root={true}
      data-plasmic-for-node={forNode}
      className={classNames(
        projectcss.all,
        projectcss.root_reset,
        projectcss.plasmic_default_styles,
        projectcss.plasmic_mixins,
        projectcss.plasmic_tokens,
        sty.caseStudyFormatting,
        {
          [sty.caseStudyFormattingcomingSoon]: hasVariant(
            $state,
            "comingSoon",
            "comingSoon"
          ),
          [sty.caseStudyFormattingcontent_photoWText]: hasVariant(
            $state,
            "content",
            "photoWText"
          ),
          [sty.caseStudyFormattingcontent_photo]: hasVariant(
            $state,
            "content",
            "photo"
          ),
          [sty.caseStudyFormattingcontent_text]: hasVariant(
            $state,
            "content",
            "text"
          ),
          [sty.caseStudyFormattinghoverAndClick_click]: hasVariant(
            $state,
            "hoverAndClick",
            "click"
          ),
          [sty.caseStudyFormattinghoverAndClick_hover]: hasVariant(
            $state,
            "hoverAndClick",
            "hover"
          ),
          [sty.caseStudyFormattingmodals_popoutModal]: hasVariant(
            $state,
            "modals",
            "popoutModal"
          ),
          [sty.caseStudyFormattingmodals_popped]: hasVariant(
            $state,
            "modals",
            "popped"
          )
        }
      )}
      onMouseDown={async event => {
        const $steps = {};

        $steps["updateHoverAndClick"] = true
          ? (() => {
              const actionArgs = {
                vgroup: "hoverAndClick",
                operation: 0,
                value: "click"
              };
              return (({ vgroup, value }) => {
                if (typeof value === "string") {
                  value = [value];
                }

                $stateSet($state, vgroup, value);
                return value;
              })?.apply(null, [actionArgs]);
            })()
          : undefined;
        if (
          $steps["updateHoverAndClick"] != null &&
          typeof $steps["updateHoverAndClick"] === "object" &&
          typeof $steps["updateHoverAndClick"].then === "function"
        ) {
          $steps["updateHoverAndClick"] = await $steps["updateHoverAndClick"];
        }
      }}
      onMouseEnter={async event => {
        const $steps = {};

        $steps["updateHoverAndClick"] = true
          ? (() => {
              const actionArgs = {
                vgroup: "hoverAndClick",
                operation: 0,
                value: "hover"
              };
              return (({ vgroup, value }) => {
                if (typeof value === "string") {
                  value = [value];
                }

                $stateSet($state, vgroup, value);
                return value;
              })?.apply(null, [actionArgs]);
            })()
          : undefined;
        if (
          $steps["updateHoverAndClick"] != null &&
          typeof $steps["updateHoverAndClick"] === "object" &&
          typeof $steps["updateHoverAndClick"].then === "function"
        ) {
          $steps["updateHoverAndClick"] = await $steps["updateHoverAndClick"];
        }
      }}
      onMouseLeave={async event => {
        const $steps = {};

        $steps["updateHoverAndClick"] = true
          ? (() => {
              const actionArgs = {
                vgroup: "hoverAndClick",
                operation: 0,
                value: []
              };
              return (({ vgroup, value }) => {
                if (typeof value === "string") {
                  value = [value];
                }

                $stateSet($state, vgroup, value);
                return value;
              })?.apply(null, [actionArgs]);
            })()
          : undefined;
        if (
          $steps["updateHoverAndClick"] != null &&
          typeof $steps["updateHoverAndClick"] === "object" &&
          typeof $steps["updateHoverAndClick"].then === "function"
        ) {
          $steps["updateHoverAndClick"] = await $steps["updateHoverAndClick"];
        }
      }}
      onMouseUp={async event => {
        const $steps = {};

        $steps["updateHoverAndClick"] = true
          ? (() => {
              const actionArgs = {
                vgroup: "hoverAndClick",
                operation: 0,
                value: "hover"
              };
              return (({ vgroup, value }) => {
                if (typeof value === "string") {
                  value = [value];
                }

                $stateSet($state, vgroup, value);
                return value;
              })?.apply(null, [actionArgs]);
            })()
          : undefined;
        if (
          $steps["updateHoverAndClick"] != null &&
          typeof $steps["updateHoverAndClick"] === "object" &&
          typeof $steps["updateHoverAndClick"].then === "function"
        ) {
          $steps["updateHoverAndClick"] = await $steps["updateHoverAndClick"];
        }
      }}
    >
      <section
        data-plasmic-name={"glassOverlay"}
        data-plasmic-override={overrides.glassOverlay}
        className={classNames(projectcss.all, sty.glassOverlay, {
          [sty.glassOverlaycomingSoon]: hasVariant(
            $state,
            "comingSoon",
            "comingSoon"
          ),
          [sty.glassOverlaycontent_photoWText]: hasVariant(
            $state,
            "content",
            "photoWText"
          ),
          [sty.glassOverlaycontent_photo]: hasVariant(
            $state,
            "content",
            "photo"
          ),
          [sty.glassOverlaycontent_text]: hasVariant($state, "content", "text")
        })}
      >
        {(hasVariant($state, "comingSoon", "comingSoon") ? true : false) ? (
          <h3
            data-plasmic-name={"h3"}
            data-plasmic-override={overrides.h3}
            className={classNames(
              projectcss.all,
              projectcss.h3,
              projectcss.__wab_text,
              sty.h3,
              {
                [sty.h3comingSoon]: hasVariant(
                  $state,
                  "comingSoon",
                  "comingSoon"
                )
              }
            )}
          >
            {" Coming Soon "}
          </h3>
        ) : null}
      </section>
      <section
        data-plasmic-name={"headerSection"}
        data-plasmic-override={overrides.headerSection}
        className={classNames(projectcss.all, sty.headerSection, {
          [sty.headerSectioncomingSoon]: hasVariant(
            $state,
            "comingSoon",
            "comingSoon"
          ),
          [sty.headerSectioncontent_photoWText]: hasVariant(
            $state,
            "content",
            "photoWText"
          ),
          [sty.headerSectioncontent_photo]: hasVariant(
            $state,
            "content",
            "photo"
          ),
          [sty.headerSectioncontent_text]: hasVariant(
            $state,
            "content",
            "text"
          ),
          [sty.headerSectionmodals_popped]: hasVariant(
            $state,
            "modals",
            "popped"
          )
        })}
      >
        <div
          data-plasmic-name={"heading2"}
          data-plasmic-override={overrides.heading2}
          className={classNames(
            projectcss.all,
            projectcss.__wab_text,
            sty.heading2,
            {
              [sty.heading2comingSoon]: hasVariant(
                $state,
                "comingSoon",
                "comingSoon"
              ),
              [sty.heading2content_photoWText]: hasVariant(
                $state,
                "content",
                "photoWText"
              ),
              [sty.heading2content_photo]: hasVariant(
                $state,
                "content",
                "photo"
              ),
              [sty.heading2content_text]: hasVariant($state, "content", "text"),
              [sty.heading2modals_popped]: hasVariant(
                $state,
                "modals",
                "popped"
              )
            }
          )}
        >
          {"Enter some text"}
        </div>
      </section>
      <section
        data-plasmic-name={"backgroundBaseColor"}
        data-plasmic-override={overrides.backgroundBaseColor}
        className={classNames(projectcss.all, sty.backgroundBaseColor, {
          [sty.backgroundBaseColorcomingSoon]: hasVariant(
            $state,
            "comingSoon",
            "comingSoon"
          ),
          [sty.backgroundBaseColorcontent_photoWText]: hasVariant(
            $state,
            "content",
            "photoWText"
          ),
          [sty.backgroundBaseColorcontent_photo]: hasVariant(
            $state,
            "content",
            "photo"
          ),
          [sty.backgroundBaseColorcontent_text]: hasVariant(
            $state,
            "content",
            "text"
          ),
          [sty.backgroundBaseColormodals_popped]: hasVariant(
            $state,
            "modals",
            "popped"
          )
        })}
      >
        <PlasmicImg__
          data-plasmic-name={"displayImage"}
          data-plasmic-override={overrides.displayImage}
          alt={""}
          className={classNames(sty.displayImage, {
            [sty.displayImagecomingSoon]: hasVariant(
              $state,
              "comingSoon",
              "comingSoon"
            ),
            [sty.displayImagecontent_photoWText]: hasVariant(
              $state,
              "content",
              "photoWText"
            ),
            [sty.displayImagecontent_photo]: hasVariant(
              $state,
              "content",
              "photo"
            )
          })}
          displayHeight={"100%"}
          displayMaxHeight={"none"}
          displayMaxWidth={"100%"}
          displayMinHeight={"0"}
          displayMinWidth={"0"}
          displayWidth={"auto"}
          loading={"lazy"}
          src={{
            src: "/plasmic/senergy_platform/images/imagePlaceholderPng.png",
            fullWidth: 800,
            fullHeight: 600,
            aspectRatio: undefined
          }}
        />
      </section>
      <Stack__
        as={"div"}
        data-plasmic-name={"freeBox"}
        data-plasmic-override={overrides.freeBox}
        hasGap={true}
        className={classNames(projectcss.all, sty.freeBox, {
          [sty.freeBoxcontent_photoWText]: hasVariant(
            $state,
            "content",
            "photoWText"
          ),
          [sty.freeBoxcontent_text]: hasVariant($state, "content", "text")
        })}
      >
        <div
          data-plasmic-name={"summaryText"}
          data-plasmic-override={overrides.summaryText}
          className={classNames(
            projectcss.all,
            projectcss.__wab_text,
            sty.summaryText,
            {
              [sty.summaryTextcomingSoon]: hasVariant(
                $state,
                "comingSoon",
                "comingSoon"
              ),
              [sty.summaryTextcontent_photoWText]: hasVariant(
                $state,
                "content",
                "photoWText"
              ),
              [sty.summaryTextcontent_photo]: hasVariant(
                $state,
                "content",
                "photo"
              ),
              [sty.summaryTextcontent_text]: hasVariant(
                $state,
                "content",
                "text"
              )
            }
          )}
        >
          {hasVariant($state, "content", "photoWText")
            ? "Sit irure do sit anim ullamco fugiat pariatur dolor enim commodo ea. Sit do commodo sunt. Pariatur occaecat proident Sit do commodo sunt. Pariatur occaecat proident Sit do commodo sunt. Pariatur occae..."
            : hasVariant($state, "content", "text")
            ? "Sit irure do sit anim ullamco fugiat pariatur dolor enim commodo ea. Sit do commodo sunt. Pariatur occaecat proident occaecat in ea elit veniam. Ex eu reprehenderit id laborum deserunt tempor officia culpa irure ullamco Lorem officia id eu cillum. Tempor nulla voluptate proident. Eiusmod aute consectetur qui sint incididunt do aliquip fugiat reprehenderit veniam id. Est nostrud amet tempor.Sit irure do sit anim ullamco fugiat pariatur dolor enim "
            : "Enter some text"}
        </div>
        <PlasmicImg__
          data-plasmic-name={"img"}
          data-plasmic-override={overrides.img}
          alt={""}
          className={classNames(sty.img, {
            [sty.imgcontent_photoWText]: hasVariant(
              $state,
              "content",
              "photoWText"
            )
          })}
          displayHeight={"210px"}
          displayMaxHeight={"none"}
          displayMaxWidth={"100%"}
          displayMinHeight={"0"}
          displayMinWidth={"0"}
          displayWidth={"185px"}
          loading={"lazy"}
        />
      </Stack__>
      <Stack__
        as={"section"}
        data-plasmic-name={"skillsBadgeBar"}
        data-plasmic-override={overrides.skillsBadgeBar}
        hasGap={true}
        className={classNames(projectcss.all, sty.skillsBadgeBar, {
          [sty.skillsBadgeBarcomingSoon]: hasVariant(
            $state,
            "comingSoon",
            "comingSoon"
          ),
          [sty.skillsBadgeBarcontent_photoWText]: hasVariant(
            $state,
            "content",
            "photoWText"
          ),
          [sty.skillsBadgeBarcontent_photo]: hasVariant(
            $state,
            "content",
            "photo"
          ),
          [sty.skillsBadgeBarcontent_text]: hasVariant(
            $state,
            "content",
            "text"
          )
        })}
      >
        {(_par => (!_par ? [] : Array.isArray(_par) ? _par : [_par]))([
          2, 3, 4
        ]).map((__plasmic_item_0, __plasmic_idx_0) => {
          const currentItem = __plasmic_item_0;
          const currentIndex = __plasmic_idx_0;
          return (
            <SubcomponentSkillsBadgeForCaseStudyTiles
              data-plasmic-name={"subcomponentSkillsBadgeForCaseStudyTiles"}
              data-plasmic-override={
                overrides.subcomponentSkillsBadgeForCaseStudyTiles
              }
              className={classNames(
                "__wab_instance",
                sty.subcomponentSkillsBadgeForCaseStudyTiles,
                {
                  [sty.subcomponentSkillsBadgeForCaseStudyTilescomingSoon]:
                    hasVariant($state, "comingSoon", "comingSoon"),
                  [sty.subcomponentSkillsBadgeForCaseStudyTilescontent_photoWText]:
                    hasVariant($state, "content", "photoWText"),
                  [sty.subcomponentSkillsBadgeForCaseStudyTilescontent_photo]:
                    hasVariant($state, "content", "photo"),
                  [sty.subcomponentSkillsBadgeForCaseStudyTilescontent_text]:
                    hasVariant($state, "content", "text")
                }
              )}
              color={
                hasVariant($state, "content", "text") ? "white" : undefined
              }
              key={currentIndex}
            />
          );
        })}
      </Stack__>
    </div>
  ) as React.ReactElement | null;
}

const PlasmicDescendants = {
  caseStudyFormatting: [
    "caseStudyFormatting",
    "glassOverlay",
    "h3",
    "headerSection",
    "heading2",
    "backgroundBaseColor",
    "displayImage",
    "freeBox",
    "summaryText",
    "img",
    "skillsBadgeBar",
    "subcomponentSkillsBadgeForCaseStudyTiles"
  ],
  glassOverlay: ["glassOverlay", "h3"],
  h3: ["h3"],
  headerSection: ["headerSection", "heading2"],
  heading2: ["heading2"],
  backgroundBaseColor: ["backgroundBaseColor", "displayImage"],
  displayImage: ["displayImage"],
  freeBox: ["freeBox", "summaryText", "img"],
  summaryText: ["summaryText"],
  img: ["img"],
  skillsBadgeBar: [
    "skillsBadgeBar",
    "subcomponentSkillsBadgeForCaseStudyTiles"
  ],
  subcomponentSkillsBadgeForCaseStudyTiles: [
    "subcomponentSkillsBadgeForCaseStudyTiles"
  ]
} as const;
type NodeNameType = keyof typeof PlasmicDescendants;
type DescendantsType<T extends NodeNameType> =
  (typeof PlasmicDescendants)[T][number];
type NodeDefaultElementType = {
  caseStudyFormatting: "div";
  glassOverlay: "section";
  h3: "h3";
  headerSection: "section";
  heading2: "div";
  backgroundBaseColor: "section";
  displayImage: typeof PlasmicImg__;
  freeBox: "div";
  summaryText: "div";
  img: typeof PlasmicImg__;
  skillsBadgeBar: "section";
  subcomponentSkillsBadgeForCaseStudyTiles: typeof SubcomponentSkillsBadgeForCaseStudyTiles;
};

type ReservedPropsType = "variants" | "args" | "overrides";
type NodeOverridesType<T extends NodeNameType> = Pick<
  PlasmicProfileTileCaseStudyTile__OverridesType,
  DescendantsType<T>
>;
type NodeComponentProps<T extends NodeNameType> =
  // Explicitly specify variants, args, and overrides as objects
  {
    variants?: PlasmicProfileTileCaseStudyTile__VariantsArgs;
    args?: PlasmicProfileTileCaseStudyTile__ArgsType;
    overrides?: NodeOverridesType<T>;
  } & Omit<PlasmicProfileTileCaseStudyTile__VariantsArgs, ReservedPropsType> & // Specify variants directly as props
    /* Specify args directly as props*/ Omit<
      PlasmicProfileTileCaseStudyTile__ArgsType,
      ReservedPropsType
    > &
    /* Specify overrides for each element directly as props*/ Omit<
      NodeOverridesType<T>,
      ReservedPropsType | VariantPropType | ArgPropType
    > &
    /* Specify props for the root element*/ Omit<
      Partial<React.ComponentProps<NodeDefaultElementType[T]>>,
      ReservedPropsType | VariantPropType | ArgPropType | DescendantsType<T>
    >;

function makeNodeComponent<NodeName extends NodeNameType>(nodeName: NodeName) {
  type PropsType = NodeComponentProps<NodeName> & { key?: React.Key };
  const func = function <T extends PropsType>(
    props: T & StrictProps<T, PropsType>
  ) {
    const { variants, args, overrides } = React.useMemo(
      () =>
        deriveRenderOpts(props, {
          name: nodeName,
          descendantNames: PlasmicDescendants[nodeName],
          internalArgPropNames: PlasmicProfileTileCaseStudyTile__ArgProps,
          internalVariantPropNames:
            PlasmicProfileTileCaseStudyTile__VariantProps
        }),
      [props, nodeName]
    );
    return PlasmicProfileTileCaseStudyTile__RenderFunc({
      variants,
      args,
      overrides,
      forNode: nodeName
    });
  };
  if (nodeName === "caseStudyFormatting") {
    func.displayName = "PlasmicProfileTileCaseStudyTile";
  } else {
    func.displayName = `PlasmicProfileTileCaseStudyTile.${nodeName}`;
  }
  return func;
}

export const PlasmicProfileTileCaseStudyTile = Object.assign(
  // Top-level PlasmicProfileTileCaseStudyTile renders the root element
  makeNodeComponent("caseStudyFormatting"),
  {
    // Helper components rendering sub-elements
    glassOverlay: makeNodeComponent("glassOverlay"),
    h3: makeNodeComponent("h3"),
    headerSection: makeNodeComponent("headerSection"),
    heading2: makeNodeComponent("heading2"),
    backgroundBaseColor: makeNodeComponent("backgroundBaseColor"),
    displayImage: makeNodeComponent("displayImage"),
    freeBox: makeNodeComponent("freeBox"),
    summaryText: makeNodeComponent("summaryText"),
    img: makeNodeComponent("img"),
    skillsBadgeBar: makeNodeComponent("skillsBadgeBar"),
    subcomponentSkillsBadgeForCaseStudyTiles: makeNodeComponent(
      "subcomponentSkillsBadgeForCaseStudyTiles"
    ),

    // Metadata about props expected for PlasmicProfileTileCaseStudyTile
    internalVariantProps: PlasmicProfileTileCaseStudyTile__VariantProps,
    internalArgProps: PlasmicProfileTileCaseStudyTile__ArgProps
  }
);

export default PlasmicProfileTileCaseStudyTile;
/* prettier-ignore-end */
