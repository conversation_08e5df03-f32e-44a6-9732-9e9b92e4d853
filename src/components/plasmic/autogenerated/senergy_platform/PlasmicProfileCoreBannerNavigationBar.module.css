.profileNavContainer {
  display: inline-flex;
  flex-direction: row;
  position: relative;
  width: auto;
  height: auto;
  justify-content: flex-start;
  align-items: center;
  justify-self: flex-start;
  background: var(--token-K5FbAPSIIrXM);
}
.profileNavContainercollapsedHeader {
  background: none;
}
.overviewButton:global(.__wab_instance) {
  max-width: 100%;
  position: relative;
  transform: none;
}
.svg__ev7Pu {
  position: relative;
  object-fit: cover;
  width: auto;
  height: 1em;
}
.svg__yQjoi {
  position: relative;
  object-fit: cover;
  width: auto;
  height: 1em;
}
.myWorkButton:global(.__wab_instance) {
  max-width: 100%;
  position: relative;
  display: none;
}
.svg___4OXku {
  position: relative;
  object-fit: cover;
  width: auto;
  height: 1em;
}
.svg___8InK {
  position: relative;
  object-fit: cover;
  width: auto;
  height: 1em;
}
.aboutMeButton:global(.__wab_instance) {
  max-width: 100%;
  position: relative;
}
.svg__kYkg3 {
  position: relative;
  object-fit: cover;
  width: auto;
  height: 1em;
}
.svg__gpDjr {
  position: relative;
  object-fit: cover;
  width: auto;
  height: 1em;
}
