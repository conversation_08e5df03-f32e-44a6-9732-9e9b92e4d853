/* eslint-disable */
/* tslint:disable */
// @ts-nocheck
/* prettier-ignore-start */
import React from "react";
import { classNames } from "@plasmicapp/react-web";

export type CalendarExpirationIconProps = React.ComponentProps<"svg"> & {
  title?: string;
};

export function CalendarExpirationIcon(props: CalendarExpirationIconProps) {
  const { className, style, title, ...restProps } = props;
  return (
    <svg
      xmlns={"http://www.w3.org/2000/svg"}
      className={classNames(
        "plasmic-default__svg",
        className,
        "icon icon-tabler icon-tabler-calendar-time"
      )}
      viewBox={"0 0 24 24"}
      strokeWidth={"1.5"}
      stroke={"currentColor"}
      fill={"none"}
      strokeLinecap={"round"}
      strokeLinejoin={"round"}
      height={"1em"}
      style={style}
      {...restProps}
    >
      {title && <title>{title}</title>}

      <path d={"M0 0h24v24H0z"} fill={"none"} stroke={"none"}></path>

      <path
        d={"M11.795 21H5a2 2 0 01-2-2V7a2 2 0 012-2h12a2 2 0 012 2v4"}
      ></path>

      <path d={"M14 18a4 4 0 108 0 4 4 0 10-8 0m1-15v4M7 3v4m-4 4h16"}></path>

      <path d={"M18 16.496V18l1 1"}></path>
    </svg>
  );
}

export default CalendarExpirationIcon;
/* prettier-ignore-end */
