import type { Field } from 'payload';

const WORD_SPLIT_REGEX = /\s+/;

export const patentsFields: Field[] = [
  {
    name: 'patent_name',
    label: 'Patent Name',
    type: 'text',
    required: true,
  },
  {
    type: 'row',
    fields: [
      {
        name: 'registration_number',
        label: 'Registration Number',
        type: 'text',
        required: false,
      },
      {
        name: 'location_of_registration',
        label: 'Location of Registration',
        type: 'text',
      },
    ],
  },
  {
    type: 'row',
    fields: [
      {
        name: 'event_date',
        label: 'Event Date',
        type: 'date',
        required: false,
      },
      {
        name: 'status',
        label: 'Status',
        type: 'select',
        options: [
          { label: 'Pending', value: 'pending' },
          { label: 'Patented', value: 'patented' },
          { label: 'Abandoned', value: 'abandoned' },
          { label: 'Expired', value: 'expired' },
        ],
        required: false,
      },
      {
        name: 'type_of_patent',
        label: 'Type of Patent',
        type: 'select',
        options: [
          { label: 'Design', value: 'design' },
          { label: 'Utility', value: 'utility' },
          { label: 'Plant', value: 'plant' },
        ],
        required: true,
      },
    ],
  },
  {
    name: 'patent_description',
    label: 'Patent Description',
    admin: {
      description: 'Description of the patent',
    },
    type: 'textarea',
    required: false,
    validate: (value: string | null | undefined) => {
      if (value) {
        const wordCount = value.trim().split(WORD_SPLIT_REGEX).length;
        if (wordCount > 300) {
          return `Description must not exceed 300 words. Current word count: ${wordCount}`;
        }
      }
      return true;
    },
  },
  {
    name: 'visible',
    type: 'checkbox',
    required: false,
    defaultValue: true,
  },
];
