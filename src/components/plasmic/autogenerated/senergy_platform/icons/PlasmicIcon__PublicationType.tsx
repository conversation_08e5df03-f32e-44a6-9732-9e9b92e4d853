/* eslint-disable */
/* tslint:disable */
// @ts-nocheck
/* prettier-ignore-start */
import React from "react";
import { classNames } from "@plasmicapp/react-web";

export type PublicationTypeIconProps = React.ComponentProps<"svg"> & {
  title?: string;
};

export function PublicationTypeIcon(props: PublicationTypeIconProps) {
  const { className, style, title, ...restProps } = props;
  return (
    <svg
      xmlns={"http://www.w3.org/2000/svg"}
      className={classNames(
        "plasmic-default__svg",
        className,
        "icon icon-tabler icon-tabler-books"
      )}
      viewBox={"0 0 24 24"}
      strokeWidth={"1.5"}
      stroke={"currentColor"}
      fill={"none"}
      strokeLinecap={"round"}
      strokeLinejoin={"round"}
      height={"1em"}
      style={style}
      {...restProps}
    >
      {title && <title>{title}</title>}

      <path d={"M0 0h24v24H0z"} fill={"none"} stroke={"none"}></path>

      <path
        d={
          "M5 5a1 1 0 011-1h2a1 1 0 011 1v14a1 1 0 01-1 1H6a1 1 0 01-1-1zm4 0a1 1 0 011-1h2a1 1 0 011 1v14a1 1 0 01-1 1h-2a1 1 0 01-1-1zM5 8h4m0 8h4"
        }
      ></path>

      <path
        d={
          "M13.803 4.56l2.184-.53c.562-.135 1.133.19 1.282.732l3.695 13.418a1.02 1.02 0 01-.634 1.219l-.133.041-2.184.53c-.562.135-1.133-.19-1.282-.732L13.036 5.82a1.02 1.02 0 01.634-1.219l.133-.041zM14 9l4-1m-2 8l3.923-.98"
        }
      ></path>
    </svg>
  );
}

export default PublicationTypeIcon;
/* prettier-ignore-end */
