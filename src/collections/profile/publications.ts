import type { Field } from 'payload';

const WORD_SPLIT_REGEX = /\s+/;

export const publicationsFields: Field[] = [
  {
    name: 'publication_title',
    label: 'Publication Title',
    type: 'text',
    required: false,
  },
  {
    name: 'publisher',
    label: 'Publisher',
    type: 'text',
    required: false,
  },
  {
    name: 'additional_authors',
    label: 'Additional Authors',
    type: 'text',
    required: false,
  },
  {
    type: 'row',
    fields: [
      {
        name: 'publication_date',
        label: 'Publication Date',
        type: 'date',
        required: false,
      },
      {
        name: 'type_of_publication',
        label: 'Type of Publication',
        type: 'select',
        options: [
          { label: 'Book', value: 'book' },
          { label: 'Magazine', value: 'magazine' },
          { label: 'Blog', value: 'blog' },
          { label: 'Academic', value: 'academic' },
          { label: 'Reference', value: 'reference' },
          { label: 'Newspaper', value: 'newspaper' },
          { label: 'Online', value: 'online' },
          { label: 'Newsletter', value: 'newsletter' },
          { label: 'Other', value: 'other' },
        ],
        required: false,
      },
      {
        name: 'genre',
        label: 'Genre',
        type: 'text',
        //type: 'relationship',
        //relationTo: 'Genres',  // Reference to Genre collection
        required: false,
      },
      {
        name: 'isbn_issn',
        label: 'ISBN-ISSN',
        type: 'text',
        required: false,
      },
    ],
  },
  {
    name: 'publication_summary',
    label: 'Publication Summary',
    type: 'textarea',
    admin: {
      description: 'Description of the publication',
    },
    required: false,
    validate: (value: string | null | undefined) => {
      if (value && value.split(WORD_SPLIT_REGEX).length > 500) {
        return 'Publication Summary must not exceed 500 words';
      }
      return true;
    },
  },
  {
    name: 'visible',
    type: 'checkbox',
    required: false,
    defaultValue: true,
  },
];
