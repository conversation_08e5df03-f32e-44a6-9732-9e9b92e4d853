.overviewContentBlock {
  display: flex;
  flex-direction: column;
  position: relative;
  width: 100%;
  height: auto;
  justify-content: flex-start;
  align-items: stretch;
  background: none;
  flex-wrap: wrap;
  align-content: flex-start;
  min-width: 0;
}
.overviewHeading:global(.__wab_instance) {
  max-width: 100%;
  position: relative;
  margin-top: 0px;
}
.text {
  font-size: var(--token-hKlnSDJVAhUx);
}
.featuredWorksArea {
  position: relative;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start;
  width: 100%;
  height: auto;
  max-width: 100%;
  min-width: 0;
  display: none;
  padding: var(--token-sazGmnf7GWAk);
  margin: var(--token-sazGmnf7GWAk);
}
.profileOverviewHighlightedWorksLayout:global(.__wab_instance) {
  max-width: 100%;
}
.seeMoreButtonContainer2 {
  display: flex;
  position: relative;
  flex-direction: row;
  align-items: center;
  justify-content: flex-end;
  width: 100%;
  height: auto;
  max-width: 100%;
  min-width: 0;
  padding: var(--token-sazGmnf7GWAk);
  margin: var(--token-sazGmnf7GWAk);
}
.profileOverviewSeeMoreButton__zMfR6:global(.__wab_instance) {
  max-width: 100%;
  position: relative;
}
.summaryArea {
  display: flex;
  position: relative;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start;
  width: 100%;
  height: auto;
  max-width: 100%;
  min-width: 0;
  padding: var(--token-sazGmnf7GWAk);
  margin: 0px var(--token-sazGmnf7GWAk) var(--token-sazGmnf7GWAk);
}
.personalSummaryHeading:global(.__wab_instance) {
  max-width: 100%;
}
.freeBox {
  display: flex;
  position: relative;
  flex-direction: column;
  align-items: flex-start;
  justify-content: flex-start;
  width: 100%;
  height: auto;
  max-width: 100%;
  min-width: 0;
  padding: var(--token-4Wrp9mDZCSCQ);
  margin: var(--token-sazGmnf7GWAk);
}
.introductionBlock:global(.__wab_instance) {
  max-width: 100%;
}
.overviewBlockGrid {
  display: flex;
  position: relative;
  flex-direction: row;
  width: auto;
  height: auto;
  max-width: 100%;
  padding: 0px var(--token-4Wrp9mDZCSCQ) var(--token-4Wrp9mDZCSCQ);
  margin: var(--token-sazGmnf7GWAk);
}
.overviewBlockGrid > :global(.__wab_flex-container) {
  flex-direction: row;
  align-items: flex-start;
  justify-content: flex-start;
  margin-left: calc(0px - var(--token-4Wrp9mDZCSCQ));
  width: calc(100% + var(--token-4Wrp9mDZCSCQ));
}
.overviewBlockGrid > :global(.__wab_flex-container) > *,
.overviewBlockGrid > :global(.__wab_flex-container) > :global(.__wab_slot) > *,
.overviewBlockGrid > :global(.__wab_flex-container) > picture > img,
.overviewBlockGrid
  > :global(.__wab_flex-container)
  > :global(.__wab_slot)
  > picture
  > img {
  margin-left: var(--token-4Wrp9mDZCSCQ);
}
@media (max-width: 860px) {
  .overviewBlockGrid {
    display: flex;
    flex-direction: column;
  }
}
@media (max-width: 860px) {
  .overviewBlockGrid > :global(.__wab_flex-container) {
    flex-direction: column;
    margin-left: calc(0px - 0px);
    width: calc(100% + 0px);
    margin-top: calc(0px - var(--token-4Wrp9mDZCSCQ));
    height: calc(100% + var(--token-4Wrp9mDZCSCQ));
  }
}
@media (max-width: 860px) {
  .overviewBlockGrid > :global(.__wab_flex-container) > *,
  .overviewBlockGrid
    > :global(.__wab_flex-container)
    > :global(.__wab_slot)
    > *,
  .overviewBlockGrid > :global(.__wab_flex-container) > picture > img,
  .overviewBlockGrid
    > :global(.__wab_flex-container)
    > :global(.__wab_slot)
    > picture
    > img {
    margin-left: 0px;
    margin-top: var(--token-4Wrp9mDZCSCQ);
  }
}
@media (max-width: 480px) {
  .overviewBlockGrid {
    display: flex;
    flex-direction: column;
  }
}
@media (max-width: 480px) {
  .overviewBlockGrid > :global(.__wab_flex-container) {
    flex-direction: column;
    margin-left: calc(0px - 0px);
    width: calc(100% + 0px);
    margin-top: calc(0px - var(--token-4Wrp9mDZCSCQ));
    height: calc(100% + var(--token-4Wrp9mDZCSCQ));
  }
}
@media (max-width: 480px) {
  .overviewBlockGrid > :global(.__wab_flex-container) > *,
  .overviewBlockGrid
    > :global(.__wab_flex-container)
    > :global(.__wab_slot)
    > *,
  .overviewBlockGrid > :global(.__wab_flex-container) > picture > img,
  .overviewBlockGrid
    > :global(.__wab_flex-container)
    > :global(.__wab_slot)
    > picture
    > img {
    margin-left: 0px;
    margin-top: var(--token-4Wrp9mDZCSCQ);
  }
}
.skillsAndToolsStack {
  display: flex;
  flex-direction: column;
  width: auto;
  height: auto;
  max-width: 100%;
  padding: var(--token-sazGmnf7GWAk);
}
.skillsAndToolsStack > :global(.__wab_flex-container) {
  flex-direction: column;
  align-items: center;
  justify-content: flex-start;
  margin-top: calc(0px - var(--token-4Wrp9mDZCSCQ));
  height: calc(100% + var(--token-4Wrp9mDZCSCQ));
}
.skillsAndToolsStack > :global(.__wab_flex-container) > *,
.skillsAndToolsStack
  > :global(.__wab_flex-container)
  > :global(.__wab_slot)
  > *,
.skillsAndToolsStack > :global(.__wab_flex-container) > picture > img,
.skillsAndToolsStack
  > :global(.__wab_flex-container)
  > :global(.__wab_slot)
  > picture
  > img {
  margin-top: var(--token-4Wrp9mDZCSCQ);
}
.skillsBlock:global(.__wab_instance) {
  max-width: 550px;
  width: 100%;
  min-width: 0;
}
.toolsBlock:global(.__wab_instance) {
  position: relative;
  max-width: 550px;
  width: 100%;
  min-width: 0;
}
.leftStackUserDefined {
  display: flex;
  position: relative;
  flex-direction: column;
  width: 100%;
  height: auto;
  max-width: 100%;
  min-width: 0;
}
.leftStackUserDefined > :global(.__wab_flex-container) {
  flex-direction: column;
  align-items: stretch;
  justify-content: flex-start;
  flex-wrap: wrap;
  align-content: flex-start;
  min-width: 0;
  margin-top: calc(0px - var(--token-j0qnbpah5w9U));
  height: calc(100% + var(--token-j0qnbpah5w9U));
}
.leftStackUserDefined > :global(.__wab_flex-container) > *,
.leftStackUserDefined
  > :global(.__wab_flex-container)
  > :global(.__wab_slot)
  > *,
.leftStackUserDefined > :global(.__wab_flex-container) > picture > img,
.leftStackUserDefined
  > :global(.__wab_flex-container)
  > :global(.__wab_slot)
  > picture
  > img {
  margin-top: var(--token-j0qnbpah5w9U);
}
._2XWideBlock:global(.__wab_instance) {
  max-width: 100%;
  width: 100%;
  margin-bottom: 8px;
  min-width: 0;
}
.bottomBlocksSections {
  display: flex;
  position: relative;
  flex-direction: row;
  width: 100%;
  height: auto;
  max-width: 100%;
  margin-top: var(--token-sazGmnf7GWAk) !important;
  margin-right: var(--token-sazGmnf7GWAk);
  margin-bottom: var(--token-sazGmnf7GWAk);
  margin-left: var(--token-sazGmnf7GWAk);
  min-width: 0;
  padding: var(--token-sazGmnf7GWAk);
}
.bottomBlocksSections > :global(.__wab_flex-container) {
  flex-direction: row;
  align-items: flex-start;
  justify-content: flex-start;
  min-width: 0;
  margin-left: calc(0px - var(--token-4Wrp9mDZCSCQ));
  width: calc(100% + var(--token-4Wrp9mDZCSCQ));
}
.bottomBlocksSections > :global(.__wab_flex-container) > *,
.bottomBlocksSections
  > :global(.__wab_flex-container)
  > :global(.__wab_slot)
  > *,
.bottomBlocksSections > :global(.__wab_flex-container) > picture > img,
.bottomBlocksSections
  > :global(.__wab_flex-container)
  > :global(.__wab_slot)
  > picture
  > img {
  margin-left: var(--token-4Wrp9mDZCSCQ);
}
@media (max-width: 1200px) {
  .bottomBlocksSections {
    margin-top: var(--token-sazGmnf7GWAk) !important;
  }
}
@media (max-width: 1200px) {
  .bottomBlocksSections > :global(.__wab_flex-container) {
    flex-wrap: nowrap;
    align-items: stretch;
    align-content: stretch;
  }
}
@media (max-width: 860px) {
  .bottomBlocksSections {
    display: flex;
    flex-direction: column;
    margin-top: var(--token-sazGmnf7GWAk) !important;
  }
}
@media (max-width: 860px) {
  .bottomBlocksSections > :global(.__wab_flex-container) {
    flex-direction: column;
    margin-left: calc(0px - 0px);
    width: calc(100% + 0px);
    margin-top: calc(0px - var(--token-4Wrp9mDZCSCQ));
    height: calc(100% + var(--token-4Wrp9mDZCSCQ));
  }
}
@media (max-width: 860px) {
  .bottomBlocksSections > :global(.__wab_flex-container) > *,
  .bottomBlocksSections
    > :global(.__wab_flex-container)
    > :global(.__wab_slot)
    > *,
  .bottomBlocksSections > :global(.__wab_flex-container) > picture > img,
  .bottomBlocksSections
    > :global(.__wab_flex-container)
    > :global(.__wab_slot)
    > picture
    > img {
    margin-left: 0px;
    margin-top: var(--token-4Wrp9mDZCSCQ);
  }
}
@media (max-width: 480px) {
  .bottomBlocksSections {
    margin-top: var(--token-sazGmnf7GWAk) !important;
  }
}
.rightBottomBlock:global(.__wab_instance) {
  position: relative;
}
.seeMoreButtonContainer {
  display: flex;
  position: relative;
  flex-direction: row;
  width: 100%;
  height: auto;
  max-width: 100%;
  min-width: 0;
  padding: var(--token-sazGmnf7GWAk);
  margin: var(--token-sazGmnf7GWAk);
}
.seeMoreButtonContainer > :global(.__wab_flex-container) {
  flex-direction: row;
  align-items: center;
  justify-content: flex-end;
  min-width: 0;
  margin-left: calc(0px - var(--token-4Wrp9mDZCSCQ));
  width: calc(100% + var(--token-4Wrp9mDZCSCQ));
}
.seeMoreButtonContainer > :global(.__wab_flex-container) > *,
.seeMoreButtonContainer
  > :global(.__wab_flex-container)
  > :global(.__wab_slot)
  > *,
.seeMoreButtonContainer > :global(.__wab_flex-container) > picture > img,
.seeMoreButtonContainer
  > :global(.__wab_flex-container)
  > :global(.__wab_slot)
  > picture
  > img {
  margin-left: var(--token-4Wrp9mDZCSCQ);
}
.profileOverviewSeeMoreButton__w8Sl6:global(.__wab_instance) {
  max-width: 100%;
  position: relative;
}
