import { getSessionCookie } from 'better-auth/cookies';
import { type NextRequest, NextResponse } from 'next/server';

// Define protected and public routes
const protectedPaths = ['/onboarding', '/settings', '/waitlisted'];
const protectedPatterns = [/^\/profile\/.+$/];
const authenticatedRedirects: Record<string, string> = {
  '/login': '/settings',
  '/admin/login': '/admin/',
};
const redirectRules: Record<string, string> = {
  '/admin/forgot': '/admin/login',
};

// Validates user session and handles waitlist-related redirects
async function validateSession(req: NextRequest, path: string) {
  try {
    // Use absolute URL with protocol to ensure consistent behavior in all environments
    const apiUrl = new URL(
      '/api/auth/get-session',
      req.nextUrl.origin
    ).toString();
    const response = await fetch(apiUrl, {
      headers: {
        cookie: req.headers.get('cookie') || '',
      },
      cache: 'no-store', // Prevent caching issues in production
    });

    // If the response is not ok, return null instead of redirecting
    if (!response.ok) {
      return null;
    }

    const session = await response.json();

    // If user is not waitlisted and trying to access waitlist page, redirect to login
    if (session?.user?.role !== 'waitlisted' && path === '/waitlisted') {
      return NextResponse.redirect(new URL('/login', req.url));
    }

    // If user is waitlisted and trying to access a protected route (except waitlist page), redirect to waitlist page
    if (
      session?.user?.role === 'waitlisted' &&
      isProtectedRoute(path) &&
      path !== '/waitlisted'
    ) {
      return NextResponse.redirect(new URL('/waitlisted', req.url));
    }
  } catch (_error) {
    return null;
  }
  return null;
}

// Checks if a path is protected by exact match or pattern
function isProtectedRoute(path: string) {
  return (
    protectedPaths.includes(path) ||
    protectedPatterns.some((pattern) => pattern.test(path))
  );
}

export default async function middleware(req: NextRequest) {
  const path = req.nextUrl.pathname;

  // Get the session cookie
  const sessionCookie = getSessionCookie(req, {
    cookiePrefix: 'spherical-auth',
  });

  // If the route is protected and there is no session, redirect to the login page
  const isProtected = isProtectedRoute(path);
  if (isProtected && !sessionCookie) {
    // Add debug logging in case cookies aren't being properly retrieved in production
    if (process.env.NODE_ENV === 'production') {
    }
    return NextResponse.redirect(new URL('/login', req.url));
  }

  // If there's a session, check for waitlisted status
  if (sessionCookie) {
    const validationResult = await validateSession(req, path);
    if (validationResult) {
      return validationResult;
    }
  }

  // If the route is authenticated, redirect as explicitly specified
  if (authenticatedRedirects[path] && sessionCookie) {
    return NextResponse.redirect(
      new URL(authenticatedRedirects[path], req.url)
    );
  }

  // Apply explicit redirect rules
  if (redirectRules[path]) {
    return NextResponse.redirect(new URL(redirectRules[path], req.url));
  }

  return NextResponse.next();
}

export const config = {
  // More specific matcher to avoid running middleware unnecessarily
  matcher: [
    '/login',
    '/admin/login',
    '/admin/forgot',
    '/onboarding/:path*',
    '/settings/:path*',
    '/profile/:path*',
    '/waitlisted',
  ],
};
