.langaugeFormatingStack {
  display: inline-flex;
  flex-direction: column;
  width: auto;
  height: auto;
  justify-content: flex-start;
  align-items: flex-start;
  position: relative;
  background: var(--token-xo_r2w5pebq-);
  box-shadow: none;
  justify-self: flex-start;
  padding: var(--token-j0qnbpah5w9U);
  margin: 0px;
  border: 0.01cm solid #e4e4e7;
}
.langaugeFormatingStackoverviewGrid {
  background: none;
  border-style: none;
}
.subcomponentSlotHeading:global(.__wab_instance) {
  max-width: 100%;
  position: relative;
}
.subIconWithText:global(.__wab_instance) {
  max-width: 100%;
  position: relative;
  margin-bottom: 8px;
}
.iconSpot {
  position: relative;
  object-fit: cover;
  max-width: 100%;
  margin-right: 0;
  width: 100%;
  height: 100%;
  min-width: 0;
  min-height: 0;
}
.subDeleteButton:global(.__wab_instance) {
  max-width: 100%;
  position: absolute;
  top: 16px;
  right: 16px;
  flex-shrink: 0;
  display: none;
}
.subDeleteButtoneditable:global(.__wab_instance) {
  flex-shrink: 0;
  display: flex;
}
