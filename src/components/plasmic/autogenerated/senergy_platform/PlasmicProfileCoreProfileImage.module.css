.profileImageCompContainer {
  display: inline-flex;
  flex-direction: column;
  position: relative;
  width: auto;
  height: auto;
  justify-content: flex-start;
  align-items: center;
  justify-self: flex-start;
  transition-property: all;
  transition-duration: 0.2s;
  -webkit-transition-property: all;
  -webkit-transition-duration: 0.2s;
}
.overlayOverflowContainer {
  position: absolute;
  left: 0px;
  top: 70px;
  overflow: hidden;
  height: 30px;
  width: 100px;
  justify-items: center;
  align-content: flex-start;
  display: grid;
  z-index: 10;
  flex-shrink: 0;
  grid-template-columns:
    var(--plsmc-viewport-gap) 1fr minmax(0, var(--plsmc-wide-chunk))
    min(
      var(--plsmc-standard-width),
      calc(100% - var(--plsmc-viewport-gap) - var(--plsmc-viewport-gap))
    )
    minmax(0, var(--plsmc-wide-chunk)) 1fr var(--plsmc-viewport-gap);
}
.overlayOverflowContainer > * {
  grid-column: 4;
}
.overlayOverflowContainermediaType_noMedia {
  display: none;
}
.overlayOverflowContainermediaType_noMedia > * {
  grid-column: 4;
}
.overlayOverflowContainerhoverState {
  opacity: 1;
  display: none;
}
.overlayOverflowContainerhoverState > * {
  grid-column: 4;
}
.overlayColor {
  display: grid;
  position: absolute;
  align-content: flex-start;
  justify-items: center;
  width: 100px;
  background: var(--token-xPLlOa54vJla);
  height: 100px;
  left: -16px;
  top: -70px;
  grid-template-columns:
    var(--plsmc-viewport-gap) 1fr minmax(0, var(--plsmc-wide-chunk))
    min(
      var(--plsmc-standard-width),
      calc(100% - var(--plsmc-viewport-gap) - var(--plsmc-viewport-gap))
    )
    minmax(0, var(--plsmc-wide-chunk)) 1fr var(--plsmc-viewport-gap);
  border-radius: 100%;
}
.overlayColor > * {
  grid-column: 4;
}
.overlayColormediaType_video > * {
  grid-column: 4;
}
.overlayColorhoverState > * {
  grid-column: 4;
}
.hoverOverlay {
  position: absolute;
  left: 0px;
  top: 0px;
  overflow: hidden;
  height: 100px;
  width: 100px;
  justify-items: center;
  align-content: flex-start;
  display: grid;
  z-index: 10;
  opacity: 0;
  transition-property: all;
  transition-duration: 0.2s;
  flex-shrink: 0;
  grid-template-columns:
    var(--plsmc-viewport-gap) 1fr minmax(0, var(--plsmc-wide-chunk))
    min(
      var(--plsmc-standard-width),
      calc(100% - var(--plsmc-viewport-gap) - var(--plsmc-viewport-gap))
    )
    minmax(0, var(--plsmc-wide-chunk)) 1fr var(--plsmc-viewport-gap);
  -webkit-transition-property: all;
  -webkit-transition-duration: 0.2s;
}
.hoverOverlay > * {
  grid-column: 4;
}
.hoverOverlaymediaType_audio > * {
  grid-column: 4;
}
.hoverOverlaymediaType_noMedia {
  display: none;
}
.hoverOverlaymediaType_noMedia > * {
  grid-column: 4;
}
.hoverOverlayhoverState {
  opacity: 1;
}
.hoverOverlayhoverState > * {
  grid-column: 4;
}
.img {
  position: relative;
  object-fit: cover;
  max-width: 100%;
  width: 100px;
  height: 100px;
  z-index: 5;
  flex-shrink: 0;
  border-radius: 100%;
}
.img > picture > img {
  object-fit: cover;
}
.mediaTypeIcon {
  position: absolute;
  left: 42px;
  top: 76px;
  z-index: 10;
  height: 1em;
  flex-shrink: 0;
  display: none;
}
.mediaTypeIconmediaType_noMedia {
  display: none;
}
.mediaTypeIconhoverState {
  transform: scaleX(2) scaleY(2) scaleZ(1) translateX(0px) translateY(-16px)
    translateZ(0px);
  display: block;
}
.mediaTypeIconmediaType_noMedia_hoverState {
  display: none;
}
.audioPlayerPopout {
  position: absolute;
  width: 324px;
  left: -1px;
  top: -1px;
  height: 102px;
  background: var(--token-K5FbAPSIIrXM);
  z-index: 5;
  flex-direction: column;
  align-items: flex-end;
  justify-content: center;
  flex-shrink: 0;
  display: none;
  border-radius: 50px 2px 2px 50px;
  padding: var(--token-4Wrp9mDZCSCQ);
}
.audioPlayerContainer {
  display: flex;
  position: relative;
  width: 180px;
  flex-direction: column;
  align-items: flex-end;
  justify-content: flex-start;
}
.videoPlayerPopout {
  position: absolute;
  width: 324px;
  left: 0px;
  top: 0px;
  height: 182px;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  z-index: 10;
  overflow: hidden;
  flex-shrink: 0;
  display: none;
  border-radius: 2px;
  padding: var(--token-sazGmnf7GWAk);
}
