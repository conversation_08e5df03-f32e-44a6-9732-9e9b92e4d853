/* eslint-disable */
/* tslint:disable */
// @ts-nocheck
/* prettier-ignore-start */

/** @jsxRuntime classic */
/** @jsx createPlasmicElementProxy */
/** @jsxFrag React.Fragment */

// This class is auto-generated by Plasmic; please do not edit!
// Plasmic Project: nZeWKcn21KijvC7eT8B3c7
// Component: ptWaGH-zd0tJ

"use client";

import * as React from "react";

import Head from "next/head";
import Link, { LinkProps } from "next/link";
import { useRouter } from "next/navigation";

import {
  Flex as Flex__,
  MultiChoiceArg,
  PlasmicDataSourceContextProvider as PlasmicDataSourceContextProvider__,
  PlasmicIcon as PlasmicIcon__,
  PlasmicImg as PlasmicImg__,
  PlasmicLink as PlasmicLink__,
  PlasmicPageGuard as PlasmicPageGuard__,
  SingleBooleanChoiceArg,
  SingleC<PERSON>ice<PERSON>rg,
  Stack as Stack__,
  StrictProps,
  Trans as Trans__,
  classNames,
  createPlasmicElementProxy,
  deriveRenderOpts,
  ensureGlobalVariants,
  generateOnMutateForSpec,
  generateStateOnChangeProp,
  generateStateOnChangePropForCodeComponents,
  generateStateValueProp,
  get as $stateGet,
  hasVariant,
  initializeCodeComponentStates,
  initializePlasmicStates,
  makeFragment,
  omit,
  pick,
  renderPlasmicSlot,
  set as $stateSet,
  useCurrentUser,
  useDollarState,
  usePlasmicTranslator,
  useTrigger,
  wrapWithClassName
} from "@plasmicapp/react-web";
import {
  DataCtxReader as DataCtxReader__,
  useDataEnv,
  useGlobalActions
} from "@plasmicapp/host";

import SubcomponentButton from "../../SubcomponentButton"; // plasmic-import: ezhuRZvm_fH9/component
import SubcomponentSelectorButtonsWSlot from "../../SubcomponentSelectorButtonsWSlot"; // plasmic-import: FPZd4ZDDgKTk/component
import ProfileCoreProfileImage from "../../ProfileCoreProfileImage"; // plasmic-import: FmLLnhtvs10f/component
import SubcomponentIconWithText from "../../SubcomponentIconWithText"; // plasmic-import: _eFlbiSm6hZU/component
import SubcomponentTextInput from "../../SubcomponentTextInput"; // plasmic-import: Pt5FPuEzirSe/component

import "@plasmicapp/react-web/lib/plasmic.css";

import projectcss from "./plasmic.module.css"; // plasmic-import: nZeWKcn21KijvC7eT8B3c7/projectcss
import sty from "./PlasmicOnboardingOnboardingModal.module.css"; // plasmic-import: ptWaGH-zd0tJ/css

import CheckInBoxIcon from "./icons/PlasmicIcon__CheckInBox"; // plasmic-import: 3fHP2U7UmHxq/icon
import UserIcon from "./icons/PlasmicIcon__User"; // plasmic-import: hcR42vt5qkrz/icon
import PenPlusIcon from "./icons/PlasmicIcon__PenPlus"; // plasmic-import: GMFDqlfUwNBt/icon
import InfoGraphicIcon from "./icons/PlasmicIcon__InfoGraphic"; // plasmic-import: gCJGgC3_7lN-/icon
import CompassIcon from "./icons/PlasmicIcon__Compass"; // plasmic-import: 7WkmBwOD3bCe/icon
import GlobeIcon from "./icons/PlasmicIcon__Globe"; // plasmic-import: 4U-HfnBQfBTq/icon
import CoffeeMugIcon from "./icons/PlasmicIcon__CoffeeMug"; // plasmic-import: kJJX9PdsKpcg/icon
import ContractTypeIcon from "./icons/PlasmicIcon__ContractType"; // plasmic-import: 8BfErdfvqP2t/icon
import SunIcon from "./icons/PlasmicIcon__Sun"; // plasmic-import: rj9bnAg3Gp9P/icon
import AtIcon from "./icons/PlasmicIcon__At"; // plasmic-import: 2m49ojpkQhGO/icon
import DesignArtBoardIcon from "./icons/PlasmicIcon__DesignArtBoard"; // plasmic-import: e4d22TnM8LDb/icon
import NoteIcon from "./icons/PlasmicIcon__Note"; // plasmic-import: cxogJbe-XADD/icon
import MoonIcon from "./icons/PlasmicIcon__Moon"; // plasmic-import: pZCm_WpNUCMd/icon

createPlasmicElementProxy;

export type PlasmicOnboardingOnboardingModal__VariantMembers = {
  stage:
    | "_0"
    | "_1"
    | "_2"
    | "_3"
    | "_4"
    | "_5"
    | "_6"
    | "_7"
    | "_8"
    | "nextStepsInfo";
};
export type PlasmicOnboardingOnboardingModal__VariantsArgs = {
  stage?: SingleChoiceArg<
    | "_0"
    | "_1"
    | "_2"
    | "_3"
    | "_4"
    | "_5"
    | "_6"
    | "_7"
    | "_8"
    | "nextStepsInfo"
  >;
};
type VariantPropType = keyof PlasmicOnboardingOnboardingModal__VariantsArgs;
export const PlasmicOnboardingOnboardingModal__VariantProps =
  new Array<VariantPropType>("stage");

export type PlasmicOnboardingOnboardingModal__ArgsType = {
  firstName?: string;
  onFirstNameChange?: (val: string) => void;
  lastName?: string;
  onLastNameChange?: (val: string) => void;
  location?: string;
  onLocationChange?: (val: string) => void;
  workingHours?: string;
  onWorkingHoursChange?: (val: string) => void;
  pitchIntro?: string;
  onPitchIntroChange?: (val: string) => void;
  pitchRole?: string;
  onPitchRoleChange?: (val: string) => void;
  pitchDescriptor?: string;
  onPitchDescriptorChange?: (val: string) => void;
};
type ArgPropType = keyof PlasmicOnboardingOnboardingModal__ArgsType;
export const PlasmicOnboardingOnboardingModal__ArgProps =
  new Array<ArgPropType>(
    "firstName",
    "onFirstNameChange",
    "lastName",
    "onLastNameChange",
    "location",
    "onLocationChange",
    "workingHours",
    "onWorkingHoursChange",
    "pitchIntro",
    "onPitchIntroChange",
    "pitchRole",
    "onPitchRoleChange",
    "pitchDescriptor",
    "onPitchDescriptorChange"
  );

export type PlasmicOnboardingOnboardingModal__OverridesType = {
  onboardingModalContainer?: Flex__<"div">;
  headerSection?: Flex__<"section">;
  progressButtons?: Flex__<"section">;
  goBackButton?: Flex__<typeof SubcomponentButton>;
  continueButton?: Flex__<typeof SubcomponentButton>;
  lastStepChecklist?: Flex__<"section">;
  contExploreButtonStack?: Flex__<"div">;
  bannerSetupSection?: Flex__<"section">;
  bannerContentFormatting?: Flex__<"section">;
  leftSideBanner?: Flex__<"section">;
  nameStack?: Flex__<"div">;
  additionalInformation?: Flex__<"div">;
  location2?: Flex__<typeof SubcomponentIconWithText>;
  iconSpot?: Flex__<"svg">;
  workingHours2?: Flex__<typeof SubcomponentIconWithText>;
  iconSpot2?: Flex__<"svg">;
  rightSideBanner?: Flex__<"section">;
  elevatorPitch?: Flex__<"div">;
  adjectiveAndTitle?: Flex__<"div">;
  exampleBanner?: Flex__<"section">;
  bannerContentFormatting2?: Flex__<"section">;
  leftSideBanner2?: Flex__<"section">;
  nameStack2?: Flex__<"div">;
  additionalInformation2?: Flex__<"div">;
  location3?: Flex__<typeof SubcomponentIconWithText>;
  iconSpot3?: Flex__<"svg">;
  workingHours3?: Flex__<typeof SubcomponentIconWithText>;
  iconSpot4?: Flex__<"svg">;
  rightSideBanner2?: Flex__<"section">;
  elevatorPitch2?: Flex__<"div">;
  adjectiveAndTitle2?: Flex__<"div">;
  onboardingProcess?: Flex__<"section">;
  buttonContainer?: Flex__<"div">;
  topStack?: Flex__<"div">;
  subTextInput?: Flex__<typeof SubcomponentTextInput>;
  subMultilinePlaceholderTextInput?: Flex__<typeof SubcomponentTextInput>;
};

export interface DefaultOnboardingOnboardingModalProps {
  firstName?: string;
  onFirstNameChange?: (val: string) => void;
  lastName?: string;
  onLastNameChange?: (val: string) => void;
  location?: string;
  onLocationChange?: (val: string) => void;
  workingHours?: string;
  onWorkingHoursChange?: (val: string) => void;
  pitchIntro?: string;
  onPitchIntroChange?: (val: string) => void;
  pitchRole?: string;
  onPitchRoleChange?: (val: string) => void;
  pitchDescriptor?: string;
  onPitchDescriptorChange?: (val: string) => void;
  className?: string;
}

const $$ = {};

function useNextRouter() {
  try {
    return useRouter();
  } catch {}
  return undefined;
}

function PlasmicOnboardingOnboardingModal__RenderFunc(props: {
  variants: PlasmicOnboardingOnboardingModal__VariantsArgs;
  args: PlasmicOnboardingOnboardingModal__ArgsType;
  overrides: PlasmicOnboardingOnboardingModal__OverridesType;
  forNode?: string;
}) {
  const { variants, overrides, forNode } = props;

  const args = React.useMemo(
    () =>
      Object.assign(
        {},
        Object.fromEntries(
          Object.entries(props.args).filter(([_, v]) => v !== undefined)
        )
      ),
    [props.args]
  );

  const $props = {
    ...args,
    ...variants
  };

  const __nextRouter = useNextRouter();
  const $ctx = useDataEnv?.() || {};
  const refsRef = React.useRef({});
  const $refs = refsRef.current;

  let [$queries, setDollarQueries] = React.useState<
    Record<string, ReturnType<typeof usePlasmicDataOp>>
  >({});
  const stateSpecs: Parameters<typeof useDollarState>[0] = React.useMemo(
    () => [
      {
        path: "stage",
        type: "private",
        variableType: "variant",
        initFunc: ({ $props, $state, $queries, $ctx }) => $props.stage
      },
      {
        path: "location2.inputValue",
        type: "private",
        variableType: "text",
        initFunc: ({ $props, $state, $queries, $ctx }) => undefined
      },
      {
        path: "workingHours2.inputValue",
        type: "private",
        variableType: "text",
        initFunc: ({ $props, $state, $queries, $ctx }) => undefined
      },
      {
        path: "firstName",
        type: "writable",
        variableType: "text",

        valueProp: "firstName",
        onChangeProp: "onFirstNameChange"
      },
      {
        path: "lastName",
        type: "writable",
        variableType: "text",

        valueProp: "lastName",
        onChangeProp: "onLastNameChange"
      },
      {
        path: "location",
        type: "writable",
        variableType: "text",

        valueProp: "location",
        onChangeProp: "onLocationChange"
      },
      {
        path: "workingHours",
        type: "writable",
        variableType: "text",

        valueProp: "workingHours",
        onChangeProp: "onWorkingHoursChange"
      },
      {
        path: "pitchIntro",
        type: "writable",
        variableType: "text",

        valueProp: "pitchIntro",
        onChangeProp: "onPitchIntroChange"
      },
      {
        path: "pitchRole",
        type: "writable",
        variableType: "text",

        valueProp: "pitchRole",
        onChangeProp: "onPitchRoleChange"
      },
      {
        path: "pitchDescriptor",
        type: "writable",
        variableType: "text",

        valueProp: "pitchDescriptor",
        onChangeProp: "onPitchDescriptorChange"
      },
      {
        path: "subMultilinePlaceholderTextInput.value",
        type: "private",
        variableType: "text",
        initFunc: ({ $props, $state, $queries, $ctx }) => undefined
      },
      {
        path: "subMultilinePlaceholderTextInput.errorMessage",
        type: "private",
        variableType: "text",
        initFunc: ({ $props, $state, $queries, $ctx }) => ""
      },
      {
        path: "subTextInput.value",
        type: "private",
        variableType: "text",
        initFunc: ({ $props, $state, $queries, $ctx }) => undefined
      },
      {
        path: "subTextInput.errorMessage",
        type: "private",
        variableType: "text",
        initFunc: ({ $props, $state, $queries, $ctx }) => ""
      },
      {
        path: "location3.inputValue",
        type: "private",
        variableType: "text",
        initFunc: ({ $props, $state, $queries, $ctx }) => undefined
      },
      {
        path: "workingHours3.inputValue",
        type: "private",
        variableType: "text",
        initFunc: ({ $props, $state, $queries, $ctx }) => undefined
      }
    ],
    [$props, $ctx, $refs]
  );
  const $state = useDollarState(stateSpecs, {
    $props,
    $ctx,
    $queries: $queries,
    $refs
  });

  const new$Queries: Record<string, ReturnType<typeof usePlasmicDataOp>> = {};
  if (Object.keys(new$Queries).some(k => new$Queries[k] !== $queries[k])) {
    setDollarQueries(new$Queries);

    $queries = new$Queries;
  }

  return (
    <div
      data-plasmic-name={"onboardingModalContainer"}
      data-plasmic-override={overrides.onboardingModalContainer}
      data-plasmic-root={true}
      data-plasmic-for-node={forNode}
      className={classNames(
        projectcss.all,
        projectcss.root_reset,
        projectcss.plasmic_default_styles,
        projectcss.plasmic_mixins,
        projectcss.plasmic_tokens,
        sty.onboardingModalContainer,
        {
          [sty.onboardingModalContainerstage__0]: hasVariant(
            $state,
            "stage",
            "_0"
          ),
          [sty.onboardingModalContainerstage__1]: hasVariant(
            $state,
            "stage",
            "_1"
          ),
          [sty.onboardingModalContainerstage__2]: hasVariant(
            $state,
            "stage",
            "_2"
          ),
          [sty.onboardingModalContainerstage__3]: hasVariant(
            $state,
            "stage",
            "_3"
          ),
          [sty.onboardingModalContainerstage__4]: hasVariant(
            $state,
            "stage",
            "_4"
          ),
          [sty.onboardingModalContainerstage__5]: hasVariant(
            $state,
            "stage",
            "_5"
          ),
          [sty.onboardingModalContainerstage__6]: hasVariant(
            $state,
            "stage",
            "_6"
          ),
          [sty.onboardingModalContainerstage__7]: hasVariant(
            $state,
            "stage",
            "_7"
          ),
          [sty.onboardingModalContainerstage__8]: hasVariant(
            $state,
            "stage",
            "_8"
          ),
          [sty.onboardingModalContainerstage_nextStepsInfo]: hasVariant(
            $state,
            "stage",
            "nextStepsInfo"
          )
        }
      )}
    >
      <section
        data-plasmic-name={"headerSection"}
        data-plasmic-override={overrides.headerSection}
        className={classNames(projectcss.all, sty.headerSection, {
          [sty.headerSectionstage__0]: hasVariant($state, "stage", "_0"),
          [sty.headerSectionstage__1]: hasVariant($state, "stage", "_1"),
          [sty.headerSectionstage__2]: hasVariant($state, "stage", "_2"),
          [sty.headerSectionstage__3]: hasVariant($state, "stage", "_3"),
          [sty.headerSectionstage__4]: hasVariant($state, "stage", "_4"),
          [sty.headerSectionstage__5]: hasVariant($state, "stage", "_5"),
          [sty.headerSectionstage__6]: hasVariant($state, "stage", "_6"),
          [sty.headerSectionstage__7]: hasVariant($state, "stage", "_7"),
          [sty.headerSectionstage__8]: hasVariant($state, "stage", "_8"),
          [sty.headerSectionstage_nextStepsInfo]: hasVariant(
            $state,
            "stage",
            "nextStepsInfo"
          )
        })}
      >
        <div
          className={classNames(
            projectcss.all,
            projectcss.__wab_text,
            sty.text__auVRm,
            {
              [sty.textstage__0__auVRmG1XPd]: hasVariant($state, "stage", "_0"),
              [sty.textstage__1__auVRmnaH0S]: hasVariant($state, "stage", "_1"),
              [sty.textstage__2__auVRmBu8Sz]: hasVariant($state, "stage", "_2"),
              [sty.textstage__3__auVRm772Qm]: hasVariant($state, "stage", "_3"),
              [sty.textstage__4__auVRm012I3]: hasVariant($state, "stage", "_4"),
              [sty.textstage__5__auVRmJtAdo]: hasVariant($state, "stage", "_5"),
              [sty.textstage__6__auVRmMwi4C]: hasVariant($state, "stage", "_6"),
              [sty.textstage__7__auVRmFs7M]: hasVariant($state, "stage", "_7"),
              [sty.textstage__8__auVRm2KsfG]: hasVariant($state, "stage", "_8"),
              [sty.textstage_nextStepsInfo__auVRm1Qfba]: hasVariant(
                $state,
                "stage",
                "nextStepsInfo"
              )
            }
          )}
        >
          {hasVariant($state, "stage", "nextStepsInfo")
            ? "Great! We've got the basics"
            : hasVariant($state, "stage", "_8")
            ? "Profile Banner Review"
            : hasVariant($state, "stage", "_7")
            ? "Profile Setup"
            : hasVariant($state, "stage", "_6")
            ? "Profile Setup"
            : hasVariant($state, "stage", "_5")
            ? "Profile Setup"
            : hasVariant($state, "stage", "_4")
            ? "Profile Setup"
            : hasVariant($state, "stage", "_3")
            ? "Profile Setup"
            : hasVariant($state, "stage", "_2")
            ? "Profile Setup"
            : hasVariant($state, "stage", "_1")
            ? "Profile Setup"
            : "Onboarding"}
        </div>
      </section>
      <Stack__
        as={"section"}
        data-plasmic-name={"progressButtons"}
        data-plasmic-override={overrides.progressButtons}
        hasGap={true}
        className={classNames(projectcss.all, sty.progressButtons, {
          [sty.progressButtonsstage__0]: hasVariant($state, "stage", "_0"),
          [sty.progressButtonsstage__1]: hasVariant($state, "stage", "_1"),
          [sty.progressButtonsstage__2]: hasVariant($state, "stage", "_2"),
          [sty.progressButtonsstage__3]: hasVariant($state, "stage", "_3"),
          [sty.progressButtonsstage__4]: hasVariant($state, "stage", "_4"),
          [sty.progressButtonsstage__5]: hasVariant($state, "stage", "_5"),
          [sty.progressButtonsstage__6]: hasVariant($state, "stage", "_6"),
          [sty.progressButtonsstage__7]: hasVariant($state, "stage", "_7"),
          [sty.progressButtonsstage__8]: hasVariant($state, "stage", "_8"),
          [sty.progressButtonsstage_nextStepsInfo]: hasVariant(
            $state,
            "stage",
            "nextStepsInfo"
          )
        })}
      >
        <SubcomponentButton
          data-plasmic-name={"goBackButton"}
          data-plasmic-override={overrides.goBackButton}
          className={classNames("__wab_instance", sty.goBackButton, {
            [sty.goBackButtonstage__0]: hasVariant($state, "stage", "_0"),
            [sty.goBackButtonstage__1]: hasVariant($state, "stage", "_1")
          })}
          endIcon={
            <svg
              className={classNames(projectcss.all, sty.svg__zPLlc)}
              role={"img"}
            />
          }
          onClick={async event => {
            const $steps = {};

            $steps["runCode"] = true
              ? (() => {
                  const actionArgs = {
                    customFunction: async () => {
                      return ($state.stage = `_${
                        parseInt($state.stage.slice(1)) - 1
                      }`);
                    }
                  };
                  return (({ customFunction }) => {
                    return customFunction();
                  })?.apply(null, [actionArgs]);
                })()
              : undefined;
            if (
              $steps["runCode"] != null &&
              typeof $steps["runCode"] === "object" &&
              typeof $steps["runCode"].then === "function"
            ) {
              $steps["runCode"] = await $steps["runCode"];
            }
          }}
          startIcon={
            <svg
              className={classNames(projectcss.all, sty.svg__hrfmq)}
              role={"img"}
            />
          }
          styling={["typewriter"]}
        >
          {hasVariant($state, "stage", "_0") ? "Cancel" : "Back"}
        </SubcomponentButton>
        <SubcomponentButton
          data-plasmic-name={"continueButton"}
          data-plasmic-override={overrides.continueButton}
          className={classNames("__wab_instance", sty.continueButton, {
            [sty.continueButtonstage__0]: hasVariant($state, "stage", "_0"),
            [sty.continueButtonstage__1]: hasVariant($state, "stage", "_1"),
            [sty.continueButtonstage__2]: hasVariant($state, "stage", "_2"),
            [sty.continueButtonstage__3]: hasVariant($state, "stage", "_3"),
            [sty.continueButtonstage__4]: hasVariant($state, "stage", "_4"),
            [sty.continueButtonstage__5]: hasVariant($state, "stage", "_5"),
            [sty.continueButtonstage__6]: hasVariant($state, "stage", "_6"),
            [sty.continueButtonstage__7]: hasVariant($state, "stage", "_7"),
            [sty.continueButtonstage__8]: hasVariant($state, "stage", "_8")
          })}
          disabledAnimation={
            hasVariant($state, "stage", "_7")
              ? "disabled"
              : hasVariant($state, "stage", "_6")
              ? "disabled"
              : hasVariant($state, "stage", "_5")
              ? "disabled"
              : hasVariant($state, "stage", "_4")
              ? "disabled"
              : hasVariant($state, "stage", "_3")
              ? "disabled"
              : undefined
          }
          disabledShakeText={
            hasVariant($state, "stage", "_7")
              ? "Invalid Input"
              : hasVariant($state, "stage", "_6")
              ? "Invalid Input"
              : hasVariant($state, "stage", "_5")
              ? "Invalid Input"
              : hasVariant($state, "stage", "_4")
              ? "Invalid Input"
              : hasVariant($state, "stage", "_3")
              ? "Invalid Input"
              : hasVariant($state, "stage", "_2")
              ? "Invalid Input"
              : undefined
          }
          endIcon={
            <svg
              className={classNames(projectcss.all, sty.svg__oIvR0)}
              role={"img"}
            />
          }
          onClick={async event => {
            const $steps = {};

            $steps["runCode"] = true
              ? (() => {
                  const actionArgs = {
                    customFunction: async () => {
                      return ($state.stage = `_${
                        parseInt($state.stage.slice(1)) + 1
                      }`);
                    }
                  };
                  return (({ customFunction }) => {
                    return customFunction();
                  })?.apply(null, [actionArgs]);
                })()
              : undefined;
            if (
              $steps["runCode"] != null &&
              typeof $steps["runCode"] === "object" &&
              typeof $steps["runCode"].then === "function"
            ) {
              $steps["runCode"] = await $steps["runCode"];
            }
          }}
          startIcon={
            <svg
              className={classNames(projectcss.all, sty.svg__a15V5)}
              role={"img"}
            />
          }
          styling={["nittiWColor"]}
        >
          {"Continue"}
        </SubcomponentButton>
      </Stack__>
      <section
        data-plasmic-name={"lastStepChecklist"}
        data-plasmic-override={overrides.lastStepChecklist}
        className={classNames(projectcss.all, sty.lastStepChecklist, {
          [sty.lastStepCheckliststage__5]: hasVariant($state, "stage", "_5"),
          [sty.lastStepCheckliststage_nextStepsInfo]: hasVariant(
            $state,
            "stage",
            "nextStepsInfo"
          )
        })}
      >
        <div
          className={classNames(
            projectcss.all,
            projectcss.__wab_text,
            sty.text__sJ38L
          )}
        >
          {
            "There is still a bit more to do!  \nBefore your profile can show up in search results, you'll need to complete a couple more things.  "
          }
        </div>
        <div className={classNames(projectcss.all, sty.freeBox__m0DrX)}>
          <Stack__
            as={"div"}
            hasGap={true}
            className={classNames(projectcss.all, sty.freeBox__xyWx7)}
          >
            <CheckInBoxIcon
              className={classNames(projectcss.all, sty.svg___6Xvh3)}
              role={"img"}
            />

            <div
              className={classNames(
                projectcss.all,
                projectcss.__wab_text,
                sty.text__aCs1E
              )}
            >
              {"Complete your Introduction section."}
            </div>
          </Stack__>
          <Stack__
            as={"div"}
            hasGap={true}
            className={classNames(projectcss.all, sty.freeBox__pkx3)}
          >
            <CheckInBoxIcon
              className={classNames(projectcss.all, sty.svg__zDrEg)}
              role={"img"}
            />

            <div
              className={classNames(
                projectcss.all,
                projectcss.__wab_text,
                sty.text__tXkKd
              )}
            >
              {"Fill out two sections of your new profile."}
            </div>
          </Stack__>
          <div
            className={classNames(
              projectcss.all,
              projectcss.__wab_text,
              sty.text__ygDmZ,
              {
                [sty.textstage_nextStepsInfo__ygDmZ1Qfba]: hasVariant(
                  $state,
                  "stage",
                  "nextStepsInfo"
                )
              }
            )}
          >
            {
              "You can also start building a case study, \nwhich is how you will show off your work. "
            }
          </div>
        </div>
        <Stack__
          as={"div"}
          data-plasmic-name={"contExploreButtonStack"}
          data-plasmic-override={overrides.contExploreButtonStack}
          hasGap={true}
          className={classNames(projectcss.all, sty.contExploreButtonStack, {
            [sty.contExploreButtonStackstage_nextStepsInfo]: hasVariant(
              $state,
              "stage",
              "nextStepsInfo"
            )
          })}
        >
          <SubcomponentSelectorButtonsWSlot
            className={classNames(
              "__wab_instance",
              sty.subcomponentSelectorButtonsWSlot___0Nwxq,
              {
                [sty.subcomponentSelectorButtonsWSlotstage_nextStepsInfo___0Nwxq1Qfba]:
                  hasVariant($state, "stage", "nextStepsInfo")
              }
            )}
            size={"medium"}
          >
            <UserIcon
              className={classNames(projectcss.all, sty.svg___8DtLr)}
              role={"img"}
            />

            <div
              className={classNames(
                projectcss.all,
                projectcss.__wab_text,
                sty.text__wkIdM
              )}
            >
              {"Write an Intro"}
            </div>
          </SubcomponentSelectorButtonsWSlot>
          <SubcomponentSelectorButtonsWSlot
            className={classNames(
              "__wab_instance",
              sty.subcomponentSelectorButtonsWSlot__u5VOj,
              {
                [sty.subcomponentSelectorButtonsWSlotstage_nextStepsInfo__u5VOj1Qfba]:
                  hasVariant($state, "stage", "nextStepsInfo")
              }
            )}
            size={"medium"}
          >
            <PenPlusIcon
              className={classNames(projectcss.all, sty.svg___8Z9Jn)}
              role={"img"}
            />

            <div
              className={classNames(
                projectcss.all,
                projectcss.__wab_text,
                sty.text__lTxg
              )}
            >
              {"Fill out Profile"}
            </div>
          </SubcomponentSelectorButtonsWSlot>
          <SubcomponentSelectorButtonsWSlot
            className={classNames(
              "__wab_instance",
              sty.subcomponentSelectorButtonsWSlot__uN8PP,
              {
                [sty.subcomponentSelectorButtonsWSlotstage_nextStepsInfo__uN8PP1Qfba]:
                  hasVariant($state, "stage", "nextStepsInfo")
              }
            )}
            size={"medium"}
          >
            <InfoGraphicIcon
              className={classNames(projectcss.all, sty.svg__qyAuf)}
              role={"img"}
            />

            <div
              className={classNames(
                projectcss.all,
                projectcss.__wab_text,
                sty.text__qy3U
              )}
            >
              {"Make a Case Study"}
            </div>
          </SubcomponentSelectorButtonsWSlot>
          <SubcomponentSelectorButtonsWSlot
            className={classNames(
              "__wab_instance",
              sty.subcomponentSelectorButtonsWSlot__q4CxW,
              {
                [sty.subcomponentSelectorButtonsWSlotstage_nextStepsInfo__q4CxW1Qfba]:
                  hasVariant($state, "stage", "nextStepsInfo")
              }
            )}
            size={"medium"}
          >
            <CompassIcon
              className={classNames(projectcss.all, sty.svg__a5Gki)}
              role={"img"}
            />

            <div
              className={classNames(
                projectcss.all,
                projectcss.__wab_text,
                sty.text___1EO9M
              )}
            >
              {"Explore Senergy"}
            </div>
          </SubcomponentSelectorButtonsWSlot>
        </Stack__>
      </section>
      <div
        className={classNames(projectcss.all, sty.freeBox__w4IPg, {
          [sty.freeBoxstage__1__w4IPgnaH0S]: hasVariant($state, "stage", "_1"),
          [sty.freeBoxstage__2__w4IPgBu8Sz]: hasVariant($state, "stage", "_2"),
          [sty.freeBoxstage__8__w4IPg2KsfG]: hasVariant($state, "stage", "_8")
        })}
      >
        <div
          className={classNames(
            projectcss.all,
            projectcss.__wab_text,
            sty.text__wuCq,
            {
              [sty.textstage__0__wuCqG1XPd]: hasVariant($state, "stage", "_0"),
              [sty.textstage__1__wuCqnaH0S]: hasVariant($state, "stage", "_1"),
              [sty.textstage__2__wuCqBu8Sz]: hasVariant($state, "stage", "_2"),
              [sty.textstage__3__wuCq772Qm]: hasVariant($state, "stage", "_3"),
              [sty.textstage__4__wuCq012I3]: hasVariant($state, "stage", "_4"),
              [sty.textstage__5__wuCqJtAdo]: hasVariant($state, "stage", "_5"),
              [sty.textstage__6__wuCqMwi4C]: hasVariant($state, "stage", "_6"),
              [sty.textstage__7__wuCqFs7M]: hasVariant($state, "stage", "_7"),
              [sty.textstage__8__wuCq2KsfG]: hasVariant($state, "stage", "_8")
            }
          )}
        >
          {hasVariant($state, "stage", "_8")
            ? "You're all done here!  Congraulations!"
            : hasVariant($state, "stage", "_7")
            ? "Let's start filling it out"
            : hasVariant($state, "stage", "_6")
            ? "Let's start filling it out"
            : hasVariant($state, "stage", "_5")
            ? "Let's start filling it out"
            : hasVariant($state, "stage", "_4")
            ? "Let's start filling it out"
            : hasVariant($state, "stage", "_3")
            ? "Let's start filling it out"
            : hasVariant($state, "stage", "_2")
            ? "Let's start filling it out"
            : hasVariant($state, "stage", "_1")
            ? "This is your profile banner. It's your quick, at a glance, digital business card with all of the most relevant information."
            : "Let's start setting up your profile"}
        </div>
        <section
          data-plasmic-name={"bannerSetupSection"}
          data-plasmic-override={overrides.bannerSetupSection}
          className={classNames(projectcss.all, sty.bannerSetupSection, {
            [sty.bannerSetupSectionstage__0]: hasVariant($state, "stage", "_0"),
            [sty.bannerSetupSectionstage__1]: hasVariant($state, "stage", "_1"),
            [sty.bannerSetupSectionstage__2]: hasVariant($state, "stage", "_2"),
            [sty.bannerSetupSectionstage__3]: hasVariant($state, "stage", "_3"),
            [sty.bannerSetupSectionstage__4]: hasVariant($state, "stage", "_4"),
            [sty.bannerSetupSectionstage__5]: hasVariant($state, "stage", "_5"),
            [sty.bannerSetupSectionstage__6]: hasVariant($state, "stage", "_6"),
            [sty.bannerSetupSectionstage__7]: hasVariant($state, "stage", "_7"),
            [sty.bannerSetupSectionstage__8]: hasVariant($state, "stage", "_8")
          })}
        >
          <section
            data-plasmic-name={"bannerContentFormatting"}
            data-plasmic-override={overrides.bannerContentFormatting}
            className={classNames(projectcss.all, sty.bannerContentFormatting, {
              [sty.bannerContentFormattingstage__1]: hasVariant(
                $state,
                "stage",
                "_1"
              ),
              [sty.bannerContentFormattingstage__2]: hasVariant(
                $state,
                "stage",
                "_2"
              ),
              [sty.bannerContentFormattingstage__3]: hasVariant(
                $state,
                "stage",
                "_3"
              ),
              [sty.bannerContentFormattingstage__4]: hasVariant(
                $state,
                "stage",
                "_4"
              ),
              [sty.bannerContentFormattingstage__5]: hasVariant(
                $state,
                "stage",
                "_5"
              ),
              [sty.bannerContentFormattingstage__6]: hasVariant(
                $state,
                "stage",
                "_6"
              ),
              [sty.bannerContentFormattingstage__7]: hasVariant(
                $state,
                "stage",
                "_7"
              ),
              [sty.bannerContentFormattingstage__8]: hasVariant(
                $state,
                "stage",
                "_8"
              )
            })}
          >
            <section
              data-plasmic-name={"leftSideBanner"}
              data-plasmic-override={overrides.leftSideBanner}
              className={classNames(projectcss.all, sty.leftSideBanner, {
                [sty.leftSideBannerstage__1]: hasVariant($state, "stage", "_1"),
                [sty.leftSideBannerstage__2]: hasVariant($state, "stage", "_2")
              })}
            >
              <ProfileCoreProfileImage
                className={classNames(
                  "__wab_instance",
                  sty.profileCoreProfileImage__idDcM,
                  {
                    [sty.profileCoreProfileImagestage__1__idDcMnaH0S]:
                      hasVariant($state, "stage", "_1")
                  }
                )}
                mediaType={"noMedia"}
              />

              <Stack__
                as={"div"}
                data-plasmic-name={"nameStack"}
                data-plasmic-override={overrides.nameStack}
                hasGap={true}
                className={classNames(projectcss.all, sty.nameStack)}
              >
                <div
                  className={classNames(
                    projectcss.all,
                    projectcss.__wab_text,
                    sty.text__cuyw5,
                    {
                      [sty.textstage__1__cuyw5NaH0S]: hasVariant(
                        $state,
                        "stage",
                        "_1"
                      ),
                      [sty.textstage__2__cuyw5Bu8Sz]: hasVariant(
                        $state,
                        "stage",
                        "_2"
                      )
                    }
                  )}
                >
                  {"First Name"}
                </div>
                <div
                  className={classNames(
                    projectcss.all,
                    projectcss.__wab_text,
                    sty.text__ovts,
                    {
                      [sty.textstage__1__ovtSnaH0S]: hasVariant(
                        $state,
                        "stage",
                        "_1"
                      )
                    }
                  )}
                >
                  {"Last Name"}
                </div>
              </Stack__>
              <Stack__
                as={"div"}
                data-plasmic-name={"additionalInformation"}
                data-plasmic-override={overrides.additionalInformation}
                hasGap={true}
                className={classNames(
                  projectcss.all,
                  sty.additionalInformation
                )}
              >
                <SubcomponentIconWithText
                  data-plasmic-name={"location2"}
                  data-plasmic-override={overrides.location2}
                  className={classNames("__wab_instance", sty.location2)}
                  displayText={"Location"}
                  iconSpot2={
                    <GlobeIcon
                      data-plasmic-name={"iconSpot"}
                      data-plasmic-override={overrides.iconSpot}
                      className={classNames(projectcss.all, sty.iconSpot)}
                      role={"img"}
                    />
                  }
                  inputValue={generateStateValueProp($state, [
                    "location2",
                    "inputValue"
                  ])}
                  onInputValueChange={async (...eventArgs: any) => {
                    generateStateOnChangeProp($state, [
                      "location2",
                      "inputValue"
                    ]).apply(null, eventArgs);

                    if (
                      eventArgs.length > 1 &&
                      eventArgs[1] &&
                      eventArgs[1]._plasmic_state_init_
                    ) {
                      return;
                    }
                  }}
                />

                <SubcomponentIconWithText
                  data-plasmic-name={"workingHours2"}
                  data-plasmic-override={overrides.workingHours2}
                  className={classNames("__wab_instance", sty.workingHours2)}
                  displayText={"Working Hours"}
                  iconSpot2={
                    <CoffeeMugIcon
                      data-plasmic-name={"iconSpot2"}
                      data-plasmic-override={overrides.iconSpot2}
                      className={classNames(projectcss.all, sty.iconSpot2)}
                      role={"img"}
                    />
                  }
                  inputValue={generateStateValueProp($state, [
                    "workingHours2",
                    "inputValue"
                  ])}
                  onInputValueChange={async (...eventArgs: any) => {
                    generateStateOnChangeProp($state, [
                      "workingHours2",
                      "inputValue"
                    ]).apply(null, eventArgs);

                    if (
                      eventArgs.length > 1 &&
                      eventArgs[1] &&
                      eventArgs[1]._plasmic_state_init_
                    ) {
                      return;
                    }
                  }}
                />
              </Stack__>
            </section>
            <section
              data-plasmic-name={"rightSideBanner"}
              data-plasmic-override={overrides.rightSideBanner}
              className={classNames(projectcss.all, sty.rightSideBanner, {
                [sty.rightSideBannerstage__0]: hasVariant(
                  $state,
                  "stage",
                  "_0"
                ),
                [sty.rightSideBannerstage__1]: hasVariant(
                  $state,
                  "stage",
                  "_1"
                ),
                [sty.rightSideBannerstage__2]: hasVariant(
                  $state,
                  "stage",
                  "_2"
                ),
                [sty.rightSideBannerstage__8]: hasVariant($state, "stage", "_8")
              })}
            >
              <div
                data-plasmic-name={"elevatorPitch"}
                data-plasmic-override={overrides.elevatorPitch}
                className={classNames(projectcss.all, sty.elevatorPitch)}
              >
                <Stack__
                  as={"div"}
                  data-plasmic-name={"adjectiveAndTitle"}
                  data-plasmic-override={overrides.adjectiveAndTitle}
                  hasGap={true}
                  className={classNames(projectcss.all, sty.adjectiveAndTitle, {
                    [sty.adjectiveAndTitlestage__2]: hasVariant(
                      $state,
                      "stage",
                      "_2"
                    ),
                    [sty.adjectiveAndTitlestage__8]: hasVariant(
                      $state,
                      "stage",
                      "_8"
                    )
                  })}
                >
                  <div
                    className={classNames(
                      projectcss.all,
                      projectcss.__wab_text,
                      sty.text__br0V9
                    )}
                  >
                    {"Creative"}
                  </div>
                  <div
                    className={classNames(
                      projectcss.all,
                      projectcss.__wab_text,
                      sty.text__j2U8L,
                      {
                        [sty.textstage__1__j2U8LnaH0S]: hasVariant(
                          $state,
                          "stage",
                          "_1"
                        ),
                        [sty.textstage__5__j2U8LjtAdo]: hasVariant(
                          $state,
                          "stage",
                          "_5"
                        )
                      }
                    )}
                  >
                    {"Software Engineer"}
                  </div>
                </Stack__>
                <div
                  className={classNames(
                    projectcss.all,
                    projectcss.__wab_text,
                    sty.text__mAyp2,
                    {
                      [sty.textstage__7__mAyp2Fs7M]: hasVariant(
                        $state,
                        "stage",
                        "_7"
                      )
                    }
                  )}
                >
                  {"creating powerful software to drive business!"}
                </div>
              </div>
              <SubcomponentButton
                className={classNames(
                  "__wab_instance",
                  sty.subcomponentButton__giXws
                )}
                disabledAnimation={"disabled"}
                size={"compact"}
              >
                {"Message Firstname"}
              </SubcomponentButton>
            </section>
          </section>
        </section>
        {(
          hasVariant($state, "stage", "_8")
            ? true
            : hasVariant($state, "stage", "_7")
            ? true
            : hasVariant($state, "stage", "_6")
            ? true
            : hasVariant($state, "stage", "_5")
            ? true
            : hasVariant($state, "stage", "_4")
            ? true
            : hasVariant($state, "stage", "_3")
            ? true
            : hasVariant($state, "stage", "_2")
            ? true
            : hasVariant($state, "stage", "_1")
            ? true
            : false
        ) ? (
          <section
            data-plasmic-name={"exampleBanner"}
            data-plasmic-override={overrides.exampleBanner}
            className={classNames(projectcss.all, sty.exampleBanner, {
              [sty.exampleBannerstage__0]: hasVariant($state, "stage", "_0"),
              [sty.exampleBannerstage__1]: hasVariant($state, "stage", "_1"),
              [sty.exampleBannerstage__2]: hasVariant($state, "stage", "_2"),
              [sty.exampleBannerstage__3]: hasVariant($state, "stage", "_3"),
              [sty.exampleBannerstage__4]: hasVariant($state, "stage", "_4"),
              [sty.exampleBannerstage__5]: hasVariant($state, "stage", "_5"),
              [sty.exampleBannerstage__6]: hasVariant($state, "stage", "_6"),
              [sty.exampleBannerstage__7]: hasVariant($state, "stage", "_7"),
              [sty.exampleBannerstage__8]: hasVariant($state, "stage", "_8")
            })}
          >
            <section
              data-plasmic-name={"bannerContentFormatting2"}
              data-plasmic-override={overrides.bannerContentFormatting2}
              className={classNames(
                projectcss.all,
                sty.bannerContentFormatting2,
                {
                  [sty.bannerContentFormatting2stage__1]: hasVariant(
                    $state,
                    "stage",
                    "_1"
                  ),
                  [sty.bannerContentFormatting2stage__2]: hasVariant(
                    $state,
                    "stage",
                    "_2"
                  ),
                  [sty.bannerContentFormatting2stage__3]: hasVariant(
                    $state,
                    "stage",
                    "_3"
                  ),
                  [sty.bannerContentFormatting2stage__4]: hasVariant(
                    $state,
                    "stage",
                    "_4"
                  ),
                  [sty.bannerContentFormatting2stage__5]: hasVariant(
                    $state,
                    "stage",
                    "_5"
                  ),
                  [sty.bannerContentFormatting2stage__6]: hasVariant(
                    $state,
                    "stage",
                    "_6"
                  ),
                  [sty.bannerContentFormatting2stage__7]: hasVariant(
                    $state,
                    "stage",
                    "_7"
                  )
                }
              )}
            >
              <section
                data-plasmic-name={"leftSideBanner2"}
                data-plasmic-override={overrides.leftSideBanner2}
                className={classNames(projectcss.all, sty.leftSideBanner2, {
                  [sty.leftSideBanner2stage__1]: hasVariant(
                    $state,
                    "stage",
                    "_1"
                  )
                })}
              >
                <ProfileCoreProfileImage
                  className={classNames(
                    "__wab_instance",
                    sty.profileCoreProfileImage__mpEw,
                    {
                      [sty.profileCoreProfileImagestage__1__mpEwnaH0S]:
                        hasVariant($state, "stage", "_1")
                    }
                  )}
                  mediaType={"noMedia"}
                />

                <Stack__
                  as={"div"}
                  data-plasmic-name={"nameStack2"}
                  data-plasmic-override={overrides.nameStack2}
                  hasGap={true}
                  className={classNames(projectcss.all, sty.nameStack2)}
                >
                  <div
                    className={classNames(
                      projectcss.all,
                      projectcss.__wab_text,
                      sty.text__doyx2,
                      {
                        [sty.textstage__1__doyx2NaH0S]: hasVariant(
                          $state,
                          "stage",
                          "_1"
                        )
                      }
                    )}
                  >
                    {"Jachob"}
                  </div>
                  <div
                    className={classNames(
                      projectcss.all,
                      projectcss.__wab_text,
                      sty.text__tApJa,
                      {
                        [sty.textstage__1__tApJanaH0S]: hasVariant(
                          $state,
                          "stage",
                          "_1"
                        )
                      }
                    )}
                  >
                    {"Wolff"}
                  </div>
                </Stack__>
                <Stack__
                  as={"div"}
                  data-plasmic-name={"additionalInformation2"}
                  data-plasmic-override={overrides.additionalInformation2}
                  hasGap={true}
                  className={classNames(
                    projectcss.all,
                    sty.additionalInformation2
                  )}
                >
                  <SubcomponentIconWithText
                    data-plasmic-name={"location3"}
                    data-plasmic-override={overrides.location3}
                    className={classNames("__wab_instance", sty.location3)}
                    displayText={"Chicago, IL"}
                    iconSpot2={
                      <GlobeIcon
                        data-plasmic-name={"iconSpot3"}
                        data-plasmic-override={overrides.iconSpot3}
                        className={classNames(projectcss.all, sty.iconSpot3)}
                        role={"img"}
                      />
                    }
                    inputValue={generateStateValueProp($state, [
                      "location3",
                      "inputValue"
                    ])}
                    onInputValueChange={async (...eventArgs: any) => {
                      generateStateOnChangeProp($state, [
                        "location3",
                        "inputValue"
                      ]).apply(null, eventArgs);

                      if (
                        eventArgs.length > 1 &&
                        eventArgs[1] &&
                        eventArgs[1]._plasmic_state_init_
                      ) {
                        return;
                      }
                    }}
                  />

                  <SubcomponentIconWithText
                    data-plasmic-name={"workingHours3"}
                    data-plasmic-override={overrides.workingHours3}
                    className={classNames("__wab_instance", sty.workingHours3)}
                    displayText={"9 am - 6 pm"}
                    iconSpot2={
                      <CoffeeMugIcon
                        data-plasmic-name={"iconSpot4"}
                        data-plasmic-override={overrides.iconSpot4}
                        className={classNames(projectcss.all, sty.iconSpot4)}
                        role={"img"}
                      />
                    }
                    inputValue={generateStateValueProp($state, [
                      "workingHours3",
                      "inputValue"
                    ])}
                    onInputValueChange={async (...eventArgs: any) => {
                      generateStateOnChangeProp($state, [
                        "workingHours3",
                        "inputValue"
                      ]).apply(null, eventArgs);

                      if (
                        eventArgs.length > 1 &&
                        eventArgs[1] &&
                        eventArgs[1]._plasmic_state_init_
                      ) {
                        return;
                      }
                    }}
                  />
                </Stack__>
              </section>
              <section
                data-plasmic-name={"rightSideBanner2"}
                data-plasmic-override={overrides.rightSideBanner2}
                className={classNames(projectcss.all, sty.rightSideBanner2, {
                  [sty.rightSideBanner2stage__0]: hasVariant(
                    $state,
                    "stage",
                    "_0"
                  ),
                  [sty.rightSideBanner2stage__1]: hasVariant(
                    $state,
                    "stage",
                    "_1"
                  )
                })}
              >
                <div
                  data-plasmic-name={"elevatorPitch2"}
                  data-plasmic-override={overrides.elevatorPitch2}
                  className={classNames(projectcss.all, sty.elevatorPitch2)}
                >
                  <Stack__
                    as={"div"}
                    data-plasmic-name={"adjectiveAndTitle2"}
                    data-plasmic-override={overrides.adjectiveAndTitle2}
                    hasGap={true}
                    className={classNames(
                      projectcss.all,
                      sty.adjectiveAndTitle2
                    )}
                  >
                    <div
                      className={classNames(
                        projectcss.all,
                        projectcss.__wab_text,
                        sty.text__wdIoh
                      )}
                    >
                      {"Creative"}
                    </div>
                    <div
                      className={classNames(
                        projectcss.all,
                        projectcss.__wab_text,
                        sty.text__rdbUx,
                        {
                          [sty.textstage__1__rdbUxnaH0S]: hasVariant(
                            $state,
                            "stage",
                            "_1"
                          )
                        }
                      )}
                    >
                      {"Graphic Designer"}
                    </div>
                  </Stack__>
                  <div
                    className={classNames(
                      projectcss.all,
                      projectcss.__wab_text,
                      sty.text__htGoP,
                      {
                        [sty.textstage__7__htGoPfs7M]: hasVariant(
                          $state,
                          "stage",
                          "_7"
                        )
                      }
                    )}
                  >
                    {"creating powerful brands to drive business!"}
                  </div>
                </div>
                <SubcomponentButton
                  className={classNames(
                    "__wab_instance",
                    sty.subcomponentButton__vwrff
                  )}
                  disabledAnimation={"disabled"}
                  size={"compact"}
                >
                  {"Message Firstname"}
                </SubcomponentButton>
              </section>
            </section>
          </section>
        ) : null}
      </div>
      <section
        data-plasmic-name={"onboardingProcess"}
        data-plasmic-override={overrides.onboardingProcess}
        className={classNames(projectcss.all, sty.onboardingProcess, {
          [sty.onboardingProcessstage__0]: hasVariant($state, "stage", "_0"),
          [sty.onboardingProcessstage__1]: hasVariant($state, "stage", "_1"),
          [sty.onboardingProcessstage__2]: hasVariant($state, "stage", "_2"),
          [sty.onboardingProcessstage__3]: hasVariant($state, "stage", "_3"),
          [sty.onboardingProcessstage__4]: hasVariant($state, "stage", "_4"),
          [sty.onboardingProcessstage__5]: hasVariant($state, "stage", "_5"),
          [sty.onboardingProcessstage__6]: hasVariant($state, "stage", "_6"),
          [sty.onboardingProcessstage__7]: hasVariant($state, "stage", "_7"),
          [sty.onboardingProcessstage__8]: hasVariant($state, "stage", "_8")
        })}
        onLoad={async event => {
          const $steps = {};

          $steps["updateLocation2InputValue"] = true
            ? (() => {
                const actionArgs = {
                  variable: {
                    objRoot: $state,
                    variablePath: ["location2", "inputValue"]
                  },
                  operation: 0
                };
                return (({ variable, value, startIndex, deleteCount }) => {
                  if (!variable) {
                    return;
                  }
                  const { objRoot, variablePath } = variable;

                  $stateSet(objRoot, variablePath, value);
                  return value;
                })?.apply(null, [actionArgs]);
              })()
            : undefined;
          if (
            $steps["updateLocation2InputValue"] != null &&
            typeof $steps["updateLocation2InputValue"] === "object" &&
            typeof $steps["updateLocation2InputValue"].then === "function"
          ) {
            $steps["updateLocation2InputValue"] = await $steps[
              "updateLocation2InputValue"
            ];
          }
        }}
      >
        <div
          className={classNames(
            projectcss.all,
            projectcss.__wab_text,
            sty.text__bhg2E,
            {
              [sty.textstage__0__bhg2Eg1XPd]: hasVariant($state, "stage", "_0")
            }
          )}
        >
          {"Hi _______ how do you plan to use Senergy?"}
        </div>
        <div
          data-plasmic-name={"buttonContainer"}
          data-plasmic-override={overrides.buttonContainer}
          className={classNames(projectcss.all, sty.buttonContainer, {
            [sty.buttonContainerstage__0]: hasVariant($state, "stage", "_0"),
            [sty.buttonContainerstage__2]: hasVariant($state, "stage", "_2")
          })}
        >
          <Stack__
            as={"div"}
            data-plasmic-name={"topStack"}
            data-plasmic-override={overrides.topStack}
            hasGap={true}
            className={classNames(projectcss.all, sty.topStack, {
              [sty.topStackstage__0]: hasVariant($state, "stage", "_0")
            })}
          >
            <SubcomponentSelectorButtonsWSlot
              className={classNames(
                "__wab_instance",
                sty.subcomponentSelectorButtonsWSlot__hNybT,
                {
                  [sty.subcomponentSelectorButtonsWSlotstage__0__hNybTg1XPd]:
                    hasVariant($state, "stage", "_0")
                }
              )}
              onClick={async event => {
                const $steps = {};

                $steps["updateStage"] = true
                  ? (() => {
                      const actionArgs = {
                        vgroup: "stage",
                        operation: 0,
                        value: "_1"
                      };
                      return (({ vgroup, value }) => {
                        if (typeof value === "string") {
                          value = [value];
                        }

                        $stateSet($state, vgroup, value);
                        return value;
                      })?.apply(null, [actionArgs]);
                    })()
                  : undefined;
                if (
                  $steps["updateStage"] != null &&
                  typeof $steps["updateStage"] === "object" &&
                  typeof $steps["updateStage"].then === "function"
                ) {
                  $steps["updateStage"] = await $steps["updateStage"];
                }
              }}
            >
              <UserIcon
                className={classNames(projectcss.all, sty.svg__qwhlw, {
                  [sty.svgstage__0__qwhlwg1XPd]: hasVariant(
                    $state,
                    "stage",
                    "_0"
                  )
                })}
                role={"img"}
              />

              <div
                className={classNames(
                  projectcss.all,
                  projectcss.__wab_text,
                  sty.text__z4Cf9,
                  {
                    [sty.textstage__0__z4Cf9G1XPd]: hasVariant(
                      $state,
                      "stage",
                      "_0"
                    )
                  }
                )}
              >
                {"Freelance Work"}
              </div>
            </SubcomponentSelectorButtonsWSlot>
            <SubcomponentSelectorButtonsWSlot
              className={classNames(
                "__wab_instance",
                sty.subcomponentSelectorButtonsWSlot__myyse,
                {
                  [sty.subcomponentSelectorButtonsWSlotstage__0__myyseG1XPd]:
                    hasVariant($state, "stage", "_0")
                }
              )}
            >
              <ContractTypeIcon
                className={classNames(projectcss.all, sty.svg__fWW7, {
                  [sty.svgstage__0__fWW7G1XPd]: hasVariant(
                    $state,
                    "stage",
                    "_0"
                  )
                })}
                role={"img"}
              />

              <div
                className={classNames(
                  projectcss.all,
                  projectcss.__wab_text,
                  sty.text___1MPbg,
                  {
                    [sty.textstage__0___1MPbgg1XPd]: hasVariant(
                      $state,
                      "stage",
                      "_0"
                    )
                  }
                )}
              >
                {"Find People for Contracts"}
              </div>
            </SubcomponentSelectorButtonsWSlot>
          </Stack__>
          <SubcomponentSelectorButtonsWSlot
            className={classNames(
              "__wab_instance",
              sty.subcomponentSelectorButtonsWSlot___6H6Q,
              {
                [sty.subcomponentSelectorButtonsWSlotstage__0___6H6QG1XPd]:
                  hasVariant($state, "stage", "_0")
              }
            )}
          >
            <Stack__
              as={"div"}
              hasGap={true}
              className={classNames(projectcss.all, sty.freeBox__ivz1, {
                [sty.freeBoxstage__0__ivz1G1XPd]: hasVariant(
                  $state,
                  "stage",
                  "_0"
                )
              })}
            >
              <UserIcon
                className={classNames(projectcss.all, sty.svg__bXyQ4, {
                  [sty.svgstage__0__bXyQ4G1XPd]: hasVariant(
                    $state,
                    "stage",
                    "_0"
                  )
                })}
                role={"img"}
              />

              <ContractTypeIcon
                className={classNames(projectcss.all, sty.svg__txs2Q, {
                  [sty.svgstage__0__txs2QG1XPd]: hasVariant(
                    $state,
                    "stage",
                    "_0"
                  )
                })}
                role={"img"}
              />
            </Stack__>
            <div
              className={classNames(
                projectcss.all,
                projectcss.__wab_text,
                sty.text__kmVzb,
                {
                  [sty.textstage__0__kmVzbG1XPd]: hasVariant(
                    $state,
                    "stage",
                    "_0"
                  )
                }
              )}
            >
              {" A bit of freelance and a bit of hiring"}
            </div>
          </SubcomponentSelectorButtonsWSlot>
        </div>
        {(
          hasVariant($state, "stage", "_8")
            ? true
            : hasVariant($state, "stage", "_7")
            ? true
            : hasVariant($state, "stage", "_6")
            ? true
            : hasVariant($state, "stage", "_5")
            ? true
            : hasVariant($state, "stage", "_4")
            ? true
            : hasVariant($state, "stage", "_3")
            ? true
            : hasVariant($state, "stage", "_2")
            ? true
            : false
        ) ? (
          <div
            className={classNames(
              projectcss.all,
              projectcss.__wab_text,
              sty.text__pSBlh,
              {
                [sty.textstage__0__pSBlhG1XPd]: hasVariant(
                  $state,
                  "stage",
                  "_0"
                ),
                [sty.textstage__1__pSBlhnaH0S]: hasVariant(
                  $state,
                  "stage",
                  "_1"
                ),
                [sty.textstage__2__pSBlhBu8Sz]: hasVariant(
                  $state,
                  "stage",
                  "_2"
                ),
                [sty.textstage__3__pSBlh772Qm]: hasVariant(
                  $state,
                  "stage",
                  "_3"
                ),
                [sty.textstage__4__pSBlh012I3]: hasVariant(
                  $state,
                  "stage",
                  "_4"
                ),
                [sty.textstage__5__pSBlhJtAdo]: hasVariant(
                  $state,
                  "stage",
                  "_5"
                ),
                [sty.textstage__6__pSBlhMwi4C]: hasVariant(
                  $state,
                  "stage",
                  "_6"
                ),
                [sty.textstage__7__pSBlhFs7M]: hasVariant(
                  $state,
                  "stage",
                  "_7"
                ),
                [sty.textstage__8__pSBlh2KsfG]: hasVariant(
                  $state,
                  "stage",
                  "_8"
                )
              }
            )}
          >
            {hasVariant($state, "stage", "_8")
              ? "Click continue to confirm your information."
              : hasVariant($state, "stage", "_7")
              ? "How would you describe your work?"
              : hasVariant($state, "stage", "_6")
              ? "What's an adjective you feel describes the work that you do?"
              : hasVariant($state, "stage", "_5")
              ? "What do you call yourself professionally?"
              : hasVariant($state, "stage", "_4")
              ? "When do you like to wrap things up?"
              : hasVariant($state, "stage", "_3")
              ? "What time do you like to start your work day?"
              : hasVariant($state, "stage", "_2")
              ? "What location do you typically work from?"
              : "Let's start setting up your profile"}
          </div>
        ) : null}
        <Stack__
          as={"div"}
          hasGap={true}
          className={classNames(projectcss.all, sty.freeBox___3ClDj, {
            [sty.freeBoxstage__1___3ClDJnaH0S]: hasVariant(
              $state,
              "stage",
              "_1"
            ),
            [sty.freeBoxstage__2___3ClDjbu8Sz]: hasVariant(
              $state,
              "stage",
              "_2"
            ),
            [sty.freeBoxstage__3___3ClDj772Qm]: hasVariant(
              $state,
              "stage",
              "_3"
            ),
            [sty.freeBoxstage__4___3ClDj012I3]: hasVariant(
              $state,
              "stage",
              "_4"
            ),
            [sty.freeBoxstage__5___3ClDjjtAdo]: hasVariant(
              $state,
              "stage",
              "_5"
            ),
            [sty.freeBoxstage__6___3ClDjmwi4C]: hasVariant(
              $state,
              "stage",
              "_6"
            ),
            [sty.freeBoxstage__7___3ClDjfs7M]: hasVariant($state, "stage", "_7")
          })}
        >
          <PlasmicIcon__
            PlasmicIconType={
              hasVariant($state, "stage", "_7")
                ? NoteIcon
                : hasVariant($state, "stage", "_6")
                ? DesignArtBoardIcon
                : hasVariant($state, "stage", "_5")
                ? AtIcon
                : hasVariant($state, "stage", "_4")
                ? MoonIcon
                : hasVariant($state, "stage", "_3")
                ? SunIcon
                : GlobeIcon
            }
            className={classNames(projectcss.all, sty.svg__oAmBm, {
              [sty.svgstage__2__oAmBmbu8Sz]: hasVariant($state, "stage", "_2"),
              [sty.svgstage__3__oAmBm772Qm]: hasVariant($state, "stage", "_3"),
              [sty.svgstage__4__oAmBm012I3]: hasVariant($state, "stage", "_4"),
              [sty.svgstage__5__oAmBmjtAdo]: hasVariant($state, "stage", "_5"),
              [sty.svgstage__6__oAmBmmwi4C]: hasVariant($state, "stage", "_6"),
              [sty.svgstage__7__oAmBmfs7M]: hasVariant($state, "stage", "_7")
            })}
            role={"img"}
          />

          <SubcomponentTextInput
            data-plasmic-name={"subTextInput"}
            data-plasmic-override={overrides.subTextInput}
            className={classNames("__wab_instance", sty.subTextInput, {
              [sty.subTextInputstage__2]: hasVariant($state, "stage", "_2"),
              [sty.subTextInputstage__3]: hasVariant($state, "stage", "_3"),
              [sty.subTextInputstage__4]: hasVariant($state, "stage", "_4"),
              [sty.subTextInputstage__5]: hasVariant($state, "stage", "_5"),
              [sty.subTextInputstage__6]: hasVariant($state, "stage", "_6"),
              [sty.subTextInputstage__7]: hasVariant($state, "stage", "_7")
            })}
            errorMessage={generateStateValueProp($state, [
              "subTextInput",
              "errorMessage"
            ])}
            fieldNameRemainVisible={false}
            inputNameAsPlaceholder={false}
            inputPlaceholder={
              hasVariant($state, "stage", "_7")
                ? "Ex: Passionate about building beautiful things"
                : hasVariant($state, "stage", "_6")
                ? "Ex: Creative"
                : hasVariant($state, "stage", "_5")
                ? "Ex: Software Engineer"
                : hasVariant($state, "stage", "_4")
                ? "Ex: 5pm"
                : hasVariant($state, "stage", "_3")
                ? "Ex: 9am"
                : hasVariant($state, "stage", "_2")
                ? "Ex: Chicago, IL"
                : undefined
            }
            inputValue={generateStateValueProp($state, [
              "subTextInput",
              "value"
            ])}
            onErrorMessageChange={async (...eventArgs: any) => {
              generateStateOnChangeProp($state, [
                "subTextInput",
                "errorMessage"
              ]).apply(null, eventArgs);

              if (
                eventArgs.length > 1 &&
                eventArgs[1] &&
                eventArgs[1]._plasmic_state_init_
              ) {
                return;
              }
            }}
            onInputValueChange={async (...eventArgs: any) => {
              generateStateOnChangeProp($state, [
                "subTextInput",
                "value"
              ]).apply(null, eventArgs);

              if (
                eventArgs.length > 1 &&
                eventArgs[1] &&
                eventArgs[1]._plasmic_state_init_
              ) {
                return;
              }
            }}
            pressEnterText={true}
            pressEnterText2={
              hasVariant($state, "stage", "_2") ? "pressEnterHidden" : undefined
            }
          />
        </Stack__>
        <div
          className={classNames(
            projectcss.all,
            projectcss.__wab_text,
            sty.text__ngmno,
            {
              [sty.textstage__0__ngmnoG1XPd]: hasVariant($state, "stage", "_0"),
              [sty.textstage__1__ngmnonaH0S]: hasVariant($state, "stage", "_1"),
              [sty.textstage__2__ngmnoBu8Sz]: hasVariant($state, "stage", "_2"),
              [sty.textstage__3__ngmno772Qm]: hasVariant($state, "stage", "_3"),
              [sty.textstage__4__ngmno012I3]: hasVariant($state, "stage", "_4"),
              [sty.textstage__5__ngmnoJtAdo]: hasVariant($state, "stage", "_5"),
              [sty.textstage__6__ngmnoMwi4C]: hasVariant($state, "stage", "_6"),
              [sty.textstage__7__ngmnoFs7M]: hasVariant($state, "stage", "_7")
            }
          )}
        >
          {hasVariant($state, "stage", "_7")
            ? "Press Enter to Submit"
            : hasVariant($state, "stage", "_6")
            ? "Press Enter to Submit"
            : hasVariant($state, "stage", "_5")
            ? "Press Enter to Submit"
            : hasVariant($state, "stage", "_4")
            ? "Press Enter to Submit"
            : hasVariant($state, "stage", "_3")
            ? "Press Enter to Submit"
            : hasVariant($state, "stage", "_2")
            ? "Press Enter to Submit"
            : ""}
        </div>
      </section>
      <SubcomponentTextInput
        data-plasmic-name={"subMultilinePlaceholderTextInput"}
        data-plasmic-override={overrides.subMultilinePlaceholderTextInput}
        className={classNames(
          "__wab_instance",
          sty.subMultilinePlaceholderTextInput,
          {
            [sty.subMultilinePlaceholderTextInputstage__8]: hasVariant(
              $state,
              "stage",
              "_8"
            )
          }
        )}
        errorMessage={generateStateValueProp($state, [
          "subMultilinePlaceholderTextInput",
          "errorMessage"
        ])}
        inputValue={generateStateValueProp($state, [
          "subMultilinePlaceholderTextInput",
          "value"
        ])}
        onErrorMessageChange={async (...eventArgs: any) => {
          generateStateOnChangeProp($state, [
            "subMultilinePlaceholderTextInput",
            "errorMessage"
          ]).apply(null, eventArgs);

          if (
            eventArgs.length > 1 &&
            eventArgs[1] &&
            eventArgs[1]._plasmic_state_init_
          ) {
            return;
          }
        }}
        onInputValueChange={async (...eventArgs: any) => {
          generateStateOnChangeProp($state, [
            "subMultilinePlaceholderTextInput",
            "value"
          ]).apply(null, eventArgs);

          if (
            eventArgs.length > 1 &&
            eventArgs[1] &&
            eventArgs[1]._plasmic_state_init_
          ) {
            return;
          }
        }}
      />
    </div>
  ) as React.ReactElement | null;
}

const PlasmicDescendants = {
  onboardingModalContainer: [
    "onboardingModalContainer",
    "headerSection",
    "progressButtons",
    "goBackButton",
    "continueButton",
    "lastStepChecklist",
    "contExploreButtonStack",
    "bannerSetupSection",
    "bannerContentFormatting",
    "leftSideBanner",
    "nameStack",
    "additionalInformation",
    "location2",
    "iconSpot",
    "workingHours2",
    "iconSpot2",
    "rightSideBanner",
    "elevatorPitch",
    "adjectiveAndTitle",
    "exampleBanner",
    "bannerContentFormatting2",
    "leftSideBanner2",
    "nameStack2",
    "additionalInformation2",
    "location3",
    "iconSpot3",
    "workingHours3",
    "iconSpot4",
    "rightSideBanner2",
    "elevatorPitch2",
    "adjectiveAndTitle2",
    "onboardingProcess",
    "buttonContainer",
    "topStack",
    "subTextInput",
    "subMultilinePlaceholderTextInput"
  ],
  headerSection: ["headerSection"],
  progressButtons: ["progressButtons", "goBackButton", "continueButton"],
  goBackButton: ["goBackButton"],
  continueButton: ["continueButton"],
  lastStepChecklist: ["lastStepChecklist", "contExploreButtonStack"],
  contExploreButtonStack: ["contExploreButtonStack"],
  bannerSetupSection: [
    "bannerSetupSection",
    "bannerContentFormatting",
    "leftSideBanner",
    "nameStack",
    "additionalInformation",
    "location2",
    "iconSpot",
    "workingHours2",
    "iconSpot2",
    "rightSideBanner",
    "elevatorPitch",
    "adjectiveAndTitle"
  ],
  bannerContentFormatting: [
    "bannerContentFormatting",
    "leftSideBanner",
    "nameStack",
    "additionalInformation",
    "location2",
    "iconSpot",
    "workingHours2",
    "iconSpot2",
    "rightSideBanner",
    "elevatorPitch",
    "adjectiveAndTitle"
  ],
  leftSideBanner: [
    "leftSideBanner",
    "nameStack",
    "additionalInformation",
    "location2",
    "iconSpot",
    "workingHours2",
    "iconSpot2"
  ],
  nameStack: ["nameStack"],
  additionalInformation: [
    "additionalInformation",
    "location2",
    "iconSpot",
    "workingHours2",
    "iconSpot2"
  ],
  location2: ["location2", "iconSpot"],
  iconSpot: ["iconSpot"],
  workingHours2: ["workingHours2", "iconSpot2"],
  iconSpot2: ["iconSpot2"],
  rightSideBanner: ["rightSideBanner", "elevatorPitch", "adjectiveAndTitle"],
  elevatorPitch: ["elevatorPitch", "adjectiveAndTitle"],
  adjectiveAndTitle: ["adjectiveAndTitle"],
  exampleBanner: [
    "exampleBanner",
    "bannerContentFormatting2",
    "leftSideBanner2",
    "nameStack2",
    "additionalInformation2",
    "location3",
    "iconSpot3",
    "workingHours3",
    "iconSpot4",
    "rightSideBanner2",
    "elevatorPitch2",
    "adjectiveAndTitle2"
  ],
  bannerContentFormatting2: [
    "bannerContentFormatting2",
    "leftSideBanner2",
    "nameStack2",
    "additionalInformation2",
    "location3",
    "iconSpot3",
    "workingHours3",
    "iconSpot4",
    "rightSideBanner2",
    "elevatorPitch2",
    "adjectiveAndTitle2"
  ],
  leftSideBanner2: [
    "leftSideBanner2",
    "nameStack2",
    "additionalInformation2",
    "location3",
    "iconSpot3",
    "workingHours3",
    "iconSpot4"
  ],
  nameStack2: ["nameStack2"],
  additionalInformation2: [
    "additionalInformation2",
    "location3",
    "iconSpot3",
    "workingHours3",
    "iconSpot4"
  ],
  location3: ["location3", "iconSpot3"],
  iconSpot3: ["iconSpot3"],
  workingHours3: ["workingHours3", "iconSpot4"],
  iconSpot4: ["iconSpot4"],
  rightSideBanner2: [
    "rightSideBanner2",
    "elevatorPitch2",
    "adjectiveAndTitle2"
  ],
  elevatorPitch2: ["elevatorPitch2", "adjectiveAndTitle2"],
  adjectiveAndTitle2: ["adjectiveAndTitle2"],
  onboardingProcess: [
    "onboardingProcess",
    "buttonContainer",
    "topStack",
    "subTextInput"
  ],
  buttonContainer: ["buttonContainer", "topStack"],
  topStack: ["topStack"],
  subTextInput: ["subTextInput"],
  subMultilinePlaceholderTextInput: ["subMultilinePlaceholderTextInput"]
} as const;
type NodeNameType = keyof typeof PlasmicDescendants;
type DescendantsType<T extends NodeNameType> =
  (typeof PlasmicDescendants)[T][number];
type NodeDefaultElementType = {
  onboardingModalContainer: "div";
  headerSection: "section";
  progressButtons: "section";
  goBackButton: typeof SubcomponentButton;
  continueButton: typeof SubcomponentButton;
  lastStepChecklist: "section";
  contExploreButtonStack: "div";
  bannerSetupSection: "section";
  bannerContentFormatting: "section";
  leftSideBanner: "section";
  nameStack: "div";
  additionalInformation: "div";
  location2: typeof SubcomponentIconWithText;
  iconSpot: "svg";
  workingHours2: typeof SubcomponentIconWithText;
  iconSpot2: "svg";
  rightSideBanner: "section";
  elevatorPitch: "div";
  adjectiveAndTitle: "div";
  exampleBanner: "section";
  bannerContentFormatting2: "section";
  leftSideBanner2: "section";
  nameStack2: "div";
  additionalInformation2: "div";
  location3: typeof SubcomponentIconWithText;
  iconSpot3: "svg";
  workingHours3: typeof SubcomponentIconWithText;
  iconSpot4: "svg";
  rightSideBanner2: "section";
  elevatorPitch2: "div";
  adjectiveAndTitle2: "div";
  onboardingProcess: "section";
  buttonContainer: "div";
  topStack: "div";
  subTextInput: typeof SubcomponentTextInput;
  subMultilinePlaceholderTextInput: typeof SubcomponentTextInput;
};

type ReservedPropsType = "variants" | "args" | "overrides";
type NodeOverridesType<T extends NodeNameType> = Pick<
  PlasmicOnboardingOnboardingModal__OverridesType,
  DescendantsType<T>
>;
type NodeComponentProps<T extends NodeNameType> =
  // Explicitly specify variants, args, and overrides as objects
  {
    variants?: PlasmicOnboardingOnboardingModal__VariantsArgs;
    args?: PlasmicOnboardingOnboardingModal__ArgsType;
    overrides?: NodeOverridesType<T>;
  } & Omit<PlasmicOnboardingOnboardingModal__VariantsArgs, ReservedPropsType> & // Specify variants directly as props
    /* Specify args directly as props*/ Omit<
      PlasmicOnboardingOnboardingModal__ArgsType,
      ReservedPropsType
    > &
    /* Specify overrides for each element directly as props*/ Omit<
      NodeOverridesType<T>,
      ReservedPropsType | VariantPropType | ArgPropType
    > &
    /* Specify props for the root element*/ Omit<
      Partial<React.ComponentProps<NodeDefaultElementType[T]>>,
      ReservedPropsType | VariantPropType | ArgPropType | DescendantsType<T>
    >;

function makeNodeComponent<NodeName extends NodeNameType>(nodeName: NodeName) {
  type PropsType = NodeComponentProps<NodeName> & { key?: React.Key };
  const func = function <T extends PropsType>(
    props: T & StrictProps<T, PropsType>
  ) {
    const { variants, args, overrides } = React.useMemo(
      () =>
        deriveRenderOpts(props, {
          name: nodeName,
          descendantNames: PlasmicDescendants[nodeName],
          internalArgPropNames: PlasmicOnboardingOnboardingModal__ArgProps,
          internalVariantPropNames:
            PlasmicOnboardingOnboardingModal__VariantProps
        }),
      [props, nodeName]
    );
    return PlasmicOnboardingOnboardingModal__RenderFunc({
      variants,
      args,
      overrides,
      forNode: nodeName
    });
  };
  if (nodeName === "onboardingModalContainer") {
    func.displayName = "PlasmicOnboardingOnboardingModal";
  } else {
    func.displayName = `PlasmicOnboardingOnboardingModal.${nodeName}`;
  }
  return func;
}

export const PlasmicOnboardingOnboardingModal = Object.assign(
  // Top-level PlasmicOnboardingOnboardingModal renders the root element
  makeNodeComponent("onboardingModalContainer"),
  {
    // Helper components rendering sub-elements
    headerSection: makeNodeComponent("headerSection"),
    progressButtons: makeNodeComponent("progressButtons"),
    goBackButton: makeNodeComponent("goBackButton"),
    continueButton: makeNodeComponent("continueButton"),
    lastStepChecklist: makeNodeComponent("lastStepChecklist"),
    contExploreButtonStack: makeNodeComponent("contExploreButtonStack"),
    bannerSetupSection: makeNodeComponent("bannerSetupSection"),
    bannerContentFormatting: makeNodeComponent("bannerContentFormatting"),
    leftSideBanner: makeNodeComponent("leftSideBanner"),
    nameStack: makeNodeComponent("nameStack"),
    additionalInformation: makeNodeComponent("additionalInformation"),
    location2: makeNodeComponent("location2"),
    iconSpot: makeNodeComponent("iconSpot"),
    workingHours2: makeNodeComponent("workingHours2"),
    iconSpot2: makeNodeComponent("iconSpot2"),
    rightSideBanner: makeNodeComponent("rightSideBanner"),
    elevatorPitch: makeNodeComponent("elevatorPitch"),
    adjectiveAndTitle: makeNodeComponent("adjectiveAndTitle"),
    exampleBanner: makeNodeComponent("exampleBanner"),
    bannerContentFormatting2: makeNodeComponent("bannerContentFormatting2"),
    leftSideBanner2: makeNodeComponent("leftSideBanner2"),
    nameStack2: makeNodeComponent("nameStack2"),
    additionalInformation2: makeNodeComponent("additionalInformation2"),
    location3: makeNodeComponent("location3"),
    iconSpot3: makeNodeComponent("iconSpot3"),
    workingHours3: makeNodeComponent("workingHours3"),
    iconSpot4: makeNodeComponent("iconSpot4"),
    rightSideBanner2: makeNodeComponent("rightSideBanner2"),
    elevatorPitch2: makeNodeComponent("elevatorPitch2"),
    adjectiveAndTitle2: makeNodeComponent("adjectiveAndTitle2"),
    onboardingProcess: makeNodeComponent("onboardingProcess"),
    buttonContainer: makeNodeComponent("buttonContainer"),
    topStack: makeNodeComponent("topStack"),
    subTextInput: makeNodeComponent("subTextInput"),
    subMultilinePlaceholderTextInput: makeNodeComponent(
      "subMultilinePlaceholderTextInput"
    ),

    // Metadata about props expected for PlasmicOnboardingOnboardingModal
    internalVariantProps: PlasmicOnboardingOnboardingModal__VariantProps,
    internalArgProps: PlasmicOnboardingOnboardingModal__ArgProps
  }
);

export default PlasmicOnboardingOnboardingModal;
/* prettier-ignore-end */
