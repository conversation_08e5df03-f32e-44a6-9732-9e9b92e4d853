import * as React from "react";
import {
  DefaultSubcomponentTextInput2Props,
  PlasmicSubcomponentTextInput2
} from "./autogenerated/senergy_platform/PlasmicSubcomponentTextInput2";

import { TextInputRef } from "@plasmicapp/react-web";

export interface SubcomponentTextInput2Props
  extends DefaultSubcomponentTextInput2Props {
  // Feel free to add any additional props that this component should receive
}
function SubcomponentTextInput2_(
  props: SubcomponentTextInput2Props,
  ref: TextInputRef
) {
  const { plasmicProps } =
    PlasmicSubcomponentTextInput2.useBehavior<SubcomponentTextInput2Props>(
      props,
      ref
    );
  return <PlasmicSubcomponentTextInput2 {...plasmicProps} />;
}

const SubcomponentTextInput2 = React.forwardRef(SubcomponentTextInput2_);

export default Object.assign(
  SubcomponentTextInput2,

  {
    __plumeType: "text-input"
  }
);
