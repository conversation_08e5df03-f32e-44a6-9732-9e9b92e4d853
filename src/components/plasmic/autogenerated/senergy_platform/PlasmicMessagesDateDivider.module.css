.formatingContainer {
  display: flex;
  flex-direction: row;
  position: relative;
  width: 100%;
  height: auto;
  justify-content: flex-end;
  align-items: center;
  min-width: 0;
}
.spacingLine {
  display: grid;
  position: relative;
  align-content: center;
  justify-items: flex-end;
  width: 100%;
  height: 1px;
  background: var(--token-p09LDPmbF81_);
  left: auto;
  top: auto;
  z-index: 1;
  right: auto;
  min-width: 0;
  grid-template-columns:
    var(--plsmc-viewport-gap) 1fr minmax(0, var(--plsmc-wide-chunk))
    min(
      var(--plsmc-standard-width),
      calc(100% - var(--plsmc-viewport-gap) - var(--plsmc-viewport-gap))
    )
    minmax(0, var(--plsmc-wide-chunk)) 1fr var(--plsmc-viewport-gap);
  padding: var(--token-sazGmnf7GWAk);
}
.spacingLine > * {
  grid-column: 4;
}
.dateBox {
  display: flex;
  position: relative;
  width: auto;
  height: 24px;
  background: none;
  left: auto;
  top: auto;
  z-index: 2;
  flex-direction: row;
  justify-content: flex-start;
  align-items: center;
  flex-shrink: 0;
  border-radius: 1px;
  padding: var(--token-sazGmnf7GWAk) 8px;
  border: 1px solid var(--token-p09LDPmbF81_);
}
.text {
  position: relative;
  width: 100%;
  height: auto;
  max-width: 100%;
  font-size: var(--token-agfFfkxgxH6d);
  color: var(--token-yPq8Z3hhDPZH);
  min-width: 0;
}
.accentLine {
  display: grid;
  position: relative;
  align-content: center;
  justify-items: flex-end;
  width: 15px;
  height: 1px;
  background: var(--token-p09LDPmbF81_);
  left: auto;
  top: auto;
  z-index: 1;
  right: auto;
  flex-shrink: 0;
  grid-template-columns:
    var(--plsmc-viewport-gap) 1fr minmax(0, var(--plsmc-wide-chunk))
    min(
      var(--plsmc-standard-width),
      calc(100% - var(--plsmc-viewport-gap) - var(--plsmc-viewport-gap))
    )
    minmax(0, var(--plsmc-wide-chunk)) 1fr var(--plsmc-viewport-gap);
  padding: var(--token-sazGmnf7GWAk);
}
.accentLine > * {
  grid-column: 4;
}
