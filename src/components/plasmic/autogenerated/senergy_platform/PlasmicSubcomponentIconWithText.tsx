/* eslint-disable */
/* tslint:disable */
// @ts-nocheck
/* prettier-ignore-start */

/** @jsxRuntime classic */
/** @jsx createPlasmicElementProxy */
/** @jsxFrag React.Fragment */

// This class is auto-generated by Plasmic; please do not edit!
// Plasmic Project: nZeWKcn21KijvC7eT8B3c7
// Component: _eFlbiSm6hZU

"use client";

import * as React from "react";

import Head from "next/head";
import Link, { LinkProps } from "next/link";
import { useRouter } from "next/navigation";

import {
  Flex as Flex__,
  MultiChoiceArg,
  PlasmicDataSourceContextProvider as PlasmicDataSourceContextProvider__,
  PlasmicIcon as PlasmicIcon__,
  PlasmicImg as PlasmicImg__,
  PlasmicLink as PlasmicLink__,
  PlasmicPageGuard as PlasmicPageGuard__,
  SingleBooleanChoiceArg,
  SingleChoiceArg,
  Stack as Stack__,
  StrictProps,
  Trans as Trans__,
  classNames,
  createPlasmicElementProxy,
  deriveRenderOpts,
  ensureGlobalVariants,
  generateOnMutateForSpec,
  generateStateOnChangeProp,
  generateStateOnChangePropForCodeComponents,
  generateStateValueProp,
  get as $stateGet,
  hasVariant,
  initializeCodeComponentStates,
  initializePlasmicStates,
  makeFragment,
  omit,
  pick,
  renderPlasmicSlot,
  set as $stateSet,
  useCurrentUser,
  useDollarState,
  usePlasmicTranslator,
  useTrigger,
  wrapWithClassName
} from "@plasmicapp/react-web";
import {
  DataCtxReader as DataCtxReader__,
  useDataEnv,
  useGlobalActions
} from "@plasmicapp/host";

import SubcomponentTextInput from "../../SubcomponentTextInput"; // plasmic-import: Pt5FPuEzirSe/component

import "@plasmicapp/react-web/lib/plasmic.css";

import projectcss from "./plasmic.module.css"; // plasmic-import: nZeWKcn21KijvC7eT8B3c7/projectcss
import sty from "./PlasmicSubcomponentIconWithText.module.css"; // plasmic-import: _eFlbiSm6hZU/css

import VerificationIcon from "./icons/PlasmicIcon__Verification"; // plasmic-import: IWHvfvi8cOL9/icon

createPlasmicElementProxy;

export type PlasmicSubcomponentIconWithText__VariantMembers = {
  editable: "editableText" | "editableSelector";
  background: "stone" | "forest" | "sage" | "rusticBrick" | "sunflower";
  rounded: "rounded";
  withoutIcon: "withoutIcon";
};
export type PlasmicSubcomponentIconWithText__VariantsArgs = {
  editable?: SingleChoiceArg<"editableText" | "editableSelector">;
  background?: SingleChoiceArg<
    "stone" | "forest" | "sage" | "rusticBrick" | "sunflower"
  >;
  rounded?: SingleBooleanChoiceArg<"rounded">;
  withoutIcon?: SingleBooleanChoiceArg<"withoutIcon">;
};
type VariantPropType = keyof PlasmicSubcomponentIconWithText__VariantsArgs;
export const PlasmicSubcomponentIconWithText__VariantProps =
  new Array<VariantPropType>(
    "editable",
    "background",
    "rounded",
    "withoutIcon"
  );

export type PlasmicSubcomponentIconWithText__ArgsType = {
  iconSpot2?: React.ReactNode;
  displayText?: string;
  inputValue?: string;
  onInputValueChange?: (val: string) => void;
  inputPlaceholder?: string;
  inputHoverText?: string;
  inputDisabled?: boolean;
  inputType?:
    | "text"
    | "password"
    | "hidden"
    | "number"
    | "date"
    | "datetime-local"
    | "time"
    | "email"
    | "tel";
  inputAutoComplete?:
    | { label: "Off"; value: "off" }
    | { label: "On"; value: "on" }
    | { label: "Name"; value: "name" }
    | { label: "Honorific-prefix"; value: "honorific-prefix" }
    | { label: "Given-name"; value: "given-name" }
    | { label: "Additional-name"; value: "additional-name" }
    | { label: "Family-name"; value: "family-name" }
    | { label: "Honorific-suffix"; value: "honorific-suffix" }
    | { label: "Nickname"; value: "nickname" }
    | { label: "Email"; value: "email" }
    | { label: "Username"; value: "username" }
    | { label: "New-password"; value: "new-password" }
    | { label: "Current-password"; value: "current-password" }
    | { label: "One-time-code"; value: "one-time-code" }
    | { label: "Organization-title"; value: "organization-title" }
    | { label: "Organization"; value: "organization" }
    | { label: "Street-address"; value: "street-address" }
    | { label: "Address-line1"; value: "address-line1" }
    | { label: "Address-line2"; value: "address-line2" }
    | { label: "Address-line3"; value: "address-line3" }
    | { label: "Address-level4"; value: "address-level4" }
    | { label: "Address-level3"; value: "address-level3" }
    | { label: "Address-level2"; value: "address-level2" }
    | { label: "Address-level1"; value: "address-level1" }
    | { label: "Country"; value: "country" }
    | { label: "Country-name"; value: "country-name" }
    | { label: "Postal-code"; value: "postal-code" }
    | { label: "Cc-name"; value: "cc-name" }
    | { label: "Cc-given-name"; value: "cc-given-name" }
    | { label: "Cc-additional-name"; value: "cc-additional-name" }
    | { label: "Cc-family-name"; value: "cc-family-name" }
    | { label: "Cc-number"; value: "cc-number" }
    | { label: "Cc-exp"; value: "cc-exp" }
    | { label: "Cc-exp-month"; value: "cc-exp-month" }
    | { label: "Cc-exp-year"; value: "cc-exp-year" }
    | { label: "Cc-csc"; value: "cc-csc" }
    | { label: "Cc-type"; value: "cc-type" }
    | { label: "Transaction-currency"; value: "transaction-currency" }
    | { label: "Transaction-amount"; value: "transaction-amount" }
    | { label: "Language"; value: "language" }
    | { label: "Bday"; value: "bday" }
    | { label: "Bday-day"; value: "bday-day" }
    | { label: "Bday-month"; value: "bday-month" }
    | { label: "Bday-year"; value: "bday-year" }
    | { label: "Sex"; value: "sex" }
    | { label: "Url"; value: "url" }
    | { label: "Photo"; value: "photo" }
    | { label: "Tel"; value: "tel" }
    | { label: "Tel-country-code"; value: "tel-country-code" }
    | { label: "Tel-national"; value: "tel-national" }
    | { label: "Tel-area-code"; value: "tel-area-code" }
    | { label: "Tel-local"; value: "tel-local" }
    | { label: "Tel-local-prefix"; value: "tel-local-prefix" }
    | { label: "Tel-local-suffix"; value: "tel-local-suffix" }
    | { label: "Tel-extension"; value: "tel-extension" }
    | { label: "Impp"; value: "impp" };
  inputTabIndex?: number;
  inputName?: string;
  inputAriaLabel?: string;
  inputAriaLabelledby?: string;
  inputAriaDescribedby?: string;
  inputAriaHidden?:
    | { label: "True"; value: "true" }
    | { label: "False"; value: "false" };
  dropdownOptions?: any;
  dropdownPlaceholderText?: string;
  dropdownName?: string;
  dropdownAriaLabel?: string;
  dropdownAriaLabelledby?: string;
  inputNameAsPlaceholder?: boolean;
  fieldNameRemainVisible?: boolean;
};
type ArgPropType = keyof PlasmicSubcomponentIconWithText__ArgsType;
export const PlasmicSubcomponentIconWithText__ArgProps = new Array<ArgPropType>(
  "iconSpot2",
  "displayText",
  "inputValue",
  "onInputValueChange",
  "inputPlaceholder",
  "inputHoverText",
  "inputDisabled",
  "inputType",
  "inputAutoComplete",
  "inputTabIndex",
  "inputName",
  "inputAriaLabel",
  "inputAriaLabelledby",
  "inputAriaDescribedby",
  "inputAriaHidden",
  "dropdownOptions",
  "dropdownPlaceholderText",
  "dropdownName",
  "dropdownAriaLabel",
  "dropdownAriaLabelledby",
  "inputNameAsPlaceholder",
  "fieldNameRemainVisible"
);

export type PlasmicSubcomponentIconWithText__OverridesType = {
  iconAndTextBondingBox?: Flex__<"div">;
  infoComp?: Flex__<"div">;
  iconSlot?: Flex__<"div">;
  text?: Flex__<"div">;
  textInput?: Flex__<typeof SubcomponentTextInput>;
};

export interface DefaultSubcomponentIconWithTextProps {
  iconSpot2?: React.ReactNode;
  displayText?: string;
  inputValue?: string;
  onInputValueChange?: (val: string) => void;
  inputPlaceholder?: string;
  inputHoverText?: string;
  inputDisabled?: boolean;
  inputType?:
    | "text"
    | "password"
    | "hidden"
    | "number"
    | "date"
    | "datetime-local"
    | "time"
    | "email"
    | "tel";
  inputAutoComplete?:
    | { label: "Off"; value: "off" }
    | { label: "On"; value: "on" }
    | { label: "Name"; value: "name" }
    | { label: "Honorific-prefix"; value: "honorific-prefix" }
    | { label: "Given-name"; value: "given-name" }
    | { label: "Additional-name"; value: "additional-name" }
    | { label: "Family-name"; value: "family-name" }
    | { label: "Honorific-suffix"; value: "honorific-suffix" }
    | { label: "Nickname"; value: "nickname" }
    | { label: "Email"; value: "email" }
    | { label: "Username"; value: "username" }
    | { label: "New-password"; value: "new-password" }
    | { label: "Current-password"; value: "current-password" }
    | { label: "One-time-code"; value: "one-time-code" }
    | { label: "Organization-title"; value: "organization-title" }
    | { label: "Organization"; value: "organization" }
    | { label: "Street-address"; value: "street-address" }
    | { label: "Address-line1"; value: "address-line1" }
    | { label: "Address-line2"; value: "address-line2" }
    | { label: "Address-line3"; value: "address-line3" }
    | { label: "Address-level4"; value: "address-level4" }
    | { label: "Address-level3"; value: "address-level3" }
    | { label: "Address-level2"; value: "address-level2" }
    | { label: "Address-level1"; value: "address-level1" }
    | { label: "Country"; value: "country" }
    | { label: "Country-name"; value: "country-name" }
    | { label: "Postal-code"; value: "postal-code" }
    | { label: "Cc-name"; value: "cc-name" }
    | { label: "Cc-given-name"; value: "cc-given-name" }
    | { label: "Cc-additional-name"; value: "cc-additional-name" }
    | { label: "Cc-family-name"; value: "cc-family-name" }
    | { label: "Cc-number"; value: "cc-number" }
    | { label: "Cc-exp"; value: "cc-exp" }
    | { label: "Cc-exp-month"; value: "cc-exp-month" }
    | { label: "Cc-exp-year"; value: "cc-exp-year" }
    | { label: "Cc-csc"; value: "cc-csc" }
    | { label: "Cc-type"; value: "cc-type" }
    | { label: "Transaction-currency"; value: "transaction-currency" }
    | { label: "Transaction-amount"; value: "transaction-amount" }
    | { label: "Language"; value: "language" }
    | { label: "Bday"; value: "bday" }
    | { label: "Bday-day"; value: "bday-day" }
    | { label: "Bday-month"; value: "bday-month" }
    | { label: "Bday-year"; value: "bday-year" }
    | { label: "Sex"; value: "sex" }
    | { label: "Url"; value: "url" }
    | { label: "Photo"; value: "photo" }
    | { label: "Tel"; value: "tel" }
    | { label: "Tel-country-code"; value: "tel-country-code" }
    | { label: "Tel-national"; value: "tel-national" }
    | { label: "Tel-area-code"; value: "tel-area-code" }
    | { label: "Tel-local"; value: "tel-local" }
    | { label: "Tel-local-prefix"; value: "tel-local-prefix" }
    | { label: "Tel-local-suffix"; value: "tel-local-suffix" }
    | { label: "Tel-extension"; value: "tel-extension" }
    | { label: "Impp"; value: "impp" };
  inputTabIndex?: number;
  inputName?: string;
  inputAriaLabel?: string;
  inputAriaLabelledby?: string;
  inputAriaDescribedby?: string;
  inputAriaHidden?:
    | { label: "True"; value: "true" }
    | { label: "False"; value: "false" };
  dropdownOptions?: any;
  dropdownPlaceholderText?: string;
  dropdownName?: string;
  dropdownAriaLabel?: string;
  dropdownAriaLabelledby?: string;
  inputNameAsPlaceholder?: boolean;
  fieldNameRemainVisible?: boolean;
  editable?: SingleChoiceArg<"editableText" | "editableSelector">;
  background?: SingleChoiceArg<
    "stone" | "forest" | "sage" | "rusticBrick" | "sunflower"
  >;
  rounded?: SingleBooleanChoiceArg<"rounded">;
  withoutIcon?: SingleBooleanChoiceArg<"withoutIcon">;
  className?: string;
}

const $$ = {};

function useNextRouter() {
  try {
    return useRouter();
  } catch {}
  return undefined;
}

function PlasmicSubcomponentIconWithText__RenderFunc(props: {
  variants: PlasmicSubcomponentIconWithText__VariantsArgs;
  args: PlasmicSubcomponentIconWithText__ArgsType;
  overrides: PlasmicSubcomponentIconWithText__OverridesType;
  forNode?: string;
}) {
  const { variants, overrides, forNode } = props;

  const args = React.useMemo(
    () =>
      Object.assign(
        {
          inputPlaceholder: ``,
          inputType: "text",
          dropdownOptions: [],
          dropdownPlaceholderText: "Select...",
          inputNameAsPlaceholder: false,
          fieldNameRemainVisible: true
        },
        Object.fromEntries(
          Object.entries(props.args).filter(([_, v]) => v !== undefined)
        )
      ),
    [props.args]
  );

  const $props = {
    ...args,
    ...variants
  };

  const __nextRouter = useNextRouter();
  const $ctx = useDataEnv?.() || {};
  const refsRef = React.useRef({});
  const $refs = refsRef.current;

  let [$queries, setDollarQueries] = React.useState<
    Record<string, ReturnType<typeof usePlasmicDataOp>>
  >({});
  const stateSpecs: Parameters<typeof useDollarState>[0] = React.useMemo(
    () => [
      {
        path: "background",
        type: "private",
        variableType: "variant",
        initFunc: ({ $props, $state, $queries, $ctx }) => $props.background
      },
      {
        path: "rounded",
        type: "private",
        variableType: "variant",
        initFunc: ({ $props, $state, $queries, $ctx }) => $props.rounded
      },
      {
        path: "withoutIcon",
        type: "private",
        variableType: "variant",
        initFunc: ({ $props, $state, $queries, $ctx }) => $props.withoutIcon
      },
      {
        path: "textInput.value",
        type: "writable",
        variableType: "text",

        valueProp: "inputValue",
        onChangeProp: "onInputValueChange"
      },
      {
        path: "editable",
        type: "private",
        variableType: "variant",
        initFunc: ({ $props, $state, $queries, $ctx }) => $props.editable
      },
      {
        path: "textInput.errorMessage",
        type: "private",
        variableType: "text",
        initFunc: ({ $props, $state, $queries, $ctx }) => ""
      }
    ],
    [$props, $ctx, $refs]
  );
  const $state = useDollarState(stateSpecs, {
    $props,
    $ctx,
    $queries: $queries,
    $refs
  });

  const new$Queries: Record<string, ReturnType<typeof usePlasmicDataOp>> = {};
  if (Object.keys(new$Queries).some(k => new$Queries[k] !== $queries[k])) {
    setDollarQueries(new$Queries);

    $queries = new$Queries;
  }

  return (
    <div
      data-plasmic-name={"iconAndTextBondingBox"}
      data-plasmic-override={overrides.iconAndTextBondingBox}
      data-plasmic-root={true}
      data-plasmic-for-node={forNode}
      className={classNames(
        projectcss.all,
        projectcss.root_reset,
        projectcss.plasmic_default_styles,
        projectcss.plasmic_mixins,
        projectcss.plasmic_tokens,
        sty.iconAndTextBondingBox,
        {
          [sty.iconAndTextBondingBoxbackground_forest]: hasVariant(
            $state,
            "background",
            "forest"
          ),
          [sty.iconAndTextBondingBoxbackground_rusticBrick]: hasVariant(
            $state,
            "background",
            "rusticBrick"
          ),
          [sty.iconAndTextBondingBoxbackground_sage]: hasVariant(
            $state,
            "background",
            "sage"
          ),
          [sty.iconAndTextBondingBoxbackground_stone]: hasVariant(
            $state,
            "background",
            "stone"
          ),
          [sty.iconAndTextBondingBoxbackground_sunflower]: hasVariant(
            $state,
            "background",
            "sunflower"
          ),
          [sty.iconAndTextBondingBoxeditable_editableSelector]: hasVariant(
            $state,
            "editable",
            "editableSelector"
          ),
          [sty.iconAndTextBondingBoxeditable_editableText]: hasVariant(
            $state,
            "editable",
            "editableText"
          ),
          [sty.iconAndTextBondingBoxrounded]: hasVariant(
            $state,
            "rounded",
            "rounded"
          ),
          [sty.iconAndTextBondingBoxwithoutIcon]: hasVariant(
            $state,
            "withoutIcon",
            "withoutIcon"
          )
        }
      )}
    >
      <div
        data-plasmic-name={"infoComp"}
        data-plasmic-override={overrides.infoComp}
        className={classNames(projectcss.all, sty.infoComp, {
          [sty.infoCompbackground_forest]: hasVariant(
            $state,
            "background",
            "forest"
          ),
          [sty.infoCompbackground_sage]: hasVariant(
            $state,
            "background",
            "sage"
          ),
          [sty.infoCompbackground_stone]: hasVariant(
            $state,
            "background",
            "stone"
          ),
          [sty.infoCompeditable_editableSelector]: hasVariant(
            $state,
            "editable",
            "editableSelector"
          ),
          [sty.infoCompeditable_editableText]: hasVariant(
            $state,
            "editable",
            "editableText"
          ),
          [sty.infoComprounded]: hasVariant($state, "rounded", "rounded"),
          [sty.infoComprounded_background_sunflower]:
            hasVariant($state, "background", "sunflower") &&
            hasVariant($state, "rounded", "rounded")
        })}
      >
        <div
          data-plasmic-name={"iconSlot"}
          data-plasmic-override={overrides.iconSlot}
          className={classNames(projectcss.all, sty.iconSlot, {
            [sty.iconSlotbackground_forest]: hasVariant(
              $state,
              "background",
              "forest"
            ),
            [sty.iconSlotbackground_rusticBrick]: hasVariant(
              $state,
              "background",
              "rusticBrick"
            ),
            [sty.iconSlotbackground_sage]: hasVariant(
              $state,
              "background",
              "sage"
            ),
            [sty.iconSlotbackground_stone]: hasVariant(
              $state,
              "background",
              "stone"
            ),
            [sty.iconSlotbackground_sunflower]: hasVariant(
              $state,
              "background",
              "sunflower"
            ),
            [sty.iconSloteditable_editableSelector]: hasVariant(
              $state,
              "editable",
              "editableSelector"
            ),
            [sty.iconSloteditable_editableText]: hasVariant(
              $state,
              "editable",
              "editableText"
            ),
            [sty.iconSlotrounded]: hasVariant($state, "rounded", "rounded"),
            [sty.iconSlotwithoutIcon]: hasVariant(
              $state,
              "withoutIcon",
              "withoutIcon"
            )
          })}
        >
          {renderPlasmicSlot({
            defaultContents: (
              <VerificationIcon
                className={classNames(projectcss.all, sty.svg___32Cl6, {
                  [sty.svgwithoutIcon___32Cl6Gyrb6]: hasVariant(
                    $state,
                    "withoutIcon",
                    "withoutIcon"
                  )
                })}
                role={"img"}
              />
            ),

            value: args.iconSpot2
          })}
        </div>
        <div
          data-plasmic-name={"text"}
          data-plasmic-override={overrides.text}
          className={classNames(projectcss.all, sty.text, {
            [sty.textbackground_rusticBrick]: hasVariant(
              $state,
              "background",
              "rusticBrick"
            ),
            [sty.textbackground_sage]: hasVariant($state, "background", "sage"),
            [sty.textbackground_stone]: hasVariant(
              $state,
              "background",
              "stone"
            ),
            [sty.texteditable_editableSelector]: hasVariant(
              $state,
              "editable",
              "editableSelector"
            ),
            [sty.texteditable_editableText]: hasVariant(
              $state,
              "editable",
              "editableText"
            )
          })}
        >
          <SubcomponentTextInput
            data-plasmic-name={"textInput"}
            data-plasmic-override={overrides.textInput}
            className={classNames("__wab_instance", sty.textInput, {
              [sty.textInputbackground_forest]: hasVariant(
                $state,
                "background",
                "forest"
              ),
              [sty.textInputbackground_stone]: hasVariant(
                $state,
                "background",
                "stone"
              ),
              [sty.textInputeditable_editableSelector]: hasVariant(
                $state,
                "editable",
                "editableSelector"
              ),
              [sty.textInputeditable_editableText]: hasVariant(
                $state,
                "editable",
                "editableText"
              ),
              [sty.textInputrounded]: hasVariant($state, "rounded", "rounded")
            })}
            displayText={args.displayText}
            editView={"core"}
            errorMessage={generateStateValueProp($state, [
              "textInput",
              "errorMessage"
            ])}
            fieldNameRemainVisible={args.fieldNameRemainVisible}
            inputAriaDescribedby={args.inputAriaDescribedby}
            inputAriaHidden={args.inputAriaHidden}
            inputAriaLabel={args.inputAriaLabel}
            inputAriaLabelledby={args.inputAriaLabelledby}
            inputAutoComplete={args.inputAutoComplete}
            inputDisabled={args.inputDisabled}
            inputHoverText={args.inputHoverText}
            inputName={args.inputName}
            inputNameAsPlaceholder={args.inputNameAsPlaceholder}
            inputPlaceholder={args.inputPlaceholder}
            inputTabIndex={args.inputTabIndex}
            inputType={args.inputType}
            inputValue={generateStateValueProp($state, ["textInput", "value"])}
            onErrorMessageChange={async (...eventArgs: any) => {
              generateStateOnChangeProp($state, [
                "textInput",
                "errorMessage"
              ]).apply(null, eventArgs);

              if (
                eventArgs.length > 1 &&
                eventArgs[1] &&
                eventArgs[1]._plasmic_state_init_
              ) {
                return;
              }
            }}
            onInputValueChange={async (...eventArgs: any) => {
              generateStateOnChangeProp($state, ["textInput", "value"]).apply(
                null,
                eventArgs
              );

              if (
                eventArgs.length > 1 &&
                eventArgs[1] &&
                eventArgs[1]._plasmic_state_init_
              ) {
                return;
              }
            }}
            viewOnly={
              hasVariant($state, "editable", "editableSelector")
                ? undefined
                : hasVariant($state, "editable", "editableText")
                ? undefined
                : true
            }
          />
        </div>
      </div>
    </div>
  ) as React.ReactElement | null;
}

const PlasmicDescendants = {
  iconAndTextBondingBox: [
    "iconAndTextBondingBox",
    "infoComp",
    "iconSlot",
    "text",
    "textInput"
  ],
  infoComp: ["infoComp", "iconSlot", "text", "textInput"],
  iconSlot: ["iconSlot"],
  text: ["text", "textInput"],
  textInput: ["textInput"]
} as const;
type NodeNameType = keyof typeof PlasmicDescendants;
type DescendantsType<T extends NodeNameType> =
  (typeof PlasmicDescendants)[T][number];
type NodeDefaultElementType = {
  iconAndTextBondingBox: "div";
  infoComp: "div";
  iconSlot: "div";
  text: "div";
  textInput: typeof SubcomponentTextInput;
};

type ReservedPropsType = "variants" | "args" | "overrides";
type NodeOverridesType<T extends NodeNameType> = Pick<
  PlasmicSubcomponentIconWithText__OverridesType,
  DescendantsType<T>
>;
type NodeComponentProps<T extends NodeNameType> =
  // Explicitly specify variants, args, and overrides as objects
  {
    variants?: PlasmicSubcomponentIconWithText__VariantsArgs;
    args?: PlasmicSubcomponentIconWithText__ArgsType;
    overrides?: NodeOverridesType<T>;
  } & Omit<PlasmicSubcomponentIconWithText__VariantsArgs, ReservedPropsType> & // Specify variants directly as props
    /* Specify args directly as props*/ Omit<
      PlasmicSubcomponentIconWithText__ArgsType,
      ReservedPropsType
    > &
    /* Specify overrides for each element directly as props*/ Omit<
      NodeOverridesType<T>,
      ReservedPropsType | VariantPropType | ArgPropType
    > &
    /* Specify props for the root element*/ Omit<
      Partial<React.ComponentProps<NodeDefaultElementType[T]>>,
      ReservedPropsType | VariantPropType | ArgPropType | DescendantsType<T>
    >;

function makeNodeComponent<NodeName extends NodeNameType>(nodeName: NodeName) {
  type PropsType = NodeComponentProps<NodeName> & { key?: React.Key };
  const func = function <T extends PropsType>(
    props: T & StrictProps<T, PropsType>
  ) {
    const { variants, args, overrides } = React.useMemo(
      () =>
        deriveRenderOpts(props, {
          name: nodeName,
          descendantNames: PlasmicDescendants[nodeName],
          internalArgPropNames: PlasmicSubcomponentIconWithText__ArgProps,
          internalVariantPropNames:
            PlasmicSubcomponentIconWithText__VariantProps
        }),
      [props, nodeName]
    );
    return PlasmicSubcomponentIconWithText__RenderFunc({
      variants,
      args,
      overrides,
      forNode: nodeName
    });
  };
  if (nodeName === "iconAndTextBondingBox") {
    func.displayName = "PlasmicSubcomponentIconWithText";
  } else {
    func.displayName = `PlasmicSubcomponentIconWithText.${nodeName}`;
  }
  return func;
}

export const PlasmicSubcomponentIconWithText = Object.assign(
  // Top-level PlasmicSubcomponentIconWithText renders the root element
  makeNodeComponent("iconAndTextBondingBox"),
  {
    // Helper components rendering sub-elements
    infoComp: makeNodeComponent("infoComp"),
    iconSlot: makeNodeComponent("iconSlot"),
    text: makeNodeComponent("text"),
    textInput: makeNodeComponent("textInput"),

    // Metadata about props expected for PlasmicSubcomponentIconWithText
    internalVariantProps: PlasmicSubcomponentIconWithText__VariantProps,
    internalArgProps: PlasmicSubcomponentIconWithText__ArgProps
  }
);

export default PlasmicSubcomponentIconWithText;
/* prettier-ignore-end */
