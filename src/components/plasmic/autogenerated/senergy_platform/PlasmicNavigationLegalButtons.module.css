.root {
  display: inline-flex;
  flex-direction: row;
  width: auto;
  height: 32px;
  justify-content: flex-start;
  align-items: center;
  justify-self: flex-start;
  position: relative;
  cursor: pointer;
  padding: 0px;
  border-width: 1px;
  border-style: none;
}
.formattingStack {
  display: flex;
  position: relative;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start;
  width: 100%;
  height: auto;
  max-width: 100%;
  min-width: 0;
  padding: var(--token-sazGmnf7GWAk);
  margin: var(--token-sazGmnf7GWAk);
}
.textSlotContainer {
  width: 100%;
  height: auto;
  max-width: 100%;
  display: flex;
  flex-direction: row;
  min-width: 0;
}
.slotTargetChildren {
  font-family: var(--token-z1yrQVi72Nj1);
  font-size: var(--token-agfFfkxgxH6d);
}
.underlineSection {
  display: flex;
  position: relative;
  width: 100%;
  height: 1px;
  flex-direction: row;
  justify-content: center;
  align-items: flex-start;
  background: var(--token-MPvkQ1rq8Due);
  min-width: 0;
  flex-shrink: 0;
  padding: var(--token-sazGmnf7GWAk);
}
.root:hover .underlineSection {
  background: linear-gradient(
      var(--token-K5FbAPSIIrXM),
      var(--token-K5FbAPSIIrXM)
    ),
    var(--token-MPvkQ1rq8Due);
}
