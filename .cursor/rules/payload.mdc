---
description: 
globs: 
alwaysApply: true
---
- We are using PayloadCMS 3.0
- A fully initialized payload instance is available to be imported from `src/lib/payload.ts`
- Whenever you need to import anything from payload the correct import is always `"from 'payload'` never a sub-module like `payload/fields` or `payload/types`.
- Use types from `payload-types.ts` whenever possible. Do not create duplicate types.
- Some types in Payload have nested relationships. For instance, a `User` type inside a `Organization` type may look like this: `User: String | User`. If the embedded field isn't loaded, it will be a string. Ensure your type patterns are robust against this.
- Use `zodToPayload` to validate fields.
- After modifying any collection (files in `src/collection`), run `pnpm generate:types` to update the types
- You shouldn't need to create routes to interact with payload data as payload auto generates API's which you should use from the client.