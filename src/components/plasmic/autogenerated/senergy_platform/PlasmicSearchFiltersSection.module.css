.root {
  display: flex;
  flex-direction: column;
  position: relative;
  width: 100%;
  height: auto;
  justify-content: flex-start;
  align-items: center;
  min-width: 0;
}
.searchCoreFilterIcon__sv47I:global(.__wab_instance) {
  max-width: 100%;
  position: relative;
}
.searchCoreFilterIcon__yz2Er:global(.__wab_instance) {
  max-width: 100%;
  position: relative;
}
.searchCoreFilterIcon__jkStd:global(.__wab_instance) {
  max-width: 100%;
  position: relative;
}
.searchCoreFilterIcon__ryUyv:global(.__wab_instance) {
  max-width: 100%;
  position: relative;
}
.searchCoreFilterIcon__coTr:global(.__wab_instance) {
  max-width: 100%;
  position: relative;
}
.searchCoreFilterIcon__f9I8W:global(.__wab_instance) {
  max-width: 100%;
  position: relative;
}
.searchCoreFilterIcon__eaPDp:global(.__wab_instance) {
  max-width: 100%;
  position: relative;
}
.searchCoreFilterIcon___24B:global(.__wab_instance) {
  max-width: 100%;
  position: relative;
}
.searchCoreFilterIcon__xIMsq:global(.__wab_instance) {
  max-width: 100%;
  position: relative;
}
.searchCoreFilterIcon__s1Fj3:global(.__wab_instance) {
  max-width: 100%;
  position: relative;
}
.searchCoreFilterIcon__jnuYa:global(.__wab_instance) {
  max-width: 100%;
  position: relative;
}
