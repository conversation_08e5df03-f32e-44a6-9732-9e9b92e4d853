import { registerComponent } from '@plasmicapp/react-web/lib/host';
import { Button } from '.';

export function registerButton() {
  registerComponent(Button, {
    name: 'Button',
    displayName: 'Button',
    importPath: '@/components/ui/button',
    classNameProp: 'className',
    props: {
      variant: {
        type: 'choice',
        options: [
          'default',
          'destructive',
          'outline',
          'secondary',
          'ghost',
          'link',
        ],
        defaultValue: 'default',
      },
      effect: {
        type: 'choice',
        options: [
          'ringHover',
          'shine',
          'shineHover',
          'gooeyRight',
          'gooeyLeft',
          'underline',
          'hoverUnderline',
        ],
        defaultValue: 'default',
      },
      size: {
        type: 'choice',
        options: ['default', 'sm', 'lg', 'icon'],
        defaultValue: 'default',
      },
      asChild: {
        type: 'boolean',
        defaultValue: false,
      },
      title: {
        type: 'string',
        defaultValue: 'Button',
      },
      children: {
        type: 'slot',
        defaultValue: ['Button'],
      },
      onClick: {
        type: 'eventHandler',
        argTypes: [],
      },
    },
  });
}
