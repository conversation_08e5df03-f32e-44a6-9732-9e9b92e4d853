.root {
  display: inline-flex;
  position: relative;
  width: auto;
  height: auto;
  justify-self: flex-start;
  flex-direction: row;
  justify-content: flex-start;
  align-items: center;
}
.section {
  display: flex;
  position: relative;
  width: 54px;
  background: var(--token-1AMvw6c2eIK7);
  flex-direction: row;
  left: auto;
  top: auto;
  height: auto;
  justify-content: flex-start;
  align-items: center;
  transition-property: background;
  transition-duration: 0.2s;
  transition-timing-function: ease-in-out;
  transition-delay: 0.04s;
  flex-shrink: 0;
  -webkit-transition-property: background;
  -webkit-transition-timing-function: ease-in-out;
  -webkit-transition-duration: 0.2s;
  -webkit-transition-delay: 0.04s;
  padding: 2px;
  border: 1px solid var(--token-p09LDPmbF81_);
}
.sectionon {
  justify-content: flex-start;
  align-items: center;
  background: var(--token-K5FbAPSIIrXM);
  transform: translateX(0px) translateY(0px) translateZ(0px);
}
.sectionsize_small {
  width: 42px;
  flex-shrink: 0;
}
.svg {
  position: relative;
  object-fit: cover;
  max-width: 100%;
  width: 24px;
  height: 24px;
  transition-property: transform, color;
  transition-duration: 0.25s, 0.3s;
  transition-timing-function: ease-in-out, ease;
  flex-shrink: 0;
  -webkit-transition-property: transform, color;
  -webkit-transition-timing-function: ease-in-out, ease;
  -webkit-transition-duration: 0.25s, 0.3s;
}
.svgon {
  color: var(--token-1AMvw6c2eIK7);
  transform: translateX(24px) translateY(0px) translateZ(0px);
}
.svgsize_small {
  width: 18px;
  height: 18px;
  flex-shrink: 0;
}
.svgsize_small_on {
  transform: translateX(18px) translateY(0px) translateZ(0px);
}
