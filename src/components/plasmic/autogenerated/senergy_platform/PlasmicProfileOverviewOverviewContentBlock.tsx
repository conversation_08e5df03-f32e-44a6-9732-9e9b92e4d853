/* eslint-disable */
/* tslint:disable */
// @ts-nocheck
/* prettier-ignore-start */

/** @jsxRuntime classic */
/** @jsx createPlasmicElementProxy */
/** @jsxFrag React.Fragment */

// This class is auto-generated by Plasmic; please do not edit!
// Plasmic Project: nZeWKcn21KijvC7eT8B3c7
// Component: 8Z-KG0onltAY

"use client";

import * as React from "react";

import Head from "next/head";
import Link, { LinkProps } from "next/link";
import { useRouter } from "next/navigation";

import {
  Flex as Flex__,
  MultiChoiceArg,
  PlasmicDataSourceContextProvider as PlasmicDataSourceContextProvider__,
  PlasmicIcon as PlasmicIcon__,
  PlasmicImg as PlasmicImg__,
  PlasmicLink as PlasmicLink__,
  PlasmicPageGuard as PlasmicPageGuard__,
  SingleBooleanChoiceArg,
  SingleChoiceArg,
  Stack as St<PERSON>__,
  StrictProps,
  Trans as Trans__,
  classNames,
  createPlasmicElementProxy,
  deriveRenderOpts,
  ensureGlobalVariants,
  generateOnMutateForSpec,
  generateStateOnChangeProp,
  generateStateOnChangePropForCodeComponents,
  generateStateValueProp,
  get as $stateGet,
  hasVariant,
  initializeCodeComponentStates,
  initializePlasmicStates,
  makeFragment,
  omit,
  pick,
  renderPlasmicSlot,
  set as $stateSet,
  useCurrentUser,
  useDollarState,
  usePlasmicTranslator,
  useTrigger,
  wrapWithClassName
} from "@plasmicapp/react-web";
import {
  DataCtxReader as DataCtxReader__,
  useDataEnv,
  useGlobalActions
} from "@plasmicapp/host";

import ProfileSectionsProfileSectionHeading from "../../ProfileSectionsProfileSectionHeading"; // plasmic-import: Uz656rZzIxzC/component
import ProfileOverviewHighlightedWorksLayout from "../../ProfileOverviewHighlightedWorksLayout"; // plasmic-import: Vq_8v2qcFNSB/component
import ProfileOverviewSeeMoreButton from "../../ProfileOverviewSeeMoreButton"; // plasmic-import: UWTH8LMACb3s/component
import ProfileOverviewOverviewBlocks from "../../ProfileOverviewOverviewBlocks"; // plasmic-import: wf5-zG-i1Jzo/component

import { useScreenVariants as useScreenVariants_4Hrhi5G5ANwQ } from "./PlasmicGlobalVariant__FormattingBreakPoint"; // plasmic-import: 4Hrhi5G5aNwQ/globalVariant

import "@plasmicapp/react-web/lib/plasmic.css";

import projectcss from "./plasmic.module.css"; // plasmic-import: nZeWKcn21KijvC7eT8B3c7/projectcss
import sty from "./PlasmicProfileOverviewOverviewContentBlock.module.css"; // plasmic-import: 8Z-KG0onltAY/css

createPlasmicElementProxy;

export type PlasmicProfileOverviewOverviewContentBlock__VariantMembers = {};
export type PlasmicProfileOverviewOverviewContentBlock__VariantsArgs = {};
type VariantPropType =
  keyof PlasmicProfileOverviewOverviewContentBlock__VariantsArgs;
export const PlasmicProfileOverviewOverviewContentBlock__VariantProps =
  new Array<VariantPropType>();

export type PlasmicProfileOverviewOverviewContentBlock__ArgsType = {
  children?: React.ReactNode;
  allEducation?: any;
  allExperience?: any;
  allPublications?: any;
  allLicenses?: any;
  allCertifications?: any;
  allPatents?: any;
  allTrademarks?: any;
  onAllEducationChange?: (val: string) => void;
  onAllExperienceChange?: (val: string) => void;
  onAllPublicationsChange?: (val: string) => void;
  onAllLicensesChange?: (val: string) => void;
  onAllCertificationsChange?: (val: string) => void;
  onAllPatentsChange?: (val: string) => void;
  onAllTrademarksChange?: (val: string) => void;
  slot?: React.ReactNode;
};
type ArgPropType = keyof PlasmicProfileOverviewOverviewContentBlock__ArgsType;
export const PlasmicProfileOverviewOverviewContentBlock__ArgProps =
  new Array<ArgPropType>(
    "children",
    "allEducation",
    "allExperience",
    "allPublications",
    "allLicenses",
    "allCertifications",
    "allPatents",
    "allTrademarks",
    "onAllEducationChange",
    "onAllExperienceChange",
    "onAllPublicationsChange",
    "onAllLicensesChange",
    "onAllCertificationsChange",
    "onAllPatentsChange",
    "onAllTrademarksChange",
    "slot"
  );

export type PlasmicProfileOverviewOverviewContentBlock__OverridesType = {
  overviewContentBlock?: Flex__<"div">;
  overviewHeading?: Flex__<typeof ProfileSectionsProfileSectionHeading>;
  text?: Flex__<"div">;
  featuredWorksArea?: Flex__<"div">;
  profileOverviewHighlightedWorksLayout?: Flex__<
    typeof ProfileOverviewHighlightedWorksLayout
  >;
  seeMoreButtonContainer2?: Flex__<"div">;
  summaryArea?: Flex__<"div">;
  personalSummaryHeading?: Flex__<typeof ProfileSectionsProfileSectionHeading>;
  freeBox?: Flex__<"div">;
  introductionBlock?: Flex__<typeof ProfileOverviewOverviewBlocks>;
  overviewBlockGrid?: Flex__<"div">;
  skillsAndToolsStack?: Flex__<"div">;
  skillsBlock?: Flex__<typeof ProfileOverviewOverviewBlocks>;
  toolsBlock?: Flex__<typeof ProfileOverviewOverviewBlocks>;
  leftStackUserDefined?: Flex__<"div">;
  _2XWideBlock?: Flex__<typeof ProfileOverviewOverviewBlocks>;
  bottomBlocksSections?: Flex__<"div">;
  leftBottomBlock?: Flex__<typeof ProfileOverviewOverviewBlocks>;
  rightBottomBlock?: Flex__<typeof ProfileOverviewOverviewBlocks>;
  seeMoreButtonContainer?: Flex__<"div">;
};

export interface DefaultProfileOverviewOverviewContentBlockProps {
  children?: React.ReactNode;
  allEducation?: any;
  allExperience?: any;
  allPublications?: any;
  allLicenses?: any;
  allCertifications?: any;
  allPatents?: any;
  allTrademarks?: any;
  onAllEducationChange?: (val: string) => void;
  onAllExperienceChange?: (val: string) => void;
  onAllPublicationsChange?: (val: string) => void;
  onAllLicensesChange?: (val: string) => void;
  onAllCertificationsChange?: (val: string) => void;
  onAllPatentsChange?: (val: string) => void;
  onAllTrademarksChange?: (val: string) => void;
  slot?: React.ReactNode;
  className?: string;
}

const $$ = {};

function useNextRouter() {
  try {
    return useRouter();
  } catch {}
  return undefined;
}

function PlasmicProfileOverviewOverviewContentBlock__RenderFunc(props: {
  variants: PlasmicProfileOverviewOverviewContentBlock__VariantsArgs;
  args: PlasmicProfileOverviewOverviewContentBlock__ArgsType;
  overrides: PlasmicProfileOverviewOverviewContentBlock__OverridesType;
  forNode?: string;
}) {
  const { variants, overrides, forNode } = props;

  const args = React.useMemo(
    () =>
      Object.assign(
        {},
        Object.fromEntries(
          Object.entries(props.args).filter(([_, v]) => v !== undefined)
        )
      ),
    [props.args]
  );

  const $props = {
    ...args,
    ...variants
  };

  const __nextRouter = useNextRouter();
  const $ctx = useDataEnv?.() || {};
  const refsRef = React.useRef({});
  const $refs = refsRef.current;

  let [$queries, setDollarQueries] = React.useState<
    Record<string, ReturnType<typeof usePlasmicDataOp>>
  >({});
  const stateSpecs: Parameters<typeof useDollarState>[0] = React.useMemo(
    () => [
      {
        path: "allEducation",
        type: "writable",
        variableType: "object",

        valueProp: "allEducation",
        onChangeProp: "onAllEducationChange"
      },
      {
        path: "introductionBlock.allEducation",
        type: "private",
        variableType: "object",
        initFunc: ({ $props, $state, $queries, $ctx }) => ({})
      },
      {
        path: "_2XWideBlock.allEducation",
        type: "private",
        variableType: "object",
        initFunc: ({ $props, $state, $queries, $ctx }) =>
          (() => {
            try {
              return $state.allEducation;
            } catch (e) {
              if (
                e instanceof TypeError ||
                e?.plasmicType === "PlasmicUndefinedDataError"
              ) {
                return undefined;
              }
              throw e;
            }
          })()
      },
      {
        path: "skillsBlock.allEducation",
        type: "private",
        variableType: "object",
        initFunc: ({ $props, $state, $queries, $ctx }) => ({})
      },
      {
        path: "leftBottomBlock.allEducation",
        type: "private",
        variableType: "object",
        initFunc: ({ $props, $state, $queries, $ctx }) =>
          (() => {
            try {
              return $state.allEducation;
            } catch (e) {
              if (
                e instanceof TypeError ||
                e?.plasmicType === "PlasmicUndefinedDataError"
              ) {
                return undefined;
              }
              throw e;
            }
          })()
      },
      {
        path: "rightBottomBlock.allEducation",
        type: "private",
        variableType: "object",
        initFunc: ({ $props, $state, $queries, $ctx }) =>
          (() => {
            try {
              return $state.allEducation;
            } catch (e) {
              if (
                e instanceof TypeError ||
                e?.plasmicType === "PlasmicUndefinedDataError"
              ) {
                return undefined;
              }
              throw e;
            }
          })()
      },
      {
        path: "toolsBlock.allEducation",
        type: "private",
        variableType: "object",
        initFunc: ({ $props, $state, $queries, $ctx }) => ({})
      },
      {
        path: "introductionBlock.allExperience",
        type: "private",
        variableType: "object",
        initFunc: ({ $props, $state, $queries, $ctx }) => ({})
      },
      {
        path: "_2XWideBlock.allExperience",
        type: "private",
        variableType: "object",
        initFunc: ({ $props, $state, $queries, $ctx }) =>
          (() => {
            try {
              return $state.allExperience;
            } catch (e) {
              if (
                e instanceof TypeError ||
                e?.plasmicType === "PlasmicUndefinedDataError"
              ) {
                return undefined;
              }
              throw e;
            }
          })()
      },
      {
        path: "skillsBlock.allExperience",
        type: "private",
        variableType: "object",
        initFunc: ({ $props, $state, $queries, $ctx }) => ({})
      },
      {
        path: "leftBottomBlock.allExperience",
        type: "private",
        variableType: "object",
        initFunc: ({ $props, $state, $queries, $ctx }) =>
          (() => {
            try {
              return $state.allExperience;
            } catch (e) {
              if (
                e instanceof TypeError ||
                e?.plasmicType === "PlasmicUndefinedDataError"
              ) {
                return undefined;
              }
              throw e;
            }
          })()
      },
      {
        path: "rightBottomBlock.allExperience",
        type: "private",
        variableType: "object",
        initFunc: ({ $props, $state, $queries, $ctx }) =>
          (() => {
            try {
              return $state.allExperience;
            } catch (e) {
              if (
                e instanceof TypeError ||
                e?.plasmicType === "PlasmicUndefinedDataError"
              ) {
                return undefined;
              }
              throw e;
            }
          })()
      },
      {
        path: "toolsBlock.allExperience",
        type: "private",
        variableType: "object",
        initFunc: ({ $props, $state, $queries, $ctx }) => ({})
      },
      {
        path: "allExperience",
        type: "writable",
        variableType: "object",

        valueProp: "allExperience",
        onChangeProp: "onAllExperienceChange"
      },
      {
        path: "allPublications",
        type: "writable",
        variableType: "object",

        valueProp: "allPublications",
        onChangeProp: "onAllPublicationsChange"
      },
      {
        path: "allLicenses",
        type: "writable",
        variableType: "object",

        valueProp: "allLicenses",
        onChangeProp: "onAllLicensesChange"
      },
      {
        path: "allCertifications",
        type: "writable",
        variableType: "object",

        valueProp: "allCertifications",
        onChangeProp: "onAllCertificationsChange"
      },
      {
        path: "allPatents",
        type: "writable",
        variableType: "object",

        valueProp: "allPatents",
        onChangeProp: "onAllPatentsChange"
      },
      {
        path: "allTrademarks",
        type: "writable",
        variableType: "object",

        valueProp: "allTrademarks",
        onChangeProp: "onAllTrademarksChange"
      },
      {
        path: "introductionBlock.allPublications",
        type: "private",
        variableType: "object",
        initFunc: ({ $props, $state, $queries, $ctx }) => ({})
      },
      {
        path: "_2XWideBlock.allPublications",
        type: "private",
        variableType: "object",
        initFunc: ({ $props, $state, $queries, $ctx }) =>
          (() => {
            try {
              return $state.allPublications;
            } catch (e) {
              if (
                e instanceof TypeError ||
                e?.plasmicType === "PlasmicUndefinedDataError"
              ) {
                return undefined;
              }
              throw e;
            }
          })()
      },
      {
        path: "skillsBlock.allPublications",
        type: "private",
        variableType: "object",
        initFunc: ({ $props, $state, $queries, $ctx }) => ({})
      },
      {
        path: "leftBottomBlock.allPublications",
        type: "private",
        variableType: "object",
        initFunc: ({ $props, $state, $queries, $ctx }) =>
          (() => {
            try {
              return $state.allPublications;
            } catch (e) {
              if (
                e instanceof TypeError ||
                e?.plasmicType === "PlasmicUndefinedDataError"
              ) {
                return undefined;
              }
              throw e;
            }
          })()
      },
      {
        path: "rightBottomBlock.allPublications",
        type: "private",
        variableType: "object",
        initFunc: ({ $props, $state, $queries, $ctx }) =>
          (() => {
            try {
              return $state.allPublications;
            } catch (e) {
              if (
                e instanceof TypeError ||
                e?.plasmicType === "PlasmicUndefinedDataError"
              ) {
                return undefined;
              }
              throw e;
            }
          })()
      },
      {
        path: "toolsBlock.allPublications",
        type: "private",
        variableType: "object",
        initFunc: ({ $props, $state, $queries, $ctx }) => ({})
      },
      {
        path: "introductionBlock.allPatents",
        type: "private",
        variableType: "object",
        initFunc: ({ $props, $state, $queries, $ctx }) => ({})
      },
      {
        path: "_2XWideBlock.allPatents",
        type: "private",
        variableType: "object",
        initFunc: ({ $props, $state, $queries, $ctx }) =>
          (() => {
            try {
              return $state.allPatents;
            } catch (e) {
              if (
                e instanceof TypeError ||
                e?.plasmicType === "PlasmicUndefinedDataError"
              ) {
                return undefined;
              }
              throw e;
            }
          })()
      },
      {
        path: "skillsBlock.allPatents",
        type: "private",
        variableType: "object",
        initFunc: ({ $props, $state, $queries, $ctx }) => ({})
      },
      {
        path: "leftBottomBlock.allPatents",
        type: "private",
        variableType: "object",
        initFunc: ({ $props, $state, $queries, $ctx }) =>
          (() => {
            try {
              return $state.allPatents;
            } catch (e) {
              if (
                e instanceof TypeError ||
                e?.plasmicType === "PlasmicUndefinedDataError"
              ) {
                return undefined;
              }
              throw e;
            }
          })()
      },
      {
        path: "rightBottomBlock.allPatents",
        type: "private",
        variableType: "object",
        initFunc: ({ $props, $state, $queries, $ctx }) =>
          (() => {
            try {
              return $state.allPatents;
            } catch (e) {
              if (
                e instanceof TypeError ||
                e?.plasmicType === "PlasmicUndefinedDataError"
              ) {
                return undefined;
              }
              throw e;
            }
          })()
      },
      {
        path: "toolsBlock.allPatents",
        type: "private",
        variableType: "object",
        initFunc: ({ $props, $state, $queries, $ctx }) => ({})
      },
      {
        path: "introductionBlock.allTrademarks",
        type: "private",
        variableType: "object",
        initFunc: ({ $props, $state, $queries, $ctx }) => ({})
      },
      {
        path: "_2XWideBlock.allTrademarks",
        type: "private",
        variableType: "object",
        initFunc: ({ $props, $state, $queries, $ctx }) =>
          (() => {
            try {
              return $state.allTrademarks;
            } catch (e) {
              if (
                e instanceof TypeError ||
                e?.plasmicType === "PlasmicUndefinedDataError"
              ) {
                return undefined;
              }
              throw e;
            }
          })()
      },
      {
        path: "skillsBlock.allTrademarks",
        type: "private",
        variableType: "object",
        initFunc: ({ $props, $state, $queries, $ctx }) => ({})
      },
      {
        path: "leftBottomBlock.allTrademarks",
        type: "private",
        variableType: "object",
        initFunc: ({ $props, $state, $queries, $ctx }) =>
          (() => {
            try {
              return $state.allTrademarks;
            } catch (e) {
              if (
                e instanceof TypeError ||
                e?.plasmicType === "PlasmicUndefinedDataError"
              ) {
                return undefined;
              }
              throw e;
            }
          })()
      },
      {
        path: "rightBottomBlock.allTrademarks",
        type: "private",
        variableType: "object",
        initFunc: ({ $props, $state, $queries, $ctx }) =>
          (() => {
            try {
              return $state.allTrademarks;
            } catch (e) {
              if (
                e instanceof TypeError ||
                e?.plasmicType === "PlasmicUndefinedDataError"
              ) {
                return undefined;
              }
              throw e;
            }
          })()
      },
      {
        path: "toolsBlock.allTrademarks",
        type: "private",
        variableType: "object",
        initFunc: ({ $props, $state, $queries, $ctx }) => ({})
      },
      {
        path: "introductionBlock.allCertifications",
        type: "private",
        variableType: "object",
        initFunc: ({ $props, $state, $queries, $ctx }) => ({})
      },
      {
        path: "_2XWideBlock.allCertifications",
        type: "private",
        variableType: "object",
        initFunc: ({ $props, $state, $queries, $ctx }) =>
          (() => {
            try {
              return $state.allCertifications;
            } catch (e) {
              if (
                e instanceof TypeError ||
                e?.plasmicType === "PlasmicUndefinedDataError"
              ) {
                return undefined;
              }
              throw e;
            }
          })()
      },
      {
        path: "skillsBlock.allCertifications",
        type: "private",
        variableType: "object",
        initFunc: ({ $props, $state, $queries, $ctx }) => ({})
      },
      {
        path: "leftBottomBlock.allCertifications",
        type: "private",
        variableType: "object",
        initFunc: ({ $props, $state, $queries, $ctx }) =>
          (() => {
            try {
              return $state.allCertifications;
            } catch (e) {
              if (
                e instanceof TypeError ||
                e?.plasmicType === "PlasmicUndefinedDataError"
              ) {
                return undefined;
              }
              throw e;
            }
          })()
      },
      {
        path: "rightBottomBlock.allCertifications",
        type: "private",
        variableType: "object",
        initFunc: ({ $props, $state, $queries, $ctx }) =>
          (() => {
            try {
              return $state.allCertifications;
            } catch (e) {
              if (
                e instanceof TypeError ||
                e?.plasmicType === "PlasmicUndefinedDataError"
              ) {
                return undefined;
              }
              throw e;
            }
          })()
      },
      {
        path: "toolsBlock.allCertifications",
        type: "private",
        variableType: "object",
        initFunc: ({ $props, $state, $queries, $ctx }) => ({})
      },
      {
        path: "introductionBlock.allLicenses",
        type: "private",
        variableType: "object",
        initFunc: ({ $props, $state, $queries, $ctx }) => ({})
      },
      {
        path: "_2XWideBlock.allLicenses",
        type: "private",
        variableType: "object",
        initFunc: ({ $props, $state, $queries, $ctx }) =>
          (() => {
            try {
              return $state.allLicenses;
            } catch (e) {
              if (
                e instanceof TypeError ||
                e?.plasmicType === "PlasmicUndefinedDataError"
              ) {
                return undefined;
              }
              throw e;
            }
          })()
      },
      {
        path: "skillsBlock.allLicenses",
        type: "private",
        variableType: "object",
        initFunc: ({ $props, $state, $queries, $ctx }) => ({})
      },
      {
        path: "leftBottomBlock.allLicenses",
        type: "private",
        variableType: "object",
        initFunc: ({ $props, $state, $queries, $ctx }) =>
          (() => {
            try {
              return $state.allLicenses;
            } catch (e) {
              if (
                e instanceof TypeError ||
                e?.plasmicType === "PlasmicUndefinedDataError"
              ) {
                return undefined;
              }
              throw e;
            }
          })()
      },
      {
        path: "rightBottomBlock.allLicenses",
        type: "private",
        variableType: "object",
        initFunc: ({ $props, $state, $queries, $ctx }) =>
          (() => {
            try {
              return $state.allLicenses;
            } catch (e) {
              if (
                e instanceof TypeError ||
                e?.plasmicType === "PlasmicUndefinedDataError"
              ) {
                return undefined;
              }
              throw e;
            }
          })()
      },
      {
        path: "toolsBlock.allLicenses",
        type: "private",
        variableType: "object",
        initFunc: ({ $props, $state, $queries, $ctx }) => ({})
      }
    ],
    [$props, $ctx, $refs]
  );
  const $state = useDollarState(stateSpecs, {
    $props,
    $ctx,
    $queries: $queries,
    $refs
  });

  const new$Queries: Record<string, ReturnType<typeof usePlasmicDataOp>> = {};
  if (Object.keys(new$Queries).some(k => new$Queries[k] !== $queries[k])) {
    setDollarQueries(new$Queries);

    $queries = new$Queries;
  }

  const globalVariants = ensureGlobalVariants({
    formattingBreakPoint: useScreenVariants_4Hrhi5G5ANwQ()
  });

  return (
    <div
      data-plasmic-name={"overviewContentBlock"}
      data-plasmic-override={overrides.overviewContentBlock}
      data-plasmic-root={true}
      data-plasmic-for-node={forNode}
      className={classNames(
        projectcss.all,
        projectcss.root_reset,
        projectcss.plasmic_default_styles,
        projectcss.plasmic_mixins,
        projectcss.plasmic_tokens,
        sty.overviewContentBlock
      )}
    >
      <ProfileSectionsProfileSectionHeading
        data-plasmic-name={"overviewHeading"}
        data-plasmic-override={overrides.overviewHeading}
        className={classNames("__wab_instance", sty.overviewHeading)}
        noUnderline={true}
      >
        <div
          data-plasmic-name={"text"}
          data-plasmic-override={overrides.text}
          className={classNames(
            projectcss.all,
            projectcss.__wab_text,
            sty.text
          )}
        >
          {"Overview"}
        </div>
      </ProfileSectionsProfileSectionHeading>
      <div
        data-plasmic-name={"featuredWorksArea"}
        data-plasmic-override={overrides.featuredWorksArea}
        className={classNames(projectcss.all, sty.featuredWorksArea)}
      >
        <ProfileOverviewHighlightedWorksLayout
          data-plasmic-name={"profileOverviewHighlightedWorksLayout"}
          data-plasmic-override={
            overrides.profileOverviewHighlightedWorksLayout
          }
          className={classNames(
            "__wab_instance",
            sty.profileOverviewHighlightedWorksLayout
          )}
        />

        <div
          data-plasmic-name={"seeMoreButtonContainer2"}
          data-plasmic-override={overrides.seeMoreButtonContainer2}
          className={classNames(projectcss.all, sty.seeMoreButtonContainer2)}
        >
          {renderPlasmicSlot({
            defaultContents: (
              <ProfileOverviewSeeMoreButton
                className={classNames(
                  "__wab_instance",
                  sty.profileOverviewSeeMoreButton__zMfR6
                )}
              />
            ),

            value: args.children
          })}
        </div>
      </div>
      <div
        data-plasmic-name={"summaryArea"}
        data-plasmic-override={overrides.summaryArea}
        className={classNames(projectcss.all, sty.summaryArea)}
      >
        <ProfileSectionsProfileSectionHeading
          data-plasmic-name={"personalSummaryHeading"}
          data-plasmic-override={overrides.personalSummaryHeading}
          className={classNames("__wab_instance", sty.personalSummaryHeading)}
        >
          {"About Me"}
        </ProfileSectionsProfileSectionHeading>
        <div
          data-plasmic-name={"freeBox"}
          data-plasmic-override={overrides.freeBox}
          className={classNames(projectcss.all, sty.freeBox)}
        >
          <ProfileOverviewOverviewBlocks
            data-plasmic-name={"introductionBlock"}
            data-plasmic-override={overrides.introductionBlock}
            allCertifications={generateStateValueProp($state, [
              "introductionBlock",
              "allCertifications"
            ])}
            allEducation={generateStateValueProp($state, [
              "introductionBlock",
              "allEducation"
            ])}
            allExperience={generateStateValueProp($state, [
              "introductionBlock",
              "allExperience"
            ])}
            allLicenses={generateStateValueProp($state, [
              "introductionBlock",
              "allLicenses"
            ])}
            allPatents={generateStateValueProp($state, [
              "introductionBlock",
              "allPatents"
            ])}
            allPublications={generateStateValueProp($state, [
              "introductionBlock",
              "allPublications"
            ])}
            allTrademarks={generateStateValueProp($state, [
              "introductionBlock",
              "allTrademarks"
            ])}
            className={classNames("__wab_instance", sty.introductionBlock)}
            onAllCertificationsChange={async (...eventArgs: any) => {
              generateStateOnChangeProp($state, [
                "introductionBlock",
                "allCertifications"
              ]).apply(null, eventArgs);

              if (
                eventArgs.length > 1 &&
                eventArgs[1] &&
                eventArgs[1]._plasmic_state_init_
              ) {
                return;
              }
            }}
            onAllEducationChange={async (...eventArgs: any) => {
              generateStateOnChangeProp($state, [
                "introductionBlock",
                "allEducation"
              ]).apply(null, eventArgs);

              if (
                eventArgs.length > 1 &&
                eventArgs[1] &&
                eventArgs[1]._plasmic_state_init_
              ) {
                return;
              }
            }}
            onAllExperienceChange={async (...eventArgs: any) => {
              generateStateOnChangeProp($state, [
                "introductionBlock",
                "allExperience"
              ]).apply(null, eventArgs);

              if (
                eventArgs.length > 1 &&
                eventArgs[1] &&
                eventArgs[1]._plasmic_state_init_
              ) {
                return;
              }
            }}
            onAllLicensesChange={async (...eventArgs: any) => {
              generateStateOnChangeProp($state, [
                "introductionBlock",
                "allLicenses"
              ]).apply(null, eventArgs);

              if (
                eventArgs.length > 1 &&
                eventArgs[1] &&
                eventArgs[1]._plasmic_state_init_
              ) {
                return;
              }
            }}
            onAllPatentsChange={async (...eventArgs: any) => {
              generateStateOnChangeProp($state, [
                "introductionBlock",
                "allPatents"
              ]).apply(null, eventArgs);

              if (
                eventArgs.length > 1 &&
                eventArgs[1] &&
                eventArgs[1]._plasmic_state_init_
              ) {
                return;
              }
            }}
            onAllPublicationsChange={async (...eventArgs: any) => {
              generateStateOnChangeProp($state, [
                "introductionBlock",
                "allPublications"
              ]).apply(null, eventArgs);

              if (
                eventArgs.length > 1 &&
                eventArgs[1] &&
                eventArgs[1]._plasmic_state_init_
              ) {
                return;
              }
            }}
            onAllTrademarksChange={async (...eventArgs: any) => {
              generateStateOnChangeProp($state, [
                "introductionBlock",
                "allTrademarks"
              ]).apply(null, eventArgs);

              if (
                eventArgs.length > 1 &&
                eventArgs[1] &&
                eventArgs[1]._plasmic_state_init_
              ) {
                return;
              }
            }}
            sectionContent={"introduction"}
          />
        </div>
        <Stack__
          as={"div"}
          data-plasmic-name={"overviewBlockGrid"}
          data-plasmic-override={overrides.overviewBlockGrid}
          hasGap={true}
          className={classNames(projectcss.all, sty.overviewBlockGrid)}
        >
          <Stack__
            as={"div"}
            data-plasmic-name={"skillsAndToolsStack"}
            data-plasmic-override={overrides.skillsAndToolsStack}
            hasGap={true}
            className={classNames(projectcss.all, sty.skillsAndToolsStack)}
          >
            <ProfileOverviewOverviewBlocks
              data-plasmic-name={"skillsBlock"}
              data-plasmic-override={overrides.skillsBlock}
              allCertifications={generateStateValueProp($state, [
                "skillsBlock",
                "allCertifications"
              ])}
              allEducation={generateStateValueProp($state, [
                "skillsBlock",
                "allEducation"
              ])}
              allExperience={generateStateValueProp($state, [
                "skillsBlock",
                "allExperience"
              ])}
              allLicenses={generateStateValueProp($state, [
                "skillsBlock",
                "allLicenses"
              ])}
              allPatents={generateStateValueProp($state, [
                "skillsBlock",
                "allPatents"
              ])}
              allPublications={generateStateValueProp($state, [
                "skillsBlock",
                "allPublications"
              ])}
              allTrademarks={generateStateValueProp($state, [
                "skillsBlock",
                "allTrademarks"
              ])}
              className={classNames("__wab_instance", sty.skillsBlock)}
              comingSoon={true}
              onAllCertificationsChange={async (...eventArgs: any) => {
                generateStateOnChangeProp($state, [
                  "skillsBlock",
                  "allCertifications"
                ]).apply(null, eventArgs);

                if (
                  eventArgs.length > 1 &&
                  eventArgs[1] &&
                  eventArgs[1]._plasmic_state_init_
                ) {
                  return;
                }
              }}
              onAllEducationChange={async (...eventArgs: any) => {
                generateStateOnChangeProp($state, [
                  "skillsBlock",
                  "allEducation"
                ]).apply(null, eventArgs);

                if (
                  eventArgs.length > 1 &&
                  eventArgs[1] &&
                  eventArgs[1]._plasmic_state_init_
                ) {
                  return;
                }
              }}
              onAllExperienceChange={async (...eventArgs: any) => {
                generateStateOnChangeProp($state, [
                  "skillsBlock",
                  "allExperience"
                ]).apply(null, eventArgs);

                if (
                  eventArgs.length > 1 &&
                  eventArgs[1] &&
                  eventArgs[1]._plasmic_state_init_
                ) {
                  return;
                }
              }}
              onAllLicensesChange={async (...eventArgs: any) => {
                generateStateOnChangeProp($state, [
                  "skillsBlock",
                  "allLicenses"
                ]).apply(null, eventArgs);

                if (
                  eventArgs.length > 1 &&
                  eventArgs[1] &&
                  eventArgs[1]._plasmic_state_init_
                ) {
                  return;
                }
              }}
              onAllPatentsChange={async (...eventArgs: any) => {
                generateStateOnChangeProp($state, [
                  "skillsBlock",
                  "allPatents"
                ]).apply(null, eventArgs);

                if (
                  eventArgs.length > 1 &&
                  eventArgs[1] &&
                  eventArgs[1]._plasmic_state_init_
                ) {
                  return;
                }
              }}
              onAllPublicationsChange={async (...eventArgs: any) => {
                generateStateOnChangeProp($state, [
                  "skillsBlock",
                  "allPublications"
                ]).apply(null, eventArgs);

                if (
                  eventArgs.length > 1 &&
                  eventArgs[1] &&
                  eventArgs[1]._plasmic_state_init_
                ) {
                  return;
                }
              }}
              onAllTrademarksChange={async (...eventArgs: any) => {
                generateStateOnChangeProp($state, [
                  "skillsBlock",
                  "allTrademarks"
                ]).apply(null, eventArgs);

                if (
                  eventArgs.length > 1 &&
                  eventArgs[1] &&
                  eventArgs[1]._plasmic_state_init_
                ) {
                  return;
                }
              }}
              sectionContent={"skills"}
            />

            <ProfileOverviewOverviewBlocks
              data-plasmic-name={"toolsBlock"}
              data-plasmic-override={overrides.toolsBlock}
              allCertifications={generateStateValueProp($state, [
                "toolsBlock",
                "allCertifications"
              ])}
              allEducation={generateStateValueProp($state, [
                "toolsBlock",
                "allEducation"
              ])}
              allExperience={generateStateValueProp($state, [
                "toolsBlock",
                "allExperience"
              ])}
              allLicenses={generateStateValueProp($state, [
                "toolsBlock",
                "allLicenses"
              ])}
              allPatents={generateStateValueProp($state, [
                "toolsBlock",
                "allPatents"
              ])}
              allPublications={generateStateValueProp($state, [
                "toolsBlock",
                "allPublications"
              ])}
              allTrademarks={generateStateValueProp($state, [
                "toolsBlock",
                "allTrademarks"
              ])}
              className={classNames("__wab_instance", sty.toolsBlock)}
              comingSoon={true}
              onAllCertificationsChange={async (...eventArgs: any) => {
                generateStateOnChangeProp($state, [
                  "toolsBlock",
                  "allCertifications"
                ]).apply(null, eventArgs);

                if (
                  eventArgs.length > 1 &&
                  eventArgs[1] &&
                  eventArgs[1]._plasmic_state_init_
                ) {
                  return;
                }
              }}
              onAllEducationChange={async (...eventArgs: any) => {
                generateStateOnChangeProp($state, [
                  "toolsBlock",
                  "allEducation"
                ]).apply(null, eventArgs);

                if (
                  eventArgs.length > 1 &&
                  eventArgs[1] &&
                  eventArgs[1]._plasmic_state_init_
                ) {
                  return;
                }
              }}
              onAllExperienceChange={async (...eventArgs: any) => {
                generateStateOnChangeProp($state, [
                  "toolsBlock",
                  "allExperience"
                ]).apply(null, eventArgs);

                if (
                  eventArgs.length > 1 &&
                  eventArgs[1] &&
                  eventArgs[1]._plasmic_state_init_
                ) {
                  return;
                }
              }}
              onAllLicensesChange={async (...eventArgs: any) => {
                generateStateOnChangeProp($state, [
                  "toolsBlock",
                  "allLicenses"
                ]).apply(null, eventArgs);

                if (
                  eventArgs.length > 1 &&
                  eventArgs[1] &&
                  eventArgs[1]._plasmic_state_init_
                ) {
                  return;
                }
              }}
              onAllPatentsChange={async (...eventArgs: any) => {
                generateStateOnChangeProp($state, [
                  "toolsBlock",
                  "allPatents"
                ]).apply(null, eventArgs);

                if (
                  eventArgs.length > 1 &&
                  eventArgs[1] &&
                  eventArgs[1]._plasmic_state_init_
                ) {
                  return;
                }
              }}
              onAllPublicationsChange={async (...eventArgs: any) => {
                generateStateOnChangeProp($state, [
                  "toolsBlock",
                  "allPublications"
                ]).apply(null, eventArgs);

                if (
                  eventArgs.length > 1 &&
                  eventArgs[1] &&
                  eventArgs[1]._plasmic_state_init_
                ) {
                  return;
                }
              }}
              onAllTrademarksChange={async (...eventArgs: any) => {
                generateStateOnChangeProp($state, [
                  "toolsBlock",
                  "allTrademarks"
                ]).apply(null, eventArgs);

                if (
                  eventArgs.length > 1 &&
                  eventArgs[1] &&
                  eventArgs[1]._plasmic_state_init_
                ) {
                  return;
                }
              }}
              sectionContent={"tools"}
            />
          </Stack__>
          <Stack__
            as={"div"}
            data-plasmic-name={"leftStackUserDefined"}
            data-plasmic-override={overrides.leftStackUserDefined}
            hasGap={true}
            className={classNames(projectcss.all, sty.leftStackUserDefined)}
          >
            <ProfileOverviewOverviewBlocks
              data-plasmic-name={"_2XWideBlock"}
              data-plasmic-override={overrides._2XWideBlock}
              allCertifications={generateStateValueProp($state, [
                "_2XWideBlock",
                "allCertifications"
              ])}
              allEducation={generateStateValueProp($state, [
                "_2XWideBlock",
                "allEducation"
              ])}
              allExperience={generateStateValueProp($state, [
                "_2XWideBlock",
                "allExperience"
              ])}
              allLicenses={generateStateValueProp($state, [
                "_2XWideBlock",
                "allLicenses"
              ])}
              allPatents={generateStateValueProp($state, [
                "_2XWideBlock",
                "allPatents"
              ])}
              allPublications={generateStateValueProp($state, [
                "_2XWideBlock",
                "allPublications"
              ])}
              allTrademarks={generateStateValueProp($state, [
                "_2XWideBlock",
                "allTrademarks"
              ])}
              className={classNames("__wab_instance", sty._2XWideBlock)}
              onAllCertificationsChange={async (...eventArgs: any) => {
                generateStateOnChangeProp($state, [
                  "_2XWideBlock",
                  "allCertifications"
                ]).apply(null, eventArgs);

                if (
                  eventArgs.length > 1 &&
                  eventArgs[1] &&
                  eventArgs[1]._plasmic_state_init_
                ) {
                  return;
                }
              }}
              onAllEducationChange={async (...eventArgs: any) => {
                generateStateOnChangeProp($state, [
                  "_2XWideBlock",
                  "allEducation"
                ]).apply(null, eventArgs);

                if (
                  eventArgs.length > 1 &&
                  eventArgs[1] &&
                  eventArgs[1]._plasmic_state_init_
                ) {
                  return;
                }
              }}
              onAllExperienceChange={async (...eventArgs: any) => {
                generateStateOnChangeProp($state, [
                  "_2XWideBlock",
                  "allExperience"
                ]).apply(null, eventArgs);

                if (
                  eventArgs.length > 1 &&
                  eventArgs[1] &&
                  eventArgs[1]._plasmic_state_init_
                ) {
                  return;
                }
              }}
              onAllLicensesChange={async (...eventArgs: any) => {
                generateStateOnChangeProp($state, [
                  "_2XWideBlock",
                  "allLicenses"
                ]).apply(null, eventArgs);

                if (
                  eventArgs.length > 1 &&
                  eventArgs[1] &&
                  eventArgs[1]._plasmic_state_init_
                ) {
                  return;
                }
              }}
              onAllPatentsChange={async (...eventArgs: any) => {
                generateStateOnChangeProp($state, [
                  "_2XWideBlock",
                  "allPatents"
                ]).apply(null, eventArgs);

                if (
                  eventArgs.length > 1 &&
                  eventArgs[1] &&
                  eventArgs[1]._plasmic_state_init_
                ) {
                  return;
                }
              }}
              onAllPublicationsChange={async (...eventArgs: any) => {
                generateStateOnChangeProp($state, [
                  "_2XWideBlock",
                  "allPublications"
                ]).apply(null, eventArgs);

                if (
                  eventArgs.length > 1 &&
                  eventArgs[1] &&
                  eventArgs[1]._plasmic_state_init_
                ) {
                  return;
                }
              }}
              onAllTrademarksChange={async (...eventArgs: any) => {
                generateStateOnChangeProp($state, [
                  "_2XWideBlock",
                  "allTrademarks"
                ]).apply(null, eventArgs);

                if (
                  eventArgs.length > 1 &&
                  eventArgs[1] &&
                  eventArgs[1]._plasmic_state_init_
                ) {
                  return;
                }
              }}
              sectionContent={"experience"}
            />

            <Stack__
              as={"div"}
              data-plasmic-name={"bottomBlocksSections"}
              data-plasmic-override={overrides.bottomBlocksSections}
              hasGap={true}
              className={classNames(projectcss.all, sty.bottomBlocksSections)}
            >
              <ProfileOverviewOverviewBlocks
                data-plasmic-name={"leftBottomBlock"}
                data-plasmic-override={overrides.leftBottomBlock}
                allCertifications={generateStateValueProp($state, [
                  "leftBottomBlock",
                  "allCertifications"
                ])}
                allEducation={generateStateValueProp($state, [
                  "leftBottomBlock",
                  "allEducation"
                ])}
                allExperience={generateStateValueProp($state, [
                  "leftBottomBlock",
                  "allExperience"
                ])}
                allLicenses={generateStateValueProp($state, [
                  "leftBottomBlock",
                  "allLicenses"
                ])}
                allPatents={generateStateValueProp($state, [
                  "leftBottomBlock",
                  "allPatents"
                ])}
                allPublications={generateStateValueProp($state, [
                  "leftBottomBlock",
                  "allPublications"
                ])}
                allTrademarks={generateStateValueProp($state, [
                  "leftBottomBlock",
                  "allTrademarks"
                ])}
                onAllCertificationsChange={async (...eventArgs: any) => {
                  generateStateOnChangeProp($state, [
                    "leftBottomBlock",
                    "allCertifications"
                  ]).apply(null, eventArgs);

                  if (
                    eventArgs.length > 1 &&
                    eventArgs[1] &&
                    eventArgs[1]._plasmic_state_init_
                  ) {
                    return;
                  }
                }}
                onAllEducationChange={async (...eventArgs: any) => {
                  generateStateOnChangeProp($state, [
                    "leftBottomBlock",
                    "allEducation"
                  ]).apply(null, eventArgs);

                  if (
                    eventArgs.length > 1 &&
                    eventArgs[1] &&
                    eventArgs[1]._plasmic_state_init_
                  ) {
                    return;
                  }
                }}
                onAllExperienceChange={async (...eventArgs: any) => {
                  generateStateOnChangeProp($state, [
                    "leftBottomBlock",
                    "allExperience"
                  ]).apply(null, eventArgs);

                  if (
                    eventArgs.length > 1 &&
                    eventArgs[1] &&
                    eventArgs[1]._plasmic_state_init_
                  ) {
                    return;
                  }
                }}
                onAllLicensesChange={async (...eventArgs: any) => {
                  generateStateOnChangeProp($state, [
                    "leftBottomBlock",
                    "allLicenses"
                  ]).apply(null, eventArgs);

                  if (
                    eventArgs.length > 1 &&
                    eventArgs[1] &&
                    eventArgs[1]._plasmic_state_init_
                  ) {
                    return;
                  }
                }}
                onAllPatentsChange={async (...eventArgs: any) => {
                  generateStateOnChangeProp($state, [
                    "leftBottomBlock",
                    "allPatents"
                  ]).apply(null, eventArgs);

                  if (
                    eventArgs.length > 1 &&
                    eventArgs[1] &&
                    eventArgs[1]._plasmic_state_init_
                  ) {
                    return;
                  }
                }}
                onAllPublicationsChange={async (...eventArgs: any) => {
                  generateStateOnChangeProp($state, [
                    "leftBottomBlock",
                    "allPublications"
                  ]).apply(null, eventArgs);

                  if (
                    eventArgs.length > 1 &&
                    eventArgs[1] &&
                    eventArgs[1]._plasmic_state_init_
                  ) {
                    return;
                  }
                }}
                onAllTrademarksChange={async (...eventArgs: any) => {
                  generateStateOnChangeProp($state, [
                    "leftBottomBlock",
                    "allTrademarks"
                  ]).apply(null, eventArgs);

                  if (
                    eventArgs.length > 1 &&
                    eventArgs[1] &&
                    eventArgs[1]._plasmic_state_init_
                  ) {
                    return;
                  }
                }}
                sectionContent={"publications"}
              />

              <ProfileOverviewOverviewBlocks
                data-plasmic-name={"rightBottomBlock"}
                data-plasmic-override={overrides.rightBottomBlock}
                allCertifications={generateStateValueProp($state, [
                  "rightBottomBlock",
                  "allCertifications"
                ])}
                allEducation={generateStateValueProp($state, [
                  "rightBottomBlock",
                  "allEducation"
                ])}
                allExperience={generateStateValueProp($state, [
                  "rightBottomBlock",
                  "allExperience"
                ])}
                allLicenses={generateStateValueProp($state, [
                  "rightBottomBlock",
                  "allLicenses"
                ])}
                allPatents={generateStateValueProp($state, [
                  "rightBottomBlock",
                  "allPatents"
                ])}
                allPublications={generateStateValueProp($state, [
                  "rightBottomBlock",
                  "allPublications"
                ])}
                allTrademarks={generateStateValueProp($state, [
                  "rightBottomBlock",
                  "allTrademarks"
                ])}
                className={classNames("__wab_instance", sty.rightBottomBlock)}
                onAllCertificationsChange={async (...eventArgs: any) => {
                  generateStateOnChangeProp($state, [
                    "rightBottomBlock",
                    "allCertifications"
                  ]).apply(null, eventArgs);

                  if (
                    eventArgs.length > 1 &&
                    eventArgs[1] &&
                    eventArgs[1]._plasmic_state_init_
                  ) {
                    return;
                  }
                }}
                onAllEducationChange={async (...eventArgs: any) => {
                  generateStateOnChangeProp($state, [
                    "rightBottomBlock",
                    "allEducation"
                  ]).apply(null, eventArgs);

                  if (
                    eventArgs.length > 1 &&
                    eventArgs[1] &&
                    eventArgs[1]._plasmic_state_init_
                  ) {
                    return;
                  }
                }}
                onAllExperienceChange={async (...eventArgs: any) => {
                  generateStateOnChangeProp($state, [
                    "rightBottomBlock",
                    "allExperience"
                  ]).apply(null, eventArgs);

                  if (
                    eventArgs.length > 1 &&
                    eventArgs[1] &&
                    eventArgs[1]._plasmic_state_init_
                  ) {
                    return;
                  }
                }}
                onAllLicensesChange={async (...eventArgs: any) => {
                  generateStateOnChangeProp($state, [
                    "rightBottomBlock",
                    "allLicenses"
                  ]).apply(null, eventArgs);

                  if (
                    eventArgs.length > 1 &&
                    eventArgs[1] &&
                    eventArgs[1]._plasmic_state_init_
                  ) {
                    return;
                  }
                }}
                onAllPatentsChange={async (...eventArgs: any) => {
                  generateStateOnChangeProp($state, [
                    "rightBottomBlock",
                    "allPatents"
                  ]).apply(null, eventArgs);

                  if (
                    eventArgs.length > 1 &&
                    eventArgs[1] &&
                    eventArgs[1]._plasmic_state_init_
                  ) {
                    return;
                  }
                }}
                onAllPublicationsChange={async (...eventArgs: any) => {
                  generateStateOnChangeProp($state, [
                    "rightBottomBlock",
                    "allPublications"
                  ]).apply(null, eventArgs);

                  if (
                    eventArgs.length > 1 &&
                    eventArgs[1] &&
                    eventArgs[1]._plasmic_state_init_
                  ) {
                    return;
                  }
                }}
                onAllTrademarksChange={async (...eventArgs: any) => {
                  generateStateOnChangeProp($state, [
                    "rightBottomBlock",
                    "allTrademarks"
                  ]).apply(null, eventArgs);

                  if (
                    eventArgs.length > 1 &&
                    eventArgs[1] &&
                    eventArgs[1]._plasmic_state_init_
                  ) {
                    return;
                  }
                }}
                sectionContent={"licenses"}
              />
            </Stack__>
          </Stack__>
        </Stack__>
        <Stack__
          as={"div"}
          data-plasmic-name={"seeMoreButtonContainer"}
          data-plasmic-override={overrides.seeMoreButtonContainer}
          hasGap={true}
          className={classNames(projectcss.all, sty.seeMoreButtonContainer)}
        >
          {renderPlasmicSlot({
            defaultContents: (
              <ProfileOverviewSeeMoreButton
                className={classNames(
                  "__wab_instance",
                  sty.profileOverviewSeeMoreButton__w8Sl6
                )}
              />
            ),

            value: args.slot
          })}
        </Stack__>
      </div>
    </div>
  ) as React.ReactElement | null;
}

const PlasmicDescendants = {
  overviewContentBlock: [
    "overviewContentBlock",
    "overviewHeading",
    "text",
    "featuredWorksArea",
    "profileOverviewHighlightedWorksLayout",
    "seeMoreButtonContainer2",
    "summaryArea",
    "personalSummaryHeading",
    "freeBox",
    "introductionBlock",
    "overviewBlockGrid",
    "skillsAndToolsStack",
    "skillsBlock",
    "toolsBlock",
    "leftStackUserDefined",
    "_2XWideBlock",
    "bottomBlocksSections",
    "leftBottomBlock",
    "rightBottomBlock",
    "seeMoreButtonContainer"
  ],
  overviewHeading: ["overviewHeading", "text"],
  text: ["text"],
  featuredWorksArea: [
    "featuredWorksArea",
    "profileOverviewHighlightedWorksLayout",
    "seeMoreButtonContainer2"
  ],
  profileOverviewHighlightedWorksLayout: [
    "profileOverviewHighlightedWorksLayout"
  ],
  seeMoreButtonContainer2: ["seeMoreButtonContainer2"],
  summaryArea: [
    "summaryArea",
    "personalSummaryHeading",
    "freeBox",
    "introductionBlock",
    "overviewBlockGrid",
    "skillsAndToolsStack",
    "skillsBlock",
    "toolsBlock",
    "leftStackUserDefined",
    "_2XWideBlock",
    "bottomBlocksSections",
    "leftBottomBlock",
    "rightBottomBlock",
    "seeMoreButtonContainer"
  ],
  personalSummaryHeading: ["personalSummaryHeading"],
  freeBox: ["freeBox", "introductionBlock"],
  introductionBlock: ["introductionBlock"],
  overviewBlockGrid: [
    "overviewBlockGrid",
    "skillsAndToolsStack",
    "skillsBlock",
    "toolsBlock",
    "leftStackUserDefined",
    "_2XWideBlock",
    "bottomBlocksSections",
    "leftBottomBlock",
    "rightBottomBlock"
  ],
  skillsAndToolsStack: ["skillsAndToolsStack", "skillsBlock", "toolsBlock"],
  skillsBlock: ["skillsBlock"],
  toolsBlock: ["toolsBlock"],
  leftStackUserDefined: [
    "leftStackUserDefined",
    "_2XWideBlock",
    "bottomBlocksSections",
    "leftBottomBlock",
    "rightBottomBlock"
  ],
  _2XWideBlock: ["_2XWideBlock"],
  bottomBlocksSections: [
    "bottomBlocksSections",
    "leftBottomBlock",
    "rightBottomBlock"
  ],
  leftBottomBlock: ["leftBottomBlock"],
  rightBottomBlock: ["rightBottomBlock"],
  seeMoreButtonContainer: ["seeMoreButtonContainer"]
} as const;
type NodeNameType = keyof typeof PlasmicDescendants;
type DescendantsType<T extends NodeNameType> =
  (typeof PlasmicDescendants)[T][number];
type NodeDefaultElementType = {
  overviewContentBlock: "div";
  overviewHeading: typeof ProfileSectionsProfileSectionHeading;
  text: "div";
  featuredWorksArea: "div";
  profileOverviewHighlightedWorksLayout: typeof ProfileOverviewHighlightedWorksLayout;
  seeMoreButtonContainer2: "div";
  summaryArea: "div";
  personalSummaryHeading: typeof ProfileSectionsProfileSectionHeading;
  freeBox: "div";
  introductionBlock: typeof ProfileOverviewOverviewBlocks;
  overviewBlockGrid: "div";
  skillsAndToolsStack: "div";
  skillsBlock: typeof ProfileOverviewOverviewBlocks;
  toolsBlock: typeof ProfileOverviewOverviewBlocks;
  leftStackUserDefined: "div";
  _2XWideBlock: typeof ProfileOverviewOverviewBlocks;
  bottomBlocksSections: "div";
  leftBottomBlock: typeof ProfileOverviewOverviewBlocks;
  rightBottomBlock: typeof ProfileOverviewOverviewBlocks;
  seeMoreButtonContainer: "div";
};

type ReservedPropsType = "variants" | "args" | "overrides";
type NodeOverridesType<T extends NodeNameType> = Pick<
  PlasmicProfileOverviewOverviewContentBlock__OverridesType,
  DescendantsType<T>
>;
type NodeComponentProps<T extends NodeNameType> =
  // Explicitly specify variants, args, and overrides as objects
  {
    variants?: PlasmicProfileOverviewOverviewContentBlock__VariantsArgs;
    args?: PlasmicProfileOverviewOverviewContentBlock__ArgsType;
    overrides?: NodeOverridesType<T>;
  } & Omit< // Specify variants directly as props
    PlasmicProfileOverviewOverviewContentBlock__VariantsArgs,
    ReservedPropsType
  > &
    /* Specify args directly as props*/ Omit<
      PlasmicProfileOverviewOverviewContentBlock__ArgsType,
      ReservedPropsType
    > &
    /* Specify overrides for each element directly as props*/ Omit<
      NodeOverridesType<T>,
      ReservedPropsType | VariantPropType | ArgPropType
    > &
    /* Specify props for the root element*/ Omit<
      Partial<React.ComponentProps<NodeDefaultElementType[T]>>,
      ReservedPropsType | VariantPropType | ArgPropType | DescendantsType<T>
    >;

function makeNodeComponent<NodeName extends NodeNameType>(nodeName: NodeName) {
  type PropsType = NodeComponentProps<NodeName> & { key?: React.Key };
  const func = function <T extends PropsType>(
    props: T & StrictProps<T, PropsType>
  ) {
    const { variants, args, overrides } = React.useMemo(
      () =>
        deriveRenderOpts(props, {
          name: nodeName,
          descendantNames: PlasmicDescendants[nodeName],
          internalArgPropNames:
            PlasmicProfileOverviewOverviewContentBlock__ArgProps,
          internalVariantPropNames:
            PlasmicProfileOverviewOverviewContentBlock__VariantProps
        }),
      [props, nodeName]
    );
    return PlasmicProfileOverviewOverviewContentBlock__RenderFunc({
      variants,
      args,
      overrides,
      forNode: nodeName
    });
  };
  if (nodeName === "overviewContentBlock") {
    func.displayName = "PlasmicProfileOverviewOverviewContentBlock";
  } else {
    func.displayName = `PlasmicProfileOverviewOverviewContentBlock.${nodeName}`;
  }
  return func;
}

export const PlasmicProfileOverviewOverviewContentBlock = Object.assign(
  // Top-level PlasmicProfileOverviewOverviewContentBlock renders the root element
  makeNodeComponent("overviewContentBlock"),
  {
    // Helper components rendering sub-elements
    overviewHeading: makeNodeComponent("overviewHeading"),
    text: makeNodeComponent("text"),
    featuredWorksArea: makeNodeComponent("featuredWorksArea"),
    profileOverviewHighlightedWorksLayout: makeNodeComponent(
      "profileOverviewHighlightedWorksLayout"
    ),
    seeMoreButtonContainer2: makeNodeComponent("seeMoreButtonContainer2"),
    summaryArea: makeNodeComponent("summaryArea"),
    personalSummaryHeading: makeNodeComponent("personalSummaryHeading"),
    freeBox: makeNodeComponent("freeBox"),
    introductionBlock: makeNodeComponent("introductionBlock"),
    overviewBlockGrid: makeNodeComponent("overviewBlockGrid"),
    skillsAndToolsStack: makeNodeComponent("skillsAndToolsStack"),
    skillsBlock: makeNodeComponent("skillsBlock"),
    toolsBlock: makeNodeComponent("toolsBlock"),
    leftStackUserDefined: makeNodeComponent("leftStackUserDefined"),
    _2XWideBlock: makeNodeComponent("_2XWideBlock"),
    bottomBlocksSections: makeNodeComponent("bottomBlocksSections"),
    leftBottomBlock: makeNodeComponent("leftBottomBlock"),
    rightBottomBlock: makeNodeComponent("rightBottomBlock"),
    seeMoreButtonContainer: makeNodeComponent("seeMoreButtonContainer"),

    // Metadata about props expected for PlasmicProfileOverviewOverviewContentBlock
    internalVariantProps:
      PlasmicProfileOverviewOverviewContentBlock__VariantProps,
    internalArgProps: PlasmicProfileOverviewOverviewContentBlock__ArgProps
  }
);

export default PlasmicProfileOverviewOverviewContentBlock;
/* prettier-ignore-end */
