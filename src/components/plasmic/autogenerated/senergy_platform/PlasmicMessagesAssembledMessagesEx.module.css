.chatBoxContainer {
  display: flex;
  flex-direction: column;
  width: 100%;
  justify-content: flex-start;
  align-items: center;
  height: 100%;
  position: relative;
  background: var(--token-xo_r2w5pebq-);
  max-width: 95%;
  justify-self: flex-start;
  min-width: 0;
  min-height: 0;
}
.messagesMessageHeader:global(.__wab_instance) {
  max-width: 100%;
  position: sticky;
  left: 0px;
  top: 0px;
  z-index: 5;
  margin-bottom: 0px;
}
.messagesSlot {
  position: relative;
  justify-content: center;
  align-items: flex-start;
  flex-direction: column;
  width: 100%;
  display: flex;
  min-width: 0;
}
.messagesMessage__xa8S7:global(.__wab_instance) {
  max-width: 100%;
  position: relative;
}
.messagesAttachments__oxYNt:global(.__wab_instance) {
  max-width: 100%;
  position: relative;
}
.messagesAttachments__bYGwj:global(.__wab_instance) {
  max-width: 100%;
  position: relative;
}
.messagesMessage__b1TFw:global(.__wab_instance) {
  max-width: 100%;
  position: relative;
}
.messagesAttachments___66E6B:global(.__wab_instance) {
  max-width: 100%;
  position: relative;
}
.messagesAttachments___6YuFw:global(.__wab_instance) {
  max-width: 100%;
  position: relative;
}
.messagesMessage__zXbrI:global(.__wab_instance) {
  max-width: 100%;
  position: relative;
}
.messagesAttachments__gfGru:global(.__wab_instance) {
  max-width: 100%;
  position: relative;
}
.messagesAttachments__z4SqX:global(.__wab_instance) {
  max-width: 100%;
  position: relative;
}
.messagesDateDivider:global(.__wab_instance) {
  max-width: 100%;
  position: relative;
}
.messagesMessage__tctwu:global(.__wab_instance) {
  max-width: 100%;
  position: relative;
}
.messagesAttachments__fda2J:global(.__wab_instance) {
  max-width: 100%;
  position: relative;
}
.messagesAttachments__lSOd:global(.__wab_instance) {
  max-width: 100%;
  position: relative;
}
.messagesMessage__vt1UO:global(.__wab_instance) {
  max-width: 100%;
  position: relative;
}
.messagesAttachments__vKfT:global(.__wab_instance) {
  max-width: 100%;
  position: relative;
}
.messagesAttachments__o7Ole:global(.__wab_instance) {
  max-width: 100%;
  position: relative;
}
.messagesMessage__gg4Uz:global(.__wab_instance) {
  max-width: 100%;
  position: relative;
}
.messagesAttachments___49ID:global(.__wab_instance) {
  max-width: 100%;
  position: relative;
}
.messagesAttachments__pqCfn:global(.__wab_instance) {
  max-width: 100%;
  position: relative;
  flex-shrink: 1;
  flex-grow: 0;
}
.messagesMessage__xq9GD:global(.__wab_instance) {
  max-width: 100%;
  position: relative;
}
.messagesAttachments__a19Zg:global(.__wab_instance) {
  max-width: 100%;
  position: relative;
}
.messagesAttachments__wpT4F:global(.__wab_instance) {
  max-width: 100%;
  position: relative;
}
.messagesMessage__liSxR:global(.__wab_instance) {
  max-width: 100%;
  position: relative;
}
.messagesAttachments__jx9Qr:global(.__wab_instance) {
  max-width: 100%;
  position: relative;
}
.messagesAttachments___37PNx:global(.__wab_instance) {
  max-width: 100%;
  position: relative;
}
.messagesInputBox:global(.__wab_instance) {
  max-width: 100%;
  position: sticky;
  left: 0px;
  z-index: 5;
  bottom: 0px;
}
