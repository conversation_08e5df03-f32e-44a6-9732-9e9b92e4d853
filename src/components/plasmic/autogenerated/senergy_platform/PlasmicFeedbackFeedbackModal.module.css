.root {
  display: flex;
  flex-direction: column;
  position: relative;
  width: 600px;
  height: 500px;
  justify-content: flex-start;
  align-items: flex-start;
  background: var(--token-1AMvw6c2eIK7);
  padding: var(--token-M1l4keX1sfKm);
}
.text {
  position: relative;
  width: auto;
  height: auto;
  max-width: 100%;
  font-size: var(--token-9KumB6TRpaad);
}
.subTextInput:global(.__wab_instance) {
  max-width: 100%;
  position: relative;
}
.subCheckbox:global(.__wab_instance) {
  max-width: 100%;
  position: relative;
}
.subTextInput2:global(.__wab_instance) {
  max-width: 100%;
  position: relative;
}
.subTextInput4:global(.__wab_instance) {
  max-width: 100%;
  position: relative;
}
.subcomponentUploadButton:global(.__wab_instance) {
  max-width: 100%;
  position: relative;
  height: 100px;
  flex-shrink: 0;
}
