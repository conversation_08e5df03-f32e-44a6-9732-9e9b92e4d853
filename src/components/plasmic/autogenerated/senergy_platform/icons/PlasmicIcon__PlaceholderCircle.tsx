/* eslint-disable */
/* tslint:disable */
// @ts-nocheck
/* prettier-ignore-start */
import React from "react";
import { classNames } from "@plasmicapp/react-web";

export type PlaceholderCircleIconProps = React.ComponentProps<"svg"> & {
  title?: string;
};

export function PlaceholderCircleIcon(props: PlaceholderCircleIconProps) {
  const { className, style, title, ...restProps } = props;
  return (
    <svg
      xmlns={"http://www.w3.org/2000/svg"}
      className={classNames(
        "plasmic-default__svg",
        className,
        "icon icon-tabler icon-tabler-circle-filled"
      )}
      viewBox={"0 0 24 24"}
      strokeWidth={"1.5"}
      stroke={"#292929"}
      fill={"none"}
      strokeLinecap={"round"}
      strokeLinejoin={"round"}
      height={"1em"}
      style={style}
      {...restProps}
    >
      {title && <title>{title}</title>}

      <path d={"M0 0h24v24H0z"} fill={"none"} stroke={"none"}></path>

      <path
        d={"M7 3.34a10 10 0 11-4.995 8.984L2 12l.005-.324A10 10 0 017 3.34z"}
        fill={"currentColor"}
        stroke={"none"}
      ></path>
    </svg>
  );
}

export default PlaceholderCircleIcon;
/* prettier-ignore-end */
