.toolsFormattingBox {
  display: flex;
  flex-direction: row;
  align-items: flex-start;
  justify-content: flex-start;
  width: 100%;
  flex-shrink: 0;
  height: auto;
  margin-bottom: 12px;
  transform: translateX(0px) translateY(0px) translateZ(0px);
  margin-top: 4px;
  margin-left: 4px;
  position: relative;
  min-width: 0;
  padding: 16px 16px 8px 0px;
}
.toolsContent {
  display: flex;
  position: relative;
  width: 100%;
  flex-direction: column;
  align-items: flex-start;
  justify-content: flex-start;
  min-width: 0;
  padding: var(--token-sazGmnf7GWAk);
}
.text {
  position: relative;
  width: 100%;
  height: auto;
  max-width: 100%;
  min-width: 0;
  display: none;
}
.textoverviewGrid {
  display: none;
}
