import type { Field } from 'payload';

export const languagesFields: Field[] = [
  {
    type: 'row',
    fields: [
      {
        name: 'language',
        label: 'Language',
        type: 'text',
        //type: 'relationship',
        //relationTo: 'Languages',
        required: true,
      },
      {
        name: 'proficiency',
        label: 'Proficiency',
        type: 'select',
        options: [
          { label: 'Beginner', value: 'beginner' },
          { label: 'Intermediate', value: 'intermediate' },
          { label: 'Advanced', value: 'advanced' },
          { label: 'Fluent', value: 'fluent' },
          { label: 'Native', value: 'native' },
        ],
        required: true,
      },
    ],
  },
  {
    name: 'primary_language',
    label: 'Is this a primary language?',
    type: 'checkbox',
    required: false,
  },
  {
    name: 'visible',
    type: 'checkbox',
    required: false,
    defaultValue: true,
  },
];
