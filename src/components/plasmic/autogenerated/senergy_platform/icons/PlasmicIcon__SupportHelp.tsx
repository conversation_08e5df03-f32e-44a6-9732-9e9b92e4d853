/* eslint-disable */
/* tslint:disable */
// @ts-nocheck
/* prettier-ignore-start */
import React from "react";
import { classNames } from "@plasmicapp/react-web";

export type SupportHelpIconProps = React.ComponentProps<"svg"> & {
  title?: string;
};

export function SupportHelpIcon(props: SupportHelpIconProps) {
  const { className, style, title, ...restProps } = props;
  return (
    <svg
      xmlns={"http://www.w3.org/2000/svg"}
      className={classNames(
        "plasmic-default__svg",
        className,
        "icon icon-tabler icon-tabler-help"
      )}
      viewBox={"0 0 24 24"}
      strokeWidth={"1.5"}
      stroke={"currentColor"}
      fill={"none"}
      strokeLinecap={"round"}
      strokeLinejoin={"round"}
      height={"1em"}
      style={style}
      {...restProps}
    >
      {title && <title>{title}</title>}

      <path d={"M0 0h24v24H0z"} fill={"none"} stroke={"none"}></path>

      <path d={"M3 12a9 9 0 1018 0 9 9 0 10-18 0m9 5v.01"}></path>

      <path d={"M12 13.5a1.5 1.5 0 011-1.5 2.6 2.6 0 10-3-4"}></path>
    </svg>
  );
}

export default SupportHelpIcon;
/* prettier-ignore-end */
