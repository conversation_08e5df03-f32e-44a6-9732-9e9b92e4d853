/* eslint-disable */
/* tslint:disable */
// @ts-nocheck
/* prettier-ignore-start */

/** @jsxRuntime classic */
/** @jsx createPlasmicElementProxy */
/** @jsxFrag React.Fragment */

// This class is auto-generated by Plasmic; please do not edit!
// Plasmic Project: nZeWKcn21KijvC7eT8B3c7
// Component: JPWHSAS340Sf

"use client";

import * as React from "react";

import Head from "next/head";
import Link, { LinkProps } from "next/link";
import { useRouter } from "next/navigation";

import {
  Flex as Flex__,
  MultiChoiceArg,
  PlasmicDataSourceContextProvider as PlasmicDataSourceContextProvider__,
  PlasmicIcon as PlasmicIcon__,
  PlasmicImg as PlasmicImg__,
  PlasmicLink as PlasmicLink__,
  PlasmicPageGuard as PlasmicPageGuard__,
  SingleBooleanChoiceArg,
  SingleChoiceArg,
  Stack as Stack__,
  StrictProps,
  Trans as Trans__,
  classNames,
  createPlasmicElementProxy,
  deriveRenderOpts,
  ensureGlobalVariants,
  generateOnMutateForSpec,
  generateStateOnChangeProp,
  generateStateOnChangePropForCodeComponents,
  generateStateValueProp,
  get as $stateGet,
  hasVariant,
  initializeCodeComponentStates,
  initializePlasmicStates,
  makeFragment,
  omit,
  pick,
  renderPlasmicSlot,
  set as $stateSet,
  useCurrentUser,
  useDollarState,
  usePlasmicTranslator,
  useTrigger,
  wrapWithClassName
} from "@plasmicapp/react-web";
import {
  DataCtxReader as DataCtxReader__,
  useDataEnv,
  useGlobalActions
} from "@plasmicapp/host";

import "@plasmicapp/react-web/lib/plasmic.css";

import projectcss from "./plasmic.module.css"; // plasmic-import: nZeWKcn21KijvC7eT8B3c7/projectcss
import sty from "./PlasmicSubcomponentIconButton.module.css"; // plasmic-import: JPWHSAS340Sf/css

import XIcon from "./icons/PlasmicIcon__X"; // plasmic-import: deLi5PaFbuAg/icon

createPlasmicElementProxy;

export type PlasmicSubcomponentIconButton__VariantMembers = {};
export type PlasmicSubcomponentIconButton__VariantsArgs = {};
type VariantPropType = keyof PlasmicSubcomponentIconButton__VariantsArgs;
export const PlasmicSubcomponentIconButton__VariantProps =
  new Array<VariantPropType>();

export type PlasmicSubcomponentIconButton__ArgsType = {
  onClickCloseButton?: (event: any) => void;
};
type ArgPropType = keyof PlasmicSubcomponentIconButton__ArgsType;
export const PlasmicSubcomponentIconButton__ArgProps = new Array<ArgPropType>(
  "onClickCloseButton"
);

export type PlasmicSubcomponentIconButton__OverridesType = {
  root?: Flex__<"button">;
  closeButton?: Flex__<"svg">;
};

export interface DefaultSubcomponentIconButtonProps {
  onClickCloseButton?: (event: any) => void;
  className?: string;
}

const $$ = {};

function useNextRouter() {
  try {
    return useRouter();
  } catch {}
  return undefined;
}

function PlasmicSubcomponentIconButton__RenderFunc(props: {
  variants: PlasmicSubcomponentIconButton__VariantsArgs;
  args: PlasmicSubcomponentIconButton__ArgsType;
  overrides: PlasmicSubcomponentIconButton__OverridesType;
  forNode?: string;
}) {
  const { variants, overrides, forNode } = props;

  const args = React.useMemo(
    () =>
      Object.assign(
        {},
        Object.fromEntries(
          Object.entries(props.args).filter(([_, v]) => v !== undefined)
        )
      ),
    [props.args]
  );

  const $props = {
    ...args,
    ...variants
  };

  const __nextRouter = useNextRouter();
  const $ctx = useDataEnv?.() || {};
  const refsRef = React.useRef({});
  const $refs = refsRef.current;

  let [$queries, setDollarQueries] = React.useState<
    Record<string, ReturnType<typeof usePlasmicDataOp>>
  >({});

  const new$Queries: Record<string, ReturnType<typeof usePlasmicDataOp>> = {};
  if (Object.keys(new$Queries).some(k => new$Queries[k] !== $queries[k])) {
    setDollarQueries(new$Queries);

    $queries = new$Queries;
  }

  const [isRootHover, triggerRootHoverProps] = useTrigger("useHover", {});
  const triggers = {
    hover_root: isRootHover
  };

  return (
    <button
      data-plasmic-name={"root"}
      data-plasmic-override={overrides.root}
      data-plasmic-root={true}
      data-plasmic-for-node={forNode}
      className={classNames(
        projectcss.all,
        projectcss.button,
        projectcss.root_reset,
        projectcss.plasmic_default_styles,
        projectcss.plasmic_mixins,
        projectcss.plasmic_tokens,
        sty.root
      )}
      onClick={args.onClickCloseButton}
      data-plasmic-trigger-props={[triggerRootHoverProps]}
    >
      <XIcon
        data-plasmic-name={"closeButton"}
        data-plasmic-override={overrides.closeButton}
        className={classNames(projectcss.all, sty.closeButton)}
        role={"img"}
      />
    </button>
  ) as React.ReactElement | null;
}

const PlasmicDescendants = {
  root: ["root", "closeButton"],
  closeButton: ["closeButton"]
} as const;
type NodeNameType = keyof typeof PlasmicDescendants;
type DescendantsType<T extends NodeNameType> =
  (typeof PlasmicDescendants)[T][number];
type NodeDefaultElementType = {
  root: "button";
  closeButton: "svg";
};

type ReservedPropsType = "variants" | "args" | "overrides";
type NodeOverridesType<T extends NodeNameType> = Pick<
  PlasmicSubcomponentIconButton__OverridesType,
  DescendantsType<T>
>;
type NodeComponentProps<T extends NodeNameType> =
  // Explicitly specify variants, args, and overrides as objects
  {
    variants?: PlasmicSubcomponentIconButton__VariantsArgs;
    args?: PlasmicSubcomponentIconButton__ArgsType;
    overrides?: NodeOverridesType<T>;
  } & Omit<PlasmicSubcomponentIconButton__VariantsArgs, ReservedPropsType> & // Specify variants directly as props
    /* Specify args directly as props*/ Omit<
      PlasmicSubcomponentIconButton__ArgsType,
      ReservedPropsType
    > &
    /* Specify overrides for each element directly as props*/ Omit<
      NodeOverridesType<T>,
      ReservedPropsType | VariantPropType | ArgPropType
    > &
    /* Specify props for the root element*/ Omit<
      Partial<React.ComponentProps<NodeDefaultElementType[T]>>,
      ReservedPropsType | VariantPropType | ArgPropType | DescendantsType<T>
    >;

function makeNodeComponent<NodeName extends NodeNameType>(nodeName: NodeName) {
  type PropsType = NodeComponentProps<NodeName> & { key?: React.Key };
  const func = function <T extends PropsType>(
    props: T & StrictProps<T, PropsType>
  ) {
    const { variants, args, overrides } = React.useMemo(
      () =>
        deriveRenderOpts(props, {
          name: nodeName,
          descendantNames: PlasmicDescendants[nodeName],
          internalArgPropNames: PlasmicSubcomponentIconButton__ArgProps,
          internalVariantPropNames: PlasmicSubcomponentIconButton__VariantProps
        }),
      [props, nodeName]
    );
    return PlasmicSubcomponentIconButton__RenderFunc({
      variants,
      args,
      overrides,
      forNode: nodeName
    });
  };
  if (nodeName === "root") {
    func.displayName = "PlasmicSubcomponentIconButton";
  } else {
    func.displayName = `PlasmicSubcomponentIconButton.${nodeName}`;
  }
  return func;
}

export const PlasmicSubcomponentIconButton = Object.assign(
  // Top-level PlasmicSubcomponentIconButton renders the root element
  makeNodeComponent("root"),
  {
    // Helper components rendering sub-elements
    closeButton: makeNodeComponent("closeButton"),

    // Metadata about props expected for PlasmicSubcomponentIconButton
    internalVariantProps: PlasmicSubcomponentIconButton__VariantProps,
    internalArgProps: PlasmicSubcomponentIconButton__ArgProps
  }
);

export default PlasmicSubcomponentIconButton;
/* prettier-ignore-end */
