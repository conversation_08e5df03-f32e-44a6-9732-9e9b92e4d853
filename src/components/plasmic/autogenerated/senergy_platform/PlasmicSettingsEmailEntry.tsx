/* eslint-disable */
/* tslint:disable */
// @ts-nocheck
/* prettier-ignore-start */

/** @jsxRuntime classic */
/** @jsx createPlasmicElementProxy */
/** @jsxFrag React.Fragment */

// This class is auto-generated by Plasmic; please do not edit!
// Plasmic Project: nZeWKcn21KijvC7eT8B3c7
// Component: oBy3j97i5seo

"use client";

import * as React from "react";

import Head from "next/head";
import Link, { LinkProps } from "next/link";
import { useRouter } from "next/navigation";

import {
  Flex as Flex__,
  MultiChoiceArg,
  PlasmicDataSourceContextProvider as PlasmicDataSourceContextProvider__,
  PlasmicIcon as PlasmicIcon__,
  PlasmicImg as PlasmicImg__,
  PlasmicLink as PlasmicLink__,
  PlasmicPageGuard as PlasmicPageGuard__,
  SingleBooleanChoiceArg,
  SingleChoiceArg,
  Stack as Stack__,
  StrictProps,
  Trans as Trans__,
  classNames,
  createPlasmicElementProxy,
  deriveRenderOpts,
  ensureGlobalVariants,
  generateOnMutateForSpec,
  generateStateOnChangeProp,
  generateStateOnChangePropForCodeComponents,
  generateStateValueProp,
  get as $stateGet,
  hasVariant,
  initializeCodeComponentStates,
  initializePlasmicStates,
  makeFragment,
  omit,
  pick,
  renderPlasmicSlot,
  set as $stateSet,
  useCurrentUser,
  useDollarState,
  usePlasmicTranslator,
  useTrigger,
  wrapWithClassName
} from "@plasmicapp/react-web";
import {
  DataCtxReader as DataCtxReader__,
  useDataEnv,
  useGlobalActions
} from "@plasmicapp/host";

import SubcomponentIconWithText from "../../SubcomponentIconWithText"; // plasmic-import: _eFlbiSm6hZU/component

import "@plasmicapp/react-web/lib/plasmic.css";

import projectcss from "./plasmic.module.css"; // plasmic-import: nZeWKcn21KijvC7eT8B3c7/projectcss
import sty from "./PlasmicSettingsEmailEntry.module.css"; // plasmic-import: oBy3j97i5seo/css

import Icon4Icon from "./icons/PlasmicIcon__Icon4"; // plasmic-import: L0D3mLblRaX0/icon
import AwardIcon from "./icons/PlasmicIcon__Award"; // plasmic-import: BXxYsiTdXJr_/icon

createPlasmicElementProxy;

export type PlasmicSettingsEmailEntry__VariantMembers = {
  primary: "primary";
  verified: "verified";
};
export type PlasmicSettingsEmailEntry__VariantsArgs = {
  primary?: SingleBooleanChoiceArg<"primary">;
  verified?: SingleBooleanChoiceArg<"verified">;
};
type VariantPropType = keyof PlasmicSettingsEmailEntry__VariantsArgs;
export const PlasmicSettingsEmailEntry__VariantProps =
  new Array<VariantPropType>("primary", "verified");

export type PlasmicSettingsEmailEntry__ArgsType = { emailAddress?: string };
type ArgPropType = keyof PlasmicSettingsEmailEntry__ArgsType;
export const PlasmicSettingsEmailEntry__ArgProps = new Array<ArgPropType>(
  "emailAddress"
);

export type PlasmicSettingsEmailEntry__OverridesType = {
  root?: Flex__<"div">;
  text?: Flex__<"div">;
  primaryTag?: Flex__<typeof SubcomponentIconWithText>;
  iconSpot?: Flex__<"svg">;
  verifiedTag?: Flex__<typeof SubcomponentIconWithText>;
  iconSpot2?: Flex__<"svg">;
};

export interface DefaultSettingsEmailEntryProps {
  emailAddress?: string;
  primary?: SingleBooleanChoiceArg<"primary">;
  verified?: SingleBooleanChoiceArg<"verified">;
  className?: string;
}

const $$ = {};

function useNextRouter() {
  try {
    return useRouter();
  } catch {}
  return undefined;
}

function PlasmicSettingsEmailEntry__RenderFunc(props: {
  variants: PlasmicSettingsEmailEntry__VariantsArgs;
  args: PlasmicSettingsEmailEntry__ArgsType;
  overrides: PlasmicSettingsEmailEntry__OverridesType;
  forNode?: string;
}) {
  const { variants, overrides, forNode } = props;

  const args = React.useMemo(
    () =>
      Object.assign(
        {},
        Object.fromEntries(
          Object.entries(props.args).filter(([_, v]) => v !== undefined)
        )
      ),
    [props.args]
  );

  const $props = {
    ...args,
    ...variants
  };

  const __nextRouter = useNextRouter();
  const $ctx = useDataEnv?.() || {};
  const refsRef = React.useRef({});
  const $refs = refsRef.current;

  let [$queries, setDollarQueries] = React.useState<
    Record<string, ReturnType<typeof usePlasmicDataOp>>
  >({});
  const stateSpecs: Parameters<typeof useDollarState>[0] = React.useMemo(
    () => [
      {
        path: "primary",
        type: "private",
        variableType: "variant",
        initFunc: ({ $props, $state, $queries, $ctx }) => $props.primary
      },
      {
        path: "verified",
        type: "private",
        variableType: "variant",
        initFunc: ({ $props, $state, $queries, $ctx }) => $props.verified
      },
      {
        path: "primaryTag.inputValue",
        type: "private",
        variableType: "text",
        initFunc: ({ $props, $state, $queries, $ctx }) => undefined
      },
      {
        path: "verifiedTag.inputValue",
        type: "private",
        variableType: "text",
        initFunc: ({ $props, $state, $queries, $ctx }) => undefined
      }
    ],
    [$props, $ctx, $refs]
  );
  const $state = useDollarState(stateSpecs, {
    $props,
    $ctx,
    $queries: $queries,
    $refs
  });

  const new$Queries: Record<string, ReturnType<typeof usePlasmicDataOp>> = {};
  if (Object.keys(new$Queries).some(k => new$Queries[k] !== $queries[k])) {
    setDollarQueries(new$Queries);

    $queries = new$Queries;
  }

  return (
    <div
      data-plasmic-name={"root"}
      data-plasmic-override={overrides.root}
      data-plasmic-root={true}
      data-plasmic-for-node={forNode}
      className={classNames(
        projectcss.all,
        projectcss.root_reset,
        projectcss.plasmic_default_styles,
        projectcss.plasmic_mixins,
        projectcss.plasmic_tokens,
        sty.root
      )}
    >
      <Stack__
        as={"div"}
        hasGap={true}
        className={classNames(projectcss.all, sty.freeBox___5BRu)}
      >
        <Stack__
          as={"div"}
          hasGap={true}
          className={classNames(projectcss.all, sty.freeBox__gqmMu, {
            [sty.freeBoxprimary__gqmMudOdY]: hasVariant(
              $state,
              "primary",
              "primary"
            ),
            [sty.freeBoxverified__gqmMugAa1K]: hasVariant(
              $state,
              "verified",
              "verified"
            )
          })}
        >
          <div
            data-plasmic-name={"text"}
            data-plasmic-override={overrides.text}
            className={classNames(
              projectcss.all,
              projectcss.__wab_text,
              sty.text
            )}
          >
            <React.Fragment>{$props.emailAddress}</React.Fragment>
          </div>
          <SubcomponentIconWithText
            data-plasmic-name={"primaryTag"}
            data-plasmic-override={overrides.primaryTag}
            background={"sunflower"}
            className={classNames("__wab_instance", sty.primaryTag, {
              [sty.primaryTagprimary]: hasVariant($state, "primary", "primary")
            })}
            displayText={"Primary"}
            iconSpot2={
              <Icon4Icon
                data-plasmic-name={"iconSpot"}
                data-plasmic-override={overrides.iconSpot}
                className={classNames(projectcss.all, sty.iconSpot)}
                role={"img"}
              />
            }
            inputValue={generateStateValueProp($state, [
              "primaryTag",
              "inputValue"
            ])}
            onInputValueChange={async (...eventArgs: any) => {
              generateStateOnChangeProp($state, [
                "primaryTag",
                "inputValue"
              ]).apply(null, eventArgs);

              if (
                eventArgs.length > 1 &&
                eventArgs[1] &&
                eventArgs[1]._plasmic_state_init_
              ) {
                return;
              }
            }}
            rounded={true}
          />

          <SubcomponentIconWithText
            data-plasmic-name={"verifiedTag"}
            data-plasmic-override={overrides.verifiedTag}
            background={"sage"}
            className={classNames("__wab_instance", sty.verifiedTag, {
              [sty.verifiedTagverified]: hasVariant(
                $state,
                "verified",
                "verified"
              )
            })}
            displayText={"Verified"}
            iconSpot2={
              <AwardIcon
                data-plasmic-name={"iconSpot2"}
                data-plasmic-override={overrides.iconSpot2}
                className={classNames(projectcss.all, sty.iconSpot2)}
                role={"img"}
              />
            }
            inputValue={generateStateValueProp($state, [
              "verifiedTag",
              "inputValue"
            ])}
            onInputValueChange={async (...eventArgs: any) => {
              generateStateOnChangeProp($state, [
                "verifiedTag",
                "inputValue"
              ]).apply(null, eventArgs);

              if (
                eventArgs.length > 1 &&
                eventArgs[1] &&
                eventArgs[1]._plasmic_state_init_
              ) {
                return;
              }
            }}
            rounded={true}
          />
        </Stack__>
      </Stack__>
    </div>
  ) as React.ReactElement | null;
}

const PlasmicDescendants = {
  root: ["root", "text", "primaryTag", "iconSpot", "verifiedTag", "iconSpot2"],
  text: ["text"],
  primaryTag: ["primaryTag", "iconSpot"],
  iconSpot: ["iconSpot"],
  verifiedTag: ["verifiedTag", "iconSpot2"],
  iconSpot2: ["iconSpot2"]
} as const;
type NodeNameType = keyof typeof PlasmicDescendants;
type DescendantsType<T extends NodeNameType> =
  (typeof PlasmicDescendants)[T][number];
type NodeDefaultElementType = {
  root: "div";
  text: "div";
  primaryTag: typeof SubcomponentIconWithText;
  iconSpot: "svg";
  verifiedTag: typeof SubcomponentIconWithText;
  iconSpot2: "svg";
};

type ReservedPropsType = "variants" | "args" | "overrides";
type NodeOverridesType<T extends NodeNameType> = Pick<
  PlasmicSettingsEmailEntry__OverridesType,
  DescendantsType<T>
>;
type NodeComponentProps<T extends NodeNameType> =
  // Explicitly specify variants, args, and overrides as objects
  {
    variants?: PlasmicSettingsEmailEntry__VariantsArgs;
    args?: PlasmicSettingsEmailEntry__ArgsType;
    overrides?: NodeOverridesType<T>;
  } & Omit<PlasmicSettingsEmailEntry__VariantsArgs, ReservedPropsType> & // Specify variants directly as props
    /* Specify args directly as props*/ Omit<
      PlasmicSettingsEmailEntry__ArgsType,
      ReservedPropsType
    > &
    /* Specify overrides for each element directly as props*/ Omit<
      NodeOverridesType<T>,
      ReservedPropsType | VariantPropType | ArgPropType
    > &
    /* Specify props for the root element*/ Omit<
      Partial<React.ComponentProps<NodeDefaultElementType[T]>>,
      ReservedPropsType | VariantPropType | ArgPropType | DescendantsType<T>
    >;

function makeNodeComponent<NodeName extends NodeNameType>(nodeName: NodeName) {
  type PropsType = NodeComponentProps<NodeName> & { key?: React.Key };
  const func = function <T extends PropsType>(
    props: T & StrictProps<T, PropsType>
  ) {
    const { variants, args, overrides } = React.useMemo(
      () =>
        deriveRenderOpts(props, {
          name: nodeName,
          descendantNames: PlasmicDescendants[nodeName],
          internalArgPropNames: PlasmicSettingsEmailEntry__ArgProps,
          internalVariantPropNames: PlasmicSettingsEmailEntry__VariantProps
        }),
      [props, nodeName]
    );
    return PlasmicSettingsEmailEntry__RenderFunc({
      variants,
      args,
      overrides,
      forNode: nodeName
    });
  };
  if (nodeName === "root") {
    func.displayName = "PlasmicSettingsEmailEntry";
  } else {
    func.displayName = `PlasmicSettingsEmailEntry.${nodeName}`;
  }
  return func;
}

export const PlasmicSettingsEmailEntry = Object.assign(
  // Top-level PlasmicSettingsEmailEntry renders the root element
  makeNodeComponent("root"),
  {
    // Helper components rendering sub-elements
    text: makeNodeComponent("text"),
    primaryTag: makeNodeComponent("primaryTag"),
    iconSpot: makeNodeComponent("iconSpot"),
    verifiedTag: makeNodeComponent("verifiedTag"),
    iconSpot2: makeNodeComponent("iconSpot2"),

    // Metadata about props expected for PlasmicSettingsEmailEntry
    internalVariantProps: PlasmicSettingsEmailEntry__VariantProps,
    internalArgProps: PlasmicSettingsEmailEntry__ArgProps
  }
);

export default PlasmicSettingsEmailEntry;
/* prettier-ignore-end */
