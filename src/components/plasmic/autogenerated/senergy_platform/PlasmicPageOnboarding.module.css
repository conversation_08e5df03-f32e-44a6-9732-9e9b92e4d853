.root {
  display: grid;
  flex-direction: column;
  position: relative;
  width: 100%;
  height: 100vh;
  align-content: center;
  justify-items: center;
  justify-content: flex-start;
  align-items: center;
  background: var(--token-1AMvw6c2eIK7);
  overflow: auto;
  min-width: 0;
  grid-template-columns:
    var(--plsmc-viewport-gap) 1fr minmax(0, var(--plsmc-wide-chunk))
    min(
      var(--plsmc-standard-width),
      calc(100% - var(--plsmc-viewport-gap) - var(--plsmc-viewport-gap))
    )
    minmax(0, var(--plsmc-wide-chunk)) 1fr var(--plsmc-viewport-gap);
}
.root > * {
  grid-column: 4;
}
.onOnboardingModal:global(.__wab_instance) {
  max-width: 100%;
  position: relative;
  width: 100%;
  grid-column-start: 3 !important;
  grid-column-end: -3 !important;
}
