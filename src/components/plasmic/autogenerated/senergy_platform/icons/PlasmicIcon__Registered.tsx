/* eslint-disable */
/* tslint:disable */
// @ts-nocheck
/* prettier-ignore-start */
import React from "react";
import { classNames } from "@plasmicapp/react-web";

export type RegisteredIconProps = React.ComponentProps<"svg"> & {
  title?: string;
};

export function RegisteredIcon(props: RegisteredIconProps) {
  const { className, style, title, ...restProps } = props;
  return (
    <svg
      xmlns={"http://www.w3.org/2000/svg"}
      className={classNames(
        "plasmic-default__svg",
        className,
        "icon icon-tabler icon-tabler-registered"
      )}
      viewBox={"0 0 24 24"}
      strokeWidth={"1.5"}
      stroke={"currentColor"}
      fill={"none"}
      strokeLinecap={"round"}
      strokeLinejoin={"round"}
      height={"1em"}
      style={style}
      {...restProps}
    >
      {title && <title>{title}</title>}

      <path d={"M0 0h24v24H0z"} fill={"none"} stroke={"none"}></path>

      <path d={"M3 12a9 9 0 1018 0 9 9 0 10-18 0"}></path>

      <path d={"M10 15V9h2a2 2 0 110 4h-2m4 2l-2-2"}></path>
    </svg>
  );
}

export default RegisteredIcon;
/* prettier-ignore-end */
