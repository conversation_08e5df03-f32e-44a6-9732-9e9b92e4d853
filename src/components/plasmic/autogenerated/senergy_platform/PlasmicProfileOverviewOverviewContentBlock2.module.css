.overviewContentBlock {
  display: flex;
  flex-direction: column;
  position: relative;
  width: 100%;
  height: auto;
  justify-content: flex-start;
  align-items: center;
  background: none;
  min-width: 0;
}
.overviewHeading:global(.__wab_instance) {
  max-width: 100%;
  position: relative;
  margin-top: 0px;
}
.text {
  font-size: var(--token-hKlnSDJVAhUx);
}
.featuredWorksArea {
  position: relative;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start;
  width: 100%;
  height: auto;
  max-width: 100%;
  min-width: 0;
  display: none;
  padding: var(--token-sazGmnf7GWAk);
  margin: var(--token-sazGmnf7GWAk);
}
.profileOverviewHighlightedWorksLayout:global(.__wab_instance) {
  max-width: 100%;
}
.seeMoreButtonContainer2 {
  display: flex;
  position: relative;
  flex-direction: row;
  align-items: center;
  justify-content: flex-end;
  width: 100%;
  height: auto;
  max-width: 100%;
  min-width: 0;
  padding: var(--token-sazGmnf7GWAk);
  margin: var(--token-sazGmnf7GWAk);
}
.profileOverviewSeeMoreButton___8Im90:global(.__wab_instance) {
  max-width: 100%;
  position: relative;
}
.summaryArea {
  display: flex;
  position: relative;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start;
  width: 100%;
  height: auto;
  max-width: 100%;
  min-width: 0;
  padding: var(--token-sazGmnf7GWAk);
  margin: 0px var(--token-sazGmnf7GWAk) var(--token-sazGmnf7GWAk);
}
.personalSummaryHeading:global(.__wab_instance) {
  max-width: 100%;
}
.freeBox___1WYsA {
  display: flex;
  position: relative;
  flex-direction: column;
  align-items: flex-start;
  justify-content: flex-start;
  width: 100%;
  height: auto;
  max-width: 100%;
  min-width: 0;
  padding: var(--token-4Wrp9mDZCSCQ);
  margin: var(--token-sazGmnf7GWAk);
}
.overviewBlocks7:global(.__wab_instance) {
  max-width: 100%;
}
.freeBox__k92Qj {
  display: flex;
  position: relative;
  flex-direction: row;
  width: 100%;
  height: auto;
  max-width: 100%;
  min-width: 0;
  padding: var(--token-4Wrp9mDZCSCQ);
}
.freeBox__k92Qj > :global(.__wab_flex-container) {
  flex-direction: row;
  align-items: stretch;
  justify-content: center;
  flex-wrap: wrap;
  align-content: center;
  min-width: 0;
  margin-left: calc(0px - var(--token-4Wrp9mDZCSCQ));
  width: calc(100% + var(--token-4Wrp9mDZCSCQ));
  margin-top: calc(0px - var(--token-j0qnbpah5w9U));
  height: calc(100% + var(--token-j0qnbpah5w9U));
}
.freeBox__k92Qj > :global(.__wab_flex-container) > *,
.freeBox__k92Qj > :global(.__wab_flex-container) > :global(.__wab_slot) > *,
.freeBox__k92Qj > :global(.__wab_flex-container) > picture > img,
.freeBox__k92Qj
  > :global(.__wab_flex-container)
  > :global(.__wab_slot)
  > picture
  > img {
  margin-left: var(--token-4Wrp9mDZCSCQ);
  margin-top: var(--token-j0qnbpah5w9U);
}
.overviewBlocks2:global(.__wab_instance) {
  max-width: 100%;
}
.overviewBlocks5:global(.__wab_instance) {
  max-width: 100%;
  width: 589px;
  flex-shrink: 0;
}
.freeBox___9AGm5 {
  display: flex;
  position: relative;
  flex-direction: row;
  width: 100%;
  height: auto;
  max-width: 100%;
  min-width: 0;
  padding: var(--token-4Wrp9mDZCSCQ);
  margin: var(--token-sazGmnf7GWAk);
}
.freeBox___9AGm5 > :global(.__wab_flex-container) {
  flex-direction: row;
  align-items: stretch;
  justify-content: center;
  flex-wrap: wrap;
  align-content: center;
  min-width: 0;
  margin-left: calc(0px - var(--token-4Wrp9mDZCSCQ));
  width: calc(100% + var(--token-4Wrp9mDZCSCQ));
  margin-top: calc(0px - var(--token-j0qnbpah5w9U));
  height: calc(100% + var(--token-j0qnbpah5w9U));
}
.freeBox___9AGm5 > :global(.__wab_flex-container) > *,
.freeBox___9AGm5 > :global(.__wab_flex-container) > :global(.__wab_slot) > *,
.freeBox___9AGm5 > :global(.__wab_flex-container) > picture > img,
.freeBox___9AGm5
  > :global(.__wab_flex-container)
  > :global(.__wab_slot)
  > picture
  > img {
  margin-left: var(--token-4Wrp9mDZCSCQ);
  margin-top: var(--token-j0qnbpah5w9U);
}
.overviewBlocks4:global(.__wab_instance) {
  max-width: 100%;
  position: relative;
}
.overviewBlocks3:global(.__wab_instance) {
  max-width: 100%;
  position: relative;
}
.overviewBlocks6:global(.__wab_instance) {
  max-width: 100%;
}
.seeMoreButtonContainer {
  display: flex;
  position: relative;
  flex-direction: row;
  width: 100%;
  height: auto;
  max-width: 100%;
  min-width: 0;
  padding: var(--token-sazGmnf7GWAk);
  margin: var(--token-sazGmnf7GWAk);
}
.seeMoreButtonContainer > :global(.__wab_flex-container) {
  flex-direction: row;
  align-items: center;
  justify-content: flex-end;
  min-width: 0;
  margin-left: calc(0px - var(--token-4Wrp9mDZCSCQ));
  width: calc(100% + var(--token-4Wrp9mDZCSCQ));
}
.seeMoreButtonContainer > :global(.__wab_flex-container) > *,
.seeMoreButtonContainer
  > :global(.__wab_flex-container)
  > :global(.__wab_slot)
  > *,
.seeMoreButtonContainer > :global(.__wab_flex-container) > picture > img,
.seeMoreButtonContainer
  > :global(.__wab_flex-container)
  > :global(.__wab_slot)
  > picture
  > img {
  margin-left: var(--token-4Wrp9mDZCSCQ);
}
.profileOverviewSeeMoreButton__v1DGe:global(.__wab_instance) {
  max-width: 100%;
  position: relative;
}
