'use client';
import AuthForm from '@/components/ui/Auth/Form/index';

import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';

export const description =
  'An authentication dialog with the ability to sign up or login.';

interface AuthDialogProps {
  open?: boolean;
  onOpenChange?: (open: boolean) => void;
  hideClose?: boolean;
  redirectPath?: string;
}

export default function AuthDialog({
  open,
  onOpenChange,
  hideClose,
  redirectPath = '/settings',
}: AuthDialogProps) {
  const handleSuccess = () => {
    // We'll let the form handle its own state for magic link
    // This will only be called when actually authenticated (e.g. passkey)
    if (onOpenChange) {
      onOpenChange(false);
    }
  };

  return (
    <Dialog onOpenChange={hideClose ? undefined : onOpenChange} open={open}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle hidden={true}>Sign up or Login</DialogTitle>
          <DialogDescription hidden={true}>
            Sign up or login by filling out the information below
          </DialogDescription>
        </DialogHeader>
        <AuthForm
          closeOnMagicLink={false}
          onSuccess={handleSuccess}
          redirectPath={redirectPath}
        />
      </DialogContent>
    </Dialog>
  );
}
