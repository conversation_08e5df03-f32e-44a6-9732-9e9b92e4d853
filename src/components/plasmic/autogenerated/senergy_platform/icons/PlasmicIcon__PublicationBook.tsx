/* eslint-disable */
/* tslint:disable */
// @ts-nocheck
/* prettier-ignore-start */
import React from "react";
import { classNames } from "@plasmicapp/react-web";

export type PublicationBookIconProps = React.ComponentProps<"svg"> & {
  title?: string;
};

export function PublicationBookIcon(props: PublicationBookIconProps) {
  const { className, style, title, ...restProps } = props;
  return (
    <svg
      xmlns={"http://www.w3.org/2000/svg"}
      className={classNames(
        "plasmic-default__svg",
        className,
        "icon icon-tabler icon-tabler-book-2"
      )}
      viewBox={"0 0 24 24"}
      strokeWidth={"1.5"}
      stroke={"currentColor"}
      fill={"none"}
      strokeLinecap={"round"}
      strokeLinejoin={"round"}
      height={"1em"}
      style={style}
      {...restProps}
    >
      {title && <title>{title}</title>}

      <path d={"M0 0h24v24H0z"} fill={"none"} stroke={"none"}></path>

      <path d={"M19 4v16H7a2 2 0 01-2-2V6a2 2 0 012-2h12z"}></path>

      <path d={"M19 16H7a2 2 0 00-2 2M9 8h6"}></path>
    </svg>
  );
}

export default PublicationBookIcon;
/* prettier-ignore-end */
