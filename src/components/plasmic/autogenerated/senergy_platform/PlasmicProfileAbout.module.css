.root {
  display: flex;
  flex-direction: column;
  position: relative;
  width: 100%;
  height: 100%;
  justify-content: flex-start;
  align-items: center;
  min-width: 0;
  min-height: 0;
}
.profileSectionsIntroductionBlock:global(.__wab_instance) {
  max-width: 100%;
}
.pSectionsExperience:global(.__wab_instance) {
  max-width: 100%;
}
.pSectionsEducation:global(.__wab_instance) {
  max-width: 100%;
}
.profileSectionsLanguageBlock:global(.__wab_instance) {
  max-width: 100%;
}
.pSectionsLicensesAndCertifications:global(.__wab_instance) {
  max-width: 100%;
}
.pSectionsPatentsAndTrademarks:global(.__wab_instance) {
  max-width: 100%;
}
.pSectionsPublications:global(.__wab_instance) {
  max-width: 100%;
}
.profileSectionsSkillsBlock:global(.__wab_instance) {
  max-width: 100%;
}
.profileSectionsToolsBlock:global(.__wab_instance) {
  max-width: 100%;
}
