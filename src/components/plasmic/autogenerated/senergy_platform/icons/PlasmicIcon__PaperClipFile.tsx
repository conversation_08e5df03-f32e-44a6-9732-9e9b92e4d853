/* eslint-disable */
/* tslint:disable */
// @ts-nocheck
/* prettier-ignore-start */
import React from "react";
import { classNames } from "@plasmicapp/react-web";

export type PaperClipFileIconProps = React.ComponentProps<"svg"> & {
  title?: string;
};

export function PaperClipFileIcon(props: PaperClipFileIconProps) {
  const { className, style, title, ...restProps } = props;
  return (
    <svg
      xmlns={"http://www.w3.org/2000/svg"}
      className={classNames(
        "plasmic-default__svg",
        className,
        "icon icon-tabler icon-tabler-paperclip"
      )}
      viewBox={"0 0 24 24"}
      strokeWidth={"1.5"}
      stroke={"currentColor"}
      fill={"none"}
      strokeLinecap={"round"}
      strokeLinejoin={"round"}
      height={"1em"}
      style={style}
      {...restProps}
    >
      {title && <title>{title}</title>}

      <path d={"M0 0h24v24H0z"} fill={"none"} stroke={"none"}></path>

      <path
        d={
          "M15 7l-6.5 6.5a1.5 1.5 0 003 3L18 10a3 3 0 00-6-6l-6.5 6.5a4.5 4.5 0 009 9L21 13"
        }
      ></path>
    </svg>
  );
}

export default PaperClipFileIcon;
/* prettier-ignore-end */
