import * as React from "react";
import {
  DefaultProfileCoreButtonForProfileNavigationProps,
  PlasmicProfileCoreButtonForProfileNavigation
} from "./autogenerated/senergy_platform/PlasmicProfileCoreButtonForProfileNavigation";

import {
  ButtonRef,
  HtmlAnchorOnlyProps,
  HtmlButtonOnlyProps
} from "@plasmicapp/react-web";

export interface ProfileCoreButtonForProfileNavigationProps
  extends DefaultProfileCoreButtonForProfileNavigationProps {
  // Feel free to add any additional props that this component should receive
}
function ProfileCoreButtonForProfileNavigation_(
  props: ProfileCoreButtonForProfileNavigationProps,
  ref: ButtonRef
) {
  const { plasmicProps } =
    PlasmicProfileCoreButtonForProfileNavigation.useBehavior<ProfileCoreButtonForProfileNavigationProps>(
      props,
      ref
    );
  return <PlasmicProfileCoreButtonForProfileNavigation {...plasmicProps} />;
}

export type ButtonComponentType = {
  (
    props: Omit<
      ProfileCoreButtonForProfileNavigationProps,
      HtmlAnchorOnlyProps
    > & { ref?: React.Ref<HTMLButtonElement> }
  ): React.ReactElement;
  (
    props: Omit<
      ProfileCoreButtonForProfileNavigationProps,
      HtmlButtonOnlyProps
    > & { ref?: React.Ref<HTMLAnchorElement> }
  ): React.ReactElement;
};
const ProfileCoreButtonForProfileNavigation = React.forwardRef(
  ProfileCoreButtonForProfileNavigation_
) as any as ButtonComponentType;

export default Object.assign(ProfileCoreButtonForProfileNavigation, {
  __plumeType: "button"
});
