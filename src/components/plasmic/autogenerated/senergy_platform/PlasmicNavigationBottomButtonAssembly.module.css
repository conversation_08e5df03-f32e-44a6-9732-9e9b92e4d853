.animationContainer {
  display: flex;
  flex-direction: column;
  position: relative;
  width: 100%;
  height: auto;
  justify-content: flex-start;
  align-items: center;
  justify-self: flex-start;
  grid-column-start: 1 !important;
  grid-column-end: -1 !important;
}
.navigationActiveSlotSlider:global(.__wab_instance) {
  position: absolute;
  z-index: 2;
  flex-shrink: 0;
  display: none;
}
.navigationActiveSlotSliderhoverTab_slot1:global(.__wab_instance) {
  flex-shrink: 0;
  display: none;
}
.navigationActiveSlotSliderhoverTab_slot2:global(.__wab_instance) {
  flex-shrink: 0;
  display: none;
}
.navigationActiveSlotSlideractiveTab_slot1:global(.__wab_instance) {
  flex-shrink: 0;
  display: flex;
}
.navigationActiveSlotSlideractiveTab_slot2:global(.__wab_instance) {
  flex-shrink: 0;
  display: flex;
}
.navigationActiveSlotSliderhoverTab_slot1_activeTab_slot1:global(
    .__wab_instance
  ) {
  flex-shrink: 0;
}
.navigationActiveSlotSlideractiveTab_slot2_hoverTab_slot2:global(
    .__wab_instance
  ) {
  flex-shrink: 0;
}
.navigationActiveSlotSlideractiveTab_slot1_unAuthed:global(.__wab_instance) {
  flex-shrink: 0;
  display: none;
}
.navigationActiveSlotSlideractiveTab_slot2_unAuthed:global(.__wab_instance) {
  flex-shrink: 0;
  display: none;
}
.navigationHoverSlotSlider:global(.__wab_instance) {
  position: absolute;
  z-index: 1;
  flex-shrink: 0;
}
.navigationHoverSlotSliderhoverTab_slot1:global(.__wab_instance) {
  flex-shrink: 0;
}
.navigationHoverSlotSliderhoverTab_slot2:global(.__wab_instance) {
  flex-shrink: 0;
}
.navigationHoverSlotSlideractiveTab_slot1:global(.__wab_instance) {
  flex-shrink: 0;
}
.navigationHoverSlotSlideractiveTab_slot2:global(.__wab_instance) {
  flex-shrink: 0;
}
.navigationHoverSlotSliderhoverTab_slot1_activeTab_slot1:global(
    .__wab_instance
  ) {
  flex-shrink: 0;
}
.navigationHoverSlotSliderhoverTab_slot2_activeTab_slot1:global(
    .__wab_instance
  ) {
  flex-shrink: 0;
}
.navigationHoverSlotSlideractiveTab_slot2_hoverTab_slot1:global(
    .__wab_instance
  ) {
  flex-shrink: 0;
}
.loginButton:global(.__wab_instance) {
  max-width: 100%;
  position: relative;
  z-index: 5;
  flex-shrink: 0;
  display: none;
}
.loginButtoncollapsed:global(.__wab_instance) {
  flex-shrink: 0;
}
.loginButtonunAuthed:global(.__wab_instance) {
  flex-shrink: 0;
  display: flex;
}
.navIcon2 {
  position: relative;
  object-fit: cover;
  max-width: 100%;
  width: var(--token-j0qnbpah5w9U);
  height: var(--token-j0qnbpah5w9U);
  margin-right: 0;
}
.profileButton:global(.__wab_instance) {
  max-width: 100%;
  position: relative;
  z-index: 5;
  flex-shrink: 0;
}
.profileButtonhoverTab_slot1:global(.__wab_instance) {
  flex-shrink: 0;
}
.profileButtonactiveTab_slot1:global(.__wab_instance) {
  flex-shrink: 0;
}
.profileButtoncollapsed:global(.__wab_instance) {
  flex-shrink: 0;
}
.profileButtonunAuthed:global(.__wab_instance) {
  flex-shrink: 0;
  display: none;
}
.profileButtonhoverTab_slot1_activeTab_slot1:global(.__wab_instance) {
  flex-shrink: 0;
}
.icon {
  position: relative;
  object-fit: cover;
  max-width: 100%;
  width: var(--token-j0qnbpah5w9U);
  height: var(--token-j0qnbpah5w9U);
  margin-right: 0;
}
.settingsButton:global(.__wab_instance) {
  max-width: 100%;
  position: relative;
  z-index: 5;
  flex-shrink: 0;
}
.settingsButtonhoverTab_slot1:global(.__wab_instance) {
  flex-shrink: 0;
}
.settingsButtonhoverTab_slot2:global(.__wab_instance) {
  flex-shrink: 0;
}
.settingsButtonactiveTab_slot1:global(.__wab_instance) {
  flex-shrink: 0;
}
.settingsButtonactiveTab_slot2:global(.__wab_instance) {
  flex-shrink: 0;
}
.settingsButtoncollapsed:global(.__wab_instance) {
  flex-shrink: 0;
}
.settingsButtonunAuthed:global(.__wab_instance) {
  flex-shrink: 0;
  display: none;
}
.icon2 {
  position: relative;
  object-fit: cover;
  max-width: 100%;
  width: var(--token-j0qnbpah5w9U);
  height: var(--token-j0qnbpah5w9U);
  margin-right: 0;
}
.moreButton:global(.__wab_instance) {
  max-width: 100%;
  position: relative;
  z-index: 5;
  flex-shrink: 0;
  display: none;
}
.moreButtoncollapsed:global(.__wab_instance) {
  flex-shrink: 0;
}
.moreButtonunAuthed:global(.__wab_instance) {
  flex-shrink: 0;
}
.navIcon {
  position: relative;
  object-fit: cover;
  max-width: 100%;
  width: var(--token-j0qnbpah5w9U);
  height: var(--token-j0qnbpah5w9U);
  margin-right: 0;
}
