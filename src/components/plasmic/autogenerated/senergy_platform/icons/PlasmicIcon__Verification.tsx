/* eslint-disable */
/* tslint:disable */
// @ts-nocheck
/* prettier-ignore-start */
import React from "react";
import { classNames } from "@plasmicapp/react-web";

export type VerificationIconProps = React.ComponentProps<"svg"> & {
  title?: string;
};

export function VerificationIcon(props: VerificationIconProps) {
  const { className, style, title, ...restProps } = props;
  return (
    <svg
      xmlns={"http://www.w3.org/2000/svg"}
      data-name={"Layer 1"}
      viewBox={"0 0 128 128"}
      height={"1em"}
      className={classNames("plasmic-default__svg", className)}
      style={style}
      {...restProps}
    >
      {title && <title>{title}</title>}

      <path
        d={
          "M122.42 57.24l-10.49-10.49c-.88-.88-1.84-3.2-1.84-4.44V27.47c0-5.27-4.29-9.56-9.56-9.56H85.69c-1.24 0-3.56-.96-4.44-1.84L70.76 5.58c-1.8-1.8-4.2-2.79-6.76-2.79s-4.96.99-6.76 2.79L46.75 16.07c-.88.88-3.2 1.84-4.44 1.84H27.47c-5.27 0-9.56 4.29-9.56 9.56v14.84c0 1.24-.96 3.56-1.84 4.44L5.58 57.24c-3.73 3.73-3.73 9.79 0 13.52l10.49 10.49c.88.88 1.84 3.2 1.84 4.44v14.84c0 5.27 4.29 9.56 9.56 9.56h14.84c1.24 0 3.56.96 4.44 1.84l10.49 10.49c1.8 1.8 4.2 2.8 6.76 2.8s4.96-.99 6.76-2.79l10.49-10.49c.88-.88 3.2-1.84 4.44-1.84h14.84c5.27 0 9.56-4.29 9.56-9.56V85.7c0-1.24.96-3.56 1.84-4.44l10.49-10.49a9.5 9.5 0 002.8-6.76c0-2.56-1-4.96-2.8-6.76zm-17.46 17.05c-2.74 2.74-4.72 7.53-4.72 11.4v14.55H85.69c-3.87 0-8.66 1.99-11.4 4.72L64 115.25l-10.29-10.29c-2.74-2.74-7.53-4.72-11.4-4.72H27.76V85.69c0-3.87-1.99-8.66-4.72-11.4L12.75 64l10.29-10.29c2.74-2.74 4.72-7.53 4.72-11.4V27.76h14.56c3.87 0 8.66-1.99 11.4-4.72l10.29-10.29L74.3 23.04c2.74 2.74 7.53 4.72 11.4 4.72h14.55v14.55c0 3.87 1.99 8.67 4.72 11.4L115.26 64l-10.3 10.29z"
        }
        fill={"currentColor"}
      ></path>

      <path
        d={
          "M58.15 74.58L40.37 56.8l-6.96 6.96L58.15 88.5l34.73-34.73-6.96-6.96-27.77 27.77z"
        }
        fill={"currentColor"}
      ></path>
    </svg>
  );
}

export default VerificationIcon;
/* prettier-ignore-end */
