/* eslint-disable */
/* tslint:disable */
// @ts-nocheck
/* prettier-ignore-start */

/** @jsxRuntime classic */
/** @jsx createPlasmicElementProxy */
/** @jsxFrag React.Fragment */

// This class is auto-generated by Plasmic; please do not edit!
// Plasmic Project: nZeWKcn21KijvC7eT8B3c7
// Component: b0Ccy1-sNNdM

"use client";

import * as React from "react";

import Head from "next/head";
import Link, { LinkProps } from "next/link";
import { useRouter } from "next/navigation";

import {
  Flex as Flex__,
  MultiChoiceArg,
  PlasmicDataSourceContextProvider as PlasmicDataSourceContextProvider__,
  PlasmicIcon as PlasmicIcon__,
  PlasmicImg as PlasmicImg__,
  PlasmicLink as PlasmicLink__,
  PlasmicPageGuard as PlasmicPageGuard__,
  SingleBooleanChoiceArg,
  SingleChoiceArg,
  Stack as Stack__,
  StrictProps,
  Trans as Trans__,
  classNames,
  createPlasmicElementProxy,
  deriveRenderOpts,
  ensureGlobalVariants,
  generateOnMutateForSpec,
  generateStateOnChangeProp,
  generateStateOnChangePropForCodeComponents,
  generateStateValueProp,
  get as $stateGet,
  hasVariant,
  initializeCodeComponentStates,
  initializePlasmicStates,
  makeFragment,
  omit,
  pick,
  renderPlasmicSlot,
  set as $stateSet,
  useCurrentUser,
  useDollarState,
  usePlasmicTranslator,
  useTrigger,
  wrapWithClassName
} from "@plasmicapp/react-web";
import {
  DataCtxReader as DataCtxReader__,
  useDataEnv,
  useGlobalActions
} from "@plasmicapp/host";

import SubcomponentSlotHeading from "../../SubcomponentSlotHeading"; // plasmic-import: pRxYe0rDyQBY/component
import SearchTile from "../../SearchTile"; // plasmic-import: oCayNd_9ZCYX/component

import "@plasmicapp/react-web/lib/plasmic.css";

import projectcss from "./plasmic.module.css"; // plasmic-import: nZeWKcn21KijvC7eT8B3c7/projectcss
import sty from "./PlasmicSearchDisplayResults.module.css"; // plasmic-import: b0Ccy1-sNNdM/css

createPlasmicElementProxy;

export type PlasmicSearchDisplayResults__VariantMembers = {};
export type PlasmicSearchDisplayResults__VariantsArgs = {};
type VariantPropType = keyof PlasmicSearchDisplayResults__VariantsArgs;
export const PlasmicSearchDisplayResults__VariantProps =
  new Array<VariantPropType>();

export type PlasmicSearchDisplayResults__ArgsType = {};
type ArgPropType = keyof PlasmicSearchDisplayResults__ArgsType;
export const PlasmicSearchDisplayResults__ArgProps = new Array<ArgPropType>();

export type PlasmicSearchDisplayResults__OverridesType = {
  root?: Flex__<"div">;
  subcomponentSlotHeading?: Flex__<typeof SubcomponentSlotHeading>;
  freeBox?: Flex__<"div">;
  searchTile?: Flex__<typeof SearchTile>;
};

export interface DefaultSearchDisplayResultsProps {
  className?: string;
}

const $$ = {};

function useNextRouter() {
  try {
    return useRouter();
  } catch {}
  return undefined;
}

function PlasmicSearchDisplayResults__RenderFunc(props: {
  variants: PlasmicSearchDisplayResults__VariantsArgs;
  args: PlasmicSearchDisplayResults__ArgsType;
  overrides: PlasmicSearchDisplayResults__OverridesType;
  forNode?: string;
}) {
  const { variants, overrides, forNode } = props;

  const args = React.useMemo(
    () =>
      Object.assign(
        {},
        Object.fromEntries(
          Object.entries(props.args).filter(([_, v]) => v !== undefined)
        )
      ),
    [props.args]
  );

  const $props = {
    ...args,
    ...variants
  };

  const __nextRouter = useNextRouter();
  const $ctx = useDataEnv?.() || {};
  const refsRef = React.useRef({});
  const $refs = refsRef.current;

  let [$queries, setDollarQueries] = React.useState<
    Record<string, ReturnType<typeof usePlasmicDataOp>>
  >({});

  const new$Queries: Record<string, ReturnType<typeof usePlasmicDataOp>> = {};
  if (Object.keys(new$Queries).some(k => new$Queries[k] !== $queries[k])) {
    setDollarQueries(new$Queries);

    $queries = new$Queries;
  }

  return (
    <div
      data-plasmic-name={"root"}
      data-plasmic-override={overrides.root}
      data-plasmic-root={true}
      data-plasmic-for-node={forNode}
      className={classNames(
        projectcss.all,
        projectcss.root_reset,
        projectcss.plasmic_default_styles,
        projectcss.plasmic_mixins,
        projectcss.plasmic_tokens,
        sty.root
      )}
    >
      <SubcomponentSlotHeading
        data-plasmic-name={"subcomponentSlotHeading"}
        data-plasmic-override={overrides.subcomponentSlotHeading}
        className={classNames("__wab_instance", sty.subcomponentSlotHeading)}
        headingTitle={"Search Results"}
      />

      <div
        data-plasmic-name={"freeBox"}
        data-plasmic-override={overrides.freeBox}
        className={classNames(projectcss.all, sty.freeBox)}
      >
        {(_par => (!_par ? [] : Array.isArray(_par) ? _par : [_par]))(
          (() => {
            try {
              return [2, 3, 4, 5, 6, 7];
            } catch (e) {
              if (
                e instanceof TypeError ||
                e?.plasmicType === "PlasmicUndefinedDataError"
              ) {
                return [];
              }
              throw e;
            }
          })()
        ).map((__plasmic_item_0, __plasmic_idx_0) => {
          const currentItem = __plasmic_item_0;
          const currentIndex = __plasmic_idx_0;
          return (
            <SearchTile
              data-plasmic-name={"searchTile"}
              data-plasmic-override={overrides.searchTile}
              className={classNames("__wab_instance", sty.searchTile)}
              key={currentIndex}
            />
          );
        })}
      </div>
    </div>
  ) as React.ReactElement | null;
}

const PlasmicDescendants = {
  root: ["root", "subcomponentSlotHeading", "freeBox", "searchTile"],
  subcomponentSlotHeading: ["subcomponentSlotHeading"],
  freeBox: ["freeBox", "searchTile"],
  searchTile: ["searchTile"]
} as const;
type NodeNameType = keyof typeof PlasmicDescendants;
type DescendantsType<T extends NodeNameType> =
  (typeof PlasmicDescendants)[T][number];
type NodeDefaultElementType = {
  root: "div";
  subcomponentSlotHeading: typeof SubcomponentSlotHeading;
  freeBox: "div";
  searchTile: typeof SearchTile;
};

type ReservedPropsType = "variants" | "args" | "overrides";
type NodeOverridesType<T extends NodeNameType> = Pick<
  PlasmicSearchDisplayResults__OverridesType,
  DescendantsType<T>
>;
type NodeComponentProps<T extends NodeNameType> =
  // Explicitly specify variants, args, and overrides as objects
  {
    variants?: PlasmicSearchDisplayResults__VariantsArgs;
    args?: PlasmicSearchDisplayResults__ArgsType;
    overrides?: NodeOverridesType<T>;
  } & Omit<PlasmicSearchDisplayResults__VariantsArgs, ReservedPropsType> & // Specify variants directly as props
    /* Specify args directly as props*/ Omit<
      PlasmicSearchDisplayResults__ArgsType,
      ReservedPropsType
    > &
    /* Specify overrides for each element directly as props*/ Omit<
      NodeOverridesType<T>,
      ReservedPropsType | VariantPropType | ArgPropType
    > &
    /* Specify props for the root element*/ Omit<
      Partial<React.ComponentProps<NodeDefaultElementType[T]>>,
      ReservedPropsType | VariantPropType | ArgPropType | DescendantsType<T>
    >;

function makeNodeComponent<NodeName extends NodeNameType>(nodeName: NodeName) {
  type PropsType = NodeComponentProps<NodeName> & { key?: React.Key };
  const func = function <T extends PropsType>(
    props: T & StrictProps<T, PropsType>
  ) {
    const { variants, args, overrides } = React.useMemo(
      () =>
        deriveRenderOpts(props, {
          name: nodeName,
          descendantNames: PlasmicDescendants[nodeName],
          internalArgPropNames: PlasmicSearchDisplayResults__ArgProps,
          internalVariantPropNames: PlasmicSearchDisplayResults__VariantProps
        }),
      [props, nodeName]
    );
    return PlasmicSearchDisplayResults__RenderFunc({
      variants,
      args,
      overrides,
      forNode: nodeName
    });
  };
  if (nodeName === "root") {
    func.displayName = "PlasmicSearchDisplayResults";
  } else {
    func.displayName = `PlasmicSearchDisplayResults.${nodeName}`;
  }
  return func;
}

export const PlasmicSearchDisplayResults = Object.assign(
  // Top-level PlasmicSearchDisplayResults renders the root element
  makeNodeComponent("root"),
  {
    // Helper components rendering sub-elements
    subcomponentSlotHeading: makeNodeComponent("subcomponentSlotHeading"),
    freeBox: makeNodeComponent("freeBox"),
    searchTile: makeNodeComponent("searchTile"),

    // Metadata about props expected for PlasmicSearchDisplayResults
    internalVariantProps: PlasmicSearchDisplayResults__VariantProps,
    internalArgProps: PlasmicSearchDisplayResults__ArgProps
  }
);

export default PlasmicSearchDisplayResults;
/* prettier-ignore-end */
