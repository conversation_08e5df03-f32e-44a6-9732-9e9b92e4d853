import type { Metadata } from 'next';
import type * as React from 'react';

import '@/styles/globals.css';

export const metadata: Metadata = {
  title: `${process.env.APP_NAME || 'Spherical CMS'}`,
  description: `${process.env.APP_NAME || 'Spherical CMS'}`,
};

export default function RootLayout({
  children,
  login,
}: {
  children: React.ReactNode;
  login: React.ReactNode;
}) {
  return (
    <>
      {login}
      {children}
    </>
  );
}
