.publicationsSection {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: flex-start;
  width: 100%;
  height: auto;
  max-width: 100%;
  position: relative;
  margin-bottom: 12px;
  justify-self: flex-start;
  min-width: 0;
  padding: var(--token-sazGmnf7GWAk);
  border-style: solid;
  border-color: #000000;
}
.profileSectionsProfileSectionHeading:global(.__wab_instance) {
  max-width: 100%;
  position: relative;
}
.pubDisplayContainer {
  display: flex;
  position: relative;
  flex-direction: column;
  align-items: flex-start;
  justify-content: flex-start;
  width: 100%;
  height: auto;
  max-width: 100%;
  min-width: 0;
  padding: var(--token-sazGmnf7GWAk);
}
.publicationTile:global(.__wab_instance) {
  max-width: 100%;
  position: relative;
}
