.myWorksSection {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start;
  width: 100%;
  height: auto;
  max-width: 100%;
  position: relative;
  justify-self: flex-start;
  margin-bottom: 12px;
  overflow: hidden;
  grid-column-start: 1 !important;
  grid-column-end: -1 !important;
  padding: 0px;
}
.myWorksHeader:global(.__wab_instance) {
  max-width: 100%;
  position: relative;
  width: 100%;
  margin-top: 0px;
  margin-bottom: 24px;
  min-width: 0;
}
.text {
  padding-right: 0px;
}
.displayWrapper {
  display: flex;
  position: relative;
  flex-direction: column;
  align-items: flex-start;
  justify-content: center;
  width: 100%;
  height: auto;
  max-width: 100%;
  overflow-x: auto;
  overflow-y: hidden;
  min-width: 0;
  padding: var(--token-sazGmnf7GWAk);
  margin: var(--token-sazGmnf7GWAk);
}
.overflowContainer {
  display: flex;
  flex-direction: row;
  padding: var(--token-j0qnbpah5w9U) 0px var(--token-j0qnbpah5w9U) 20px;
  margin: 0px 25px 0px 0px;
}
.overflowContainer > :global(.__wab_flex-container) {
  flex-wrap: nowrap;
  align-content: stretch;
  align-items: center;
  justify-content: center;
  flex-direction: row;
  margin-left: calc(0px - var(--token-j0qnbpah5w9U));
  width: calc(100% + var(--token-j0qnbpah5w9U));
  margin-top: calc(0px - var(--token-j0qnbpah5w9U));
  height: calc(100% + var(--token-j0qnbpah5w9U));
}
.overflowContainer > :global(.__wab_flex-container) > *,
.overflowContainer > :global(.__wab_flex-container) > :global(.__wab_slot) > *,
.overflowContainer > :global(.__wab_flex-container) > picture > img,
.overflowContainer
  > :global(.__wab_flex-container)
  > :global(.__wab_slot)
  > picture
  > img {
  margin-left: var(--token-j0qnbpah5w9U);
  margin-top: var(--token-j0qnbpah5w9U);
}
.profileTileCaseStudyTile:global(.__wab_instance) {
  max-width: 100%;
  position: relative;
  flex-shrink: 0;
}
