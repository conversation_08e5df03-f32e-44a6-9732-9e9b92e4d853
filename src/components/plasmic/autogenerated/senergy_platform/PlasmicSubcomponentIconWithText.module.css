.iconAndTextBondingBox {
  display: inline-flex;
  position: relative;
  width: auto;
  height: auto;
  justify-self: flex-start;
  justify-content: flex-start;
  align-items: center;
}
.iconAndTextBondingBoxbackground_stone {
  background: var(--token-K5FbAPSIIrXM);
}
.iconAndTextBondingBoxbackground_forest {
  background: var(--token-XQQdLIBrONt7);
}
.iconAndTextBondingBoxbackground_sage {
  background: var(--token-lUfRIntGAfk9);
}
.iconAndTextBondingBoxbackground_rusticBrick {
  background: var(--token-to1fT9g-t4Js);
}
.iconAndTextBondingBoxbackground_sunflower {
  background: var(--token-cXZCUAmMzuNV);
}
.iconAndTextBondingBoxrounded {
  border-radius: 32px;
  padding: var(--token-0GsCPVoopKtO) 8px;
}
.infoComp {
  display: flex;
  position: relative;
  flex-direction: row;
  align-items: center;
  justify-content: flex-start;
  width: auto;
  height: auto;
  flex-shrink: 0;
  margin-right: 0px;
  margin-top: 0px;
  padding: var(--token-sazGmnf7GWAk);
}
.infoCompeditable_editableText {
  justify-content: flex-start;
  align-items: flex-start;
}
.infoCompeditable_editableSelector {
  justify-content: flex-start;
  align-items: center;
}
.infoCompbackground_stone {
  justify-content: flex-start;
  align-items: center;
}
.iconSlot {
  display: flex;
  position: relative;
  width: var(--token-j0qnbpah5w9U);
  height: 100%;
  margin-right: 4px;
  justify-content: flex-start;
  align-items: flex-end;
  margin-bottom: 1px;
  margin-top: 1px;
  flex-shrink: 0;
  min-height: 0;
}
.iconSloteditable_editableText {
  margin-top: 13px;
  margin-bottom: 1px;
  justify-content: flex-start;
  align-items: flex-end;
}
.iconSloteditable_editableSelector {
  justify-content: flex-start;
  align-items: center;
}
.iconSlotbackground_stone {
  width: var(--token-j0qnbpah5w9U);
  height: var(--token-j0qnbpah5w9U);
  flex-shrink: 0;
}
.iconSlotrounded {
  width: var(--token-rB3BKCgczoWa);
  height: var(--token-rB3BKCgczoWa);
  flex-shrink: 0;
}
.iconSlotwithoutIcon {
  display: none;
}
.svg___32Cl6 {
  position: relative;
  object-fit: cover;
  max-width: 100%;
  margin-right: 0;
  width: 27px;
  height: 20px;
  margin-bottom: 0px;
  flex-shrink: 0;
}
.svgwithoutIcon___32Cl6Gyrb6 {
  display: none;
}
.text {
  width: 100%;
  height: auto;
  max-width: 100%;
  margin-left: 0px;
  margin-right: 0px;
  display: flex;
  flex-direction: row;
  min-width: 0;
}
.textbackground_stone {
  justify-content: flex-start;
  align-items: center;
}
.textInput:global(.__wab_instance) {
  max-width: 100%;
  position: relative;
  margin-bottom: -2px;
}
.textInputeditable_editableText:global(.__wab_instance) {
  margin-bottom: 0px;
}
.textInputeditable_editableSelector:global(.__wab_instance) {
  display: none;
}
