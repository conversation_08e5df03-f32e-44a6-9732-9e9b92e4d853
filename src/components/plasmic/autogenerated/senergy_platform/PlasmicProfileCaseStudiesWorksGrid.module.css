.caseStudiesFormatting {
  display: flex;
  flex-direction: row;
  position: relative;
  width: 100%;
  height: auto;
  min-width: 0;
  padding: var(--token-j0qnbpah5w9U) 0px;
}
.caseStudiesFormatting > :global(.__wab_flex-container) {
  flex-direction: row;
  justify-content: space-evenly;
  align-items: stretch;
  flex-wrap: wrap;
  align-content: center;
  min-width: 0;
  margin-left: calc(0px - var(--token-j0qnbpah5w9U));
  width: calc(100% + var(--token-j0qnbpah5w9U));
  margin-top: calc(0px - var(--token-M1l4keX1sfKm));
  height: calc(100% + var(--token-M1l4keX1sfKm));
}
.caseStudiesFormatting > :global(.__wab_flex-container) > *,
.caseStudiesFormatting
  > :global(.__wab_flex-container)
  > :global(.__wab_slot)
  > *,
.caseStudiesFormatting > :global(.__wab_flex-container) > picture > img,
.caseStudiesFormatting
  > :global(.__wab_flex-container)
  > :global(.__wab_slot)
  > picture
  > img {
  margin-left: var(--token-j0qnbpah5w9U);
  margin-top: var(--token-M1l4keX1sfKm);
}
.profileTileCaseStudyTile:global(.__wab_instance) {
  position: relative;
  flex-shrink: 0;
}
