.root {
  display: grid;
  position: relative;
  grid-row-gap: 50px;
  flex-direction: column;
  justify-items: stretch;
  width: 100%;
  min-width: 0;
  grid-template-columns:
    var(--plsmc-viewport-gap) 1fr minmax(0, var(--plsmc-wide-chunk))
    min(
      var(--plsmc-standard-width),
      calc(100% - var(--plsmc-viewport-gap) - var(--plsmc-viewport-gap))
    )
    minmax(0, var(--plsmc-wide-chunk)) 1fr var(--plsmc-viewport-gap);
}
.root > * {
  grid-column: 4;
}
.profileBanner:global(.__wab_instance) {
  width: 100%;
  grid-column-start: 3 !important;
  grid-column-end: -3 !important;
}
.content:global(.__wab_instance) {
  width: 100%;
  grid-column-start: 3 !important;
  grid-column-end: -3 !important;
}
