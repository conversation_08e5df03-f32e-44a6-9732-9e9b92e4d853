/* eslint-disable */
/* tslint:disable */
// @ts-nocheck
/* prettier-ignore-start */
import React from "react";
import { classNames } from "@plasmicapp/react-web";

export type WorldIconProps = React.ComponentProps<"svg"> & {
  title?: string;
};

export function WorldIcon(props: WorldIconProps) {
  const { className, style, title, ...restProps } = props;
  return (
    <svg
      xmlns={"http://www.w3.org/2000/svg"}
      className={classNames(
        "plasmic-default__svg",
        className,
        "icon icon-tabler icon-tabler-world"
      )}
      viewBox={"0 0 24 24"}
      strokeWidth={"1.5"}
      stroke={"currentColor"}
      fill={"none"}
      strokeLinecap={"round"}
      strokeLinejoin={"round"}
      height={"1em"}
      style={style}
      {...restProps}
    >
      {title && <title>{title}</title>}

      <path d={"M0 0h24v24H0z"} fill={"none"} stroke={"none"}></path>

      <path
        d={
          "M3 12a9 9 0 1018 0 9 9 0 00-18 0m.6-3h16.8M3.6 15h16.8M11.5 3a17 17 0 000 18m1-18a17 17 0 010 18"
        }
      ></path>
    </svg>
  );
}

export default WorldIcon;
/* prettier-ignore-end */
