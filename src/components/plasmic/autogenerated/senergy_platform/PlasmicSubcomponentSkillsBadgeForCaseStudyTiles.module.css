.root {
  display: inline-flex;
  flex-direction: column;
  position: relative;
  width: auto;
  height: auto;
  justify-content: center;
  align-items: flex-end;
  justify-self: flex-start;
  transition-property: all;
  transition-duration: 0.3s;
  -webkit-transition-property: all;
  -webkit-transition-duration: 0.3s;
}
.rootinteractions_pressed {
  transform: none;
}
.skillsTextStack {
  display: flex;
  position: relative;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start;
  width: 100%;
  height: auto;
  max-width: 100%;
  z-index: 3;
  min-width: 0;
  padding: var(--token-sazGmnf7GWAk);
  margin: var(--token-sazGmnf7GWAk);
}
.skillsTextStackinteractions_hoverVariant {
  transform: translateX(-2px) translateY(0px) translateZ(0px);
  align-items: center;
  justify-content: flex-start;
}
.skillsTextStackinteractions_pressed {
  transform: translateX(-2px) translateY(0px) translateZ(0px);
  align-items: center;
  justify-content: flex-start;
}
.text {
  width: auto;
  height: auto;
  max-width: 100%;
  font-family: var(--token-z1yrQVi72Nj1);
  font-weight: 700;
  padding-top: 0px;
  text-align: right;
  z-index: 3;
  color: var(--token-K5FbAPSIIrXM);
}
.textinteractions_hoverVariant {
  color: var(--token-K5FbAPSIIrXM);
  z-index: 3;
  transform: none;
}
.textinteractions_pressed {
  color: var(--token-K5FbAPSIIrXM);
  z-index: 3;
  transform: none;
}
.textcolor_white {
  color: var(--token-xo_r2w5pebq-);
}
.textcolor_green {
  color: var(--token-XQQdLIBrONt7);
}
.backgroundColor {
  display: flex;
  position: absolute;
  width: 100%;
  left: 0px;
  top: 4px;
  flex-direction: column;
  height: 16px;
  background: var(--token-1AMvw6c2eIK7);
  z-index: 1;
  min-width: 0;
  flex-shrink: 0;
  padding: var(--token-sazGmnf7GWAk);
}
.backgroundColorinteractions_hoverVariant {
  width: 98%;
  align-items: center;
  justify-content: flex-start;
}
.backgroundColorinteractions_pressed {
  width: 98%;
  align-items: center;
  justify-content: flex-start;
}
.backgroundColorcolor_white {
  background: var(--token-Jis9JaUzrgN0);
  display: none;
}
.backgroundColorcolor_green {
  display: none;
}
