.navBarButtonContainer {
  display: flex;
  flex-direction: row;
  position: relative;
  width: 100%;
  height: 36px;
  justify-content: flex-start;
  align-items: center;
  justify-self: flex-start;
  margin-top: 0px;
  margin-bottom: 0px;
  cursor: pointer;
  overflow: hidden;
  grid-column-start: 3 !important;
  grid-column-end: -3 !important;
  padding: 8px 8px 8px 18px;
  border-width: 1px;
  border-style: none;
}
.navBarButtonContainerhover {
  background: var(--token-Ab1KZcxm-kp_);
}
.navBarButtonContainermodals_legal {
  overflow: visible;
}
.freeBox {
  display: flex;
  position: relative;
  flex-direction: row;
  width: auto;
  height: auto;
  max-width: 100%;
}
.freeBox > :global(.__wab_flex-container) {
  flex-direction: row;
  align-items: center;
  justify-content: flex-start;
  margin-left: calc(0px - var(--token-4Wrp9mDZCSCQ));
  width: calc(100% + var(--token-4Wrp9mDZCSCQ));
}
.freeBox > :global(.__wab_flex-container) > *,
.freeBox > :global(.__wab_flex-container) > :global(.__wab_slot) > *,
.freeBox > :global(.__wab_flex-container) > picture > img,
.freeBox
  > :global(.__wab_flex-container)
  > :global(.__wab_slot)
  > picture
  > img {
  margin-left: var(--token-4Wrp9mDZCSCQ);
}
.iconSectionContainer {
  display: flex;
  width: var(--token-j0qnbpah5w9U);
  height: var(--token-j0qnbpah5w9U);
  z-index: 1;
  flex-shrink: 0;
}
.svg__rVb6N {
  position: relative;
  object-fit: cover;
  max-width: 100%;
  width: var(--token-j0qnbpah5w9U);
  height: var(--token-j0qnbpah5w9U);
  color: var(--token-yPq8Z3hhDPZH);
  min-width: 100%;
  min-height: 100%;
  max-height: 100%;
  flex-shrink: 0;
}
.pageTitle {
  position: relative;
  width: auto;
  height: auto;
  max-width: 100%;
  margin-right: 2px;
  display: flex;
  flex-direction: row;
}
.text {
  font-family: var(--token-z1yrQVi72Nj1);
  width: 100%;
  white-space: pre;
  text-overflow: clip;
  overflow: hidden;
  user-select: none;
  transition-property: color;
  transition-duration: 0.2s;
  min-width: 0;
  -webkit-transition-property: color;
  -webkit-transition-duration: 0.2s;
}
.textcollapsed {
  color: #29292900;
}
.textdisabled {
  color: var(--token-yPq8Z3hhDPZH);
}
.navigationPopupModals:global(.__wab_instance) {
  position: relative;
  left: auto;
  bottom: auto;
  align-self: flex-end;
  margin-left: 95px;
  flex-shrink: 0;
  display: none;
}
.navigationPopupModalsmodals_legal:global(.__wab_instance) {
  flex-shrink: 0;
  display: flex;
}
