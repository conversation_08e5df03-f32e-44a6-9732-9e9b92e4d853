/* eslint-disable */
/* tslint:disable */
// @ts-nocheck
/* prettier-ignore-start */

/** @jsxRuntime classic */
/** @jsx createPlasmicElementProxy */
/** @jsxFrag React.Fragment */

// This class is auto-generated by Plasmic; please do not edit!
// Plasmic Project: nZeWKcn21KijvC7eT8B3c7
// Component: vjtGF3glVSRj

"use client";

import * as React from "react";

import Head from "next/head";
import Link, { LinkProps } from "next/link";
import { useRouter } from "next/navigation";

import {
  Flex as Flex__,
  MultiChoiceArg,
  PlasmicDataSourceContextProvider as PlasmicDataSourceContextProvider__,
  PlasmicIcon as PlasmicIcon__,
  PlasmicImg as PlasmicImg__,
  PlasmicLink as PlasmicLink__,
  PlasmicPageGuard as PlasmicPageGuard__,
  SingleBooleanChoiceArg,
  SingleChoiceArg,
  Stack as Stack__,
  StrictProps,
  Trans as Trans__,
  classNames,
  createPlasmicElementProxy,
  deriveRenderOpts,
  ensureGlobalVariants,
  generateOnMutateForSpec,
  generateStateOnChangeProp,
  generateStateOnChangePropForCodeComponents,
  generateStateValueProp,
  get as $stateGet,
  hasVariant,
  initializeCodeComponentStates,
  initializePlasmicStates,
  makeFragment,
  omit,
  pick,
  renderPlasmicSlot,
  set as $stateSet,
  useCurrentUser,
  useDollarState,
  usePlasmicTranslator,
  useTrigger,
  wrapWithClassName
} from "@plasmicapp/react-web";
import {
  DataCtxReader as DataCtxReader__,
  useDataEnv,
  useGlobalActions
} from "@plasmicapp/host";

import "@plasmicapp/react-web/lib/plasmic.css";

import projectcss from "./plasmic.module.css"; // plasmic-import: nZeWKcn21KijvC7eT8B3c7/projectcss
import sty from "./PlasmicSubcomponentSkillsBadgeForCaseStudyTiles.module.css"; // plasmic-import: vjtGF3glVSRj/css

createPlasmicElementProxy;

export type PlasmicSubcomponentSkillsBadgeForCaseStudyTiles__VariantMembers = {
  interactions: "hoverVariant" | "pressed";
  color: "white" | "green";
};
export type PlasmicSubcomponentSkillsBadgeForCaseStudyTiles__VariantsArgs = {
  interactions?: SingleChoiceArg<"hoverVariant" | "pressed">;
  color?: SingleChoiceArg<"white" | "green">;
};
type VariantPropType =
  keyof PlasmicSubcomponentSkillsBadgeForCaseStudyTiles__VariantsArgs;
export const PlasmicSubcomponentSkillsBadgeForCaseStudyTiles__VariantProps =
  new Array<VariantPropType>("interactions", "color");

export type PlasmicSubcomponentSkillsBadgeForCaseStudyTiles__ArgsType = {};
type ArgPropType =
  keyof PlasmicSubcomponentSkillsBadgeForCaseStudyTiles__ArgsType;
export const PlasmicSubcomponentSkillsBadgeForCaseStudyTiles__ArgProps =
  new Array<ArgPropType>();

export type PlasmicSubcomponentSkillsBadgeForCaseStudyTiles__OverridesType = {
  root?: Flex__<"div">;
  skillsTextStack?: Flex__<"div">;
  text?: Flex__<"div">;
  backgroundColor?: Flex__<"section">;
};

export interface DefaultSubcomponentSkillsBadgeForCaseStudyTilesProps {
  interactions?: SingleChoiceArg<"hoverVariant" | "pressed">;
  color?: SingleChoiceArg<"white" | "green">;
  className?: string;
}

const $$ = {};

function useNextRouter() {
  try {
    return useRouter();
  } catch {}
  return undefined;
}

function PlasmicSubcomponentSkillsBadgeForCaseStudyTiles__RenderFunc(props: {
  variants: PlasmicSubcomponentSkillsBadgeForCaseStudyTiles__VariantsArgs;
  args: PlasmicSubcomponentSkillsBadgeForCaseStudyTiles__ArgsType;
  overrides: PlasmicSubcomponentSkillsBadgeForCaseStudyTiles__OverridesType;
  forNode?: string;
}) {
  const { variants, overrides, forNode } = props;

  const args = React.useMemo(
    () =>
      Object.assign(
        {},
        Object.fromEntries(
          Object.entries(props.args).filter(([_, v]) => v !== undefined)
        )
      ),
    [props.args]
  );

  const $props = {
    ...args,
    ...variants
  };

  const __nextRouter = useNextRouter();
  const $ctx = useDataEnv?.() || {};
  const refsRef = React.useRef({});
  const $refs = refsRef.current;

  let [$queries, setDollarQueries] = React.useState<
    Record<string, ReturnType<typeof usePlasmicDataOp>>
  >({});
  const stateSpecs: Parameters<typeof useDollarState>[0] = React.useMemo(
    () => [
      {
        path: "interactions",
        type: "private",
        variableType: "variant",
        initFunc: ({ $props, $state, $queries, $ctx }) => $props.interactions
      },
      {
        path: "color",
        type: "private",
        variableType: "variant",
        initFunc: ({ $props, $state, $queries, $ctx }) => $props.color
      }
    ],
    [$props, $ctx, $refs]
  );
  const $state = useDollarState(stateSpecs, {
    $props,
    $ctx,
    $queries: $queries,
    $refs
  });

  const new$Queries: Record<string, ReturnType<typeof usePlasmicDataOp>> = {};
  if (Object.keys(new$Queries).some(k => new$Queries[k] !== $queries[k])) {
    setDollarQueries(new$Queries);

    $queries = new$Queries;
  }

  return (
    <div
      data-plasmic-name={"root"}
      data-plasmic-override={overrides.root}
      data-plasmic-root={true}
      data-plasmic-for-node={forNode}
      className={classNames(
        projectcss.all,
        projectcss.root_reset,
        projectcss.plasmic_default_styles,
        projectcss.plasmic_mixins,
        projectcss.plasmic_tokens,
        sty.root,
        {
          [sty.rootinteractions_hoverVariant]: hasVariant(
            $state,
            "interactions",
            "hoverVariant"
          ),
          [sty.rootinteractions_pressed]: hasVariant(
            $state,
            "interactions",
            "pressed"
          )
        }
      )}
      onMouseDown={async event => {
        const $steps = {};

        $steps["updateInteractions"] = true
          ? (() => {
              const actionArgs = {
                vgroup: "interactions",
                operation: 0,
                value: "pressed"
              };
              return (({ vgroup, value }) => {
                if (typeof value === "string") {
                  value = [value];
                }

                $stateSet($state, vgroup, value);
                return value;
              })?.apply(null, [actionArgs]);
            })()
          : undefined;
        if (
          $steps["updateInteractions"] != null &&
          typeof $steps["updateInteractions"] === "object" &&
          typeof $steps["updateInteractions"].then === "function"
        ) {
          $steps["updateInteractions"] = await $steps["updateInteractions"];
        }
      }}
      onMouseEnter={async event => {
        const $steps = {};

        $steps["updateInteractions"] = true
          ? (() => {
              const actionArgs = {
                vgroup: "interactions",
                operation: 0,
                value: "hoverVariant"
              };
              return (({ vgroup, value }) => {
                if (typeof value === "string") {
                  value = [value];
                }

                $stateSet($state, vgroup, value);
                return value;
              })?.apply(null, [actionArgs]);
            })()
          : undefined;
        if (
          $steps["updateInteractions"] != null &&
          typeof $steps["updateInteractions"] === "object" &&
          typeof $steps["updateInteractions"].then === "function"
        ) {
          $steps["updateInteractions"] = await $steps["updateInteractions"];
        }
      }}
      onMouseLeave={async event => {
        const $steps = {};

        $steps["updateInteractions"] = true
          ? (() => {
              const actionArgs = {
                vgroup: "interactions",
                operation: 1,
                value: "hoverVariant"
              };
              return (({ vgroup, value }) => {
                if (typeof value === "string") {
                  value = [value];
                }

                $stateSet($state, vgroup, undefined);
                return undefined;
              })?.apply(null, [actionArgs]);
            })()
          : undefined;
        if (
          $steps["updateInteractions"] != null &&
          typeof $steps["updateInteractions"] === "object" &&
          typeof $steps["updateInteractions"].then === "function"
        ) {
          $steps["updateInteractions"] = await $steps["updateInteractions"];
        }
      }}
      onMouseUp={async event => {
        const $steps = {};

        $steps["updateInteractions"] = true
          ? (() => {
              const actionArgs = {
                vgroup: "interactions",
                operation: 0,
                value: "hoverVariant"
              };
              return (({ vgroup, value }) => {
                if (typeof value === "string") {
                  value = [value];
                }

                $stateSet($state, vgroup, value);
                return value;
              })?.apply(null, [actionArgs]);
            })()
          : undefined;
        if (
          $steps["updateInteractions"] != null &&
          typeof $steps["updateInteractions"] === "object" &&
          typeof $steps["updateInteractions"].then === "function"
        ) {
          $steps["updateInteractions"] = await $steps["updateInteractions"];
        }
      }}
    >
      <div
        data-plasmic-name={"skillsTextStack"}
        data-plasmic-override={overrides.skillsTextStack}
        className={classNames(projectcss.all, sty.skillsTextStack, {
          [sty.skillsTextStackinteractions_hoverVariant]: hasVariant(
            $state,
            "interactions",
            "hoverVariant"
          ),
          [sty.skillsTextStackinteractions_pressed]: hasVariant(
            $state,
            "interactions",
            "pressed"
          )
        })}
      >
        <div
          data-plasmic-name={"text"}
          data-plasmic-override={overrides.text}
          className={classNames(
            projectcss.all,
            projectcss.__wab_text,
            sty.text,
            {
              [sty.textcolor_green]: hasVariant($state, "color", "green"),
              [sty.textcolor_white]: hasVariant($state, "color", "white"),
              [sty.textinteractions_hoverVariant]: hasVariant(
                $state,
                "interactions",
                "hoverVariant"
              ),
              [sty.textinteractions_pressed]: hasVariant(
                $state,
                "interactions",
                "pressed"
              )
            }
          )}
        >
          {" Skill Name "}
        </div>
        <section
          data-plasmic-name={"backgroundColor"}
          data-plasmic-override={overrides.backgroundColor}
          className={classNames(projectcss.all, sty.backgroundColor, {
            [sty.backgroundColorcolor_green]: hasVariant(
              $state,
              "color",
              "green"
            ),
            [sty.backgroundColorcolor_white]: hasVariant(
              $state,
              "color",
              "white"
            ),
            [sty.backgroundColorinteractions_hoverVariant]: hasVariant(
              $state,
              "interactions",
              "hoverVariant"
            ),
            [sty.backgroundColorinteractions_pressed]: hasVariant(
              $state,
              "interactions",
              "pressed"
            )
          })}
        />
      </div>
    </div>
  ) as React.ReactElement | null;
}

const PlasmicDescendants = {
  root: ["root", "skillsTextStack", "text", "backgroundColor"],
  skillsTextStack: ["skillsTextStack", "text", "backgroundColor"],
  text: ["text"],
  backgroundColor: ["backgroundColor"]
} as const;
type NodeNameType = keyof typeof PlasmicDescendants;
type DescendantsType<T extends NodeNameType> =
  (typeof PlasmicDescendants)[T][number];
type NodeDefaultElementType = {
  root: "div";
  skillsTextStack: "div";
  text: "div";
  backgroundColor: "section";
};

type ReservedPropsType = "variants" | "args" | "overrides";
type NodeOverridesType<T extends NodeNameType> = Pick<
  PlasmicSubcomponentSkillsBadgeForCaseStudyTiles__OverridesType,
  DescendantsType<T>
>;
type NodeComponentProps<T extends NodeNameType> =
  // Explicitly specify variants, args, and overrides as objects
  {
    variants?: PlasmicSubcomponentSkillsBadgeForCaseStudyTiles__VariantsArgs;
    args?: PlasmicSubcomponentSkillsBadgeForCaseStudyTiles__ArgsType;
    overrides?: NodeOverridesType<T>;
  } & Omit< // Specify variants directly as props
    PlasmicSubcomponentSkillsBadgeForCaseStudyTiles__VariantsArgs,
    ReservedPropsType
  > &
    /* Specify args directly as props*/ Omit<
      PlasmicSubcomponentSkillsBadgeForCaseStudyTiles__ArgsType,
      ReservedPropsType
    > &
    /* Specify overrides for each element directly as props*/ Omit<
      NodeOverridesType<T>,
      ReservedPropsType | VariantPropType | ArgPropType
    > &
    /* Specify props for the root element*/ Omit<
      Partial<React.ComponentProps<NodeDefaultElementType[T]>>,
      ReservedPropsType | VariantPropType | ArgPropType | DescendantsType<T>
    >;

function makeNodeComponent<NodeName extends NodeNameType>(nodeName: NodeName) {
  type PropsType = NodeComponentProps<NodeName> & { key?: React.Key };
  const func = function <T extends PropsType>(
    props: T & StrictProps<T, PropsType>
  ) {
    const { variants, args, overrides } = React.useMemo(
      () =>
        deriveRenderOpts(props, {
          name: nodeName,
          descendantNames: PlasmicDescendants[nodeName],
          internalArgPropNames:
            PlasmicSubcomponentSkillsBadgeForCaseStudyTiles__ArgProps,
          internalVariantPropNames:
            PlasmicSubcomponentSkillsBadgeForCaseStudyTiles__VariantProps
        }),
      [props, nodeName]
    );
    return PlasmicSubcomponentSkillsBadgeForCaseStudyTiles__RenderFunc({
      variants,
      args,
      overrides,
      forNode: nodeName
    });
  };
  if (nodeName === "root") {
    func.displayName = "PlasmicSubcomponentSkillsBadgeForCaseStudyTiles";
  } else {
    func.displayName = `PlasmicSubcomponentSkillsBadgeForCaseStudyTiles.${nodeName}`;
  }
  return func;
}

export const PlasmicSubcomponentSkillsBadgeForCaseStudyTiles = Object.assign(
  // Top-level PlasmicSubcomponentSkillsBadgeForCaseStudyTiles renders the root element
  makeNodeComponent("root"),
  {
    // Helper components rendering sub-elements
    skillsTextStack: makeNodeComponent("skillsTextStack"),
    text: makeNodeComponent("text"),
    backgroundColor: makeNodeComponent("backgroundColor"),

    // Metadata about props expected for PlasmicSubcomponentSkillsBadgeForCaseStudyTiles
    internalVariantProps:
      PlasmicSubcomponentSkillsBadgeForCaseStudyTiles__VariantProps,
    internalArgProps: PlasmicSubcomponentSkillsBadgeForCaseStudyTiles__ArgProps
  }
);

export default PlasmicSubcomponentSkillsBadgeForCaseStudyTiles;
/* prettier-ignore-end */
