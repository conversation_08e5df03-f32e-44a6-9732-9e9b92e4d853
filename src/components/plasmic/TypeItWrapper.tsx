import TypeIt from 'typeit-react';
import React, { ReactNode } from 'react';

interface TypeItWrapperProps {
  className?: string;
  children: ReactNode;
  speed?: number;
  cursor?: boolean;
  loop?: boolean;
  waitUntilVisible?: boolean;
}

export function TypeItWrapper({
  className,
  children,
  speed = 50,
  cursor = true,
  loop = false,
  waitUntilVisible = true,
}: TypeItWrapperProps) {
  // Convert children to string, handling Plasmic's text nodes
  const content = React.Children.toArray(children)
    .map(child => {
      if (typeof child === 'string') return child;
      if (typeof child === 'number') return String(child);
      // @ts-ignore - Plasmic specific structure
      if (child?.props?.value) return child.props.value;
      return '';
    })
    .join('');

  return (
    <TypeIt
      className={className}
      options={{
        strings: [content],
        speed,
        cursor,
        loop,
        waitUntilVisible,
      }}
    />
  );
}

// Export both named and default exports
export { TypeItWrapper as TypeIt };
export default TypeItWrapper; 