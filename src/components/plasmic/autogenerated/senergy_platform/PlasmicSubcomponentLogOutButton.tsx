/* eslint-disable */
/* tslint:disable */
// @ts-nocheck
/* prettier-ignore-start */

/** @jsxRuntime classic */
/** @jsx createPlasmicElementProxy */
/** @jsxFrag React.Fragment */

// This class is auto-generated by Plasmic; please do not edit!
// Plasmic Project: nZeWKcn21KijvC7eT8B3c7
// Component: hyPjg5023lCQ

"use client";

import * as React from "react";

import Head from "next/head";
import Link, { LinkProps } from "next/link";
import { useRouter } from "next/navigation";

import {
  Flex as Flex__,
  MultiChoiceArg,
  PlasmicDataSourceContextProvider as PlasmicDataSourceContextProvider__,
  PlasmicIcon as PlasmicIcon__,
  PlasmicImg as PlasmicImg__,
  PlasmicLink as PlasmicLink__,
  PlasmicPageGuard as PlasmicPageGuard__,
  SingleBooleanChoiceArg,
  SingleChoiceArg,
  Stack as Stack__,
  StrictProps,
  Trans as Trans__,
  classNames,
  createPlasmicElementProxy,
  deriveRenderOpts,
  ensureGlobalVariants,
  generateOnMutateForSpec,
  generateStateOnChangeProp,
  generateStateOnChangePropForCodeComponents,
  generateStateValueProp,
  get as $stateGet,
  hasVariant,
  initializeCodeComponentStates,
  initializePlasmicStates,
  makeFragment,
  omit,
  pick,
  renderPlasmicSlot,
  set as $stateSet,
  useCurrentUser,
  useDollarState,
  usePlasmicTranslator,
  useTrigger,
  wrapWithClassName
} from "@plasmicapp/react-web";
import {
  DataCtxReader as DataCtxReader__,
  useDataEnv,
  useGlobalActions
} from "@plasmicapp/host";

import * as pp from "@plasmicapp/react-web";

import "@plasmicapp/react-web/lib/plasmic.css";

import projectcss from "./plasmic.module.css"; // plasmic-import: nZeWKcn21KijvC7eT8B3c7/projectcss
import sty from "./PlasmicSubcomponentLogOutButton.module.css"; // plasmic-import: hyPjg5023lCQ/css

createPlasmicElementProxy;

export type PlasmicSubcomponentLogOutButton__VariantMembers = {
  showStartIcon: "showStartIcon";
  showEndIcon: "showEndIcon";
  isDisabled: "isDisabled";
  shape: "rounded" | "sharp";
  size: "compact" | "minimal";
  styling: "square" | "cameo" | "typewriter" | "nittiWColor";
  justification: "left" | "right";
  selected: "selected";
  color: "eggplant" | "forest" | "sage" | "sunflower";
  disabledAnimation: "disabled" | "shakeLeft" | "shakeRight" | "disabledText";
};
export type PlasmicSubcomponentLogOutButton__VariantsArgs = {
  showStartIcon?: SingleBooleanChoiceArg<"showStartIcon">;
  showEndIcon?: SingleBooleanChoiceArg<"showEndIcon">;
  isDisabled?: SingleBooleanChoiceArg<"isDisabled">;
  shape?: SingleChoiceArg<"rounded" | "sharp">;
  size?: SingleChoiceArg<"compact" | "minimal">;
  styling?: MultiChoiceArg<"square" | "cameo" | "typewriter" | "nittiWColor">;
  justification?: SingleChoiceArg<"left" | "right">;
  selected?: SingleBooleanChoiceArg<"selected">;
  color?: SingleChoiceArg<"eggplant" | "forest" | "sage" | "sunflower">;
  disabledAnimation?: SingleChoiceArg<
    "disabled" | "shakeLeft" | "shakeRight" | "disabledText"
  >;
};
type VariantPropType = keyof PlasmicSubcomponentLogOutButton__VariantsArgs;
export const PlasmicSubcomponentLogOutButton__VariantProps =
  new Array<VariantPropType>(
    "showStartIcon",
    "showEndIcon",
    "isDisabled",
    "shape",
    "size",
    "styling",
    "justification",
    "selected",
    "color",
    "disabledAnimation"
  );

export type PlasmicSubcomponentLogOutButton__ArgsType = {
  children?: React.ReactNode;
  endIcon?: React.ReactNode;
  link?: string;
  submitsForm?: boolean;
  target?: boolean;
  startIcon?: React.ReactNode;
  disabledShakeText?: string;
};
type ArgPropType = keyof PlasmicSubcomponentLogOutButton__ArgsType;
export const PlasmicSubcomponentLogOutButton__ArgProps = new Array<ArgPropType>(
  "children",
  "endIcon",
  "link",
  "submitsForm",
  "target",
  "startIcon",
  "disabledShakeText"
);

export type PlasmicSubcomponentLogOutButton__OverridesType = {
  root?: Flex__<"button">;
  freeBox?: Flex__<"div">;
  startIconContainer?: Flex__<"div">;
  contentContainer?: Flex__<"div">;
  endIconContainer?: Flex__<"div">;
  section?: Flex__<"section">;
  text?: Flex__<"div">;
};

export interface DefaultSubcomponentLogOutButtonProps
  extends pp.BaseButtonProps {
  submitsForm?: boolean;
  target?: boolean;
  disabledShakeText?: string;
  shape?: SingleChoiceArg<"rounded" | "sharp">;
  size?: SingleChoiceArg<"compact" | "minimal">;
  styling?: MultiChoiceArg<"square" | "cameo" | "typewriter" | "nittiWColor">;
  justification?: SingleChoiceArg<"left" | "right">;
  selected?: SingleBooleanChoiceArg<"selected">;
  color?: SingleChoiceArg<"eggplant" | "forest" | "sage" | "sunflower">;
  disabledAnimation?: SingleChoiceArg<
    "disabled" | "shakeLeft" | "shakeRight" | "disabledText"
  >;
}

const $$ = {};

function useNextRouter() {
  try {
    return useRouter();
  } catch {}
  return undefined;
}

function PlasmicSubcomponentLogOutButton__RenderFunc(props: {
  variants: PlasmicSubcomponentLogOutButton__VariantsArgs;
  args: PlasmicSubcomponentLogOutButton__ArgsType;
  overrides: PlasmicSubcomponentLogOutButton__OverridesType;
  forNode?: string;
}) {
  const { variants, overrides, forNode } = props;

  const args = React.useMemo(
    () =>
      Object.assign(
        {
          disabledShakeText: "Disabled"
        },
        Object.fromEntries(
          Object.entries(props.args).filter(([_, v]) => v !== undefined)
        )
      ),
    [props.args]
  );

  const $props = {
    ...args,
    ...variants
  };

  const __nextRouter = useNextRouter();
  const $ctx = useDataEnv?.() || {};
  const refsRef = React.useRef({});
  const $refs = refsRef.current;

  let [$queries, setDollarQueries] = React.useState<
    Record<string, ReturnType<typeof usePlasmicDataOp>>
  >({});
  const stateSpecs: Parameters<typeof useDollarState>[0] = React.useMemo(
    () => [
      {
        path: "showStartIcon",
        type: "private",
        variableType: "variant",
        initFunc: ({ $props, $state, $queries, $ctx }) => $props.showStartIcon
      },
      {
        path: "showEndIcon",
        type: "private",
        variableType: "variant",
        initFunc: ({ $props, $state, $queries, $ctx }) => $props.showEndIcon
      },
      {
        path: "isDisabled",
        type: "private",
        variableType: "variant",
        initFunc: ({ $props, $state, $queries, $ctx }) => $props.isDisabled
      },
      {
        path: "shape",
        type: "private",
        variableType: "variant",
        initFunc: ({ $props, $state, $queries, $ctx }) => $props.shape
      },
      {
        path: "size",
        type: "private",
        variableType: "variant",
        initFunc: ({ $props, $state, $queries, $ctx }) => $props.size
      },
      {
        path: "styling",
        type: "private",
        variableType: "variant",
        initFunc: ({ $props, $state, $queries, $ctx }) => $props.styling
      },
      {
        path: "justification",
        type: "private",
        variableType: "variant",
        initFunc: ({ $props, $state, $queries, $ctx }) => $props.justification
      },
      {
        path: "selected",
        type: "private",
        variableType: "variant",
        initFunc: ({ $props, $state, $queries, $ctx }) => $props.selected
      },
      {
        path: "color",
        type: "private",
        variableType: "variant",
        initFunc: ({ $props, $state, $queries, $ctx }) => $props.color
      },
      {
        path: "disabledAnimation",
        type: "private",
        variableType: "variant",
        initFunc: ({ $props, $state, $queries, $ctx }) =>
          $props.disabledAnimation
      }
    ],
    [$props, $ctx, $refs]
  );
  const $state = useDollarState(stateSpecs, {
    $props,
    $ctx,
    $queries: $queries,
    $refs
  });

  const new$Queries: Record<string, ReturnType<typeof usePlasmicDataOp>> = {};
  if (Object.keys(new$Queries).some(k => new$Queries[k] !== $queries[k])) {
    setDollarQueries(new$Queries);

    $queries = new$Queries;
  }

  return (
    <Stack__
      as={"button"}
      data-plasmic-name={"root"}
      data-plasmic-override={overrides.root}
      data-plasmic-root={true}
      data-plasmic-for-node={forNode}
      hasGap={true}
      className={classNames(
        projectcss.all,
        projectcss.button,
        projectcss.root_reset,
        projectcss.plasmic_default_styles,
        projectcss.plasmic_mixins,
        projectcss.plasmic_tokens,
        sty.root,
        {
          [sty.rootcolor_eggplant]: hasVariant($state, "color", "eggplant"),
          [sty.rootcolor_forest]: hasVariant($state, "color", "forest"),
          [sty.rootcolor_sage]: hasVariant($state, "color", "sage"),
          [sty.rootcolor_sunflower]: hasVariant($state, "color", "sunflower"),
          [sty.rootdisabledAnimation_disabledText]: hasVariant(
            $state,
            "disabledAnimation",
            "disabledText"
          ),
          [sty.rootdisabledAnimation_disabled]: hasVariant(
            $state,
            "disabledAnimation",
            "disabled"
          ),
          [sty.rootdisabledAnimation_shakeLeft]: hasVariant(
            $state,
            "disabledAnimation",
            "shakeLeft"
          ),
          [sty.rootdisabledAnimation_shakeRight]: hasVariant(
            $state,
            "disabledAnimation",
            "shakeRight"
          ),
          [sty.rootisDisabled]: hasVariant($state, "isDisabled", "isDisabled"),
          [sty.rootjustification_left]: hasVariant(
            $state,
            "justification",
            "left"
          ),
          [sty.rootjustification_right]: hasVariant(
            $state,
            "justification",
            "right"
          ),
          [sty.rootselected]: hasVariant($state, "selected", "selected"),
          [sty.rootshape_rounded]: hasVariant($state, "shape", "rounded"),
          [sty.rootshape_rounded_showEndIcon]:
            hasVariant($state, "showEndIcon", "showEndIcon") &&
            hasVariant($state, "shape", "rounded"),
          [sty.rootshape_rounded_showStartIcon]:
            hasVariant($state, "shape", "rounded") &&
            hasVariant($state, "showStartIcon", "showStartIcon"),
          [sty.rootshape_rounded_size_compact]:
            hasVariant($state, "size", "compact") &&
            hasVariant($state, "shape", "rounded"),
          [sty.rootshape_sharp]: hasVariant($state, "shape", "sharp"),
          [sty.rootshowEndIcon]: hasVariant(
            $state,
            "showEndIcon",
            "showEndIcon"
          ),
          [sty.rootshowEndIcon_size_compact]:
            hasVariant($state, "size", "compact") &&
            hasVariant($state, "showEndIcon", "showEndIcon"),
          [sty.rootshowEndIcon_size_compact_showStartIcon]:
            hasVariant($state, "size", "compact") &&
            hasVariant($state, "showStartIcon", "showStartIcon") &&
            hasVariant($state, "showEndIcon", "showEndIcon"),
          [sty.rootshowStartIcon]: hasVariant(
            $state,
            "showStartIcon",
            "showStartIcon"
          ),
          [sty.rootsize_compact]: hasVariant($state, "size", "compact"),
          [sty.rootsize_compact_showStartIcon]:
            hasVariant($state, "size", "compact") &&
            hasVariant($state, "showStartIcon", "showStartIcon"),
          [sty.rootsize_minimal]: hasVariant($state, "size", "minimal"),
          [sty.rootstyling_cameo]: hasVariant($state, "styling", "cameo"),
          [sty.rootstyling_nittiWColor]: hasVariant(
            $state,
            "styling",
            "nittiWColor"
          ),
          [sty.rootstyling_square]: hasVariant($state, "styling", "square"),
          [sty.rootstyling_typewriter]: hasVariant(
            $state,
            "styling",
            "typewriter"
          )
        }
      )}
      onClickCapture={async event => {
        const $steps = {};

        $steps["runCode"] =
          $state.isDisabled == true || $state.disabledAnimation != undefined
            ? (() => {
                const actionArgs = {
                  customFunction: async () => {
                    return setTimeout(() => {
                      $state.disabledAnimation = "shakeRight";
                      setTimeout(() => {
                        $state.disabledAnimation = "shakeLeft";
                        setTimeout(() => {
                          $state.disabledAnimation = "shakeRight";
                          setTimeout(() => {
                            $state.disabledAnimation = "shakeLeft";
                            setTimeout(() => {
                              $state.disabledAnimation = "disabledText";
                              setTimeout(() => {
                                $state.disabledAnimation = "disabled";
                              }, 500);
                            }, 65);
                          }, 65);
                        }, 60);
                      }, 60);
                    }, 60);
                  }
                };
                return (({ customFunction }) => {
                  return customFunction();
                })?.apply(null, [actionArgs]);
              })()
            : undefined;
        if (
          $steps["runCode"] != null &&
          typeof $steps["runCode"] === "object" &&
          typeof $steps["runCode"].then === "function"
        ) {
          $steps["runCode"] = await $steps["runCode"];
        }
      }}
    >
      <Stack__
        as={"div"}
        data-plasmic-name={"freeBox"}
        data-plasmic-override={overrides.freeBox}
        hasGap={true}
        className={classNames(projectcss.all, sty.freeBox, {
          [sty.freeBoxdisabledAnimation_disabledText]: hasVariant(
            $state,
            "disabledAnimation",
            "disabledText"
          ),
          [sty.freeBoxdisabledAnimation_disabled]: hasVariant(
            $state,
            "disabledAnimation",
            "disabled"
          ),
          [sty.freeBoxdisabledAnimation_shakeRight]: hasVariant(
            $state,
            "disabledAnimation",
            "shakeRight"
          ),
          [sty.freeBoxselected]: hasVariant($state, "selected", "selected"),
          [sty.freeBoxshowStartIcon]: hasVariant(
            $state,
            "showStartIcon",
            "showStartIcon"
          )
        })}
      >
        {(
          hasVariant($state, "showEndIcon", "showEndIcon")
            ? true
            : hasVariant($state, "showStartIcon", "showStartIcon")
            ? true
            : false
        ) ? (
          <div
            data-plasmic-name={"startIconContainer"}
            data-plasmic-override={overrides.startIconContainer}
            className={classNames(projectcss.all, sty.startIconContainer, {
              [sty.startIconContainerselected]: hasVariant(
                $state,
                "selected",
                "selected"
              ),
              [sty.startIconContainershowEndIcon]: hasVariant(
                $state,
                "showEndIcon",
                "showEndIcon"
              ),
              [sty.startIconContainershowStartIcon]: hasVariant(
                $state,
                "showStartIcon",
                "showStartIcon"
              ),
              [sty.startIconContainersize_minimal]: hasVariant(
                $state,
                "size",
                "minimal"
              )
            })}
          >
            {renderPlasmicSlot({
              defaultContents: (
                <svg
                  className={classNames(projectcss.all, sty.svg___73K3G, {
                    [sty.svgshowEndIcon___73K3GSqDuB]: hasVariant(
                      $state,
                      "showEndIcon",
                      "showEndIcon"
                    )
                  })}
                  role={"img"}
                />
              ),

              value: args.startIcon,
              className: classNames(sty.slotTargetStartIcon, {
                [sty.slotTargetStartIconshowEndIcon]: hasVariant(
                  $state,
                  "showEndIcon",
                  "showEndIcon"
                ),
                [sty.slotTargetStartIconshowStartIcon]: hasVariant(
                  $state,
                  "showStartIcon",
                  "showStartIcon"
                ),
                [sty.slotTargetStartIconsize_minimal]: hasVariant(
                  $state,
                  "size",
                  "minimal"
                )
              })
            })}
          </div>
        ) : null}
        <div
          data-plasmic-name={"contentContainer"}
          data-plasmic-override={overrides.contentContainer}
          className={classNames(projectcss.all, sty.contentContainer, {
            [sty.contentContainerdisabledAnimation_disabledText]: hasVariant(
              $state,
              "disabledAnimation",
              "disabledText"
            ),
            [sty.contentContainerdisabledAnimation_disabled]: hasVariant(
              $state,
              "disabledAnimation",
              "disabled"
            ),
            [sty.contentContainerdisabledAnimation_shakeLeft]: hasVariant(
              $state,
              "disabledAnimation",
              "shakeLeft"
            ),
            [sty.contentContainerdisabledAnimation_shakeRight]: hasVariant(
              $state,
              "disabledAnimation",
              "shakeRight"
            ),
            [sty.contentContainerisDisabled]: hasVariant(
              $state,
              "isDisabled",
              "isDisabled"
            ),
            [sty.contentContainerselected]: hasVariant(
              $state,
              "selected",
              "selected"
            ),
            [sty.contentContainershape_rounded]: hasVariant(
              $state,
              "shape",
              "rounded"
            ),
            [sty.contentContainershowEndIcon]: hasVariant(
              $state,
              "showEndIcon",
              "showEndIcon"
            )
          })}
        >
          {renderPlasmicSlot({
            defaultContents: "Log Out",
            value: args.children,
            className: classNames(sty.slotTargetChildren, {
              [sty.slotTargetChildrendisabledAnimation_disabledText]:
                hasVariant($state, "disabledAnimation", "disabledText"),
              [sty.slotTargetChildrendisabledAnimation_disabled]: hasVariant(
                $state,
                "disabledAnimation",
                "disabled"
              ),
              [sty.slotTargetChildrendisabledAnimation_shakeLeft]: hasVariant(
                $state,
                "disabledAnimation",
                "shakeLeft"
              ),
              [sty.slotTargetChildrendisabledAnimation_shakeRight]: hasVariant(
                $state,
                "disabledAnimation",
                "shakeRight"
              ),
              [sty.slotTargetChildrenisDisabled]: hasVariant(
                $state,
                "isDisabled",
                "isDisabled"
              ),
              [sty.slotTargetChildrenselected]: hasVariant(
                $state,
                "selected",
                "selected"
              ),
              [sty.slotTargetChildrenselected_styling_nittiWColor]:
                hasVariant($state, "styling", "nittiWColor") &&
                hasVariant($state, "selected", "selected"),
              [sty.slotTargetChildrenshape_rounded]: hasVariant(
                $state,
                "shape",
                "rounded"
              ),
              [sty.slotTargetChildrenshowEndIcon]: hasVariant(
                $state,
                "showEndIcon",
                "showEndIcon"
              ),
              [sty.slotTargetChildrenshowStartIcon]: hasVariant(
                $state,
                "showStartIcon",
                "showStartIcon"
              ),
              [sty.slotTargetChildrensize_minimal]: hasVariant(
                $state,
                "size",
                "minimal"
              ),
              [sty.slotTargetChildrenstyling_cameo]: hasVariant(
                $state,
                "styling",
                "cameo"
              ),
              [sty.slotTargetChildrenstyling_nittiWColor]: hasVariant(
                $state,
                "styling",
                "nittiWColor"
              ),
              [sty.slotTargetChildrenstyling_typewriter]: hasVariant(
                $state,
                "styling",
                "typewriter"
              )
            })
          })}
        </div>
        {(hasVariant($state, "showEndIcon", "showEndIcon") ? true : false) ? (
          <div
            data-plasmic-name={"endIconContainer"}
            data-plasmic-override={overrides.endIconContainer}
            className={classNames(projectcss.all, sty.endIconContainer, {
              [sty.endIconContainerselected]: hasVariant(
                $state,
                "selected",
                "selected"
              ),
              [sty.endIconContainershowEndIcon]: hasVariant(
                $state,
                "showEndIcon",
                "showEndIcon"
              )
            })}
          >
            {renderPlasmicSlot({
              defaultContents: (
                <svg
                  className={classNames(projectcss.all, sty.svg__rQOv)}
                  role={"img"}
                />
              ),

              value: args.endIcon,
              className: classNames(sty.slotTargetEndIcon, {
                [sty.slotTargetEndIconshowEndIcon]: hasVariant(
                  $state,
                  "showEndIcon",
                  "showEndIcon"
                )
              })
            })}
          </div>
        ) : null}
      </Stack__>
      <section
        data-plasmic-name={"section"}
        data-plasmic-override={overrides.section}
        className={classNames(projectcss.all, sty.section, {
          [sty.sectiondisabledAnimation_shakeLeft]: hasVariant(
            $state,
            "disabledAnimation",
            "shakeLeft"
          ),
          [sty.sectionselected]: hasVariant($state, "selected", "selected"),
          [sty.sectionstyling_typewriter_selected]:
            hasVariant($state, "styling", "typewriter") &&
            hasVariant($state, "selected", "selected")
        })}
      />

      <div
        data-plasmic-name={"text"}
        data-plasmic-override={overrides.text}
        className={classNames(projectcss.all, projectcss.__wab_text, sty.text, {
          [sty.textdisabledAnimation_disabledText]: hasVariant(
            $state,
            "disabledAnimation",
            "disabledText"
          ),
          [sty.textdisabledAnimation_disabled]: hasVariant(
            $state,
            "disabledAnimation",
            "disabled"
          ),
          [sty.textdisabledAnimation_shakeLeft]: hasVariant(
            $state,
            "disabledAnimation",
            "shakeLeft"
          ),
          [sty.textdisabledAnimation_shakeRight]: hasVariant(
            $state,
            "disabledAnimation",
            "shakeRight"
          )
        })}
      >
        <React.Fragment>{$props.disabledShakeText}</React.Fragment>
      </div>
    </Stack__>
  ) as React.ReactElement | null;
}

function useBehavior<P extends pp.PlumeButtonProps>(
  props: P,
  ref: pp.ButtonRef
) {
  const b = pp.useButton<P, typeof PlasmicSubcomponentLogOutButton>(
    PlasmicSubcomponentLogOutButton,
    props,
    {
      showStartIconVariant: {
        group: "showStartIcon",
        variant: "showStartIcon"
      },
      showEndIconVariant: { group: "showEndIcon", variant: "showEndIcon" },
      isDisabledVariant: { group: "isDisabled", variant: "isDisabled" },
      contentSlot: "children",
      startIconSlot: "startIcon",
      endIconSlot: "endIcon",
      root: "root"
    },
    ref
  );
  if (b.plasmicProps.overrides.root.as === "a") {
    b.plasmicProps.overrides.root.as = PlasmicLink__;
    b.plasmicProps.overrides.root.props.component = Link;
    b.plasmicProps.overrides.root.props.platform = "nextjs";
  }
  return b;
}

const PlasmicDescendants = {
  root: [
    "root",
    "freeBox",
    "startIconContainer",
    "contentContainer",
    "endIconContainer",
    "section",
    "text"
  ],
  freeBox: [
    "freeBox",
    "startIconContainer",
    "contentContainer",
    "endIconContainer"
  ],
  startIconContainer: ["startIconContainer"],
  contentContainer: ["contentContainer"],
  endIconContainer: ["endIconContainer"],
  section: ["section"],
  text: ["text"]
} as const;
type NodeNameType = keyof typeof PlasmicDescendants;
type DescendantsType<T extends NodeNameType> =
  (typeof PlasmicDescendants)[T][number];
type NodeDefaultElementType = {
  root: "button";
  freeBox: "div";
  startIconContainer: "div";
  contentContainer: "div";
  endIconContainer: "div";
  section: "section";
  text: "div";
};

type ReservedPropsType = "variants" | "args" | "overrides";
type NodeOverridesType<T extends NodeNameType> = Pick<
  PlasmicSubcomponentLogOutButton__OverridesType,
  DescendantsType<T>
>;
type NodeComponentProps<T extends NodeNameType> =
  // Explicitly specify variants, args, and overrides as objects
  {
    variants?: PlasmicSubcomponentLogOutButton__VariantsArgs;
    args?: PlasmicSubcomponentLogOutButton__ArgsType;
    overrides?: NodeOverridesType<T>;
  } & Omit<PlasmicSubcomponentLogOutButton__VariantsArgs, ReservedPropsType> & // Specify variants directly as props
    /* Specify args directly as props*/ Omit<
      PlasmicSubcomponentLogOutButton__ArgsType,
      ReservedPropsType
    > &
    /* Specify overrides for each element directly as props*/ Omit<
      NodeOverridesType<T>,
      ReservedPropsType | VariantPropType | ArgPropType
    > &
    /* Specify props for the root element*/ Omit<
      Partial<React.ComponentProps<NodeDefaultElementType[T]>>,
      ReservedPropsType | VariantPropType | ArgPropType | DescendantsType<T>
    >;

function makeNodeComponent<NodeName extends NodeNameType>(nodeName: NodeName) {
  type PropsType = NodeComponentProps<NodeName> & { key?: React.Key };
  const func = function <T extends PropsType>(
    props: T & StrictProps<T, PropsType>
  ) {
    const { variants, args, overrides } = React.useMemo(
      () =>
        deriveRenderOpts(props, {
          name: nodeName,
          descendantNames: PlasmicDescendants[nodeName],
          internalArgPropNames: PlasmicSubcomponentLogOutButton__ArgProps,
          internalVariantPropNames:
            PlasmicSubcomponentLogOutButton__VariantProps
        }),
      [props, nodeName]
    );
    return PlasmicSubcomponentLogOutButton__RenderFunc({
      variants,
      args,
      overrides,
      forNode: nodeName
    });
  };
  if (nodeName === "root") {
    func.displayName = "PlasmicSubcomponentLogOutButton";
  } else {
    func.displayName = `PlasmicSubcomponentLogOutButton.${nodeName}`;
  }
  return func;
}

export const PlasmicSubcomponentLogOutButton = Object.assign(
  // Top-level PlasmicSubcomponentLogOutButton renders the root element
  makeNodeComponent("root"),
  {
    // Helper components rendering sub-elements
    freeBox: makeNodeComponent("freeBox"),
    startIconContainer: makeNodeComponent("startIconContainer"),
    contentContainer: makeNodeComponent("contentContainer"),
    endIconContainer: makeNodeComponent("endIconContainer"),
    section: makeNodeComponent("section"),
    text: makeNodeComponent("text"),

    // Metadata about props expected for PlasmicSubcomponentLogOutButton
    internalVariantProps: PlasmicSubcomponentLogOutButton__VariantProps,
    internalArgProps: PlasmicSubcomponentLogOutButton__ArgProps,

    useBehavior
  }
);

export default PlasmicSubcomponentLogOutButton;
/* prettier-ignore-end */
