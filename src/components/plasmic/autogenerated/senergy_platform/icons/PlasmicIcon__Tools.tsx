/* eslint-disable */
/* tslint:disable */
// @ts-nocheck
/* prettier-ignore-start */
import React from "react";
import { classNames } from "@plasmicapp/react-web";

export type ToolsIconProps = React.ComponentProps<"svg"> & {
  title?: string;
};

export function ToolsIcon(props: ToolsIconProps) {
  const { className, style, title, ...restProps } = props;
  return (
    <svg
      xmlns={"http://www.w3.org/2000/svg"}
      className={classNames(
        "plasmic-default__svg",
        className,
        "icon icon-tabler icon-tabler-tools"
      )}
      viewBox={"0 0 24 24"}
      strokeWidth={"1.5"}
      stroke={"currentColor"}
      fill={"none"}
      strokeLinecap={"round"}
      strokeLinejoin={"round"}
      height={"1em"}
      style={style}
      {...restProps}
    >
      {title && <title>{title}</title>}

      <path d={"M0 0h24v24H0z"} fill={"none"} stroke={"none"}></path>

      <path d={"M3 21h4L20 8a1.5 1.5 0 00-4-4L3 17v4M14.5 5.5l4 4"}></path>

      <path
        d={"M12 8L7 3 3 7l5 5M7 8L5.5 9.5M16 12l5 5-4 4-5-5m4 1l-1.5 1.5"}
      ></path>
    </svg>
  );
}

export default ToolsIcon;
/* prettier-ignore-end */
