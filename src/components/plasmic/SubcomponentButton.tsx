import * as React from "react";
import {
  DefaultSubcomponentButtonProps,
  PlasmicSubcomponentButton
} from "./autogenerated/senergy_platform/PlasmicSubcomponentButton";

import {
  ButtonRef,
  HtmlAnchorOnlyProps,
  HtmlButtonOnlyProps
} from "@plasmicapp/react-web";

export interface SubcomponentButtonProps
  extends DefaultSubcomponentButtonProps {
  // Feel free to add any additional props that this component should receive
}
function SubcomponentButton_(props: SubcomponentButtonProps, ref: ButtonRef) {
  const { plasmicProps } =
    PlasmicSubcomponentButton.useBehavior<SubcomponentButtonProps>(props, ref);
  return <PlasmicSubcomponentButton {...plasmicProps} />;
}

export type ButtonComponentType = {
  (
    props: Omit<SubcomponentButtonProps, HtmlAnchorOnlyProps> & {
      ref?: React.Ref<HTMLButtonElement>;
    }
  ): React.ReactElement;
  (
    props: Omit<SubcomponentButtonProps, HtmlButtonOnlyProps> & {
      ref?: React.Ref<HTMLAnchorElement>;
    }
  ): React.ReactElement;
};
const SubcomponentButton = React.forwardRef(
  SubcomponentButton_
) as any as ButtonComponentType;

export default Object.assign(SubcomponentButton, { __plumeType: "button" });
