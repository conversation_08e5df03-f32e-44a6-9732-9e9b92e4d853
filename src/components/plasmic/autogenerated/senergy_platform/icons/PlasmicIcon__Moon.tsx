/* eslint-disable */
/* tslint:disable */
// @ts-nocheck
/* prettier-ignore-start */
import React from "react";
import { classNames } from "@plasmicapp/react-web";

export type MoonIconProps = React.ComponentProps<"svg"> & {
  title?: string;
};

export function MoonIcon(props: MoonIconProps) {
  const { className, style, title, ...restProps } = props;
  return (
    <svg
      xmlns={"http://www.w3.org/2000/svg"}
      className={classNames(
        "plasmic-default__svg",
        className,
        "icon icon-tabler icon-tabler-moon-stars"
      )}
      viewBox={"0 0 24 24"}
      strokeWidth={"1.5"}
      stroke={"currentColor"}
      fill={"none"}
      strokeLinecap={"round"}
      strokeLinejoin={"round"}
      height={"1em"}
      style={style}
      {...restProps}
    >
      {title && <title>{title}</title>}

      <path d={"M0 0h24v24H0z"} fill={"none"} stroke={"none"}></path>

      <path
        d={
          "M12 3h.393a7.5 7.5 0 007.92 12.446A9 9 0 1112 2.992zm5 1a2 2 0 002 2 2 2 0 00-2 2 2 2 0 00-2-2 2 2 0 002-2m2 7h2m-1-1v2"
        }
      ></path>
    </svg>
  );
}

export default MoonIcon;
/* prettier-ignore-end */
