/* eslint-disable */
/* tslint:disable */
// @ts-nocheck
/* prettier-ignore-start */

/** @jsxRuntime classic */
/** @jsx createPlasmicElementProxy */
/** @jsxFrag React.Fragment */

// This class is auto-generated by Plasmic; please do not edit!
// Plasmic Project: nZeWKcn21KijvC7eT8B3c7
// Component: oWulBe4jXB2e

"use client";

import * as React from "react";

import Head from "next/head";
import Link, { LinkProps } from "next/link";
import { useRouter } from "next/navigation";

import {
  Flex as Flex__,
  MultiChoiceArg,
  PlasmicDataSourceContextProvider as PlasmicDataSourceContextProvider__,
  PlasmicIcon as PlasmicIcon__,
  PlasmicImg as PlasmicImg__,
  PlasmicLink as PlasmicLink__,
  PlasmicPageGuard as PlasmicPageGuard__,
  SingleBooleanChoiceArg,
  SingleChoiceArg,
  Stack as Stack__,
  StrictProps,
  Trans as Trans__,
  classNames,
  createPlasmicElementProxy,
  deriveRenderOpts,
  ensureGlobalVariants,
  generateOnMutateForSpec,
  generateStateOnChangeProp,
  generateStateOnChangePropForCodeComponents,
  generateStateValueProp,
  get as $stateGet,
  hasVariant,
  initializeCodeComponentStates,
  initializePlasmicStates,
  makeFragment,
  omit,
  pick,
  renderPlasmicSlot,
  set as $stateSet,
  useCurrentUser,
  useDollarState,
  usePlasmicTranslator,
  useTrigger,
  wrapWithClassName
} from "@plasmicapp/react-web";
import {
  DataCtxReader as DataCtxReader__,
  useDataEnv,
  useGlobalActions
} from "@plasmicapp/host";

import SubcomponentTextInput from "../../SubcomponentTextInput"; // plasmic-import: Pt5FPuEzirSe/component
import SubcomponentCheckbox from "../../SubcomponentCheckbox"; // plasmic-import: OeFTSKgMult8/component
import SubcomponentUploadButton from "../../SubcomponentUploadButton"; // plasmic-import: q4dIf6ELFdYO/component

import "@plasmicapp/react-web/lib/plasmic.css";

import projectcss from "./plasmic.module.css"; // plasmic-import: nZeWKcn21KijvC7eT8B3c7/projectcss
import sty from "./PlasmicFeedbackFeedbackModal.module.css"; // plasmic-import: oWulBe4jXB2e/css

createPlasmicElementProxy;

export type PlasmicFeedbackFeedbackModal__VariantMembers = {};
export type PlasmicFeedbackFeedbackModal__VariantsArgs = {};
type VariantPropType = keyof PlasmicFeedbackFeedbackModal__VariantsArgs;
export const PlasmicFeedbackFeedbackModal__VariantProps =
  new Array<VariantPropType>();

export type PlasmicFeedbackFeedbackModal__ArgsType = {};
type ArgPropType = keyof PlasmicFeedbackFeedbackModal__ArgsType;
export const PlasmicFeedbackFeedbackModal__ArgProps = new Array<ArgPropType>();

export type PlasmicFeedbackFeedbackModal__OverridesType = {
  root?: Flex__<"div">;
  text?: Flex__<"div">;
  subTextInput?: Flex__<typeof SubcomponentTextInput>;
  subCheckbox?: Flex__<typeof SubcomponentCheckbox>;
  subTextInput2?: Flex__<typeof SubcomponentTextInput>;
  subTextInput4?: Flex__<typeof SubcomponentTextInput>;
  subcomponentUploadButton?: Flex__<typeof SubcomponentUploadButton>;
};

export interface DefaultFeedbackFeedbackModalProps {
  className?: string;
}

const $$ = {};

function useNextRouter() {
  try {
    return useRouter();
  } catch {}
  return undefined;
}

function PlasmicFeedbackFeedbackModal__RenderFunc(props: {
  variants: PlasmicFeedbackFeedbackModal__VariantsArgs;
  args: PlasmicFeedbackFeedbackModal__ArgsType;
  overrides: PlasmicFeedbackFeedbackModal__OverridesType;
  forNode?: string;
}) {
  const { variants, overrides, forNode } = props;

  const args = React.useMemo(
    () =>
      Object.assign(
        {},
        Object.fromEntries(
          Object.entries(props.args).filter(([_, v]) => v !== undefined)
        )
      ),
    [props.args]
  );

  const $props = {
    ...args,
    ...variants
  };

  const __nextRouter = useNextRouter();
  const $ctx = useDataEnv?.() || {};
  const refsRef = React.useRef({});
  const $refs = refsRef.current;

  let [$queries, setDollarQueries] = React.useState<
    Record<string, ReturnType<typeof usePlasmicDataOp>>
  >({});
  const stateSpecs: Parameters<typeof useDollarState>[0] = React.useMemo(
    () => [
      {
        path: "subTextInput.value",
        type: "private",
        variableType: "text",
        initFunc: ({ $props, $state, $queries, $ctx }) => "Name"
      },
      {
        path: "subTextInput.errorMessage",
        type: "private",
        variableType: "text",
        initFunc: ({ $props, $state, $queries, $ctx }) => ""
      },
      {
        path: "subTextInput2.value",
        type: "private",
        variableType: "text",
        initFunc: ({ $props, $state, $queries, $ctx }) => "Contact Email"
      },
      {
        path: "subTextInput2.errorMessage",
        type: "private",
        variableType: "text",
        initFunc: ({ $props, $state, $queries, $ctx }) => ""
      },
      {
        path: "subTextInput4.value",
        type: "private",
        variableType: "text",
        initFunc: ({ $props, $state, $queries, $ctx }) =>
          "Please describe the issue in as much detail as possible.  "
      },
      {
        path: "subTextInput4.errorMessage",
        type: "private",
        variableType: "text",
        initFunc: ({ $props, $state, $queries, $ctx }) => ""
      },
      {
        path: "subCheckbox.isChecked",
        type: "private",
        variableType: "boolean",
        initFunc: ({ $props, $state, $queries, $ctx }) => undefined
      }
    ],
    [$props, $ctx, $refs]
  );
  const $state = useDollarState(stateSpecs, {
    $props,
    $ctx,
    $queries: $queries,
    $refs
  });

  const new$Queries: Record<string, ReturnType<typeof usePlasmicDataOp>> = {};
  if (Object.keys(new$Queries).some(k => new$Queries[k] !== $queries[k])) {
    setDollarQueries(new$Queries);

    $queries = new$Queries;
  }

  return (
    <div
      data-plasmic-name={"root"}
      data-plasmic-override={overrides.root}
      data-plasmic-root={true}
      data-plasmic-for-node={forNode}
      className={classNames(
        projectcss.all,
        projectcss.root_reset,
        projectcss.plasmic_default_styles,
        projectcss.plasmic_mixins,
        projectcss.plasmic_tokens,
        sty.root
      )}
    >
      <div
        data-plasmic-name={"text"}
        data-plasmic-override={overrides.text}
        className={classNames(projectcss.all, projectcss.__wab_text, sty.text)}
      >
        {"Feedback Form"}
      </div>
      <SubcomponentTextInput
        data-plasmic-name={"subTextInput"}
        data-plasmic-override={overrides.subTextInput}
        className={classNames("__wab_instance", sty.subTextInput)}
        errorMessage={generateStateValueProp($state, [
          "subTextInput",
          "errorMessage"
        ])}
        fieldNameRemainVisible={true}
        inputAutoComplete={"name"}
        inputPlaceholder={"Name"}
        inputValue={generateStateValueProp($state, ["subTextInput", "value"])}
        onErrorMessageChange={async (...eventArgs: any) => {
          generateStateOnChangeProp($state, [
            "subTextInput",
            "errorMessage"
          ]).apply(null, eventArgs);

          if (
            eventArgs.length > 1 &&
            eventArgs[1] &&
            eventArgs[1]._plasmic_state_init_
          ) {
            return;
          }
        }}
        onInputValueChange={async (...eventArgs: any) => {
          generateStateOnChangeProp($state, ["subTextInput", "value"]).apply(
            null,
            eventArgs
          );

          if (
            eventArgs.length > 1 &&
            eventArgs[1] &&
            eventArgs[1]._plasmic_state_init_
          ) {
            return;
          }
        }}
      />

      <SubcomponentCheckbox
        data-plasmic-name={"subCheckbox"}
        data-plasmic-override={overrides.subCheckbox}
        className={classNames("__wab_instance", sty.subCheckbox)}
        isChecked={
          generateStateValueProp($state, ["subCheckbox", "isChecked"]) ?? false
        }
        onChange={async (...eventArgs: any) => {
          ((...eventArgs) => {
            generateStateOnChangeProp($state, ["subCheckbox", "isChecked"])(
              eventArgs[0]
            );
          }).apply(null, eventArgs);

          if (
            eventArgs.length > 1 &&
            eventArgs[1] &&
            eventArgs[1]._plasmic_state_init_
          ) {
            return;
          }
        }}
      >
        {"Permission to follow up with you on submitted ticket?"}
      </SubcomponentCheckbox>
      <SubcomponentTextInput
        data-plasmic-name={"subTextInput2"}
        data-plasmic-override={overrides.subTextInput2}
        className={classNames("__wab_instance", sty.subTextInput2)}
        errorMessage={generateStateValueProp($state, [
          "subTextInput2",
          "errorMessage"
        ])}
        inputPlaceholder={"Contact Email"}
        inputValue={generateStateValueProp($state, ["subTextInput2", "value"])}
        onErrorMessageChange={async (...eventArgs: any) => {
          generateStateOnChangeProp($state, [
            "subTextInput2",
            "errorMessage"
          ]).apply(null, eventArgs);

          if (
            eventArgs.length > 1 &&
            eventArgs[1] &&
            eventArgs[1]._plasmic_state_init_
          ) {
            return;
          }
        }}
        onInputValueChange={async (...eventArgs: any) => {
          generateStateOnChangeProp($state, ["subTextInput2", "value"]).apply(
            null,
            eventArgs
          );

          if (
            eventArgs.length > 1 &&
            eventArgs[1] &&
            eventArgs[1]._plasmic_state_init_
          ) {
            return;
          }
        }}
      />

      <SubcomponentTextInput
        data-plasmic-name={"subTextInput4"}
        data-plasmic-override={overrides.subTextInput4}
        className={classNames("__wab_instance", sty.subTextInput4)}
        errorMessage={generateStateValueProp($state, [
          "subTextInput4",
          "errorMessage"
        ])}
        inputPlaceholder={
          "Please describe the issue in as much detail as possible.  "
        }
        inputValue={generateStateValueProp($state, ["subTextInput4", "value"])}
        onErrorMessageChange={async (...eventArgs: any) => {
          generateStateOnChangeProp($state, [
            "subTextInput4",
            "errorMessage"
          ]).apply(null, eventArgs);

          if (
            eventArgs.length > 1 &&
            eventArgs[1] &&
            eventArgs[1]._plasmic_state_init_
          ) {
            return;
          }
        }}
        onInputValueChange={async (...eventArgs: any) => {
          generateStateOnChangeProp($state, ["subTextInput4", "value"]).apply(
            null,
            eventArgs
          );

          if (
            eventArgs.length > 1 &&
            eventArgs[1] &&
            eventArgs[1]._plasmic_state_init_
          ) {
            return;
          }
        }}
      />

      <SubcomponentUploadButton
        data-plasmic-name={"subcomponentUploadButton"}
        data-plasmic-override={overrides.subcomponentUploadButton}
        className={classNames("__wab_instance", sty.subcomponentUploadButton)}
        editable={"dragAndDrop"}
      />
    </div>
  ) as React.ReactElement | null;
}

const PlasmicDescendants = {
  root: [
    "root",
    "text",
    "subTextInput",
    "subCheckbox",
    "subTextInput2",
    "subTextInput4",
    "subcomponentUploadButton"
  ],
  text: ["text"],
  subTextInput: ["subTextInput"],
  subCheckbox: ["subCheckbox"],
  subTextInput2: ["subTextInput2"],
  subTextInput4: ["subTextInput4"],
  subcomponentUploadButton: ["subcomponentUploadButton"]
} as const;
type NodeNameType = keyof typeof PlasmicDescendants;
type DescendantsType<T extends NodeNameType> =
  (typeof PlasmicDescendants)[T][number];
type NodeDefaultElementType = {
  root: "div";
  text: "div";
  subTextInput: typeof SubcomponentTextInput;
  subCheckbox: typeof SubcomponentCheckbox;
  subTextInput2: typeof SubcomponentTextInput;
  subTextInput4: typeof SubcomponentTextInput;
  subcomponentUploadButton: typeof SubcomponentUploadButton;
};

type ReservedPropsType = "variants" | "args" | "overrides";
type NodeOverridesType<T extends NodeNameType> = Pick<
  PlasmicFeedbackFeedbackModal__OverridesType,
  DescendantsType<T>
>;
type NodeComponentProps<T extends NodeNameType> =
  // Explicitly specify variants, args, and overrides as objects
  {
    variants?: PlasmicFeedbackFeedbackModal__VariantsArgs;
    args?: PlasmicFeedbackFeedbackModal__ArgsType;
    overrides?: NodeOverridesType<T>;
  } & Omit<PlasmicFeedbackFeedbackModal__VariantsArgs, ReservedPropsType> & // Specify variants directly as props
    /* Specify args directly as props*/ Omit<
      PlasmicFeedbackFeedbackModal__ArgsType,
      ReservedPropsType
    > &
    /* Specify overrides for each element directly as props*/ Omit<
      NodeOverridesType<T>,
      ReservedPropsType | VariantPropType | ArgPropType
    > &
    /* Specify props for the root element*/ Omit<
      Partial<React.ComponentProps<NodeDefaultElementType[T]>>,
      ReservedPropsType | VariantPropType | ArgPropType | DescendantsType<T>
    >;

function makeNodeComponent<NodeName extends NodeNameType>(nodeName: NodeName) {
  type PropsType = NodeComponentProps<NodeName> & { key?: React.Key };
  const func = function <T extends PropsType>(
    props: T & StrictProps<T, PropsType>
  ) {
    const { variants, args, overrides } = React.useMemo(
      () =>
        deriveRenderOpts(props, {
          name: nodeName,
          descendantNames: PlasmicDescendants[nodeName],
          internalArgPropNames: PlasmicFeedbackFeedbackModal__ArgProps,
          internalVariantPropNames: PlasmicFeedbackFeedbackModal__VariantProps
        }),
      [props, nodeName]
    );
    return PlasmicFeedbackFeedbackModal__RenderFunc({
      variants,
      args,
      overrides,
      forNode: nodeName
    });
  };
  if (nodeName === "root") {
    func.displayName = "PlasmicFeedbackFeedbackModal";
  } else {
    func.displayName = `PlasmicFeedbackFeedbackModal.${nodeName}`;
  }
  return func;
}

export const PlasmicFeedbackFeedbackModal = Object.assign(
  // Top-level PlasmicFeedbackFeedbackModal renders the root element
  makeNodeComponent("root"),
  {
    // Helper components rendering sub-elements
    text: makeNodeComponent("text"),
    subTextInput: makeNodeComponent("subTextInput"),
    subCheckbox: makeNodeComponent("subCheckbox"),
    subTextInput2: makeNodeComponent("subTextInput2"),
    subTextInput4: makeNodeComponent("subTextInput4"),
    subcomponentUploadButton: makeNodeComponent("subcomponentUploadButton"),

    // Metadata about props expected for PlasmicFeedbackFeedbackModal
    internalVariantProps: PlasmicFeedbackFeedbackModal__VariantProps,
    internalArgProps: PlasmicFeedbackFeedbackModal__ArgProps
  }
);

export default PlasmicFeedbackFeedbackModal;
/* prettier-ignore-end */
