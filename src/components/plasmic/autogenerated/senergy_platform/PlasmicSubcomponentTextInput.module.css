.textInputContainer {
  display: flex;
  flex-direction: column;
  position: relative;
  width: 100%;
  height: auto;
  justify-content: center;
  align-items: flex-start;
  min-width: 0;
}
.textInputContainer:focus-within {
  outline: none;
}
.textInputContainer___focusVisibleWithin {
  outline: none;
}
.freeBox__hgsOr {
  display: flex;
  flex-direction: row;
  position: relative;
  top: auto;
  z-index: 1;
  left: auto;
  height: 10px;
  justify-content: flex-start;
  align-items: center;
  flex-shrink: 0;
}
.freeBoxviewOnly__hgsOr0Hff2 {
  display: none;
}
.textInputContainer:focus-within .freeBox__hgsOr {
  outline: none;
}
.textInputContainer .freeBox___focusVisibleWithin__hgsOr1P9I {
  outline: none;
}
.text__hi727 {
  width: auto;
  max-width: 100%;
  font-size: var(--token-agfFfkxgxH6d);
  color: var(--token-yPq8Z3hhDPZH);
  transition-duration: 0.2s;
  transition-property: color;
  -webkit-transition-property: color;
  -webkit-transition-duration: 0.2s;
}
.textviewOnly__hi7270Hff2 {
  display: none;
}
.textInputContainer:focus-within .text__hi727 {
  color: var(--token-to1fT9g-t4Js);
  display: block;
  outline: none;
}
.textInputContainer .text___focusVisibleWithin__hi7271P9I {
  color: var(--token-to1fT9g-t4Js);
  display: block;
  outline: none;
}
.text__jNpeO {
  position: relative;
  margin-bottom: 1px;
  white-space: pre-wrap;
  display: none;
}
.textviewOnly__jNpeO0Hff2 {
  display: block;
}
.textviewOnly_editView_subHeading__jNpeO0Hff21Uxi2 {
  font-size: var(--token-_-i82ElPHE7I);
}
.textviewOnly_editView_heading3__jNpeO0Hff2KpWkg {
  font-size: var(--token-9KumB6TRpaad);
}
.textviewOnly_editView_heading2__jNpeO0Hff2UMhCt {
  font-size: var(--token-AMP1angFPBtf);
}
.textviewOnly_editView_heading1__jNpeO0Hff2UxriI {
  font-size: var(--token-hZBgG8kTaig3);
}
.textviewOnly_bold__jNpeO0Hff2ZiRge {
  font-weight: 700;
}
.textInputContainer:focus-within .text__jNpeO {
  outline: none;
}
.freeBox___20NMw {
  display: flex;
  position: relative;
  flex-direction: row;
  width: 100%;
  height: auto;
  max-width: 100%;
  min-width: 0;
  padding: var(--token-sazGmnf7GWAk);
  margin: var(--token-sazGmnf7GWAk);
}
.freeBox___20NMw > :global(.__wab_flex-container) {
  flex-direction: row;
  align-items: stretch;
  justify-content: flex-start;
  min-width: 0;
  margin-left: calc(0px - var(--token-4Wrp9mDZCSCQ));
  width: calc(100% + var(--token-4Wrp9mDZCSCQ));
}
.freeBox___20NMw > :global(.__wab_flex-container) > *,
.freeBox___20NMw > :global(.__wab_flex-container) > :global(.__wab_slot) > *,
.freeBox___20NMw > :global(.__wab_flex-container) > picture > img,
.freeBox___20NMw
  > :global(.__wab_flex-container)
  > :global(.__wab_slot)
  > picture
  > img {
  margin-left: var(--token-4Wrp9mDZCSCQ);
}
.textInputContainer .freeBox___focusVisibleWithin___20NMw1P9I {
  margin-top: 0px;
  outline: none;
}
.input {
  width: 100%;
  background: none;
  border-left-style: none;
  border-bottom-style: solid;
  border-right-style: none;
  border-top-style: none;
  transition-property: color;
  transition-duration: 0.2s;
  min-width: 0;
  -webkit-transition-property: color;
  -webkit-transition-duration: 0.2s;
  padding: var(--token-sazGmnf7GWAk);
  border-width: 1px;
  border-color: var(--token-3OLw18f8JRN3);
}
.inputcolor_dark {
  background: var(--token-Jis9JaUzrgN0);
  color: var(--token-1AMvw6c2eIK7);
}
.inputcolor_light {
  background: var(--token-1AMvw6c2eIK7);
  border-style: solid;
  border-color: var(--token-p09LDPmbF81_);
}
.inputeditView_subHeading {
  font-size: var(--token-_-i82ElPHE7I);
}
.inputeditView_heading3 {
  font-size: var(--token-9KumB6TRpaad);
}
.inputeditView_heading2 {
  font-size: var(--token-AMP1angFPBtf);
}
.inputeditView_heading1 {
  font-size: var(--token-hZBgG8kTaig3);
}
.inputviewOnly {
  display: none;
}
.inputbold {
  font-weight: 700;
}
.inputleftJustification {
  text-align: right;
}
.inputerrorState {
  border-color: var(--token-vmreU-dg02sh);
}
.textInputContainer:focus-within .input {
  outline: none;
  border-color: var(--token-to1fT9g-t4Js);
}
.textInputContainer .input___focusVisibleWithin {
  outline: none;
  border-color: var(--token-to1fT9g-t4Js);
}
.svg___3PkS {
  position: relative;
  object-fit: cover;
  max-width: 100%;
  align-self: center;
  height: 1em;
  display: none;
}
.popupText {
  position: absolute;
  width: auto;
  height: auto;
  max-width: 100%;
  color: var(--token-yPq8Z3hhDPZH);
  right: 0px;
  margin-top: 0px;
  bottom: -16px;
  display: none;
}
.popupTextpressEnterText {
  display: block;
}
.popupTextpressEnterText2_pressEnterHidden {
  display: none;
}
.popupTextpressEnterText2_pressEnterShown {
  display: block;
}
.textInputContainer:focus-within .popupText {
  outline: none;
}
.errorStateContainer {
  display: flex;
  position: relative;
  flex-direction: row;
  justify-content: flex-start;
  align-items: center;
  left: auto;
  top: auto;
  height: 9px;
  margin-top: 6px;
  flex-shrink: 0;
}
.errorStateContainerviewOnly {
  display: none;
}
.textInputContainer:focus-within .errorStateContainer {
  outline: none;
}
.svg__bncuj {
  position: relative;
  object-fit: cover;
  max-width: 100%;
  color: var(--token-vmreU-dg02sh);
  margin-right: 4px;
  height: 1em;
  display: none;
}
.svgerrorState__bncujTmMmW {
  display: block;
}
.textInputContainer:focus-within .svg__bncuj {
  outline: none;
}
.errorMessageDisplay {
  color: var(--token-vmreU-dg02sh);
  font-size: var(--token-agfFfkxgxH6d);
  display: none;
}
.errorMessageDisplaypressEnterText {
  display: none;
}
.errorMessageDisplayerrorState {
  display: block;
}
.errorMessageDisplaypressEnterText2_pressEnterHidden {
  display: none;
}
.errorMessageDisplaypressEnterText2_pressEnterShown {
  display: none;
}
.textInputContainer:focus-within .errorMessageDisplay {
  outline: none;
}
