.introductionFormatting {
  display: flex;
  flex-direction: row;
  width: 100%;
  flex-shrink: 1;
  height: auto;
  margin-bottom: 0px;
  transform: translateX(0px) translateY(0px) translateZ(0px);
  margin-top: 0px;
  margin-left: 0px;
  position: relative;
  justify-self: flex-start;
  flex-grow: 0;
  min-width: 0;
  padding: 16px 16px 8px 0px;
}
.introductionFormatting > :global(.__wab_flex-container) {
  flex-direction: row;
  align-items: stretch;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-content: stretch;
  min-width: 0;
  margin-left: calc(0px - var(--token-4Wrp9mDZCSCQ));
  width: calc(100% + var(--token-4Wrp9mDZCSCQ));
  margin-top: calc(0px - var(--token-j0qnbpah5w9U));
  height: calc(100% + var(--token-j0qnbpah5w9U));
}
.introductionFormatting > :global(.__wab_flex-container) > *,
.introductionFormatting
  > :global(.__wab_flex-container)
  > :global(.__wab_slot)
  > *,
.introductionFormatting > :global(.__wab_flex-container) > picture > img,
.introductionFormatting
  > :global(.__wab_flex-container)
  > :global(.__wab_slot)
  > picture
  > img {
  margin-left: var(--token-4Wrp9mDZCSCQ);
  margin-top: var(--token-j0qnbpah5w9U);
}
@media (max-width: 860px) {
  .introductionFormatting {
    display: flex;
    flex-direction: column;
  }
}
@media (max-width: 860px) {
  .introductionFormatting > :global(.__wab_flex-container) {
    flex-direction: column;
    align-items: center;
    justify-content: flex-start;
    margin-left: calc(0px - 0px);
    width: calc(100% + 0px);
    margin-top: calc(0px - var(--token-4Wrp9mDZCSCQ));
    height: calc(100% + var(--token-4Wrp9mDZCSCQ));
  }
}
@media (max-width: 860px) {
  .introductionFormatting > :global(.__wab_flex-container) > *,
  .introductionFormatting
    > :global(.__wab_flex-container)
    > :global(.__wab_slot)
    > *,
  .introductionFormatting > :global(.__wab_flex-container) > picture > img,
  .introductionFormatting
    > :global(.__wab_flex-container)
    > :global(.__wab_slot)
    > picture
    > img {
    margin-left: 0px;
    margin-top: var(--token-4Wrp9mDZCSCQ);
  }
}
.introductionFormattingeditable {
  display: flex;
  flex-direction: row;
}
.introductionFormattingeditable > :global(.__wab_flex-container) {
  flex-direction: row;
  margin-top: calc(0px - 0px);
  height: calc(100% + 0px);
}
.introductionFormattingeditable > :global(.__wab_flex-container) > *,
.introductionFormattingeditable
  > :global(.__wab_flex-container)
  > :global(.__wab_slot)
  > *,
.introductionFormattingeditable
  > :global(.__wab_flex-container)
  > picture
  > img,
.introductionFormattingeditable
  > :global(.__wab_flex-container)
  > :global(.__wab_slot)
  > picture
  > img {
  margin-top: 0px;
}
.introSummaryDisplay {
  width: auto;
  height: auto;
  flex-grow: 0;
  flex-shrink: 1;
  left: auto;
  top: auto;
  position: relative;
  padding: var(--token-j0qnbpah5w9U);
}
@media (max-width: 860px) {
  .introSummaryDisplay {
    left: auto;
    top: auto;
  }
}
.imageContainerWithUpload {
  display: flex;
  position: relative;
  width: auto;
  flex-direction: column;
  left: auto;
  top: auto;
  padding: var(--token-sazGmnf7GWAk);
}
@media (max-width: 860px) {
  .imageContainerWithUpload {
    left: auto;
    top: auto;
  }
}
.introductionImageFile {
  position: relative;
  object-fit: cover;
  width: 350px;
  height: 400px;
  flex-shrink: 0;
  margin: var(--token-4Wrp9mDZCSCQ);
}
.introductionImageFile > picture > img {
  object-fit: cover;
}
.introductionImageFileoverview_introOverview {
  display: none !important;
}
.introductionImageFileintroOnboarding_introComposition {
  display: block !important;
}
.subcomponentUploadButton:global(.__wab_instance) {
  position: absolute;
  top: 0px;
  left: 0px;
  display: none;
}
.subcomponentUploadButtoneditable:global(.__wab_instance) {
  display: block;
}
.subcomponentUploadButtonintroOnboarding_introComposition:global(
    .__wab_instance
  ) {
  display: none;
}
.subcomponentUploadButtonintroOnboarding_imageUpload:global(.__wab_instance) {
  display: block;
}
