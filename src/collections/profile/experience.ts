import type { Field } from 'payload';

const WORD_SPLIT_REGEX = /\s+/;

export const experienceFields: Field[] = [
  {
    name: 'job_title',
    label: 'Job Title',
    type: 'text',
    required: true,
  },
  {
    type: 'row',
    fields: [
      {
        name: 'company_name',
        label: 'Company Name',
        type: 'text',
        required: true,
      },
      {
        name: 'company_location',
        label: 'Location of Company',
        type: 'text',
        required: false,
      },
    ],
  },
  {
    name: 'employment_type',
    label: 'Type of Employment',
    type: 'select',
    options: [
      { label: 'Full-time', value: 'full-time' },
      { label: 'Part-time', value: 'part-time' },
      { label: 'Self-employed', value: 'self-employed' },
      { label: 'Freelance', value: 'freelance' },
      { label: 'Contract', value: 'contract' },
      { label: 'Internship', value: 'internship' },
      { label: 'Apprenticeship', value: 'apprenticeship' },
      { label: 'Temporary', value: 'temporary' },
      { label: 'Seasonal', value: 'seasonal' },
    ],
    required: true,
  },
  {
    type: 'row',
    fields: [
      {
        name: 'start_date',
        label: 'Start Date',
        type: 'date',
        required: true,
      },
      {
        name: 'currently_working',
        label: 'Currently Working There',
        type: 'checkbox',
        required: true,
      },
      {
        name: 'end_date',
        label: 'End Date',
        type: 'date',
        required: false,
      },
    ],
  },
  {
    name: 'experience_summary',
    label: 'Experience Summary',
    admin: {
      description: 'Description of the experience',
    },
    type: 'textarea',
    required: false,
    validate: (value: string | null | undefined) => {
      if (value) {
        const wordCount = value.trim().split(WORD_SPLIT_REGEX).length;
        if (wordCount > 300) {
          return `Summary must not exceed 300 words. Current word count: ${wordCount}`;
        }
      }
      return true;
    },
  },
  {
    name: 'visible',
    type: 'checkbox',
    required: false,
    defaultValue: true,
  },
];
