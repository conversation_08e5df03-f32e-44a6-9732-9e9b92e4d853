.messagePreviewContainer {
  transition-property: all;
  transition-duration: 0.05s;
  transition-timing-function: linear;
  display: flex;
  flex-direction: row;
  position: relative;
  width: 100%;
  height: auto;
  justify-content: flex-start;
  align-items: flex-start;
  justify-self: flex-start;
  background: var(--token-5_Q90hFZ9CmK);
  min-width: 296px;
  max-width: 25%;
  border-left-style: none;
  border-left-width: 1px;
  border-bottom-style: solid;
  border-bottom-width: 1px;
  border-right-style: none;
  border-right-width: 1px;
  -webkit-transition-property: all;
  -webkit-transition-timing-function: linear;
  -webkit-transition-duration: 0.05s;
  padding: var(--token-4Wrp9mDZCSCQ);
}
.messagePreviewContainermodalPreview_messageModalPreview {
  min-width: 250px;
  max-width: auto;
}
.messagePreviewContainermodalPreview_groupChat {
  min-width: 250px;
  max-width: auto;
}
.messagePreviewContainer:hover {
  background: var(--token-F0-tInDly4PE);
}
.usersProfileImage {
  position: relative;
  object-fit: cover;
  max-width: 100%;
  width: var(--token-R0GUjkbC5dJn);
  height: var(--token-R0GUjkbC5dJn);
  margin-right: 4px;
  margin-top: 6px;
  flex-shrink: 0;
  border-radius: 100%;
}
.usersProfileImage > picture > img {
  object-fit: cover;
}
.messageInfoContainer {
  display: flex;
  position: relative;
  flex-direction: column;
  align-items: flex-start;
  justify-content: flex-start;
  width: 100%;
  height: auto;
  max-width: 100%;
  min-width: 0;
  padding: var(--token-sazGmnf7GWAk);
  margin: var(--token-sazGmnf7GWAk);
}
.nameAndTime {
  display: flex;
  flex-direction: row;
  align-items: flex-end;
  justify-content: space-between;
  width: 100%;
  height: auto;
  max-width: 100%;
  min-width: 0;
  padding: var(--token-sazGmnf7GWAk);
}
.text__feQum {
  height: auto;
  max-width: 100%;
  position: relative;
  font-weight: 600;
  flex-shrink: 1;
  width: 160px;
}
.text__heXi {
  position: relative;
  width: auto;
  height: auto;
  max-width: 100%;
  flex-shrink: 0;
  margin-bottom: 1px;
  color: var(--token-ttBxfk-RsDf2);
  font-size: var(--token-agfFfkxgxH6d);
  margin-right: 2px;
}
.text__wwlEb {
  position: relative;
  width: 100%;
  height: 38px;
  max-width: 300px;
  font-size: var(--token-agfFfkxgxH6d);
  white-space: pre-wrap;
  line-height: 1.25;
  min-width: 0;
  flex-shrink: 0;
}
.coloredDivider {
  position: absolute;
  align-content: flex-start;
  justify-items: center;
  width: auto;
  left: 0px;
  top: auto;
  height: 1px;
  bottom: 0px;
  background: var(--token-Q3bPqi9oloJF);
  display: none;
  grid-template-columns:
    var(--plsmc-viewport-gap) 1fr minmax(0, var(--plsmc-wide-chunk))
    min(
      var(--plsmc-standard-width),
      calc(100% - var(--plsmc-viewport-gap) - var(--plsmc-viewport-gap))
    )
    minmax(0, var(--plsmc-wide-chunk)) 1fr var(--plsmc-viewport-gap);
  padding: var(--token-sazGmnf7GWAk);
}
.coloredDivider > * {
  grid-column: 4;
}
