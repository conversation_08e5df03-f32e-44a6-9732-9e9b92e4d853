.formattingAndShadow {
  display: flex;
  flex-direction: column;
  position: relative;
  width: 325px;
  height: auto;
  justify-content: flex-start;
  align-items: flex-start;
  background: var(--token-xo_r2w5pebq-);
  box-shadow: 0px 2px 20px -2px #8b8b8b33;
  justify-self: flex-start;
  margin: 0px;
  border: 0.01cm solid #e4e4e7;
}
.formattingAndShadowcaseStudySlides_scrollingButtons {
  background: var(--token-1AMvw6c2eIK7);
}
.formattingAndShadowbookmark_bookmarkFormat {
  width: 300px;
  background: var(--token-1AMvw6c2eIK7);
  border-top-style: none;
  border-right-style: none;
  border-left-style: none;
  box-shadow: none;
  border-bottom: 0.5px solid var(--token-p09LDPmbF81_);
}
.formattingAndShadowbookmark_hover {
  width: 300px;
  background: var(--token-1AMvw6c2eIK7);
  border-top-style: none;
  border-right-style: none;
  border-left-style: none;
  box-shadow: none;
  border-bottom: 0.5px solid var(--token-p09LDPmbF81_);
}
.formattingAndShadow:hover {
  transform: translateX(1.02px) translateY(1.015px) translateZ(0px);
}
.headingBar {
  display: flex;
  position: relative;
  flex-direction: row;
  align-items: flex-start;
  justify-content: flex-start;
  width: 325px;
  height: 65px;
  max-width: 100%;
  flex-shrink: 0;
}
.headingBarcaseStudySlides_scrollingButtons {
  background: var(--token-1AMvw6c2eIK7);
}
.headingBarbookmark_bookmarkFormat {
  width: 300px;
  justify-content: flex-start;
  align-items: flex-start;
  height: auto;
}
.headingBarbookmark_hover {
  width: 300px;
  justify-content: flex-start;
  align-items: flex-start;
  height: auto;
}
.formattingAndShadow:hover .headingBar {
  width: 100%;
  min-width: 0;
}
.img {
  position: relative;
  object-fit: cover;
  max-width: 100%;
  width: var(--token-M1l4keX1sfKm);
  height: var(--token-M1l4keX1sfKm);
  margin-left: 8px;
  margin-top: 11px;
  margin-right: 4px;
  flex-shrink: 0;
  display: none !important;
  border-radius: 100%;
}
.img > picture > img {
  object-fit: cover;
}
.imgbookmark_bookmarkFormat {
  display: block !important;
}
.imgbookmark_hover {
  display: block !important;
}
.freeBox {
  display: flex;
  position: relative;
  flex-direction: column;
  align-items: flex-start;
  justify-content: flex-start;
  width: auto;
  height: auto;
  max-width: 100%;
}
.freeBoxbookmark_bookmarkFormat {
  width: 100%;
  min-width: 0;
}
.freeBoxbookmark_hover {
  width: 100%;
  min-width: 0;
}
.text__aPyHg {
  width: 280px;
  height: auto;
  max-width: 100%;
  font-size: var(--token-_-i82ElPHE7I);
  padding-top: 8px;
  padding-left: 16px;
  white-space: pre;
  text-overflow: ellipsis;
  overflow: hidden;
}
.textbookmark_bookmarkFormat__aPyHgCaqmZ {
  width: 208px;
  padding-left: 0px;
  padding-top: 4px;
  transform: none;
}
.textbookmark_hover__aPyHgW9E5V {
  width: 208px;
  padding-left: 0px;
  padding-top: 4px;
  transform: none;
}
.text__r77Sr {
  position: relative;
  width: 100%;
  height: auto;
  max-width: 100%;
  padding-left: 16px;
  font-size: var(--token-agfFfkxgxH6d);
  min-width: 0;
}
.textbookmark_bookmarkFormat__r77SrCaqmZ {
  padding-left: 0px;
}
.textbookmark_hover__r77SrW9E5V {
  padding-left: 0px;
}
.searchCoreBookmarkButton:global(.__wab_instance) {
  max-width: 100%;
  position: relative;
  height: 24px;
  width: 24px;
  flex-shrink: 0;
  margin: 12px 12px 12px 7px;
}
.searchCoreBookmarkButtonbookmark_bookmarkFormat:global(.__wab_instance) {
  margin-top: 10px;
  margin-right: 10px;
  margin-left: 0px;
  flex-shrink: 0;
}
.searchCoreBookmarkButtonbookmark_hover:global(.__wab_instance) {
  margin-top: 10px;
  margin-right: 10px;
  margin-left: 0px;
  flex-shrink: 0;
}
.formattingAndShadow:hover .searchCoreBookmarkButton:global(.__wab_instance) {
  flex-shrink: 0;
}
.imageSection {
  display: flex;
  position: relative;
  width: 100%;
  flex-direction: column;
  align-items: flex-end;
  justify-content: flex-end;
  min-width: 0;
  padding: var(--token-sazGmnf7GWAk);
}
.imageSectionbookmark_bookmarkFormat {
  display: none;
}
.imageSectionbookmark_hover {
  display: none;
}
.chevronStack {
  display: flex;
  position: relative;
  flex-direction: row;
  width: 100%;
  height: auto;
  max-width: 100%;
  min-width: 0;
  padding: var(--token-sazGmnf7GWAk);
  margin: var(--token-sazGmnf7GWAk);
}
.chevronStack > :global(.__wab_flex-container) {
  flex-direction: row;
  align-items: stretch;
  justify-content: flex-start;
  min-width: 0;
  margin-left: calc(0px - var(--token-4Wrp9mDZCSCQ));
  width: calc(100% + var(--token-4Wrp9mDZCSCQ));
}
.chevronStack > :global(.__wab_flex-container) > *,
.chevronStack > :global(.__wab_flex-container) > :global(.__wab_slot) > *,
.chevronStack > :global(.__wab_flex-container) > picture > img,
.chevronStack
  > :global(.__wab_flex-container)
  > :global(.__wab_slot)
  > picture
  > img {
  margin-left: var(--token-4Wrp9mDZCSCQ);
}
.chevronStackcaseStudySlides_scrollingButtons {
  width: 100%;
  min-width: 0;
}
.chevronStackcaseStudySlides_scrollingButtons > :global(.__wab_flex-container) {
  justify-content: flex-start;
  align-items: flex-start;
  min-width: 0;
}
.profileImage {
  object-fit: cover;
  max-width: 100%;
  width: 325px;
  height: 190px;
  flex-shrink: 0;
}
.profileImage > picture > img {
  object-fit: cover;
}
.profileImagecaseStudySlides_scrollingButtons {
  width: auto;
  max-width: 323px;
}
.subcomponentChevrons__cVeP8:global(.__wab_instance) {
  max-width: 100%;
  position: absolute;
  top: 74px;
  right: 13px;
  flex-shrink: 0;
}
.subcomponentChevronscaseStudySlides_scrollingButtons__cVeP8KN1F:global(
    .__wab_instance
  ) {
  flex-shrink: 0;
  display: flex;
}
.subcomponentChevrons__c1Zn:global(.__wab_instance) {
  max-width: 100%;
  position: absolute;
  left: 0px;
  top: 74px;
  flex-shrink: 0;
  display: none;
}
.subcomponentChevronscaseStudySlides_scrollingButtons__c1ZnkN1F:global(
    .__wab_instance
  ) {
  flex-shrink: 0;
  display: flex;
}
.buttonSection {
  display: flex;
  position: absolute;
  width: 100%;
  flex-direction: column;
  align-items: flex-end;
  justify-content: flex-end;
  left: 0px;
  top: 0px;
  z-index: 1;
  height: auto;
  bottom: 0px;
  opacity: 0;
  min-width: 0;
  padding: var(--token-sazGmnf7GWAk);
}
.buttonSectioncaseStudySlides_scrollingButtons {
  display: none;
}
.buttonSectionbookmark_bookmarkFormat {
  display: none;
}
.buttonSectionbookmark_hover {
  display: none;
}
.formattingAndShadow:hover .buttonSection {
  opacity: 1;
  display: flex;
}
.subButton:global(.__wab_instance) {
  max-width: 100%;
  position: relative;
  margin-bottom: 4px;
}
.formattingAndShadow:hover .subButton:global(.__wab_instance) {
  opacity: 1;
}
.svg__sObm9 {
  position: relative;
  object-fit: cover;
  width: auto;
  height: 1em;
}
.svg__dBfZx {
  position: relative;
  object-fit: cover;
  width: auto;
  height: 1em;
}
.subButton2:global(.__wab_instance) {
  max-width: 100%;
  position: relative;
  margin-bottom: 4px;
}
.svg__b3CEo {
  position: relative;
  object-fit: cover;
  width: auto;
  height: 1em;
}
.svg__vHwsJ {
  position: relative;
  object-fit: cover;
  width: auto;
  height: 1em;
}
.shortDescription {
  position: relative;
  width: auto;
  height: 145px;
  max-width: 325px;
  flex-shrink: 0;
  padding: 10px 16px 16px;
}
.shortDescriptioncaseStudySlides_scrollingButtons {
  padding-bottom: 0px;
  height: 130px;
  width: auto;
  flex-shrink: 0;
}
.shortDescriptionbookmark_bookmarkFormat {
  display: none;
}
.shortDescriptionbookmark_hover {
  display: none;
}
.section {
  display: flex;
  position: relative;
  width: 325px;
  flex-direction: row;
  padding: var(--token-4Wrp9mDZCSCQ) 16px 16px var(--token-4Wrp9mDZCSCQ);
}
.section > :global(.__wab_flex-container) {
  flex-direction: row;
  justify-content: flex-end;
  align-items: center;
  margin-left: calc(0px - var(--token-j0qnbpah5w9U));
  width: calc(100% + var(--token-j0qnbpah5w9U));
}
.section > :global(.__wab_flex-container) > *,
.section > :global(.__wab_flex-container) > :global(.__wab_slot) > *,
.section > :global(.__wab_flex-container) > picture > img,
.section
  > :global(.__wab_flex-container)
  > :global(.__wab_slot)
  > picture
  > img {
  margin-left: var(--token-j0qnbpah5w9U);
}
.sectioncaseStudySlides_scrollingButtons {
  width: 100%;
  min-width: 0;
}
.sectioncaseStudySlides_scrollingButtons > :global(.__wab_flex-container) {
  min-width: 0;
}
.sectionbookmark_bookmarkFormat {
  padding-bottom: 10px;
  padding-right: 8px;
  width: 300px;
}
.sectionbookmark_hover {
  padding-bottom: 10px;
  padding-right: 8px;
  width: 300px;
}
.svg__wdxlw {
  position: relative;
  object-fit: cover;
  max-width: 100%;
  width: var(--token-j0qnbpah5w9U);
  height: var(--token-j0qnbpah5w9U);
  flex-shrink: 0;
}
