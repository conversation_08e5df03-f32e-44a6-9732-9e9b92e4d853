/* eslint-disable */
/* tslint:disable */
// @ts-nocheck
/* prettier-ignore-start */

"use client";

import * as React from "react";
import { createUseScreenVariants } from "@plasmicapp/react-web";

export type FormattingBreakPointValue =
  | "tabletLarge"
  | "mobile"
  | "tabletSmall";
export const FormattingBreakPointContext = React.createContext<
  FormattingBreakPointValue[] | undefined
>("PLEASE_RENDER_INSIDE_PROVIDER" as any);

export const useScreenVariants = createUseScreenVariants(true, {
  tabletLarge: "(max-width:1200px)",
  mobile: "(max-width:480px)",
  tabletSmall: "(max-width:860px)",
});

export default FormattingBreakPointContext;
/* prettier-ignore-end */
