/* eslint-disable */
/* tslint:disable */
// @ts-nocheck
/* prettier-ignore-start */
import React from "react";
import { classNames } from "@plasmicapp/react-web";

export type IdIconProps = React.ComponentProps<"svg"> & {
  title?: string;
};

export function IdIcon(props: IdIconProps) {
  const { className, style, title, ...restProps } = props;
  return (
    <svg
      xmlns={"http://www.w3.org/2000/svg"}
      className={classNames(
        "plasmic-default__svg",
        className,
        "icon icon-tabler icon-tabler-id"
      )}
      viewBox={"0 0 24 24"}
      strokeWidth={"1.5"}
      stroke={"currentColor"}
      fill={"none"}
      strokeLinecap={"round"}
      strokeLinejoin={"round"}
      height={"1em"}
      style={style}
      {...restProps}
    >
      {title && <title>{title}</title>}

      <path d={"M0 0h24v24H0z"} fill={"none"} stroke={"none"}></path>

      <path
        d={"M3 7a3 3 0 013-3h12a3 3 0 013 3v10a3 3 0 01-3 3H6a3 3 0 01-3-3z"}
      ></path>

      <path d={"M7 10a2 2 0 104 0 2 2 0 10-4 0m8-2h2m-2 4h2M7 16h10"}></path>
    </svg>
  );
}

export default IdIcon;
/* prettier-ignore-end */
