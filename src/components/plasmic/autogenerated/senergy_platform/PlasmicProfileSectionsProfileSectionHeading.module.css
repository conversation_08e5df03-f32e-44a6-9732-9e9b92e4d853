.headerContainer {
  display: flex;
  flex-direction: column;
  position: relative;
  width: 100%;
  height: auto;
  justify-content: flex-start;
  align-items: center;
  justify-self: flex-start;
  min-width: 0;
}
.headerContaineroverviewGrid {
  align-items: flex-start;
  justify-content: flex-start;
  margin-left: 0px;
  padding-left: 0px;
}
.headerWorks {
  width: 100%;
  height: auto;
  max-width: 100%;
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  min-width: 0;
  padding: var(--token-M1l4keX1sfKm) 0px 8px;
}
.headerWorksoverviewGrid {
  padding-top: 4px;
  padding-bottom: 4px;
}
.slotTargetChildren {
  font-size: var(--token-hZBgG8kTaig3);
  font-family: var(--token-z1yrQVi72Nj1);
}
.slotTargetChildrenoverviewGrid {
  font-size: var(--token-9KumB6TRpaad);
}
.subcomponentAddSectionButton:global(.__wab_instance) {
  max-width: 100%;
  position: relative;
  flex-shrink: 0;
  display: none;
}
.subcomponentAddSectionButtoneditable:global(.__wab_instance) {
  flex-shrink: 0;
  display: flex;
}
.underline {
  display: grid;
  position: relative;
  align-content: flex-start;
  justify-items: center;
  width: 100%;
  padding-left: 0;
  padding-right: 0;
  background: var(--token-3OLw18f8JRN3);
  min-width: auto;
  height: 2px;
  margin-bottom: 4px;
  flex-shrink: 0;
  grid-template-columns:
    var(--plsmc-viewport-gap) 1fr minmax(0, var(--plsmc-wide-chunk))
    min(
      var(--plsmc-standard-width),
      calc(100% - var(--plsmc-viewport-gap) - var(--plsmc-viewport-gap))
    )
    minmax(0, var(--plsmc-wide-chunk)) 1fr var(--plsmc-viewport-gap);
}
.underline > * {
  grid-column: 4;
}
.underlinenoUnderline {
  display: none;
}
.underlinenoUnderline > * {
  grid-column: 4;
}
.underlineoverviewGrid {
  height: 1px;
  justify-items: flex-start;
  align-content: flex-start;
  flex-shrink: 0;
}
.underlineoverviewGrid > * {
  grid-column: 4;
}
