'use client';
import { DangerZone } from '@/components/settings/danger-zone';
import { ProfileInformation } from '@/components/settings/profile-information';
import { SignOutSection } from '@/components/settings/sign-out-section';
import { useSession } from '@/contexts/session-context';

export default function SettingsPage() {
  const { user, loading, signOut, deleteAccount } = useSession();

  // Show loading state until user data is fetched
  if (loading || !user) {
    return (
      <div className="flex min-h-screen items-center justify-center">
        <div className="h-8 w-8 animate-spin rounded-full border-4 border-current border-t-transparent" />
      </div>
    );
  }

  return (
    <div className="flex justify-center">
      <div className="container max-w-4xl py-8">
        <h1 className="mb-8 font-bold text-4xl">Account Settings</h1>

        <div className="space-y-8">
          <ProfileInformation
            email={user.email}
            initialName={user.name ?? ''}
          />

          {/* <PasskeyManager /> */}

          {/* <SessionsComponent /> */}

          {/* <ConnectedAccounts /> */}

          <SignOutSection loading={loading} onSignOut={signOut} />

          <DangerZone onDeleteAccount={deleteAccount} />
        </div>
      </div>
    </div>
  );
}
