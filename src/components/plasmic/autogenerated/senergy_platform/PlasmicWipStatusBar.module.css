.statusFormattingContainer {
  display: flex;
  flex-direction: row;
  position: relative;
  width: 100%;
  height: auto;
  justify-content: flex-start;
  align-items: center;
  min-width: 0;
}
.personalInfoStack {
  display: flex;
  position: relative;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start;
  width: 100%;
  height: auto;
  max-width: 100%;
  top: auto;
  left: auto;
  min-width: 0;
  padding: var(--token-sazGmnf7GWAk);
  margin: var(--token-sazGmnf7GWAk);
}
.text__sddkz {
  opacity: 0.699;
  display: block;
  font-size: 16px;
  font-family: "Gelato Typewriter";
  font-weight: 700;
  letter-spacing: 0em;
  line-height: 100%;
  color: rgba(40, 40, 40, 1);
  margin-bottom: 12px;
  padding-bottom: 0px;
}
.svg__dPnI {
  object-fit: cover;
  max-width: 100%;
  color: var(--token-K5FbAPSIIrXM);
  height: 1em;
  flex-shrink: 0;
}
.svgprogress_profileInfo__dPnIyyhiu {
  color: var(--token-XQQdLIBrONt7);
}
.svg___5Ld6X {
  position: relative;
  object-fit: cover;
  max-width: 100%;
  color: #292929;
  opacity: 0.699;
  width: 166px;
  height: 1px;
  display: block;
  top: auto;
  left: auto;
  flex-shrink: 0;
}
.experienceInfoStack {
  display: flex;
  position: relative;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start;
  width: 100%;
  height: auto;
  max-width: 100%;
  top: auto;
  left: auto;
  min-width: 0;
  padding: var(--token-sazGmnf7GWAk);
  margin: var(--token-sazGmnf7GWAk);
}
.text___7Yeja {
  opacity: 0.699;
  display: block;
  font-size: 16px;
  font-family: "Gelato Typewriter";
  font-weight: 700;
  letter-spacing: 0em;
  line-height: 100%;
  color: rgba(40, 40, 40, 1);
  margin-bottom: 12px;
}
.svg__zdWuz {
  position: relative;
  object-fit: cover;
  max-width: 100%;
  height: 1em;
  flex-shrink: 0;
}
.svgprogress_yourExperience__zdWuzPkLJ {
  color: var(--token-XQQdLIBrONt7);
}
.svg__hTmMr {
  position: relative;
  object-fit: cover;
  max-width: 100%;
  color: #292929;
  opacity: 0.699;
  width: 166px;
  height: 1px;
  display: block;
  top: auto;
  left: auto;
  flex-shrink: 0;
}
.profileInfoStack {
  display: flex;
  position: relative;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start;
  width: 100%;
  height: auto;
  max-width: 100%;
  top: auto;
  left: auto;
  min-width: 0;
  padding: var(--token-sazGmnf7GWAk);
  margin: var(--token-sazGmnf7GWAk);
}
.text__t24Vw {
  opacity: 0.699;
  display: block;
  font-size: 16px;
  font-family: "Gelato Typewriter";
  font-weight: 700;
  letter-spacing: 0em;
  line-height: 100%;
  color: rgba(40, 40, 40, 1);
  margin-bottom: 12px;
}
.svg__htraK {
  position: relative;
  object-fit: cover;
  max-width: 100%;
  height: 1em;
  flex-shrink: 0;
}
.svgprogress_buildingProfile__htraK3ITyF {
  color: var(--token-XQQdLIBrONt7);
}
.svg__qsaRj {
  position: relative;
  object-fit: cover;
  max-width: 100%;
  color: #292929;
  opacity: 0.699;
  width: 166px;
  height: 1px;
  display: block;
  top: auto;
  left: auto;
  flex-shrink: 0;
}
.explorationInfoStack {
  display: flex;
  position: relative;
  flex-direction: column;
  width: 100%;
  height: auto;
  max-width: 100%;
  min-width: 0;
  padding: var(--token-sazGmnf7GWAk);
  margin: var(--token-sazGmnf7GWAk);
}
.explorationInfoStack > :global(.__wab_flex-container) {
  flex-direction: column;
  align-items: center;
  justify-content: flex-start;
  min-width: 0;
  margin-top: calc(0px - var(--token-sazGmnf7GWAk));
  height: calc(100% + var(--token-sazGmnf7GWAk));
}
.explorationInfoStack > :global(.__wab_flex-container) > *,
.explorationInfoStack
  > :global(.__wab_flex-container)
  > :global(.__wab_slot)
  > *,
.explorationInfoStack > :global(.__wab_flex-container) > picture > img,
.explorationInfoStack
  > :global(.__wab_flex-container)
  > :global(.__wab_slot)
  > picture
  > img {
  margin-top: var(--token-sazGmnf7GWAk);
}
.text__f1XqC {
  opacity: 0.699;
  display: block;
  font-size: 16px;
  font-family: "Gelato Typewriter";
  font-weight: 700;
  letter-spacing: 0em;
  line-height: 100%;
  color: rgba(40, 40, 40, 1);
  position: relative;
  top: auto;
  left: auto;
  margin-bottom: 12px;
}
.svg__bJq5T {
  object-fit: cover;
  max-width: 100%;
  height: 1em;
  flex-shrink: 0;
}
.svgprogress_exploration__bJq5TKp9Xk {
  color: var(--token-XQQdLIBrONt7);
}
