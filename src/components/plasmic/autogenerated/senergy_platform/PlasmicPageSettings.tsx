/* eslint-disable */
/* tslint:disable */
// @ts-nocheck
/* prettier-ignore-start */

/** @jsxRuntime classic */
/** @jsx createPlasmicElementProxy */
/** @jsxFrag React.Fragment */

// This class is auto-generated by Plasmic; please do not edit!
// Plasmic Project: nZeWKcn21KijvC7eT8B3c7
// Component: gmYVz9YWamHt

"use client";

import * as React from "react";

import Head from "next/head";
import Link, { LinkProps } from "next/link";
import { useRouter } from "next/navigation";

import {
  Flex as Flex__,
  MultiChoiceArg,
  PlasmicDataSourceContextProvider as PlasmicDataSourceContextProvider__,
  PlasmicIcon as PlasmicIcon__,
  PlasmicImg as PlasmicImg__,
  PlasmicLink as PlasmicLink__,
  PlasmicPageGuard as PlasmicPageGuard__,
  SingleBooleanChoiceArg,
  SingleChoiceArg,
  Stack as Stack__,
  StrictProps,
  Trans as Trans__,
  classNames,
  createPlasmicElementProxy,
  deriveRenderOpts,
  ensureGlobalVariants,
  generateOnMutateForSpec,
  generateStateOnChangeProp,
  generateStateOnChangePropForCodeComponents,
  generateStateValueProp,
  get as $stateGet,
  hasVariant,
  initializeCodeComponentStates,
  initializePlasmicStates,
  makeFragment,
  omit,
  pick,
  renderPlasmicSlot,
  set as $stateSet,
  useCurrentUser,
  useDollarState,
  usePlasmicTranslator,
  useTrigger,
  wrapWithClassName
} from "@plasmicapp/react-web";
import {
  DataCtxReader as DataCtxReader__,
  useDataEnv,
  useGlobalActions
} from "@plasmicapp/host";

import SubcomponentButton from "../../SubcomponentButton"; // plasmic-import: ezhuRZvm_fH9/component
import SettingsEmailEntry from "../../SettingsEmailEntry"; // plasmic-import: oBy3j97i5seo/component
import SubcomponentToggleSwitch from "../../SubcomponentToggleSwitch"; // plasmic-import: A7wamfGEqfKZ/component

import "@plasmicapp/react-web/lib/plasmic.css";

import projectcss from "./plasmic.module.css"; // plasmic-import: nZeWKcn21KijvC7eT8B3c7/projectcss
import sty from "./PlasmicPageSettings.module.css"; // plasmic-import: gmYVz9YWamHt/css

import AtIcon from "./icons/PlasmicIcon__At"; // plasmic-import: 2m49ojpkQhGO/icon
import UserIcon from "./icons/PlasmicIcon__User"; // plasmic-import: hcR42vt5qkrz/icon

createPlasmicElementProxy;

export type PlasmicPageSettings__VariantMembers = {};
export type PlasmicPageSettings__VariantsArgs = {};
type VariantPropType = keyof PlasmicPageSettings__VariantsArgs;
export const PlasmicPageSettings__VariantProps = new Array<VariantPropType>();

export type PlasmicPageSettings__ArgsType = {};
type ArgPropType = keyof PlasmicPageSettings__ArgsType;
export const PlasmicPageSettings__ArgProps = new Array<ArgPropType>();

export type PlasmicPageSettings__OverridesType = {
  root?: Flex__<"div">;
  settingsHeader?: Flex__<"section">;
  emails?: Flex__<"div">;
  subButton?: Flex__<typeof SubcomponentButton>;
  activeSessions?: Flex__<"section">;
  notificationSettings?: Flex__<"section">;
};

export interface DefaultPageSettingsProps {
  className?: string;
}

const $$ = {};

function useNextRouter() {
  try {
    return useRouter();
  } catch {}
  return undefined;
}

function PlasmicPageSettings__RenderFunc(props: {
  variants: PlasmicPageSettings__VariantsArgs;
  args: PlasmicPageSettings__ArgsType;
  overrides: PlasmicPageSettings__OverridesType;
  forNode?: string;
}) {
  const { variants, overrides, forNode } = props;

  const args = React.useMemo(
    () =>
      Object.assign(
        {},
        Object.fromEntries(
          Object.entries(props.args).filter(([_, v]) => v !== undefined)
        )
      ),
    [props.args]
  );

  const $props = {
    ...args,
    ...variants
  };

  const __nextRouter = useNextRouter();
  const $ctx = useDataEnv?.() || {};
  const refsRef = React.useRef({});
  const $refs = refsRef.current;

  let [$queries, setDollarQueries] = React.useState<
    Record<string, ReturnType<typeof usePlasmicDataOp>>
  >({});

  const new$Queries: Record<string, ReturnType<typeof usePlasmicDataOp>> = {};
  if (Object.keys(new$Queries).some(k => new$Queries[k] !== $queries[k])) {
    setDollarQueries(new$Queries);

    $queries = new$Queries;
  }

  return (
    <div
      data-plasmic-name={"root"}
      data-plasmic-override={overrides.root}
      data-plasmic-root={true}
      data-plasmic-for-node={forNode}
      className={classNames(
        projectcss.all,
        projectcss.root_reset,
        projectcss.plasmic_default_styles,
        projectcss.plasmic_mixins,
        projectcss.plasmic_tokens,
        sty.root
      )}
    >
      <section
        data-plasmic-name={"settingsHeader"}
        data-plasmic-override={overrides.settingsHeader}
        className={classNames(projectcss.all, sty.settingsHeader)}
      >
        <h3
          className={classNames(
            projectcss.all,
            projectcss.h3,
            projectcss.__wab_text,
            sty.h3__iOYzW
          )}
        >
          {"Sign In & Contact"}
        </h3>
        <div
          data-plasmic-name={"emails"}
          data-plasmic-override={overrides.emails}
          className={classNames(projectcss.all, sty.emails)}
        >
          <div className={classNames(projectcss.all, sty.freeBox__yidSg)}>
            <AtIcon
              className={classNames(projectcss.all, sty.svg___01Lvg)}
              role={"img"}
            />

            <div
              className={classNames(
                projectcss.all,
                projectcss.__wab_text,
                sty.text__zbyB
              )}
            >
              {"Linked emails"}
            </div>
            <SubcomponentButton
              data-plasmic-name={"subButton"}
              data-plasmic-override={overrides.subButton}
              className={classNames("__wab_instance", sty.subButton)}
              disabledAnimation={"disabled"}
              disabledShakeText={``}
              endIcon={
                <svg
                  className={classNames(projectcss.all, sty.svg__n7Y)}
                  role={"img"}
                />
              }
              shape={"sharp"}
              size={"compact"}
              startIcon={
                <svg
                  className={classNames(projectcss.all, sty.svg__sm0Zk)}
                  role={"img"}
                />
              }
              styling={["nittiWColor"]}
              submitsForm={false}
            >
              {"Add +"}
            </SubcomponentButton>
          </div>
          <SettingsEmailEntry
            className={classNames(
              "__wab_instance",
              sty.settingsEmailEntry__l5Vq
            )}
            primary={true}
            verified={true}
          />
        </div>
      </section>
      <section
        data-plasmic-name={"activeSessions"}
        data-plasmic-override={overrides.activeSessions}
        className={classNames(projectcss.all, sty.activeSessions)}
      >
        <div className={classNames(projectcss.all, sty.freeBox__uhGiI)}>
          <div className={classNames(projectcss.all, sty.freeBox__vDdvu)}>
            <UserIcon
              className={classNames(projectcss.all, sty.svg__vXkBy)}
              role={"img"}
            />

            <div
              className={classNames(
                projectcss.all,
                projectcss.__wab_text,
                sty.text___2MKkA
              )}
            >
              {"Active Sessions"}
            </div>
          </div>
          <SettingsEmailEntry
            className={classNames(
              "__wab_instance",
              sty.settingsEmailEntry__vrHOo
            )}
            emailAddress={"This Device"}
          />
        </div>
      </section>
      <section
        data-plasmic-name={"notificationSettings"}
        data-plasmic-override={overrides.notificationSettings}
        className={classNames(projectcss.all, sty.notificationSettings)}
      >
        <h3
          className={classNames(
            projectcss.all,
            projectcss.h3,
            projectcss.__wab_text,
            sty.h3__s6ApU
          )}
        >
          {"Notifications"}
        </h3>
        <Stack__
          as={"div"}
          hasGap={true}
          className={classNames(projectcss.all, sty.freeBox__nlZw1)}
        >
          <div className={classNames(projectcss.all, sty.freeBox__yVmcq)}>
            <h5
              className={classNames(
                projectcss.all,
                projectcss.h5,
                projectcss.__wab_text,
                sty.h5__c1BUt
              )}
            >
              {"Email me surveys to provide platform feedback"}
            </h5>
            <div
              className={classNames(
                projectcss.all,
                projectcss.__wab_text,
                sty.text___9J8CK
              )}
            >
              {"Periodic emails to provide platform Feedback"}
            </div>
          </div>
          <SubcomponentToggleSwitch
            className={classNames(
              "__wab_instance",
              sty.subcomponentToggleSwitch___0AjZx
            )}
          />
        </Stack__>
        <Stack__
          as={"div"}
          hasGap={true}
          className={classNames(projectcss.all, sty.freeBox__ewPxl)}
        >
          <div className={classNames(projectcss.all, sty.freeBox__qphxa)}>
            <h5
              className={classNames(
                projectcss.all,
                projectcss.h5,
                projectcss.__wab_text,
                sty.h5__mgeT
              )}
            >
              {"Email me surveys to provide platform feedback"}
            </h5>
            <div
              className={classNames(
                projectcss.all,
                projectcss.__wab_text,
                sty.text__e4IDr
              )}
            >
              {"Periodic emails to provide platform Feedback"}
            </div>
          </div>
          <SubcomponentToggleSwitch
            className={classNames(
              "__wab_instance",
              sty.subcomponentToggleSwitch__urL3
            )}
          />
        </Stack__>
        <Stack__
          as={"div"}
          hasGap={true}
          className={classNames(projectcss.all, sty.freeBox__njBWu)}
        >
          <div className={classNames(projectcss.all, sty.freeBox__dkaqJ)}>
            <h5
              className={classNames(
                projectcss.all,
                projectcss.h5,
                projectcss.__wab_text,
                sty.h5__bM1Ht
              )}
            >
              {"Email me surveys to provide platform feedback"}
            </h5>
            <div
              className={classNames(
                projectcss.all,
                projectcss.__wab_text,
                sty.text__nRaZj
              )}
            >
              {"Periodic emails to provide platform Feedback"}
            </div>
          </div>
          <SubcomponentToggleSwitch
            className={classNames(
              "__wab_instance",
              sty.subcomponentToggleSwitch__zSscO
            )}
          />
        </Stack__>
      </section>
    </div>
  ) as React.ReactElement | null;
}

const PlasmicDescendants = {
  root: [
    "root",
    "settingsHeader",
    "emails",
    "subButton",
    "activeSessions",
    "notificationSettings"
  ],
  settingsHeader: ["settingsHeader", "emails", "subButton"],
  emails: ["emails", "subButton"],
  subButton: ["subButton"],
  activeSessions: ["activeSessions"],
  notificationSettings: ["notificationSettings"]
} as const;
type NodeNameType = keyof typeof PlasmicDescendants;
type DescendantsType<T extends NodeNameType> =
  (typeof PlasmicDescendants)[T][number];
type NodeDefaultElementType = {
  root: "div";
  settingsHeader: "section";
  emails: "div";
  subButton: typeof SubcomponentButton;
  activeSessions: "section";
  notificationSettings: "section";
};

type ReservedPropsType = "variants" | "args" | "overrides";
type NodeOverridesType<T extends NodeNameType> = Pick<
  PlasmicPageSettings__OverridesType,
  DescendantsType<T>
>;
type NodeComponentProps<T extends NodeNameType> =
  // Explicitly specify variants, args, and overrides as objects
  {
    variants?: PlasmicPageSettings__VariantsArgs;
    args?: PlasmicPageSettings__ArgsType;
    overrides?: NodeOverridesType<T>;
  } & Omit<PlasmicPageSettings__VariantsArgs, ReservedPropsType> & // Specify variants directly as props
    /* Specify args directly as props*/ Omit<
      PlasmicPageSettings__ArgsType,
      ReservedPropsType
    > &
    /* Specify overrides for each element directly as props*/ Omit<
      NodeOverridesType<T>,
      ReservedPropsType | VariantPropType | ArgPropType
    > &
    /* Specify props for the root element*/ Omit<
      Partial<React.ComponentProps<NodeDefaultElementType[T]>>,
      ReservedPropsType | VariantPropType | ArgPropType | DescendantsType<T>
    >;

function makeNodeComponent<NodeName extends NodeNameType>(nodeName: NodeName) {
  type PropsType = NodeComponentProps<NodeName> & { key?: React.Key };
  const func = function <T extends PropsType>(
    props: T & StrictProps<T, PropsType>
  ) {
    const { variants, args, overrides } = React.useMemo(
      () =>
        deriveRenderOpts(props, {
          name: nodeName,
          descendantNames: PlasmicDescendants[nodeName],
          internalArgPropNames: PlasmicPageSettings__ArgProps,
          internalVariantPropNames: PlasmicPageSettings__VariantProps
        }),
      [props, nodeName]
    );
    return PlasmicPageSettings__RenderFunc({
      variants,
      args,
      overrides,
      forNode: nodeName
    });
  };
  if (nodeName === "root") {
    func.displayName = "PlasmicPageSettings";
  } else {
    func.displayName = `PlasmicPageSettings.${nodeName}`;
  }
  return func;
}

export const PlasmicPageSettings = Object.assign(
  // Top-level PlasmicPageSettings renders the root element
  makeNodeComponent("root"),
  {
    // Helper components rendering sub-elements
    settingsHeader: makeNodeComponent("settingsHeader"),
    emails: makeNodeComponent("emails"),
    subButton: makeNodeComponent("subButton"),
    activeSessions: makeNodeComponent("activeSessions"),
    notificationSettings: makeNodeComponent("notificationSettings"),

    // Metadata about props expected for PlasmicPageSettings
    internalVariantProps: PlasmicPageSettings__VariantProps,
    internalArgProps: PlasmicPageSettings__ArgProps
  }
);

export default PlasmicPageSettings;
/* prettier-ignore-end */
