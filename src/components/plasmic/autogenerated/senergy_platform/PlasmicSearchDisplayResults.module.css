.root {
  display: flex;
  flex-direction: column;
  position: relative;
  width: 100%;
  height: auto;
  justify-content: flex-start;
  align-items: flex-start;
  justify-self: flex-start;
  min-height: 300px;
  max-width: 1500px;
  background: var(--token-xo_r2w5pebq-);
  box-shadow: none;
  min-width: 0;
  padding: 4%;
}
.subcomponentSlotHeading:global(.__wab_instance) {
  max-width: 100%;
  position: relative;
  margin-bottom: 16px;
}
.freeBox {
  display: grid;
  position: relative;
  grid-template-columns: repeat(auto-fill, minmax(325px, 1fr));
  width: auto;
  height: auto;
  max-width: 100%;
  grid-row-gap: var(--token-M1l4keX1sfKm);
  grid-column-gap: var(--token-M1l4keX1sfKm);
}
.searchTile:global(.__wab_instance) {
  max-width: 100%;
  position: relative;
}
