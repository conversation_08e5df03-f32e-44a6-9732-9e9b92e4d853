import type { CollectionConfig } from 'payload';

export const CaseStudies: CollectionConfig = {
  slug: 'case-studies',
  admin: {
    useAsTitle: 'title',
    group: 'Platform',
  },
  versions: {
    drafts: {
      autosave: true,
    },
  },
  access: {},

  fields: [
    {
      type: 'row',
      fields: [
        {
          name: 'title',
          label: 'Title',
          type: 'text',
          required: true,
        },
        {
          name: 'cover_image',
          label: 'Cover Image',
          type: 'relationship',
          relationTo: 'media',
        },
      ],
    },
    {
      name: 'description',
      label: 'Description',
      type: 'textarea',
      admin: {
        description: 'Description for the case study',
      },
    },
    {
      name: 'tags',
      label: 'Tags',
      type: 'select',
      hasMany: true,
      options: [
        'Web Development',
        'Mobile Development',
        'UI/UX Design',
        'Branding',
        'SEO',
        'Marketing',
      ],
      admin: {
        position: 'sidebar',
      },
    },
    {
      name: 'owner',
      type: 'relationship',
      relationTo: 'profile',
      hasMany: true,
      admin: {
        position: 'sidebar',
      },
    },
    {
      name: 'visible',
      type: 'checkbox',
      required: false,
      defaultValue: true,
      admin: {
        description: 'Whether the case study is visible to the public',
        position: 'sidebar',
      },
    },
    {
      name: 'content',
      label: 'Content',
      type: 'richText',
    },
  ],
};
