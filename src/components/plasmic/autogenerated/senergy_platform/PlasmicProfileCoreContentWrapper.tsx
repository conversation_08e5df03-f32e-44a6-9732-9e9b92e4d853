/* eslint-disable */
/* tslint:disable */
// @ts-nocheck
/* prettier-ignore-start */

/** @jsxRuntime classic */
/** @jsx createPlasmicElementProxy */
/** @jsxFrag React.Fragment */

// This class is auto-generated by Plasmic; please do not edit!
// Plasmic Project: nZeWKcn21KijvC7eT8B3c7
// Component: 6qIgV9tA-TG7

"use client";

import * as React from "react";

import Head from "next/head";
import Link, { LinkProps } from "next/link";
import { useRouter } from "next/navigation";

import {
  Flex as Flex__,
  MultiChoiceArg,
  PlasmicDataSourceContextProvider as PlasmicDataSourceContextProvider__,
  PlasmicIcon as PlasmicIcon__,
  PlasmicImg as PlasmicImg__,
  PlasmicLink as PlasmicLink__,
  PlasmicPageGuard as PlasmicPageGuard__,
  SingleBooleanChoiceArg,
  SingleC<PERSON>ice<PERSON>rg,
  Stack as Stack__,
  StrictProps,
  Trans as Trans__,
  classNames,
  createPlasmicElementProxy,
  deriveRenderOpts,
  ensureGlobalVariants,
  generateOnMutateForSpec,
  generateStateOnChangeProp,
  generateStateOnChangePropForCodeComponents,
  generateStateValueProp,
  get as $stateGet,
  hasVariant,
  initializeCodeComponentStates,
  initializePlasmicStates,
  makeFragment,
  omit,
  pick,
  renderPlasmicSlot,
  set as $stateSet,
  useCurrentUser,
  useDollarState,
  usePlasmicTranslator,
  useTrigger,
  wrapWithClassName
} from "@plasmicapp/react-web";
import {
  DataCtxReader as DataCtxReader__,
  useDataEnv,
  useGlobalActions
} from "@plasmicapp/host";

import "@plasmicapp/react-web/lib/plasmic.css";

import projectcss from "./plasmic.module.css"; // plasmic-import: nZeWKcn21KijvC7eT8B3c7/projectcss
import sty from "./********************************.module.css"; // plasmic-import: 6qIgV9tA-TG7/css

createPlasmicElementProxy;

export type ********************************__VariantMembers = {};
export type ********************************__VariantsArgs = {};
type VariantPropType = keyof ********************************__VariantsArgs;
export const ********************************__VariantProps =
  new Array<VariantPropType>();

export type ********************************__ArgsType = {
  children?: React.ReactNode;
};
type ArgPropType = keyof ********************************__ArgsType;
export const ********************************__ArgProps =
  new Array<ArgPropType>("children");

export type ********************************__OverridesType = {
  pageContentStyleBox?: Flex__<"div">;
  contentRestraintsBox?: Flex__<"section">;
};

export interface DefaultProfileCoreContentWrapperProps {
  children?: React.ReactNode;
  className?: string;
}

const $$ = {};

function useNextRouter() {
  try {
    return useRouter();
  } catch {}
  return undefined;
}

function ********************************__RenderFunc(props: {
  variants: ********************************__VariantsArgs;
  args: ********************************__ArgsType;
  overrides: ********************************__OverridesType;
  forNode?: string;
}) {
  const { variants, overrides, forNode } = props;

  const args = React.useMemo(
    () =>
      Object.assign(
        {},
        Object.fromEntries(
          Object.entries(props.args).filter(([_, v]) => v !== undefined)
        )
      ),
    [props.args]
  );

  const $props = {
    ...args,
    ...variants
  };

  const __nextRouter = useNextRouter();
  const $ctx = useDataEnv?.() || {};
  const refsRef = React.useRef({});
  const $refs = refsRef.current;

  let [$queries, setDollarQueries] = React.useState<
    Record<string, ReturnType<typeof usePlasmicDataOp>>
  >({});

  const new$Queries: Record<string, ReturnType<typeof usePlasmicDataOp>> = {};
  if (Object.keys(new$Queries).some(k => new$Queries[k] !== $queries[k])) {
    setDollarQueries(new$Queries);

    $queries = new$Queries;
  }

  return (
    <div
      data-plasmic-name={"pageContentStyleBox"}
      data-plasmic-override={overrides.pageContentStyleBox}
      data-plasmic-root={true}
      data-plasmic-for-node={forNode}
      className={classNames(
        projectcss.all,
        projectcss.root_reset,
        projectcss.plasmic_default_styles,
        projectcss.plasmic_mixins,
        projectcss.plasmic_tokens,
        sty.pageContentStyleBox
      )}
    >
      <section
        data-plasmic-name={"contentRestraintsBox"}
        data-plasmic-override={overrides.contentRestraintsBox}
        className={classNames(projectcss.all, sty.contentRestraintsBox)}
      >
        {renderPlasmicSlot({
          defaultContents: null,
          value: args.children
        })}
      </section>
    </div>
  ) as React.ReactElement | null;
}

const PlasmicDescendants = {
  pageContentStyleBox: ["pageContentStyleBox", "contentRestraintsBox"],
  contentRestraintsBox: ["contentRestraintsBox"]
} as const;
type NodeNameType = keyof typeof PlasmicDescendants;
type DescendantsType<T extends NodeNameType> =
  (typeof PlasmicDescendants)[T][number];
type NodeDefaultElementType = {
  pageContentStyleBox: "div";
  contentRestraintsBox: "section";
};

type ReservedPropsType = "variants" | "args" | "overrides";
type NodeOverridesType<T extends NodeNameType> = Pick<
  ********************************__OverridesType,
  DescendantsType<T>
>;
type NodeComponentProps<T extends NodeNameType> =
  // Explicitly specify variants, args, and overrides as objects
  {
    variants?: ********************************__VariantsArgs;
    args?: ********************************__ArgsType;
    overrides?: NodeOverridesType<T>;
  } & Omit<********************************__VariantsArgs, ReservedPropsType> & // Specify variants directly as props
    /* Specify args directly as props*/ Omit<
      ********************************__ArgsType,
      ReservedPropsType
    > &
    /* Specify overrides for each element directly as props*/ Omit<
      NodeOverridesType<T>,
      ReservedPropsType | VariantPropType | ArgPropType
    > &
    /* Specify props for the root element*/ Omit<
      Partial<React.ComponentProps<NodeDefaultElementType[T]>>,
      ReservedPropsType | VariantPropType | ArgPropType | DescendantsType<T>
    >;

function makeNodeComponent<NodeName extends NodeNameType>(nodeName: NodeName) {
  type PropsType = NodeComponentProps<NodeName> & { key?: React.Key };
  const func = function <T extends PropsType>(
    props: T & StrictProps<T, PropsType>
  ) {
    const { variants, args, overrides } = React.useMemo(
      () =>
        deriveRenderOpts(props, {
          name: nodeName,
          descendantNames: PlasmicDescendants[nodeName],
          internalArgPropNames: ********************************__ArgProps,
          internalVariantPropNames:
            ********************************__VariantProps
        }),
      [props, nodeName]
    );
    return ********************************__RenderFunc({
      variants,
      args,
      overrides,
      forNode: nodeName
    });
  };
  if (nodeName === "pageContentStyleBox") {
    func.displayName = "********************************";
  } else {
    func.displayName = `********************************.${nodeName}`;
  }
  return func;
}

export const ******************************** = Object.assign(
  // Top-level ******************************** renders the root element
  makeNodeComponent("pageContentStyleBox"),
  {
    // Helper components rendering sub-elements
    contentRestraintsBox: makeNodeComponent("contentRestraintsBox"),

    // Metadata about props expected for ********************************
    internalVariantProps: ********************************__VariantProps,
    internalArgProps: ********************************__ArgProps
  }
);

export default ********************************;
/* prettier-ignore-end */
