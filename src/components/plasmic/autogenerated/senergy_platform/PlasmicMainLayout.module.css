.root {
  display: flex;
  position: relative;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start;
  width: 100%;
  height: 100vh;
  background: var(--token-1AMvw6c2eIK7);
  overflow: auto;
  min-width: 0;
  padding: var(--token-sazGmnf7GWAk);
  margin: var(--token-sazGmnf7GWAk);
}
.pageMasterFormatting {
  display: flex;
  flex-direction: row;
  align-items: stretch;
  justify-content: flex-start;
  width: 100%;
  max-width: 100%;
  height: 100%;
  min-width: 0;
  min-height: 0;
  padding: var(--token-sazGmnf7GWAk);
}
.sideBarFormattingContainer {
  display: flex;
  position: relative;
  width: auto;
  flex-grow: 0;
  flex-shrink: 1;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start;
  height: 100%;
  left: auto;
  top: auto;
  z-index: 50;
  min-height: 0;
}
@media (max-width: 480px) {
  .sideBarFormattingContainer {
    left: auto;
    top: auto;
    display: none;
  }
}
.navigationSidebar:global(.__wab_instance) {
  position: relative;
  left: auto;
  top: auto;
  z-index: 1;
  margin-bottom: 0px;
  height: 100%;
  min-height: 100%;
}
.masterContainerForCentering {
  display: flex;
  position: relative;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start;
  width: 100%;
  height: 100%;
  max-width: 100%;
  box-shadow: none;
  margin-top: 0px;
  margin-left: 0px;
  left: auto;
  top: auto;
  z-index: 1;
  overflow: hidden;
  transition-property: all;
  transition-duration: 0.25s;
  min-width: 0;
  min-height: 0;
  -webkit-transition-property: all;
  -webkit-transition-duration: 0.25s;
  padding: 0px;
}
@media (max-width: 480px) {
  .masterContainerForCentering {
    left: auto;
    top: auto;
    display: none;
  }
}
.scrollableContentContainer {
  display: flex;
  position: relative;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start;
  width: 100%;
  max-width: 100%;
  overflow: auto;
  height: auto;
  min-width: 0;
  padding: 36px;
  margin: var(--token-sazGmnf7GWAk) var(--token-sazGmnf7GWAk) 0px;
}
.pageContent {
  display: flex;
  width: 100%;
  flex-direction: column;
  max-width: 1500px;
  margin-bottom: 0px;
  min-width: 0;
  padding: var(--token-sazGmnf7GWAk);
}
.main {
  position: relative;
  width: 100%;
  padding-left: 0;
  padding-right: 0;
  min-height: 200px;
  background: var(--token-PJrtmRUMFAJb);
  height: 100%;
  flex-direction: column;
  min-width: 0;
  display: none;
}
.main > :global(.__wab_flex-container) {
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-width: 0;
  margin-top: calc(0px - 8px);
  height: calc(100% + 8px);
}
.main > :global(.__wab_flex-container) > *,
.main > :global(.__wab_flex-container) > :global(.__wab_slot) > *,
.main > :global(.__wab_flex-container) > picture > img,
.main > :global(.__wab_flex-container) > :global(.__wab_slot) > picture > img {
  margin-top: 8px;
}
@media (max-width: 480px) {
  .main {
    display: flex;
  }
}
.textWrappingSection {
  display: flex;
  position: relative;
  width: auto;
  flex-direction: row;
  flex-wrap: wrap;
  align-items: stretch;
  justify-content: center;
  align-content: center;
  padding: var(--token-sazGmnf7GWAk) 4px;
}
.text__n0T1R {
  position: relative;
  width: auto;
  height: auto;
  max-width: 100%;
  font-family: var(--token-z1yrQVi72Nj1);
  font-size: var(--token-hZBgG8kTaig3);
}
.text__kht0C {
  position: relative;
  width: auto;
  height: auto;
  max-width: 100%;
  font-family: var(--token-z1yrQVi72Nj1);
  font-size: var(--token-hZBgG8kTaig3);
  text-align: right;
}
.h5 {
  position: relative;
  width: auto;
  height: auto;
  max-width: 70%;
  font-family: var(--token-IfwtVZvVvF7g);
  text-align: center;
  color: var(--token-yPq8Z3hhDPZH);
  font-weight: 700;
  transform: none;
  left: auto;
  top: auto;
  text-decoration-line: none;
  user-select: none;
  padding: var(--token-4Wrp9mDZCSCQ);
}
