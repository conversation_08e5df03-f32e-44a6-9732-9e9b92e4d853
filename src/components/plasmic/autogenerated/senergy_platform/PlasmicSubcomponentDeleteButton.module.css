.deleteButtonContainer {
  display: flex;
  position: relative;
  width: 32px;
  height: 32px;
  background: var(--token-K5FbAPSIIrXM);
  transition-property: all;
  transition-duration: 0.2s;
  cursor: pointer;
  flex-direction: column;
  align-items: flex-end;
  justify-content: center;
  -webkit-transition-property: all;
  -webkit-transition-duration: 0.2s;
}
.deleteButtonContainerinteractionVariants_hover {
  width: 100px;
  align-items: center;
  justify-content: space-between;
  display: flex;
  flex-direction: row;
  margin-bottom: 0px;
  padding-left: 10px;
}
.deleteButtonContainerinteractionVariants_pendingConfirmation {
  width: 100px;
  align-items: center;
  justify-content: space-between;
  display: flex;
  flex-direction: row;
  background: var(--token-vmreU-dg02sh);
  padding-left: 10px;
}
.deleteButtonContainerinteractionVariants_confirmed {
  background: var(--token-vmreU-dg02sh);
}
.deleteButtonContainerdisabled_disabled {
  background: var(--token-3OLw18f8JRN3);
}
.deleteButtonContainerdisabled_disabledHover {
  width: 100px;
  align-items: center;
  justify-content: space-between;
  display: flex;
  flex-direction: row;
  margin-bottom: 0px;
  padding-left: 10px;
  background: var(--token-3OLw18f8JRN3);
}
.deleteButtonContainerdisabled_shakeLeft {
  width: 100px;
  align-items: center;
  justify-content: space-between;
  display: flex;
  flex-direction: row;
  margin-bottom: 0px;
  padding-left: 10px;
  background: var(--token-3OLw18f8JRN3);
  transform: translateX(-5px) translateY(0px) translateZ(0px);
}
.deleteButtonContainerdisabled_shakeRight {
  width: 100px;
  align-items: center;
  justify-content: space-between;
  display: flex;
  flex-direction: row;
  margin-bottom: 0px;
  padding-left: 10px;
  background: var(--token-3OLw18f8JRN3);
  transform: translateX(5px) translateY(0px) translateZ(0px);
}
.text {
  position: relative;
  width: auto;
  height: auto;
  max-width: 100%;
  color: var(--token-1AMvw6c2eIK7);
  margin-right: 8px;
  left: auto;
  top: auto;
  pointer-events: none;
  display: none;
}
.textinteractionVariants_hover {
  margin-right: 4px;
  padding-bottom: 0px;
  text-align: right;
  white-space: pre;
  text-overflow: clip;
  overflow: hidden;
  display: block;
}
.textinteractionVariants_pendingConfirmation {
  margin-right: 0px;
  text-align: right;
  display: block;
}
.textdisabled_disabled {
  display: none;
}
.textdisabled_disabledHover {
  margin-right: 4px;
  padding-bottom: 0px;
  text-align: right;
  white-space: pre;
  text-overflow: clip;
  overflow: hidden;
  display: block;
}
.textdisabled_shakeLeft {
  margin-right: 4px;
  padding-bottom: 0px;
  text-align: right;
  white-space: pre;
  text-overflow: clip;
  overflow: hidden;
  display: block;
}
.textdisabled_shakeRight {
  margin-right: 4px;
  padding-bottom: 0px;
  text-align: right;
  white-space: pre;
  text-overflow: clip;
  overflow: hidden;
  display: block;
}
.xIcon {
  object-fit: cover;
  max-width: 100%;
  width: var(--token-rB3BKCgczoWa);
  height: var(--token-rB3BKCgczoWa);
  color: var(--token-1AMvw6c2eIK7);
  position: relative;
  left: auto;
  top: auto;
  margin-left: 9px;
  margin-right: 9px;
  flex-shrink: 0;
}
.xIconinteractionVariants_hover {
  margin-right: 9px;
  width: var(--token-rB3BKCgczoWa);
  height: var(--token-rB3BKCgczoWa);
  flex-shrink: 0;
}
.xIconinteractionVariants_pendingConfirmation {
  margin-right: 9px;
}
.xIcondisabled_disabledHover {
  margin-right: 9px;
  width: var(--token-rB3BKCgczoWa);
  height: var(--token-rB3BKCgczoWa);
  flex-shrink: 0;
}
.xIcondisabled_shakeLeft {
  margin-right: 9px;
  width: var(--token-rB3BKCgczoWa);
  height: var(--token-rB3BKCgczoWa);
  flex-shrink: 0;
}
.xIcondisabled_shakeRight {
  margin-right: 9px;
  width: var(--token-rB3BKCgczoWa);
  height: var(--token-rB3BKCgczoWa);
  flex-shrink: 0;
}
