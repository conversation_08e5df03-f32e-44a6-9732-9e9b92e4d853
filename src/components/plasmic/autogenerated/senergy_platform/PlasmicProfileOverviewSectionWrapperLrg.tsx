/* eslint-disable */
/* tslint:disable */
// @ts-nocheck
/* prettier-ignore-start */

/** @jsxRuntime classic */
/** @jsx createPlasmicElementProxy */
/** @jsxFrag React.Fragment */

// This class is auto-generated by Plasmic; please do not edit!
// Plasmic Project: nZeWKcn21KijvC7eT8B3c7
// Component: 6bj154JTcwB9

"use client";

import * as React from "react";

import Head from "next/head";
import Link, { LinkProps } from "next/link";
import { useRouter } from "next/navigation";

import {
  Flex as Flex__,
  MultiChoiceArg,
  PlasmicDataSourceContextProvider as PlasmicDataSourceContextProvider__,
  PlasmicIcon as PlasmicIcon__,
  PlasmicImg as PlasmicImg__,
  PlasmicLink as PlasmicLink__,
  PlasmicPageGuard as PlasmicPageGuard__,
  SingleBooleanChoiceArg,
  SingleChoiceArg,
  Stack as Stack__,
  StrictProps,
  Trans as Trans__,
  classNames,
  createPlasmicElementProxy,
  deriveRenderOpts,
  ensureGlobalVariants,
  generateOnMutateForSpec,
  generateStateOnChangeProp,
  generateStateOnChangePropForCodeComponents,
  generateStateValueProp,
  get as $stateGet,
  hasVariant,
  initializeCodeComponentStates,
  initializePlasmicStates,
  makeFragment,
  omit,
  pick,
  renderPlasmicSlot,
  set as $stateSet,
  useCurrentUser,
  useDollarState,
  usePlasmicTranslator,
  useTrigger,
  wrapWithClassName
} from "@plasmicapp/react-web";
import {
  DataCtxReader as DataCtxReader__,
  useDataEnv,
  useGlobalActions
} from "@plasmicapp/host";

import "@plasmicapp/react-web/lib/plasmic.css";

import projectcss from "./plasmic.module.css"; // plasmic-import: nZeWKcn21KijvC7eT8B3c7/projectcss
import sty from "./PlasmicProfileOverviewSectionWrapperLrg.module.css"; // plasmic-import: 6bj154JTcwB9/css

createPlasmicElementProxy;

export type PlasmicProfileOverviewSectionWrapperLrg__VariantMembers = {};
export type PlasmicProfileOverviewSectionWrapperLrg__VariantsArgs = {};
type VariantPropType =
  keyof PlasmicProfileOverviewSectionWrapperLrg__VariantsArgs;
export const PlasmicProfileOverviewSectionWrapperLrg__VariantProps =
  new Array<VariantPropType>();

export type PlasmicProfileOverviewSectionWrapperLrg__ArgsType = {
  overviewSlotFormatting2?: React.ReactNode;
};
type ArgPropType = keyof PlasmicProfileOverviewSectionWrapperLrg__ArgsType;
export const PlasmicProfileOverviewSectionWrapperLrg__ArgProps =
  new Array<ArgPropType>("overviewSlotFormatting2");

export type PlasmicProfileOverviewSectionWrapperLrg__OverridesType = {
  overviewSlotFormatting?: Flex__<"section">;
};

export interface DefaultProfileOverviewSectionWrapperLrgProps {
  overviewSlotFormatting2?: React.ReactNode;
  className?: string;
}

const $$ = {};

function useNextRouter() {
  try {
    return useRouter();
  } catch {}
  return undefined;
}

function PlasmicProfileOverviewSectionWrapperLrg__RenderFunc(props: {
  variants: PlasmicProfileOverviewSectionWrapperLrg__VariantsArgs;
  args: PlasmicProfileOverviewSectionWrapperLrg__ArgsType;
  overrides: PlasmicProfileOverviewSectionWrapperLrg__OverridesType;
  forNode?: string;
}) {
  const { variants, overrides, forNode } = props;

  const args = React.useMemo(
    () =>
      Object.assign(
        {},
        Object.fromEntries(
          Object.entries(props.args).filter(([_, v]) => v !== undefined)
        )
      ),
    [props.args]
  );

  const $props = {
    ...args,
    ...variants
  };

  const __nextRouter = useNextRouter();
  const $ctx = useDataEnv?.() || {};
  const refsRef = React.useRef({});
  const $refs = refsRef.current;

  let [$queries, setDollarQueries] = React.useState<
    Record<string, ReturnType<typeof usePlasmicDataOp>>
  >({});

  const new$Queries: Record<string, ReturnType<typeof usePlasmicDataOp>> = {};
  if (Object.keys(new$Queries).some(k => new$Queries[k] !== $queries[k])) {
    setDollarQueries(new$Queries);

    $queries = new$Queries;
  }

  return (
    <section
      data-plasmic-name={"overviewSlotFormatting"}
      data-plasmic-override={overrides.overviewSlotFormatting}
      data-plasmic-root={true}
      data-plasmic-for-node={forNode}
      className={classNames(
        projectcss.all,
        projectcss.root_reset,
        projectcss.plasmic_default_styles,
        projectcss.plasmic_mixins,
        projectcss.plasmic_tokens,
        sty.overviewSlotFormatting
      )}
    >
      {renderPlasmicSlot({
        defaultContents: null,
        value: args.overviewSlotFormatting2
      })}
    </section>
  ) as React.ReactElement | null;
}

const PlasmicDescendants = {
  overviewSlotFormatting: ["overviewSlotFormatting"]
} as const;
type NodeNameType = keyof typeof PlasmicDescendants;
type DescendantsType<T extends NodeNameType> =
  (typeof PlasmicDescendants)[T][number];
type NodeDefaultElementType = {
  overviewSlotFormatting: "section";
};

type ReservedPropsType = "variants" | "args" | "overrides";
type NodeOverridesType<T extends NodeNameType> = Pick<
  PlasmicProfileOverviewSectionWrapperLrg__OverridesType,
  DescendantsType<T>
>;
type NodeComponentProps<T extends NodeNameType> =
  // Explicitly specify variants, args, and overrides as objects
  {
    variants?: PlasmicProfileOverviewSectionWrapperLrg__VariantsArgs;
    args?: PlasmicProfileOverviewSectionWrapperLrg__ArgsType;
    overrides?: NodeOverridesType<T>;
  } & Omit< // Specify variants directly as props
    PlasmicProfileOverviewSectionWrapperLrg__VariantsArgs,
    ReservedPropsType
  > &
    /* Specify args directly as props*/ Omit<
      PlasmicProfileOverviewSectionWrapperLrg__ArgsType,
      ReservedPropsType
    > &
    /* Specify overrides for each element directly as props*/ Omit<
      NodeOverridesType<T>,
      ReservedPropsType | VariantPropType | ArgPropType
    > &
    /* Specify props for the root element*/ Omit<
      Partial<React.ComponentProps<NodeDefaultElementType[T]>>,
      ReservedPropsType | VariantPropType | ArgPropType | DescendantsType<T>
    >;

function makeNodeComponent<NodeName extends NodeNameType>(nodeName: NodeName) {
  type PropsType = NodeComponentProps<NodeName> & { key?: React.Key };
  const func = function <T extends PropsType>(
    props: T & StrictProps<T, PropsType>
  ) {
    const { variants, args, overrides } = React.useMemo(
      () =>
        deriveRenderOpts(props, {
          name: nodeName,
          descendantNames: PlasmicDescendants[nodeName],
          internalArgPropNames:
            PlasmicProfileOverviewSectionWrapperLrg__ArgProps,
          internalVariantPropNames:
            PlasmicProfileOverviewSectionWrapperLrg__VariantProps
        }),
      [props, nodeName]
    );
    return PlasmicProfileOverviewSectionWrapperLrg__RenderFunc({
      variants,
      args,
      overrides,
      forNode: nodeName
    });
  };
  if (nodeName === "overviewSlotFormatting") {
    func.displayName = "PlasmicProfileOverviewSectionWrapperLrg";
  } else {
    func.displayName = `PlasmicProfileOverviewSectionWrapperLrg.${nodeName}`;
  }
  return func;
}

export const PlasmicProfileOverviewSectionWrapperLrg = Object.assign(
  // Top-level PlasmicProfileOverviewSectionWrapperLrg renders the root element
  makeNodeComponent("overviewSlotFormatting"),
  {
    // Helper components rendering sub-elements

    // Metadata about props expected for PlasmicProfileOverviewSectionWrapperLrg
    internalVariantProps: PlasmicProfileOverviewSectionWrapperLrg__VariantProps,
    internalArgProps: PlasmicProfileOverviewSectionWrapperLrg__ArgProps
  }
);

export default PlasmicProfileOverviewSectionWrapperLrg;
/* prettier-ignore-end */
