.wrapper {
  display: flex;
  height: 100%;
  flex-shrink: 1;
  background: none;
  flex-direction: column;
  align-items: flex-start;
  justify-content: center;
  transition-property: all;
  transition-duration: 0.25s;
  width: var(--token-qEtVY7lTL0pW);
  min-height: 0;
  -webkit-transition-property: all;
  -webkit-transition-duration: 0.25s;
}
.wrapperactiveSubBar_search {
  width: var(--token-3mz1TnB2n28G);
}
.wrapperactiveSubBar_bookmarks {
  width: 515px;
}
.wrapperactiveSubBar_messages {
  width: 515px;
  justify-content: flex-end;
  align-items: flex-start;
}
.wrapperactiveSubBar_search_collapse_fullCollapsed {
  width: 356px;
}
.wrappercollapse_fullCollapsed_activeSubBar_bookmarks {
  width: 356px;
}
.wrapperactiveSubBar_search_collapse_hover {
  width: 356px;
}
.wrapperactiveSubBar_messages_collapse_fullCollapsed {
  width: 356px;
}
.wrapperactiveSubBar_bookmarks_collapse_hover {
  width: 356px;
}
.wrapperactiveSubBar_messages_collapse_hover {
  width: 356px;
}
.container {
  display: flex;
  height: 100%;
  flex-shrink: 1;
  background: none;
  transition-property: all;
  transition-duration: 0.25s;
  width: var(--token-qEtVY7lTL0pW);
  left: 0px;
  top: 0px;
  z-index: 1;
  position: fixed;
  justify-content: flex-end;
  min-height: 0;
  -webkit-transition-property: all;
  -webkit-transition-duration: 0.25s;
}
.containeractiveSubBar_search {
  width: var(--token-3mz1TnB2n28G);
}
.containeractiveSubBar_bookmarks {
  width: 515px;
}
.containeractiveSubBar_messages {
  width: 515px;
}
.containeractiveSubBar_search_collapse_fullCollapsed {
  width: 356px;
}
.containercollapse_fullCollapsed_activeSubBar_bookmarks {
  width: 356px;
}
.containeractiveSubBar_search_collapse_hover {
  width: 356px;
}
.containeractiveSubBar_messages_collapse_fullCollapsed {
  width: 356px;
}
.containeractiveSubBar_bookmarks_collapse_hover {
  width: 356px;
}
.containeractiveSubBar_messages_collapse_hover {
  width: 356px;
}
.navMainMenuContainer {
  box-shadow: 0px 4px 16px -8px rgba(0, 0, 0, 0.2);
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: space-between;
  width: var(--token-qEtVY7lTL0pW);
  height: 100%;
  background: var(--token-F0-tInDly4PE);
  position: fixed;
  transition-duration: 0.25s;
  transition-property: all;
  left: 0px;
  top: 0px;
  z-index: 1;
  max-width: 100%;
  flex-shrink: 0;
  min-height: 0;
  -webkit-transition-property: all;
  -webkit-transition-duration: 0.25s;
  padding: var(--token-sazGmnf7GWAk) var(--token-sazGmnf7GWAk)
    var(--token-sazGmnf7GWAk) 0px;
  margin: 0px 0px 2em var(--token-sazGmnf7GWAk);
}
.navMainMenuContaineractiveSubBar_search {
  width: 215px;
  flex-shrink: 0;
}
.navMainMenuContaineractiveSubBar_bookmarks {
  width: 215px;
  flex-shrink: 0;
}
.navMainMenuContaineractiveSubBar_messages {
  width: var(--token-FtzoSQMzGLh-);
  flex-shrink: 0;
}
.navMainMenuContainercollapse_fullCollapsed {
  width: 56px;
  padding-left: 1px;
  flex-shrink: 0;
}
.navMainMenuContainercollapse_hover {
  width: 215px;
  flex-shrink: 0;
}
.navigationInteractions {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start;
  width: 100%;
  height: auto;
  max-width: 300px;
  min-width: 0;
  padding: 16px 0px 0px;
}
.navigationLogoStack:global(.__wab_instance) {
  position: relative;
  max-width: 100%;
}
.sideBarLinks {
  display: flex;
  position: relative;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start;
  width: 100%;
  height: auto;
  max-width: 100%;
  left: auto;
  top: auto;
  flex-wrap: nowrap;
  align-content: stretch;
  z-index: 1;
  min-width: 0;
}
.navigationTopButtonAssembly:global(.__wab_instance) {
  max-width: 100%;
  position: relative;
}
.workingInfo {
  display: flex;
  position: relative;
  width: 100%;
  height: auto;
  flex-direction: column;
  align-items: flex-start;
  justify-content: center;
  flex-shrink: 1;
  margin-left: 0px;
  left: auto;
  top: auto;
  padding-left: 0px;
  padding-bottom: 0%;
  min-width: 0;
}
.navigationBottomButtonAssembly:global(.__wab_instance) {
  max-width: 100%;
  position: relative;
  margin-bottom: 8px;
}
.subMenuInformation {
  display: flex;
  position: relative;
  flex-direction: column;
  align-items: flex-start;
  justify-content: flex-start;
  height: 100%;
  top: auto;
  width: 300px;
  left: auto;
  transform: translateX(0px) translateY(0px) translateZ(0px);
  opacity: 0;
  transition-property: opacity;
  transition-duration: 0.25s;
  flex-shrink: 0;
  min-height: 0;
  -webkit-transition-property: opacity;
  -webkit-transition-duration: 0.25s;
  padding: var(--token-sazGmnf7GWAk);
}
.subMenuInformationactiveSubBar_search {
  opacity: 1;
}
.subMenuInformationactiveSubBar_bookmarks {
  opacity: 1;
}
.subMenuInformationactiveSubBar_messages {
  opacity: 1;
}
.navigationSubBarMenus:global(.__wab_instance) {
  max-width: 100%;
  position: relative;
}
