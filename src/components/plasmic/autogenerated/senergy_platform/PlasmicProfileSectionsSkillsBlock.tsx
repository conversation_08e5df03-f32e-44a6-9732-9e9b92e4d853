/* eslint-disable */
/* tslint:disable */
// @ts-nocheck
/* prettier-ignore-start */

/** @jsxRuntime classic */
/** @jsx createPlasmicElementProxy */
/** @jsxFrag React.Fragment */

// This class is auto-generated by Plasmic; please do not edit!
// Plasmic Project: nZeWKcn21KijvC7eT8B3c7
// Component: 9Tp3HjHzEzcX

"use client";

import * as React from "react";

import Head from "next/head";
import Link, { LinkProps } from "next/link";
import { useRouter } from "next/navigation";

import {
  Flex as Flex__,
  MultiChoiceArg,
  PlasmicDataSourceContextProvider as PlasmicDataSourceContextProvider__,
  PlasmicIcon as PlasmicIcon__,
  PlasmicImg as PlasmicImg__,
  PlasmicLink as PlasmicLink__,
  PlasmicPageGuard as PlasmicPageGuard__,
  SingleBooleanChoiceArg,
  SingleC<PERSON><PERSON><PERSON>rg,
  Stack as Stack__,
  StrictProps,
  Trans as Trans__,
  classNames,
  createPlasmicElementProxy,
  deriveRenderOpts,
  ensureGlobalVariants,
  generateOnMutateForSpec,
  generateStateOnChangeProp,
  generateStateOnChangePropForCodeComponents,
  generateStateValueProp,
  get as $stateGet,
  hasVariant,
  initializeCodeComponentStates,
  initializePlasmicStates,
  makeFragment,
  omit,
  pick,
  renderPlasmicSlot,
  set as $stateSet,
  useCurrentUser,
  useDollarState,
  usePlasmicTranslator,
  useTrigger,
  wrapWithClassName
} from "@plasmicapp/react-web";
import {
  DataCtxReader as DataCtxReader__,
  useDataEnv,
  useGlobalActions
} from "@plasmicapp/host";

import ProfileSectionsProfileSectionHeading from "../../ProfileSectionsProfileSectionHeading"; // plasmic-import: Uz656rZzIxzC/component
import ProfileTileSkillsTile from "../../ProfileTileSkillsTile"; // plasmic-import: V4zwOqV9lT52/component

import "@plasmicapp/react-web/lib/plasmic.css";

import projectcss from "./plasmic.module.css"; // plasmic-import: nZeWKcn21KijvC7eT8B3c7/projectcss
import sty from "./PlasmicProfileSectionsSkillsBlock.module.css"; // plasmic-import: 9Tp3HjHzEzcX/css

createPlasmicElementProxy;

export type PlasmicProfileSectionsSkillsBlock__VariantMembers = {
  overviewTile: "overviewTile";
  editable: "editable";
};
export type PlasmicProfileSectionsSkillsBlock__VariantsArgs = {
  overviewTile?: SingleBooleanChoiceArg<"overviewTile">;
  editable?: SingleBooleanChoiceArg<"editable">;
};
type VariantPropType = keyof PlasmicProfileSectionsSkillsBlock__VariantsArgs;
export const PlasmicProfileSectionsSkillsBlock__VariantProps =
  new Array<VariantPropType>("overviewTile", "editable");

export type PlasmicProfileSectionsSkillsBlock__ArgsType = {};
type ArgPropType = keyof PlasmicProfileSectionsSkillsBlock__ArgsType;
export const PlasmicProfileSectionsSkillsBlock__ArgProps =
  new Array<ArgPropType>();

export type PlasmicProfileSectionsSkillsBlock__OverridesType = {
  skillsSection?: Flex__<"div">;
  profileSectionsProfileSectionHeading?: Flex__<
    typeof ProfileSectionsProfileSectionHeading
  >;
  skillsContentSection?: Flex__<"div">;
  profileTileSkillsTile?: Flex__<typeof ProfileTileSkillsTile>;
};

export interface DefaultProfileSectionsSkillsBlockProps {
  overviewTile?: SingleBooleanChoiceArg<"overviewTile">;
  editable?: SingleBooleanChoiceArg<"editable">;
  className?: string;
}

const $$ = {};

function useNextRouter() {
  try {
    return useRouter();
  } catch {}
  return undefined;
}

function PlasmicProfileSectionsSkillsBlock__RenderFunc(props: {
  variants: PlasmicProfileSectionsSkillsBlock__VariantsArgs;
  args: PlasmicProfileSectionsSkillsBlock__ArgsType;
  overrides: PlasmicProfileSectionsSkillsBlock__OverridesType;
  forNode?: string;
}) {
  const { variants, overrides, forNode } = props;

  const args = React.useMemo(
    () =>
      Object.assign(
        {},
        Object.fromEntries(
          Object.entries(props.args).filter(([_, v]) => v !== undefined)
        )
      ),
    [props.args]
  );

  const $props = {
    ...args,
    ...variants
  };

  const __nextRouter = useNextRouter();
  const $ctx = useDataEnv?.() || {};
  const refsRef = React.useRef({});
  const $refs = refsRef.current;

  let [$queries, setDollarQueries] = React.useState<
    Record<string, ReturnType<typeof usePlasmicDataOp>>
  >({});
  const stateSpecs: Parameters<typeof useDollarState>[0] = React.useMemo(
    () => [
      {
        path: "overviewTile",
        type: "private",
        variableType: "variant",
        initFunc: ({ $props, $state, $queries, $ctx }) => $props.overviewTile
      },
      {
        path: "editable",
        type: "private",
        variableType: "variant",
        initFunc: ({ $props, $state, $queries, $ctx }) => $props.editable
      }
    ],
    [$props, $ctx, $refs]
  );
  const $state = useDollarState(stateSpecs, {
    $props,
    $ctx,
    $queries: $queries,
    $refs
  });

  const new$Queries: Record<string, ReturnType<typeof usePlasmicDataOp>> = {};
  if (Object.keys(new$Queries).some(k => new$Queries[k] !== $queries[k])) {
    setDollarQueries(new$Queries);

    $queries = new$Queries;
  }

  return (
    <div
      data-plasmic-name={"skillsSection"}
      data-plasmic-override={overrides.skillsSection}
      data-plasmic-root={true}
      data-plasmic-for-node={forNode}
      className={classNames(
        projectcss.all,
        projectcss.root_reset,
        projectcss.plasmic_default_styles,
        projectcss.plasmic_mixins,
        projectcss.plasmic_tokens,
        sty.skillsSection,
        {
          [sty.skillsSectioneditable]: hasVariant(
            $state,
            "editable",
            "editable"
          ),
          [sty.skillsSectionoverviewTile]: hasVariant(
            $state,
            "overviewTile",
            "overviewTile"
          )
        }
      )}
    >
      <ProfileSectionsProfileSectionHeading
        data-plasmic-name={"profileSectionsProfileSectionHeading"}
        data-plasmic-override={overrides.profileSectionsProfileSectionHeading}
        className={classNames(
          "__wab_instance",
          sty.profileSectionsProfileSectionHeading,
          {
            [sty.profileSectionsProfileSectionHeadingeditable]: hasVariant(
              $state,
              "editable",
              "editable"
            ),
            [sty.profileSectionsProfileSectionHeadingoverviewTile]: hasVariant(
              $state,
              "overviewTile",
              "overviewTile"
            )
          }
        )}
        editable={hasVariant($state, "editable", "editable") ? true : undefined}
        overviewGrid={
          hasVariant($state, "overviewTile", "overviewTile") ? true : undefined
        }
      >
        {"Skills"}
      </ProfileSectionsProfileSectionHeading>
      <div
        data-plasmic-name={"skillsContentSection"}
        data-plasmic-override={overrides.skillsContentSection}
        className={classNames(projectcss.all, sty.skillsContentSection, {
          [sty.skillsContentSectionoverviewTile]: hasVariant(
            $state,
            "overviewTile",
            "overviewTile"
          )
        })}
      >
        <ProfileTileSkillsTile
          data-plasmic-name={"profileTileSkillsTile"}
          data-plasmic-override={overrides.profileTileSkillsTile}
          className={classNames("__wab_instance", sty.profileTileSkillsTile, {
            [sty.profileTileSkillsTileeditable]: hasVariant(
              $state,
              "editable",
              "editable"
            ),
            [sty.profileTileSkillsTileoverviewTile]: hasVariant(
              $state,
              "overviewTile",
              "overviewTile"
            )
          })}
          editable={
            hasVariant($state, "editable", "editable") ? true : undefined
          }
          overviewGrid={
            hasVariant($state, "overviewTile", "overviewTile")
              ? true
              : undefined
          }
        />
      </div>
    </div>
  ) as React.ReactElement | null;
}

const PlasmicDescendants = {
  skillsSection: [
    "skillsSection",
    "profileSectionsProfileSectionHeading",
    "skillsContentSection",
    "profileTileSkillsTile"
  ],
  profileSectionsProfileSectionHeading: [
    "profileSectionsProfileSectionHeading"
  ],
  skillsContentSection: ["skillsContentSection", "profileTileSkillsTile"],
  profileTileSkillsTile: ["profileTileSkillsTile"]
} as const;
type NodeNameType = keyof typeof PlasmicDescendants;
type DescendantsType<T extends NodeNameType> =
  (typeof PlasmicDescendants)[T][number];
type NodeDefaultElementType = {
  skillsSection: "div";
  profileSectionsProfileSectionHeading: typeof ProfileSectionsProfileSectionHeading;
  skillsContentSection: "div";
  profileTileSkillsTile: typeof ProfileTileSkillsTile;
};

type ReservedPropsType = "variants" | "args" | "overrides";
type NodeOverridesType<T extends NodeNameType> = Pick<
  PlasmicProfileSectionsSkillsBlock__OverridesType,
  DescendantsType<T>
>;
type NodeComponentProps<T extends NodeNameType> =
  // Explicitly specify variants, args, and overrides as objects
  {
    variants?: PlasmicProfileSectionsSkillsBlock__VariantsArgs;
    args?: PlasmicProfileSectionsSkillsBlock__ArgsType;
    overrides?: NodeOverridesType<T>;
  } & Omit<PlasmicProfileSectionsSkillsBlock__VariantsArgs, ReservedPropsType> & // Specify variants directly as props
    /* Specify args directly as props*/ Omit<
      PlasmicProfileSectionsSkillsBlock__ArgsType,
      ReservedPropsType
    > &
    /* Specify overrides for each element directly as props*/ Omit<
      NodeOverridesType<T>,
      ReservedPropsType | VariantPropType | ArgPropType
    > &
    /* Specify props for the root element*/ Omit<
      Partial<React.ComponentProps<NodeDefaultElementType[T]>>,
      ReservedPropsType | VariantPropType | ArgPropType | DescendantsType<T>
    >;

function makeNodeComponent<NodeName extends NodeNameType>(nodeName: NodeName) {
  type PropsType = NodeComponentProps<NodeName> & { key?: React.Key };
  const func = function <T extends PropsType>(
    props: T & StrictProps<T, PropsType>
  ) {
    const { variants, args, overrides } = React.useMemo(
      () =>
        deriveRenderOpts(props, {
          name: nodeName,
          descendantNames: PlasmicDescendants[nodeName],
          internalArgPropNames: PlasmicProfileSectionsSkillsBlock__ArgProps,
          internalVariantPropNames:
            PlasmicProfileSectionsSkillsBlock__VariantProps
        }),
      [props, nodeName]
    );
    return PlasmicProfileSectionsSkillsBlock__RenderFunc({
      variants,
      args,
      overrides,
      forNode: nodeName
    });
  };
  if (nodeName === "skillsSection") {
    func.displayName = "PlasmicProfileSectionsSkillsBlock";
  } else {
    func.displayName = `PlasmicProfileSectionsSkillsBlock.${nodeName}`;
  }
  return func;
}

export const PlasmicProfileSectionsSkillsBlock = Object.assign(
  // Top-level PlasmicProfileSectionsSkillsBlock renders the root element
  makeNodeComponent("skillsSection"),
  {
    // Helper components rendering sub-elements
    profileSectionsProfileSectionHeading: makeNodeComponent(
      "profileSectionsProfileSectionHeading"
    ),
    skillsContentSection: makeNodeComponent("skillsContentSection"),
    profileTileSkillsTile: makeNodeComponent("profileTileSkillsTile"),

    // Metadata about props expected for PlasmicProfileSectionsSkillsBlock
    internalVariantProps: PlasmicProfileSectionsSkillsBlock__VariantProps,
    internalArgProps: PlasmicProfileSectionsSkillsBlock__ArgProps
  }
);

export default PlasmicProfileSectionsSkillsBlock;
/* prettier-ignore-end */
