.profileBannerContianer {
  box-shadow: 0px 4px 4px 0px var(--token-p09LDPmbF81_);
  display: flex;
  flex-direction: column;
  position: relative;
  width: 100%;
  height: auto;
  justify-content: center;
  align-items: stretch;
  background: var(--token-xo_r2w5pebq-);
  min-height: 275px;
  align-content: stretch;
  overflow: visible;
  transition-property: all;
  transition-duration: 0.2s;
  min-width: 0;
  -webkit-transition-property: all;
  -webkit-transition-duration: 0.2s;
  padding: 0px 36px 0px 32px;
}
.profileBannerContianercollapsed {
  min-height: 100px;
}
@media (max-width: 1200px) {
  .profileBannerContianercollapsed {
    height: auto;
    flex-wrap: wrap;
    align-items: stretch;
  }
}
.mainInformationContainer {
  display: flex;
  position: relative;
  flex-direction: row;
  width: 100%;
  height: auto;
  max-width: 100%;
  min-width: 0;
  padding: var(--token-sazGmnf7GWAk);
  margin: var(--token-sazGmnf7GWAk);
}
.mainInformationContainer > :global(.__wab_flex-container) {
  flex-direction: row;
  align-items: stretch;
  justify-content: space-between;
  flex-wrap: nowrap;
  align-content: stretch;
  min-width: 0;
  margin-left: calc(0px - var(--token-4Wrp9mDZCSCQ));
  width: calc(100% + var(--token-4Wrp9mDZCSCQ));
}
.mainInformationContainer > :global(.__wab_flex-container) > *,
.mainInformationContainer
  > :global(.__wab_flex-container)
  > :global(.__wab_slot)
  > *,
.mainInformationContainer > :global(.__wab_flex-container) > picture > img,
.mainInformationContainer
  > :global(.__wab_flex-container)
  > :global(.__wab_slot)
  > picture
  > img {
  margin-left: var(--token-4Wrp9mDZCSCQ);
}
.mainInformationContainercollapsed > :global(.__wab_flex-container) {
  justify-content: space-between;
  align-items: center;
  margin-left: calc(0px - var(--token-sazGmnf7GWAk));
  width: calc(100% + var(--token-sazGmnf7GWAk));
  margin-top: calc(0px - 0px);
  height: calc(100% + 0px);
}
.mainInformationContainercollapsed > :global(.__wab_flex-container) > *,
.mainInformationContainercollapsed
  > :global(.__wab_flex-container)
  > :global(.__wab_slot)
  > *,
.mainInformationContainercollapsed
  > :global(.__wab_flex-container)
  > picture
  > img,
.mainInformationContainercollapsed
  > :global(.__wab_flex-container)
  > :global(.__wab_slot)
  > picture
  > img {
  margin-left: var(--token-sazGmnf7GWAk);
  margin-top: 0px;
}
.leftSideBanner {
  display: flex;
  flex-direction: column;
  width: auto;
  height: 100%;
  max-width: 100%;
  padding-right: 48px;
  margin-right: 0px;
  min-height: 0;
}
.leftSideBanner > :global(.__wab_flex-container) {
  flex-direction: column;
  align-items: flex-start;
  justify-content: flex-start;
  min-height: 0;
  margin-top: calc(0px - var(--token-sazGmnf7GWAk));
  height: calc(100% + var(--token-sazGmnf7GWAk));
}
.leftSideBanner > :global(.__wab_flex-container) > *,
.leftSideBanner > :global(.__wab_flex-container) > :global(.__wab_slot) > *,
.leftSideBanner > :global(.__wab_flex-container) > picture > img,
.leftSideBanner
  > :global(.__wab_flex-container)
  > :global(.__wab_slot)
  > picture
  > img {
  margin-top: var(--token-sazGmnf7GWAk);
}
.leftSideBannercollapsed {
  display: flex;
  flex-direction: row;
}
.leftSideBannercollapsed > :global(.__wab_flex-container) {
  align-items: center;
  justify-content: flex-start;
  flex-direction: row;
  margin-left: calc(0px - var(--token-4Wrp9mDZCSCQ));
  width: calc(100% + var(--token-4Wrp9mDZCSCQ));
  margin-top: calc(0px - 0px);
  height: calc(100% + 0px);
}
.leftSideBannercollapsed > :global(.__wab_flex-container) > *,
.leftSideBannercollapsed
  > :global(.__wab_flex-container)
  > :global(.__wab_slot)
  > *,
.leftSideBannercollapsed > :global(.__wab_flex-container) > picture > img,
.leftSideBannercollapsed
  > :global(.__wab_flex-container)
  > :global(.__wab_slot)
  > picture
  > img {
  margin-left: var(--token-4Wrp9mDZCSCQ);
  margin-top: 0px;
}
.profileCoreProfileImage:global(.__wab_instance) {
  max-width: 100%;
  position: relative;
}
.freeBox {
  display: flex;
  position: relative;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start;
  width: 100%;
  height: auto;
  max-width: 100%;
  margin-top: var(--token-sazGmnf7GWAk) !important;
  margin-right: var(--token-sazGmnf7GWAk);
  margin-bottom: 0px;
  margin-left: var(--token-sazGmnf7GWAk);
  min-width: 0;
  padding: var(--token-sazGmnf7GWAk);
}
.freeBoxcollapsed {
  margin-bottom: 0px;
  margin-left: calc(8px + var(--token-4Wrp9mDZCSCQ)) !important;
  margin-top: var(--token-sazGmnf7GWAk) !important;
}
.preferredNameStack {
  display: flex;
  flex-direction: row;
  width: 100%;
  height: auto;
  max-width: 100%;
  min-width: 0;
  padding: var(--token-sazGmnf7GWAk);
}
.preferredNameStack > :global(.__wab_flex-container) {
  flex-direction: row;
  align-items: stretch;
  justify-content: flex-start;
  min-width: 0;
  margin-left: calc(0px - var(--token-4Wrp9mDZCSCQ));
  width: calc(100% + var(--token-4Wrp9mDZCSCQ));
}
.preferredNameStack > :global(.__wab_flex-container) > *,
.preferredNameStack > :global(.__wab_flex-container) > :global(.__wab_slot) > *,
.preferredNameStack > :global(.__wab_flex-container) > picture > img,
.preferredNameStack
  > :global(.__wab_flex-container)
  > :global(.__wab_slot)
  > picture
  > img {
  margin-left: var(--token-4Wrp9mDZCSCQ);
}
.preferredNameStackeditable {
  width: auto;
}
.preferredName:global(.__wab_instance) {
  max-width: 100%;
  position: relative;
  width: auto;
}
.preferredNameeditable:global(.__wab_instance) {
  width: 100%;
  min-width: 0;
}
.bottomAdditionalInformation {
  display: flex;
  position: relative;
  width: 100%;
  flex-direction: row;
  min-width: 0;
  padding: var(--token-sazGmnf7GWAk);
}
.bottomAdditionalInformation > :global(.__wab_flex-container) {
  flex-direction: row;
  flex-wrap: wrap;
  align-items: stretch;
  min-width: 0;
  margin-left: calc(0px - var(--token-8HwyO4mnTWdf));
  width: calc(100% + var(--token-8HwyO4mnTWdf));
  margin-top: calc(0px - var(--token-ptnlAHOp9Vq0));
  height: calc(100% + var(--token-ptnlAHOp9Vq0));
}
.bottomAdditionalInformation > :global(.__wab_flex-container) > *,
.bottomAdditionalInformation
  > :global(.__wab_flex-container)
  > :global(.__wab_slot)
  > *,
.bottomAdditionalInformation > :global(.__wab_flex-container) > picture > img,
.bottomAdditionalInformation
  > :global(.__wab_flex-container)
  > :global(.__wab_slot)
  > picture
  > img {
  margin-left: var(--token-8HwyO4mnTWdf);
  margin-top: var(--token-ptnlAHOp9Vq0);
}
.bottomAdditionalInformationcollapsed > :global(.__wab_flex-container) {
  justify-content: flex-start;
  align-content: flex-start;
}
.location:global(.__wab_instance) {
  max-width: 100%;
  position: relative;
}
.iconSpot {
  position: relative;
  object-fit: cover;
  max-width: 100%;
  margin-right: 0;
  width: 100%;
  height: 100%;
  min-width: 0;
  min-height: 0;
}
.displayWorkingHours:global(.__wab_instance) {
  max-width: 100%;
  position: relative;
}
.displayWorkingHourseditable:global(.__wab_instance) {
  display: none;
}
.iconSpot2 {
  position: relative;
  object-fit: cover;
  max-width: 100%;
  margin-right: 0;
  width: 100%;
  height: 100%;
  min-width: 0;
  min-height: 0;
}
.startTimeInput:global(.__wab_instance) {
  max-width: 100%;
  position: relative;
  display: none;
}
.startTimeInputeditable:global(.__wab_instance) {
  display: flex;
}
.iconSpot4 {
  position: relative;
  object-fit: cover;
  max-width: 100%;
  margin-right: 0;
  width: 100%;
  height: 100%;
  min-width: 0;
  min-height: 0;
}
.endTimeInput:global(.__wab_instance) {
  max-width: 100%;
  position: relative;
  display: none;
}
.endTimeInputeditable:global(.__wab_instance) {
  display: flex;
}
.iconSpot3 {
  position: relative;
  object-fit: cover;
  max-width: 100%;
  margin-right: 0;
  width: 100%;
  height: 100%;
  min-width: 0;
  min-height: 0;
}
.languages {
  position: relative;
  flex-direction: row;
  width: auto;
  height: auto;
  max-width: 100%;
  margin-top: var(--token-sazGmnf7GWAk) !important;
  margin-right: var(--token-sazGmnf7GWAk);
  margin-bottom: var(--token-sazGmnf7GWAk);
  margin-left: var(--token-sazGmnf7GWAk) !important;
  display: none;
  padding: var(--token-4Wrp9mDZCSCQ) 0px var(--token-4Wrp9mDZCSCQ)
    var(--token-4Wrp9mDZCSCQ);
}
.languages > :global(.__wab_flex-container) {
  flex-direction: row;
  align-items: center;
  justify-content: flex-end;
  margin-left: calc(0px - var(--token-4Wrp9mDZCSCQ));
  width: calc(100% + var(--token-4Wrp9mDZCSCQ));
}
.languages > :global(.__wab_flex-container) > *,
.languages > :global(.__wab_flex-container) > :global(.__wab_slot) > *,
.languages > :global(.__wab_flex-container) > picture > img,
.languages
  > :global(.__wab_flex-container)
  > :global(.__wab_slot)
  > picture
  > img {
  margin-left: var(--token-4Wrp9mDZCSCQ);
}
.languagescollapsed {
  margin-left: var(--token-sazGmnf7GWAk) !important;
  margin-top: var(--token-sazGmnf7GWAk) !important;
  display: none;
}
.languageseditable {
  margin-left: var(--token-sazGmnf7GWAk) !important;
  margin-top: var(--token-sazGmnf7GWAk) !important;
}
.langauge:global(.__wab_instance) {
  max-width: 100%;
  position: relative;
}
.langaugeeditable:global(.__wab_instance) {
  display: flex;
}
.text___3MOdG {
  position: relative;
  width: 100%;
  height: auto;
  max-width: 100%;
  padding-top: 0px;
  white-space: pre;
  min-width: 0;
}
.texteditable___3MOdGwSi2Y {
  display: none;
}
.svg__vSkg3 {
  position: relative;
  object-fit: cover;
  max-width: 100%;
  width: var(--token-j0qnbpah5w9U);
  height: var(--token-j0qnbpah5w9U);
  margin-right: 0px;
  flex-shrink: 0;
}
.rightSideBanner {
  display: flex;
  position: relative;
  flex-direction: column;
  align-items: flex-end;
  justify-content: space-between;
  width: auto;
  height: 100%;
  max-width: 100%;
  min-height: 0;
}
.rightSideBannercollapsed {
  align-items: flex-end;
  justify-content: center;
}
.elevatorPitch {
  display: flex;
  position: relative;
  flex-direction: column;
  align-items: flex-end;
  justify-content: flex-start;
  width: 100%;
  height: auto;
  max-width: 70%;
  min-width: 0;
  padding: var(--token-sazGmnf7GWAk);
  margin: var(--token-sazGmnf7GWAk);
}
.text__h8GgH {
  position: relative;
  width: 100%;
  height: auto;
  max-width: 100%;
  margin-bottom: 1px;
  white-space: pre-wrap;
  font-size: var(--token-AMP1angFPBtf);
  text-align: right;
  min-width: 0;
}
@media (max-width: 1200px) {
  .text__h8GgH {
    font-size: var(--token-9KumB6TRpaad);
  }
}
@media (max-width: 860px) {
  .text__h8GgH {
    font-size: var(--token-2UEfYzPsoOY0);
  }
}
@media (max-width: 480px) {
  .text__h8GgH {
    font-size: var(--token-2UEfYzPsoOY0);
  }
}
.textcollapsed__h8GgHv8Swz {
  display: none;
}
.texteditable__h8GgHwSi2Y {
  display: none;
}
.adjectiveAndTitleStack {
  flex-direction: row;
  width: auto;
  height: auto;
  max-width: 100%;
  display: none;
  padding: var(--token-sazGmnf7GWAk);
}
.adjectiveAndTitleStack > :global(.__wab_flex-container) {
  flex-direction: row;
  align-items: center;
  justify-content: flex-end;
  margin-left: calc(0px - var(--token-4Wrp9mDZCSCQ));
  width: calc(100% + var(--token-4Wrp9mDZCSCQ));
}
.adjectiveAndTitleStack > :global(.__wab_flex-container) > *,
.adjectiveAndTitleStack
  > :global(.__wab_flex-container)
  > :global(.__wab_slot)
  > *,
.adjectiveAndTitleStack > :global(.__wab_flex-container) > picture > img,
.adjectiveAndTitleStack
  > :global(.__wab_flex-container)
  > :global(.__wab_slot)
  > picture
  > img {
  margin-left: var(--token-4Wrp9mDZCSCQ);
}
.adjectiveAndTitleStackcollapsed {
  display: flex;
}
.adjectiveAndTitleStackeditable {
  width: 100%;
  min-width: 0;
  display: flex;
}
.adjectiveAndTitleStackeditable > :global(.__wab_flex-container) {
  min-width: 0;
}
.pitchIntro:global(.__wab_instance) {
  position: relative;
  width: auto;
}
.pitchIntrocollapsed:global(.__wab_instance) {
  display: none;
}
.pitchIntroeditable:global(.__wab_instance) {
  width: auto;
  display: flex;
}
.pitchRole:global(.__wab_instance) {
  max-width: 100%;
  position: relative;
  width: auto;
}
.pitchRoleeditable:global(.__wab_instance) {
  width: 100%;
  min-width: 0;
}
.pitchDescriptor:global(.__wab_instance) {
  position: relative;
  width: auto;
  display: none;
}
.pitchDescriptoreditable:global(.__wab_instance) {
  width: 100%;
  min-width: 0;
  display: flex;
}
.interactionButtons {
  display: flex;
  position: relative;
  flex-direction: row;
  width: auto;
  height: auto;
  max-width: 100%;
  padding: var(--token-4Wrp9mDZCSCQ) 8px 0px var(--token-4Wrp9mDZCSCQ);
  margin: 16px var(--token-sazGmnf7GWAk) var(--token-sazGmnf7GWAk);
}
.interactionButtons > :global(.__wab_flex-container) {
  flex-direction: row;
  align-items: stretch;
  justify-content: flex-start;
  margin-left: calc(0px - var(--token-4Wrp9mDZCSCQ));
  width: calc(100% + var(--token-4Wrp9mDZCSCQ));
}
.interactionButtons > :global(.__wab_flex-container) > *,
.interactionButtons > :global(.__wab_flex-container) > :global(.__wab_slot) > *,
.interactionButtons > :global(.__wab_flex-container) > picture > img,
.interactionButtons
  > :global(.__wab_flex-container)
  > :global(.__wab_slot)
  > picture
  > img {
  margin-left: var(--token-4Wrp9mDZCSCQ);
}
@media (max-width: 860px) {
  .interactionButtons {
    padding-right: 0px;
  }
}
.interactionButtonscollapsed {
  margin-top: 0px;
  padding-top: 0px;
}
.teamUpButton:global(.__wab_instance) {
  max-width: 100%;
  display: none;
}
.teamUpButtoneditable:global(.__wab_instance) {
  display: none;
}
.svg___9MsbW {
  position: relative;
  object-fit: cover;
  width: auto;
  height: 1em;
}
.text__ma02V {
  font-family: var(--token-z1yrQVi72Nj1);
}
.svg__wLzb {
  position: relative;
  object-fit: cover;
  width: auto;
  height: 1em;
}
.messageMeButton:global(.__wab_instance) {
  max-width: 100%;
  position: relative;
  margin-bottom: 0px;
}
.svg__qvxQr {
  position: relative;
  object-fit: cover;
  width: auto;
  height: 1em;
}
.svg__a8RNi {
  position: relative;
  object-fit: cover;
  width: auto;
  height: 1em;
}
.containerForPortNavBar {
  display: flex;
  position: absolute;
  flex-direction: column;
  height: auto;
  align-items: flex-start;
  justify-content: flex-start;
  left: 0px;
  top: auto;
  z-index: 1;
  bottom: -30px;
}
.containerForPortNavBarcollapsed {
  bottom: -47px;
}
.profileCoreBannerNavigationBar__tAhde:global(.__wab_instance) {
  max-width: 100%;
  position: relative;
  margin-top: 0;
  margin-bottom: 0;
  margin-left: 0;
  left: auto;
  top: auto;
}
