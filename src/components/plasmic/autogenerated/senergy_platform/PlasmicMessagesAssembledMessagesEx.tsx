/* eslint-disable */
/* tslint:disable */
// @ts-nocheck
/* prettier-ignore-start */

/** @jsxRuntime classic */
/** @jsx createPlasmicElementProxy */
/** @jsxFrag React.Fragment */

// This class is auto-generated by Plasmic; please do not edit!
// Plasmic Project: nZeWKcn21KijvC7eT8B3c7
// Component: l8L6cX9yUnMy

"use client";

import * as React from "react";

import Head from "next/head";
import Link, { LinkProps } from "next/link";
import { useRouter } from "next/navigation";

import {
  Flex as Flex__,
  MultiChoiceArg,
  PlasmicDataSourceContextProvider as PlasmicDataSourceContextProvider__,
  PlasmicIcon as PlasmicIcon__,
  PlasmicImg as PlasmicImg__,
  PlasmicLink as PlasmicLink__,
  PlasmicPageGuard as PlasmicPageGuard__,
  SingleBooleanChoiceArg,
  SingleC<PERSON>iceArg,
  Stack as Stack__,
  StrictProps,
  Trans as Trans__,
  classNames,
  createPlasmicElementProxy,
  deriveRenderOpts,
  ensureGlobalVariants,
  generateOnMutateForSpec,
  generateStateOnChangeProp,
  generateStateOnChangePropForCodeComponents,
  generateStateValueProp,
  get as $stateGet,
  hasVariant,
  initializeCodeComponentStates,
  initializePlasmicStates,
  makeFragment,
  omit,
  pick,
  renderPlasmicSlot,
  set as $stateSet,
  useCurrentUser,
  useDollarState,
  usePlasmicTranslator,
  useTrigger,
  wrapWithClassName
} from "@plasmicapp/react-web";
import {
  DataCtxReader as DataCtxReader__,
  useDataEnv,
  useGlobalActions
} from "@plasmicapp/host";

import MessagesMessageHeader from "../../MessagesMessageHeader"; // plasmic-import: 3wep4sj90mYo/component
import MessagesMessage from "../../MessagesMessage"; // plasmic-import: J-ppsQZTajkA/component
import MessagesAttachments from "../../MessagesAttachments"; // plasmic-import: C1Ttec365eGb/component
import MessagesDateDivider from "../../MessagesDateDivider"; // plasmic-import: U2k0KPt8XoY6/component
import MessagesInputBox from "../../MessagesInputBox"; // plasmic-import: _xepyTVjeZkI/component

import "@plasmicapp/react-web/lib/plasmic.css";

import projectcss from "./plasmic.module.css"; // plasmic-import: nZeWKcn21KijvC7eT8B3c7/projectcss
import sty from "./PlasmicMessagesAssembledMessagesEx.module.css"; // plasmic-import: l8L6cX9yUnMy/css

createPlasmicElementProxy;

export type PlasmicMessagesAssembledMessagesEx__VariantMembers = {};
export type PlasmicMessagesAssembledMessagesEx__VariantsArgs = {};
type VariantPropType = keyof PlasmicMessagesAssembledMessagesEx__VariantsArgs;
export const PlasmicMessagesAssembledMessagesEx__VariantProps =
  new Array<VariantPropType>();

export type PlasmicMessagesAssembledMessagesEx__ArgsType = {};
type ArgPropType = keyof PlasmicMessagesAssembledMessagesEx__ArgsType;
export const PlasmicMessagesAssembledMessagesEx__ArgProps =
  new Array<ArgPropType>();

export type PlasmicMessagesAssembledMessagesEx__OverridesType = {
  chatBoxContainer?: Flex__<"div">;
  messagesMessageHeader?: Flex__<typeof MessagesMessageHeader>;
  messagesSlot?: Flex__<"section">;
  messagesDateDivider?: Flex__<typeof MessagesDateDivider>;
  messagesInputBox?: Flex__<typeof MessagesInputBox>;
};

export interface DefaultMessagesAssembledMessagesExProps {
  className?: string;
}

const $$ = {};

function useNextRouter() {
  try {
    return useRouter();
  } catch {}
  return undefined;
}

function PlasmicMessagesAssembledMessagesEx__RenderFunc(props: {
  variants: PlasmicMessagesAssembledMessagesEx__VariantsArgs;
  args: PlasmicMessagesAssembledMessagesEx__ArgsType;
  overrides: PlasmicMessagesAssembledMessagesEx__OverridesType;
  forNode?: string;
}) {
  const { variants, overrides, forNode } = props;

  const args = React.useMemo(
    () =>
      Object.assign(
        {},
        Object.fromEntries(
          Object.entries(props.args).filter(([_, v]) => v !== undefined)
        )
      ),
    [props.args]
  );

  const $props = {
    ...args,
    ...variants
  };

  const __nextRouter = useNextRouter();
  const $ctx = useDataEnv?.() || {};
  const refsRef = React.useRef({});
  const $refs = refsRef.current;

  let [$queries, setDollarQueries] = React.useState<
    Record<string, ReturnType<typeof usePlasmicDataOp>>
  >({});

  const new$Queries: Record<string, ReturnType<typeof usePlasmicDataOp>> = {};
  if (Object.keys(new$Queries).some(k => new$Queries[k] !== $queries[k])) {
    setDollarQueries(new$Queries);

    $queries = new$Queries;
  }

  return (
    <div
      data-plasmic-name={"chatBoxContainer"}
      data-plasmic-override={overrides.chatBoxContainer}
      data-plasmic-root={true}
      data-plasmic-for-node={forNode}
      className={classNames(
        projectcss.all,
        projectcss.root_reset,
        projectcss.plasmic_default_styles,
        projectcss.plasmic_mixins,
        projectcss.plasmic_tokens,
        sty.chatBoxContainer
      )}
    >
      <MessagesMessageHeader
        data-plasmic-name={"messagesMessageHeader"}
        data-plasmic-override={overrides.messagesMessageHeader}
        className={classNames("__wab_instance", sty.messagesMessageHeader)}
      />

      <section
        data-plasmic-name={"messagesSlot"}
        data-plasmic-override={overrides.messagesSlot}
        className={classNames(projectcss.all, sty.messagesSlot)}
      >
        <MessagesMessage
          className={classNames("__wab_instance", sty.messagesMessage__xa8S7)}
          sentReceived={"sent"}
        />

        <MessagesMessage
          className={classNames("__wab_instance", sty.messagesMessage__b1TFw)}
          sentReceived={"received"}
        />

        <MessagesMessage
          className={classNames("__wab_instance", sty.messagesMessage__zXbrI)}
          messageType={"textAndAttachment"}
          sentReceived={"sent"}
        />

        <MessagesDateDivider
          data-plasmic-name={"messagesDateDivider"}
          data-plasmic-override={overrides.messagesDateDivider}
          className={classNames("__wab_instance", sty.messagesDateDivider)}
        />

        <MessagesMessage
          className={classNames("__wab_instance", sty.messagesMessage__tctwu)}
          sentReceived={"received"}
        />

        <MessagesMessage
          className={classNames("__wab_instance", sty.messagesMessage__vt1UO)}
          messageType={"textAndAttachment"}
          sentReceived={"sent"}
          unnamedProp={
            <MessagesAttachments
              className={classNames(
                "__wab_instance",
                sty.messagesAttachments__vKfT
              )}
            />
          }
        />

        <MessagesMessage
          chainMessages={"chainAttachment"}
          className={classNames("__wab_instance", sty.messagesMessage__gg4Uz)}
          messageType={"attachment"}
          sentReceived={"sent"}
          slot2={
            <MessagesAttachments
              className={classNames(
                "__wab_instance",
                sty.messagesAttachments__pqCfn
              )}
              image={true}
            />
          }
          unnamedProp={
            <MessagesAttachments
              className={classNames(
                "__wab_instance",
                sty.messagesAttachments___49ID
              )}
              image={true}
            />
          }
        />

        <MessagesMessage
          className={classNames("__wab_instance", sty.messagesMessage__xq9GD)}
          messageType={"text"}
          sentReceived={"received"}
          unnamedProp={
            <MessagesAttachments
              className={classNames(
                "__wab_instance",
                sty.messagesAttachments__a19Zg
              )}
              image={true}
            />
          }
        />

        <MessagesMessage
          chainMessages={"chainText"}
          className={classNames("__wab_instance", sty.messagesMessage__liSxR)}
          sentReceived={"received"}
          unnamedProp={
            <MessagesAttachments
              className={classNames(
                "__wab_instance",
                sty.messagesAttachments__jx9Qr
              )}
              image={true}
            />
          }
        />
      </section>
      <MessagesInputBox
        data-plasmic-name={"messagesInputBox"}
        data-plasmic-override={overrides.messagesInputBox}
        className={classNames("__wab_instance", sty.messagesInputBox)}
      />
    </div>
  ) as React.ReactElement | null;
}

const PlasmicDescendants = {
  chatBoxContainer: [
    "chatBoxContainer",
    "messagesMessageHeader",
    "messagesSlot",
    "messagesDateDivider",
    "messagesInputBox"
  ],
  messagesMessageHeader: ["messagesMessageHeader"],
  messagesSlot: ["messagesSlot", "messagesDateDivider"],
  messagesDateDivider: ["messagesDateDivider"],
  messagesInputBox: ["messagesInputBox"]
} as const;
type NodeNameType = keyof typeof PlasmicDescendants;
type DescendantsType<T extends NodeNameType> =
  (typeof PlasmicDescendants)[T][number];
type NodeDefaultElementType = {
  chatBoxContainer: "div";
  messagesMessageHeader: typeof MessagesMessageHeader;
  messagesSlot: "section";
  messagesDateDivider: typeof MessagesDateDivider;
  messagesInputBox: typeof MessagesInputBox;
};

type ReservedPropsType = "variants" | "args" | "overrides";
type NodeOverridesType<T extends NodeNameType> = Pick<
  PlasmicMessagesAssembledMessagesEx__OverridesType,
  DescendantsType<T>
>;
type NodeComponentProps<T extends NodeNameType> =
  // Explicitly specify variants, args, and overrides as objects
  {
    variants?: PlasmicMessagesAssembledMessagesEx__VariantsArgs;
    args?: PlasmicMessagesAssembledMessagesEx__ArgsType;
    overrides?: NodeOverridesType<T>;
  } & Omit< // Specify variants directly as props
    PlasmicMessagesAssembledMessagesEx__VariantsArgs,
    ReservedPropsType
  > &
    /* Specify args directly as props*/ Omit<
      PlasmicMessagesAssembledMessagesEx__ArgsType,
      ReservedPropsType
    > &
    /* Specify overrides for each element directly as props*/ Omit<
      NodeOverridesType<T>,
      ReservedPropsType | VariantPropType | ArgPropType
    > &
    /* Specify props for the root element*/ Omit<
      Partial<React.ComponentProps<NodeDefaultElementType[T]>>,
      ReservedPropsType | VariantPropType | ArgPropType | DescendantsType<T>
    >;

function makeNodeComponent<NodeName extends NodeNameType>(nodeName: NodeName) {
  type PropsType = NodeComponentProps<NodeName> & { key?: React.Key };
  const func = function <T extends PropsType>(
    props: T & StrictProps<T, PropsType>
  ) {
    const { variants, args, overrides } = React.useMemo(
      () =>
        deriveRenderOpts(props, {
          name: nodeName,
          descendantNames: PlasmicDescendants[nodeName],
          internalArgPropNames: PlasmicMessagesAssembledMessagesEx__ArgProps,
          internalVariantPropNames:
            PlasmicMessagesAssembledMessagesEx__VariantProps
        }),
      [props, nodeName]
    );
    return PlasmicMessagesAssembledMessagesEx__RenderFunc({
      variants,
      args,
      overrides,
      forNode: nodeName
    });
  };
  if (nodeName === "chatBoxContainer") {
    func.displayName = "PlasmicMessagesAssembledMessagesEx";
  } else {
    func.displayName = `PlasmicMessagesAssembledMessagesEx.${nodeName}`;
  }
  return func;
}

export const PlasmicMessagesAssembledMessagesEx = Object.assign(
  // Top-level PlasmicMessagesAssembledMessagesEx renders the root element
  makeNodeComponent("chatBoxContainer"),
  {
    // Helper components rendering sub-elements
    messagesMessageHeader: makeNodeComponent("messagesMessageHeader"),
    messagesSlot: makeNodeComponent("messagesSlot"),
    messagesDateDivider: makeNodeComponent("messagesDateDivider"),
    messagesInputBox: makeNodeComponent("messagesInputBox"),

    // Metadata about props expected for PlasmicMessagesAssembledMessagesEx
    internalVariantProps: PlasmicMessagesAssembledMessagesEx__VariantProps,
    internalArgProps: PlasmicMessagesAssembledMessagesEx__ArgProps
  }
);

export default PlasmicMessagesAssembledMessagesEx;
/* prettier-ignore-end */
