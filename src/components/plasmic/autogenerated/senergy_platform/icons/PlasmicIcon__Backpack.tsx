/* eslint-disable */
/* tslint:disable */
// @ts-nocheck
/* prettier-ignore-start */
import React from "react";
import { classNames } from "@plasmicapp/react-web";

export type BackpackIconProps = React.ComponentProps<"svg"> & {
  title?: string;
};

export function BackpackIcon(props: BackpackIconProps) {
  const { className, style, title, ...restProps } = props;
  return (
    <svg
      xmlns={"http://www.w3.org/2000/svg"}
      className={classNames(
        "plasmic-default__svg",
        className,
        "icon icon-tabler icon-tabler-backpack"
      )}
      viewBox={"0 0 24 24"}
      strokeWidth={"1.5"}
      stroke={"currentColor"}
      fill={"none"}
      strokeLinecap={"round"}
      strokeLinejoin={"round"}
      height={"1em"}
      style={style}
      {...restProps}
    >
      {title && <title>{title}</title>}

      <path d={"M0 0h24v24H0z"} fill={"none"} stroke={"none"}></path>

      <path
        d={
          "M5 18v-6a6 6 0 016-6h2a6 6 0 016 6v6a3 3 0 01-3 3H8a3 3 0 01-3-3zm5-12V5a2 2 0 114 0v1"
        }
      ></path>

      <path d={"M9 21v-4a2 2 0 012-2h2a2 2 0 012 2v4m-4-11h2"}></path>
    </svg>
  );
}

export default BackpackIcon;
/* prettier-ignore-end */
