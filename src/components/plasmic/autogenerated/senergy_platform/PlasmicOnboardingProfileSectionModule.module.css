.formattingContainer {
  box-shadow: 0px 4px 4px 0px var(--token-p09LDPmbF81_);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start;
  width: 100%;
  height: auto;
  max-width: 100%;
  position: relative;
  justify-self: flex-start;
  margin-bottom: 12px;
  background: var(--token-1AMvw6c2eIK7);
  grid-column-start: 3 !important;
  grid-column-end: -3 !important;
  padding: var(--token-M1l4keX1sfKm) var(--token-M1l4keX1sfKm) 55px;
  border: 1px solid var(--token-5_Q90hFZ9CmK);
}
.text___3ZZpP {
  position: relative;
  width: auto;
  height: auto;
  max-width: 100%;
  font-size: var(--token-AMP1angFPBtf);
  text-align: center;
  margin-left: 30px;
  margin-right: 30px;
  margin-bottom: 30px;
  font-family: var(--token-z1yrQVi72Nj1);
}
.mainSectionButtons {
  display: flex;
  position: relative;
  flex-direction: row;
  width: 100%;
  height: auto;
  max-width: 100%;
  min-width: 0;
  padding: var(--token-sazGmnf7GWAk);
  margin: var(--token-sazGmnf7GWAk);
}
.mainSectionButtons > :global(.__wab_flex-container) {
  flex-direction: row;
  align-items: stretch;
  justify-content: center;
  flex-wrap: wrap;
  min-width: 0;
  margin-left: calc(0px - var(--token-M1cOQzpe0467));
  width: calc(100% + var(--token-M1cOQzpe0467));
  margin-top: calc(0px - var(--token-M1cOQzpe0467));
  height: calc(100% + var(--token-M1cOQzpe0467));
}
.mainSectionButtons > :global(.__wab_flex-container) > *,
.mainSectionButtons > :global(.__wab_flex-container) > :global(.__wab_slot) > *,
.mainSectionButtons > :global(.__wab_flex-container) > picture > img,
.mainSectionButtons
  > :global(.__wab_flex-container)
  > :global(.__wab_slot)
  > picture
  > img {
  margin-left: var(--token-M1cOQzpe0467);
  margin-top: var(--token-M1cOQzpe0467);
}
.profileBanner:global(.__wab_instance) {
  max-width: 100%;
  position: relative;
  flex-shrink: 0;
}
.svg__ypD08 {
  position: relative;
  object-fit: cover;
  max-width: 100%;
  width: 100px;
  height: 100px;
}
.text___5OBl5 {
  position: relative;
  width: auto;
  height: auto;
  max-width: 100%;
  padding-right: 0px;
  font-family: var(--token-z1yrQVi72Nj1);
}
.introduction:global(.__wab_instance) {
  max-width: 100%;
  position: relative;
  flex-shrink: 0;
}
.introductionsectionOnboarding_optionSelected:global(.__wab_instance) {
  flex-shrink: 0;
}
.svg__xLvtw {
  position: relative;
  object-fit: cover;
  max-width: 100%;
  width: 100px;
  height: 100px;
}
.text__cJrv1 {
  position: relative;
  width: auto;
  height: auto;
  max-width: 100%;
  padding-right: 0px;
  font-family: var(--token-z1yrQVi72Nj1);
}
.educationButton:global(.__wab_instance) {
  max-width: 100%;
  position: relative;
  flex-shrink: 0;
}
.educationButtonselection_seeMore:global(.__wab_instance) {
  flex-shrink: 0;
}
.svg__yBdoc {
  position: relative;
  object-fit: cover;
  max-width: 100%;
  width: 100px;
  height: 100px;
}
.text__gJqri {
  position: relative;
  width: auto;
  height: auto;
  max-width: 100%;
  font-family: var(--token-z1yrQVi72Nj1);
}
.experienceButton:global(.__wab_instance) {
  max-width: 100%;
  position: relative;
  flex-shrink: 0;
}
.svg___90Lqx {
  position: relative;
  object-fit: cover;
  max-width: 100%;
  width: 100px;
  height: 100px;
}
.text___6Gq1 {
  position: relative;
  width: auto;
  height: auto;
  max-width: 100%;
  padding-right: 0px;
  font-family: var(--token-z1yrQVi72Nj1);
}
.toolsButton:global(.__wab_instance) {
  max-width: 100%;
  position: relative;
  flex-shrink: 0;
}
.svg__d7J9S {
  position: relative;
  object-fit: cover;
  max-width: 100%;
  width: 100px;
  height: 100px;
}
.text__d8F25 {
  position: relative;
  width: auto;
  height: auto;
  max-width: 100%;
  padding-right: 0px;
  font-family: var(--token-z1yrQVi72Nj1);
}
.skillsButton:global(.__wab_instance) {
  max-width: 100%;
  position: relative;
  flex-shrink: 0;
}
.svg__pAu {
  position: relative;
  object-fit: cover;
  max-width: 100%;
  width: 100px;
  height: 100px;
}
.text__pIr4V {
  position: relative;
  width: auto;
  height: auto;
  max-width: 100%;
  padding-right: 0px;
  font-family: var(--token-z1yrQVi72Nj1);
}
.languagesButton:global(.__wab_instance) {
  max-width: 100%;
  position: relative;
  flex-shrink: 0;
}
.svg__uGuEl {
  position: relative;
  object-fit: cover;
  max-width: 100%;
  width: 100px;
  height: 100px;
}
.text__gdbbJ {
  position: relative;
  width: auto;
  height: auto;
  max-width: 100%;
  padding-right: 0px;
  font-family: var(--token-z1yrQVi72Nj1);
}
.patentsButton:global(.__wab_instance) {
  max-width: 100%;
  position: relative;
  flex-shrink: 0;
  display: none;
}
.patentsButtonselection_seeMore:global(.__wab_instance) {
  flex-shrink: 0;
  display: flex;
}
.svg__h9PL5 {
  position: relative;
  object-fit: cover;
  max-width: 100%;
  width: 100px;
  height: 100px;
}
.text__nUmJn {
  position: relative;
  width: auto;
  height: auto;
  max-width: 100%;
  padding-bottom: 0px;
  font-family: var(--token-z1yrQVi72Nj1);
}
.publicationsButton:global(.__wab_instance) {
  max-width: 100%;
  position: relative;
  flex-shrink: 0;
  display: none;
}
.publicationsButtonselection_seeMore:global(.__wab_instance) {
  flex-shrink: 0;
  display: flex;
}
.svg__vRpy0 {
  position: relative;
  object-fit: cover;
  max-width: 100%;
  width: 100px;
  height: 100px;
}
.text__lTqE7 {
  position: relative;
  width: auto;
  height: auto;
  max-width: 100%;
  font-family: var(--token-z1yrQVi72Nj1);
}
.certificationsButton:global(.__wab_instance) {
  max-width: 100%;
  position: relative;
  flex-shrink: 0;
  display: none;
}
.certificationsButtonselection_seeMore:global(.__wab_instance) {
  flex-shrink: 0;
  display: flex;
}
.svg___34E0Y {
  position: relative;
  object-fit: cover;
  max-width: 100%;
  width: 100px;
  height: 100px;
}
.text__zln2R {
  position: relative;
  width: auto;
  height: auto;
  max-width: 100%;
  font-family: var(--token-z1yrQVi72Nj1);
}
.licensesButton:global(.__wab_instance) {
  max-width: 100%;
  position: relative;
  flex-shrink: 0;
  display: none;
}
.licensesButtonselection_seeMore:global(.__wab_instance) {
  flex-shrink: 0;
  display: flex;
}
.svg__gBu6 {
  position: relative;
  object-fit: cover;
  max-width: 100%;
  width: 100px;
  height: 100px;
}
.text__gphE {
  position: relative;
  width: auto;
  height: auto;
  max-width: 100%;
  font-family: var(--token-z1yrQVi72Nj1);
}
.trademarksButton:global(.__wab_instance) {
  max-width: 100%;
  position: relative;
  flex-shrink: 0;
  display: none;
}
.trademarksButtonselection_seeMore:global(.__wab_instance) {
  flex-shrink: 0;
  display: flex;
}
.svg___8QP93 {
  position: relative;
  object-fit: cover;
  max-width: 100%;
  width: 100px;
  height: 100px;
}
.text__irLzN {
  position: relative;
  width: auto;
  height: auto;
  max-width: 100%;
  padding-top: 0px;
  padding-bottom: 0px;
  font-family: var(--token-z1yrQVi72Nj1);
}
.seeLessButton2:global(.__wab_instance) {
  max-width: 100%;
  position: relative;
  flex-shrink: 0;
}
.seeLessButton2selection_seeMore:global(.__wab_instance) {
  flex-shrink: 0;
  display: none;
}
.svg__yPhTl {
  position: relative;
  object-fit: cover;
  max-width: 100%;
  width: 100px;
  height: 100px;
  color: var(--token-XQQdLIBrONt7);
}
.text__xBcP1 {
  position: relative;
  width: auto;
  height: auto;
  max-width: 100%;
  padding-right: 0px;
  font-family: var(--token-z1yrQVi72Nj1);
  color: var(--token-XQQdLIBrONt7);
}
.seeLessButton:global(.__wab_instance) {
  max-width: 100%;
  position: relative;
  flex-shrink: 0;
  display: none;
}
.seeLessButtonselection_seeMore:global(.__wab_instance) {
  flex-shrink: 0;
  display: flex;
}
.svg__p9Vn {
  position: relative;
  object-fit: cover;
  max-width: 100%;
  width: 100px;
  height: 100px;
  color: var(--token-v4jufhOu3lt9);
}
.text___2YvFp {
  position: relative;
  width: auto;
  height: auto;
  max-width: 100%;
  padding-right: 0px;
  font-family: var(--token-z1yrQVi72Nj1);
  color: var(--token-v4jufhOu3lt9);
}
.completedSections {
  position: relative;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start;
  width: 100%;
  height: auto;
  max-width: 100%;
  min-width: 0;
  display: none;
  padding: var(--token-sazGmnf7GWAk);
  margin: var(--token-sazGmnf7GWAk);
}
.text__rUbev {
  padding-top: 24px;
  padding-bottom: 24px;
  padding-left: 0px;
  font-family: var(--token-z1yrQVi72Nj1);
  width: 100%;
  height: auto;
  max-width: 100%;
  font-size: var(--token-AMP1angFPBtf);
  min-width: 0;
}
.sectionsWithContent {
  display: flex;
  position: relative;
  flex-direction: row;
  width: 100%;
  height: auto;
  max-width: 100%;
  min-width: 0;
  padding: var(--token-sazGmnf7GWAk);
  margin: var(--token-sazGmnf7GWAk) var(--token-sazGmnf7GWAk) 30px;
}
.sectionsWithContent > :global(.__wab_flex-container) {
  flex-direction: row;
  align-items: stretch;
  justify-content: flex-start;
  flex-wrap: wrap;
  align-content: flex-start;
  min-width: 0;
  margin-left: calc(0px - var(--token-M1l4keX1sfKm));
  width: calc(100% + var(--token-M1l4keX1sfKm));
  margin-top: calc(0px - var(--token-M1l4keX1sfKm));
  height: calc(100% + var(--token-M1l4keX1sfKm));
}
.sectionsWithContent > :global(.__wab_flex-container) > *,
.sectionsWithContent
  > :global(.__wab_flex-container)
  > :global(.__wab_slot)
  > *,
.sectionsWithContent > :global(.__wab_flex-container) > picture > img,
.sectionsWithContent
  > :global(.__wab_flex-container)
  > :global(.__wab_slot)
  > picture
  > img {
  margin-left: var(--token-M1l4keX1sfKm);
  margin-top: var(--token-M1l4keX1sfKm);
}
.introductionButtonEntry:global(.__wab_instance) {
  max-width: 100%;
  position: relative;
  flex-shrink: 0;
}
.svg__uuHdw {
  position: relative;
  object-fit: cover;
  max-width: 100%;
  width: 50px;
  height: 50px;
}
.text__zwaI3 {
  position: relative;
  width: auto;
  height: auto;
  max-width: 100%;
  padding-right: 0px;
  font-family: var(--token-z1yrQVi72Nj1);
}
.educationButtonEntry:global(.__wab_instance) {
  max-width: 100%;
  position: relative;
  flex-shrink: 0;
}
.svg___5SvUy {
  position: relative;
  object-fit: cover;
  max-width: 100%;
  width: 50px;
  height: 50px;
}
.text__paO5F {
  position: relative;
  width: auto;
  height: auto;
  max-width: 100%;
  font-family: var(--token-z1yrQVi72Nj1);
}
.experienceButtonEntry:global(.__wab_instance) {
  max-width: 100%;
  position: relative;
  flex-shrink: 0;
}
.svg__jbaX {
  position: relative;
  object-fit: cover;
  max-width: 100%;
  width: 50px;
  height: 50px;
}
.text__kCqa {
  position: relative;
  width: auto;
  height: auto;
  max-width: 100%;
  padding-right: 0px;
  font-family: var(--token-z1yrQVi72Nj1);
}
.skillsButtonEntry:global(.__wab_instance) {
  max-width: 100%;
  position: relative;
  flex-shrink: 0;
}
.svg__m7GHp {
  position: relative;
  object-fit: cover;
  max-width: 100%;
  width: 50px;
  height: 50px;
}
.text__fbElQ {
  position: relative;
  width: auto;
  height: auto;
  max-width: 100%;
  padding-right: 0px;
  font-family: var(--token-z1yrQVi72Nj1);
}
.toolsButtonEntry:global(.__wab_instance) {
  max-width: 100%;
  position: relative;
  flex-shrink: 0;
}
.svg___7FP6P {
  position: relative;
  object-fit: cover;
  max-width: 100%;
  width: 50px;
  height: 50px;
}
.text__zi6Gz {
  position: relative;
  width: auto;
  height: auto;
  max-width: 100%;
  padding-right: 0px;
  font-family: var(--token-z1yrQVi72Nj1);
}
.languagesButtonEntry:global(.__wab_instance) {
  max-width: 100%;
  position: relative;
  flex-shrink: 0;
}
.svg__vByQw {
  position: relative;
  object-fit: cover;
  max-width: 100%;
  width: 50px;
  height: 50px;
}
.text__jjUn7 {
  position: relative;
  width: auto;
  height: auto;
  max-width: 100%;
  padding-right: 0px;
  font-family: var(--token-z1yrQVi72Nj1);
}
.publicationsButtonEntry:global(.__wab_instance) {
  max-width: 100%;
  position: relative;
  flex-shrink: 0;
}
.svg__ovHd5 {
  position: relative;
  object-fit: cover;
  max-width: 100%;
  width: 50px;
  height: 50px;
}
.text__bAeQn {
  position: relative;
  width: auto;
  height: auto;
  max-width: 100%;
  font-family: var(--token-z1yrQVi72Nj1);
}
.licensesButtonEntry:global(.__wab_instance) {
  max-width: 100%;
  position: relative;
  flex-shrink: 0;
}
.svg___9Ztn7 {
  position: relative;
  object-fit: cover;
  max-width: 100%;
  width: 50px;
  height: 50px;
}
.text__g1HhZ {
  position: relative;
  width: auto;
  height: auto;
  max-width: 100%;
  font-family: var(--token-z1yrQVi72Nj1);
}
.certificationsButtonEntry:global(.__wab_instance) {
  max-width: 100%;
  position: relative;
  flex-shrink: 0;
}
.svg__ocBOm {
  position: relative;
  object-fit: cover;
  max-width: 100%;
  width: 50px;
  height: 50px;
}
.text__o7JVn {
  position: relative;
  width: auto;
  height: auto;
  max-width: 100%;
  font-family: var(--token-z1yrQVi72Nj1);
}
.patentsButtonEntry:global(.__wab_instance) {
  max-width: 100%;
  position: relative;
  flex-shrink: 0;
}
.svg__gfrBd {
  position: relative;
  object-fit: cover;
  max-width: 100%;
  width: 50px;
  height: 50px;
}
.text__zix1X {
  position: relative;
  width: auto;
  height: auto;
  max-width: 100%;
  padding-bottom: 0px;
  font-family: var(--token-z1yrQVi72Nj1);
}
.trademarksButtonEntry:global(.__wab_instance) {
  max-width: 100%;
  position: relative;
  flex-shrink: 0;
}
.svg__juyro {
  position: relative;
  object-fit: cover;
  max-width: 100%;
  width: 50px;
  height: 50px;
}
.text__gQDoE {
  position: relative;
  width: auto;
  height: auto;
  max-width: 100%;
  padding-top: 0px;
  padding-bottom: 0px;
  font-family: var(--token-z1yrQVi72Nj1);
}
