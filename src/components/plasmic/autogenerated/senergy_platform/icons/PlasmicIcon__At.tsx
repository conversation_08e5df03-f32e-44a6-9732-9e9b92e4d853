/* eslint-disable */
/* tslint:disable */
// @ts-nocheck
/* prettier-ignore-start */
import React from "react";
import { classNames } from "@plasmicapp/react-web";

export type AtIconProps = React.ComponentProps<"svg"> & {
  title?: string;
};

export function AtIcon(props: AtIconProps) {
  const { className, style, title, ...restProps } = props;
  return (
    <svg
      xmlns={"http://www.w3.org/2000/svg"}
      className={classNames(
        "plasmic-default__svg",
        className,
        "icon icon-tabler icon-tabler-at"
      )}
      viewBox={"0 0 24 24"}
      strokeWidth={"1.5"}
      stroke={"currentColor"}
      fill={"none"}
      strokeLinecap={"round"}
      strokeLinejoin={"round"}
      height={"1em"}
      style={style}
      {...restProps}
    >
      {title && <title>{title}</title>}

      <path d={"M0 0h24v24H0z"} fill={"none"} stroke={"none"}></path>

      <path d={"M8 12a4 4 0 108 0 4 4 0 10-8 0"}></path>

      <path d={"M16 12v1.5a2.5 2.5 0 005 0V12a9 9 0 10-5.5 8.28"}></path>
    </svg>
  );
}

export default AtIcon;
/* prettier-ignore-end */
