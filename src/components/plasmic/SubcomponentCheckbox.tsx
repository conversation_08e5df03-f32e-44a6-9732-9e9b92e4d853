import * as React from "react";
import {
  DefaultSubcomponentCheckboxProps,
  PlasmicSubcomponentCheckbox
} from "./autogenerated/senergy_platform/PlasmicSubcomponentCheckbox";

import { CheckboxRef } from "@plasmicapp/react-web";

export interface SubcomponentCheckboxProps
  extends DefaultSubcomponentCheckboxProps {
  // Feel free to add any additional props that this component should receive
}
function SubcomponentCheckbox_(
  props: SubcomponentCheckboxProps,
  ref: CheckboxRef
) {
  const { plasmicProps, state } =
    PlasmicSubcomponentCheckbox.useBehavior<SubcomponentCheckboxProps>(
      props,
      ref
    );
  return <PlasmicSubcomponentCheckbox {...plasmicProps} />;
}

const SubcomponentCheckbox = React.forwardRef(SubcomponentCheckbox_);

export default Object.assign(
  SubcomponentCheckbox,

  {
    __plumeType: "checkbox",
    __plasmicFormFieldMeta: { valueProp: "isChecked" }
  }
);
