/* eslint-disable */
/* tslint:disable */
// @ts-nocheck
/* prettier-ignore-start */
import React from "react";
import { classNames } from "@plasmicapp/react-web";

export type LoadingDotsIconProps = React.ComponentProps<"svg"> & {
  title?: string;
};

export function LoadingDotsIcon(props: LoadingDotsIconProps) {
  const { className, style, title, ...restProps } = props;
  return (
    <svg
      xmlns={"http://www.w3.org/2000/svg"}
      viewBox={"0 0 120 30"}
      fill={"currentColor"}
      height={"1em"}
      className={classNames("plasmic-default__svg", className)}
      style={style}
      {...restProps}
    >
      {title && <title>{title}</title>}

      <circle cx={"15"} cy={"15"} r={"15"}>
        <animate
          attributeName={"r"}
          from={"15"}
          to={"15"}
          begin={"0s"}
          dur={"0.8s"}
          values={"15;9;15"}
          calcMode={"linear"}
          repeatCount={"indefinite"}
        ></animate>
      </circle>

      <circle cx={"60"} cy={"15"} r={"15"}>
        <animate
          attributeName={"r"}
          from={"15"}
          to={"15"}
          begin={"0.2s"}
          dur={"0.8s"}
          values={"15;9;15"}
          calcMode={"linear"}
          repeatCount={"indefinite"}
        ></animate>
      </circle>

      <circle cx={"105"} cy={"15"} r={"15"}>
        <animate
          attributeName={"r"}
          from={"15"}
          to={"15"}
          begin={"0.4s"}
          dur={"0.8s"}
          values={"15;9;15"}
          calcMode={"linear"}
          repeatCount={"indefinite"}
        ></animate>
      </circle>
    </svg>
  );
}

export default LoadingDotsIcon;
/* prettier-ignore-end */
