/* eslint-disable */
/* tslint:disable */
// @ts-nocheck
/* prettier-ignore-start */
import React from "react";
import { classNames } from "@plasmicapp/react-web";

export type MenuDeepIconProps = React.ComponentProps<"svg"> & {
  title?: string;
};

export function MenuDeepIcon(props: MenuDeepIconProps) {
  const { className, style, title, ...restProps } = props;
  return (
    <svg
      xmlns={"http://www.w3.org/2000/svg"}
      className={classNames(
        "plasmic-default__svg",
        className,
        "icon icon-tabler icon-tabler-menu-deep"
      )}
      viewBox={"0 0 24 24"}
      strokeWidth={"1.5"}
      stroke={"currentColor"}
      fill={"none"}
      strokeLinecap={"round"}
      strokeLinejoin={"round"}
      height={"1em"}
      style={style}
      {...restProps}
    >
      {title && <title>{title}</title>}

      <path stroke={"none"} d={"M0 0h24v24H0z"} fill={"none"}></path>

      <path d={"M4 6h16M7 12h13m-10 6h10"}></path>
    </svg>
  );
}

export default MenuDeepIcon;
/* prettier-ignore-end */
