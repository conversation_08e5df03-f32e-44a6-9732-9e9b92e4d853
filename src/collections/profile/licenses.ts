import type { Field } from 'payload';

const WORD_SPLIT_REGEX = /\s+/;

export const licensesFields: Field[] = [
  {
    name: 'license_name',
    label: 'License Name',
    type: 'text',
    required: true,
  },
  {
    type: 'row',
    fields: [
      {
        name: 'licensing_body',
        label: 'Licensing Body',
        type: 'text',
        required: true,
      },
      {
        name: 'registration_number',
        label: 'Registration Number',
        type: 'text',
        required: false,
      },
    ],
  },
  {
    type: 'row',
    fields: [
      {
        name: 'issue_location',
        label: 'Issue Location',
        type: 'text',
      },
      {
        name: 'instructor',
        label: 'Instructor',
        type: 'text',
        required: false,
      },
    ],
  },
  {
    type: 'row',
    fields: [
      {
        name: 'issue_date',
        label: 'Issue Date',
        type: 'date',
        required: true,
      },
      {
        name: 'expiration_date',
        label: 'Expiration Date',
        type: 'date',
        required: false,
      },
    ],
  },
  {
    name: 'license_description',
    label: 'License Description',
    admin: {
      description: 'Description of the license',
    },
    type: 'textarea',
    required: false,
    validate: (value: string | null | undefined) => {
      if (value) {
        const wordCount = value.trim().split(WORD_SPLIT_REGEX).length;
        if (wordCount > 300) {
          return `Description must not exceed 300 words. Current word count: ${wordCount}`;
        }
      }
      return true;
    },
  },
  {
    name: 'visible',
    type: 'checkbox',
    required: false,
    defaultValue: true,
  },
];
