/* eslint-disable */
/* tslint:disable */
// @ts-nocheck
/* prettier-ignore-start */

/** @jsxRuntime classic */
/** @jsx createPlasmicElementProxy */
/** @jsxFrag React.Fragment */

// This class is auto-generated by Plasmic; please do not edit!
// Plasmic Project: nZeWKcn21KijvC7eT8B3c7
// Component: A7wamfGEqfKZ

"use client";

import * as React from "react";

import Head from "next/head";
import Link, { LinkProps } from "next/link";
import { useRouter } from "next/navigation";

import {
  Flex as Flex__,
  MultiChoiceArg,
  PlasmicDataSourceContextProvider as PlasmicDataSourceContextProvider__,
  PlasmicIcon as PlasmicIcon__,
  PlasmicImg as PlasmicImg__,
  PlasmicLink as PlasmicLink__,
  PlasmicPageGuard as PlasmicPageGuard__,
  SingleBooleanChoiceArg,
  SingleChoiceArg,
  Stack as St<PERSON>__,
  StrictProps,
  Trans as Trans__,
  classNames,
  createPlasmicElementProxy,
  deriveRenderOpts,
  ensureGlobalVariants,
  generateOnMutateForSpec,
  generateStateOnChangeProp,
  generateStateOnChangePropForCodeComponents,
  generateStateValueProp,
  get as $stateGet,
  hasVariant,
  initializeCodeComponentStates,
  initializePlasmicStates,
  makeFragment,
  omit,
  pick,
  renderPlasmicSlot,
  set as $stateSet,
  useCurrentUser,
  useDollarState,
  usePlasmicTranslator,
  useTrigger,
  wrapWithClassName
} from "@plasmicapp/react-web";
import {
  DataCtxReader as DataCtxReader__,
  useDataEnv,
  useGlobalActions
} from "@plasmicapp/host";

import "@plasmicapp/react-web/lib/plasmic.css";

import projectcss from "./plasmic.module.css"; // plasmic-import: nZeWKcn21KijvC7eT8B3c7/projectcss
import sty from "./PlasmicSubcomponentToggleSwitch.module.css"; // plasmic-import: A7wamfGEqfKZ/css

import ToggleBoxIcon from "./icons/PlasmicIcon__ToggleBox"; // plasmic-import: VeGBlXIH6Sji/icon

createPlasmicElementProxy;

export type PlasmicSubcomponentToggleSwitch__VariantMembers = {
  on: "on";
  size: "small";
};
export type PlasmicSubcomponentToggleSwitch__VariantsArgs = {
  on?: SingleBooleanChoiceArg<"on">;
  size?: SingleChoiceArg<"small">;
};
type VariantPropType = keyof PlasmicSubcomponentToggleSwitch__VariantsArgs;
export const PlasmicSubcomponentToggleSwitch__VariantProps =
  new Array<VariantPropType>("on", "size");

export type PlasmicSubcomponentToggleSwitch__ArgsType = {};
type ArgPropType = keyof PlasmicSubcomponentToggleSwitch__ArgsType;
export const PlasmicSubcomponentToggleSwitch__ArgProps =
  new Array<ArgPropType>();

export type PlasmicSubcomponentToggleSwitch__OverridesType = {
  root?: Flex__<"div">;
  section?: Flex__<"section">;
  svg?: Flex__<"svg">;
};

export interface DefaultSubcomponentToggleSwitchProps {
  on?: SingleBooleanChoiceArg<"on">;
  size?: SingleChoiceArg<"small">;
  className?: string;
}

const $$ = {};

function useNextRouter() {
  try {
    return useRouter();
  } catch {}
  return undefined;
}

function PlasmicSubcomponentToggleSwitch__RenderFunc(props: {
  variants: PlasmicSubcomponentToggleSwitch__VariantsArgs;
  args: PlasmicSubcomponentToggleSwitch__ArgsType;
  overrides: PlasmicSubcomponentToggleSwitch__OverridesType;
  forNode?: string;
}) {
  const { variants, overrides, forNode } = props;

  const args = React.useMemo(
    () =>
      Object.assign(
        {},
        Object.fromEntries(
          Object.entries(props.args).filter(([_, v]) => v !== undefined)
        )
      ),
    [props.args]
  );

  const $props = {
    ...args,
    ...variants
  };

  const __nextRouter = useNextRouter();
  const $ctx = useDataEnv?.() || {};
  const refsRef = React.useRef({});
  const $refs = refsRef.current;

  let [$queries, setDollarQueries] = React.useState<
    Record<string, ReturnType<typeof usePlasmicDataOp>>
  >({});
  const stateSpecs: Parameters<typeof useDollarState>[0] = React.useMemo(
    () => [
      {
        path: "on",
        type: "private",
        variableType: "variant",
        initFunc: ({ $props, $state, $queries, $ctx }) => $props.on
      },
      {
        path: "size",
        type: "private",
        variableType: "variant",
        initFunc: ({ $props, $state, $queries, $ctx }) => $props.size
      }
    ],
    [$props, $ctx, $refs]
  );
  const $state = useDollarState(stateSpecs, {
    $props,
    $ctx,
    $queries: $queries,
    $refs
  });

  const new$Queries: Record<string, ReturnType<typeof usePlasmicDataOp>> = {};
  if (Object.keys(new$Queries).some(k => new$Queries[k] !== $queries[k])) {
    setDollarQueries(new$Queries);

    $queries = new$Queries;
  }

  return (
    <div
      data-plasmic-name={"root"}
      data-plasmic-override={overrides.root}
      data-plasmic-root={true}
      data-plasmic-for-node={forNode}
      className={classNames(
        projectcss.all,
        projectcss.root_reset,
        projectcss.plasmic_default_styles,
        projectcss.plasmic_mixins,
        projectcss.plasmic_tokens,
        sty.root,
        { [sty.rooton]: hasVariant($state, "on", "on") }
      )}
    >
      <section
        data-plasmic-name={"section"}
        data-plasmic-override={overrides.section}
        className={classNames(projectcss.all, sty.section, {
          [sty.sectionon]: hasVariant($state, "on", "on"),
          [sty.sectionsize_small]: hasVariant($state, "size", "small")
        })}
        onClick={async event => {
          const $steps = {};

          $steps["updateOn"] = true
            ? (() => {
                const actionArgs = { vgroup: "on", operation: 2 };
                return (({ vgroup, value }) => {
                  if (typeof value === "string") {
                    value = [value];
                  }

                  const oldValue = $stateGet($state, vgroup);
                  $stateSet($state, vgroup, !oldValue);
                  return !oldValue;
                })?.apply(null, [actionArgs]);
              })()
            : undefined;
          if (
            $steps["updateOn"] != null &&
            typeof $steps["updateOn"] === "object" &&
            typeof $steps["updateOn"].then === "function"
          ) {
            $steps["updateOn"] = await $steps["updateOn"];
          }
        }}
      >
        <ToggleBoxIcon
          data-plasmic-name={"svg"}
          data-plasmic-override={overrides.svg}
          className={classNames(projectcss.all, sty.svg, {
            [sty.svgon]: hasVariant($state, "on", "on"),
            [sty.svgsize_small]: hasVariant($state, "size", "small"),
            [sty.svgsize_small_on]:
              hasVariant($state, "on", "on") &&
              hasVariant($state, "size", "small")
          })}
          role={"img"}
        />
      </section>
    </div>
  ) as React.ReactElement | null;
}

const PlasmicDescendants = {
  root: ["root", "section", "svg"],
  section: ["section", "svg"],
  svg: ["svg"]
} as const;
type NodeNameType = keyof typeof PlasmicDescendants;
type DescendantsType<T extends NodeNameType> =
  (typeof PlasmicDescendants)[T][number];
type NodeDefaultElementType = {
  root: "div";
  section: "section";
  svg: "svg";
};

type ReservedPropsType = "variants" | "args" | "overrides";
type NodeOverridesType<T extends NodeNameType> = Pick<
  PlasmicSubcomponentToggleSwitch__OverridesType,
  DescendantsType<T>
>;
type NodeComponentProps<T extends NodeNameType> =
  // Explicitly specify variants, args, and overrides as objects
  {
    variants?: PlasmicSubcomponentToggleSwitch__VariantsArgs;
    args?: PlasmicSubcomponentToggleSwitch__ArgsType;
    overrides?: NodeOverridesType<T>;
  } & Omit<PlasmicSubcomponentToggleSwitch__VariantsArgs, ReservedPropsType> & // Specify variants directly as props
    /* Specify args directly as props*/ Omit<
      PlasmicSubcomponentToggleSwitch__ArgsType,
      ReservedPropsType
    > &
    /* Specify overrides for each element directly as props*/ Omit<
      NodeOverridesType<T>,
      ReservedPropsType | VariantPropType | ArgPropType
    > &
    /* Specify props for the root element*/ Omit<
      Partial<React.ComponentProps<NodeDefaultElementType[T]>>,
      ReservedPropsType | VariantPropType | ArgPropType | DescendantsType<T>
    >;

function makeNodeComponent<NodeName extends NodeNameType>(nodeName: NodeName) {
  type PropsType = NodeComponentProps<NodeName> & { key?: React.Key };
  const func = function <T extends PropsType>(
    props: T & StrictProps<T, PropsType>
  ) {
    const { variants, args, overrides } = React.useMemo(
      () =>
        deriveRenderOpts(props, {
          name: nodeName,
          descendantNames: PlasmicDescendants[nodeName],
          internalArgPropNames: PlasmicSubcomponentToggleSwitch__ArgProps,
          internalVariantPropNames:
            PlasmicSubcomponentToggleSwitch__VariantProps
        }),
      [props, nodeName]
    );
    return PlasmicSubcomponentToggleSwitch__RenderFunc({
      variants,
      args,
      overrides,
      forNode: nodeName
    });
  };
  if (nodeName === "root") {
    func.displayName = "PlasmicSubcomponentToggleSwitch";
  } else {
    func.displayName = `PlasmicSubcomponentToggleSwitch.${nodeName}`;
  }
  return func;
}

export const PlasmicSubcomponentToggleSwitch = Object.assign(
  // Top-level PlasmicSubcomponentToggleSwitch renders the root element
  makeNodeComponent("root"),
  {
    // Helper components rendering sub-elements
    section: makeNodeComponent("section"),
    svg: makeNodeComponent("svg"),

    // Metadata about props expected for PlasmicSubcomponentToggleSwitch
    internalVariantProps: PlasmicSubcomponentToggleSwitch__VariantProps,
    internalArgProps: PlasmicSubcomponentToggleSwitch__ArgProps
  }
);

export default PlasmicSubcomponentToggleSwitch;
/* prettier-ignore-end */
