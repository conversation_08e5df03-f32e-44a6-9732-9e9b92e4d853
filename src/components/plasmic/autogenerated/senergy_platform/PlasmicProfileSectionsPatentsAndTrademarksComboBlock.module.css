.patentsAndTrademarksSection {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: flex-start;
  width: 100%;
  height: auto;
  max-width: 100%;
  position: relative;
  justify-self: flex-start;
  margin-bottom: 12px;
  grid-column-start: 3 !important;
  grid-column-end: -3 !important;
  padding: var(--token-sazGmnf7GWAk);
}
.profileSectionsProfileSectionHeading:global(.__wab_instance) {
  max-width: 100%;
  position: relative;
}
.displayContainer {
  display: flex;
  position: relative;
  width: 100%;
  flex-direction: column;
  min-width: 0;
  padding: var(--token-sazGmnf7GWAk);
}
.tTrademarkTile:global(.__wab_instance) {
  max-width: 100%;
  position: relative;
}
.tPatentTile:global(.__wab_instance) {
  max-width: 100%;
  position: relative;
}
