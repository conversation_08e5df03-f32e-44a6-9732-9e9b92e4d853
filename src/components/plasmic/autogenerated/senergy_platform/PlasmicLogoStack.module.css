.logoStack {
  display: inline-flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  width: auto;
  height: auto;
  max-width: 100%;
  overflow: hidden;
  position: relative;
}
.img {
  object-fit: cover;
  max-width: 100%;
  width: 77px;
  height: 77px;
  margin-right: 0px;
  flex-shrink: 0;
}
.img > picture > img {
  object-fit: cover;
}
@media (max-width: 480px) {
  .img {
    width: 64px;
    height: 64px;
    flex-shrink: 0;
  }
}
.text {
  position: relative;
  width: auto;
  height: auto;
  max-width: 100%;
  text-align: right;
  font-family: var(--token-IfwtVZvVvF7g);
  font-weight: 700;
  user-select: text;
  line-height: 1.65;
  font-size: var(--token-9KumB6TRpaad);
}
@media (max-width: 480px) {
  .text {
    font-size: var(--token-_-i82ElPHE7I);
  }
}
