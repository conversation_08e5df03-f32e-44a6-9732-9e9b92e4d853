/* eslint-disable */
/* tslint:disable */
// @ts-nocheck
/* prettier-ignore-start */

/** @jsxRuntime classic */
/** @jsx createPlasmicElementProxy */
/** @jsxFrag React.Fragment */

// This class is auto-generated by Plasmic; please do not edit!
// Plasmic Project: nZeWKcn21KijvC7eT8B3c7
// Component: Uz656rZzIxzC

"use client";

import * as React from "react";

import Head from "next/head";
import Link, { LinkProps } from "next/link";
import { useRouter } from "next/navigation";

import {
  Flex as Flex__,
  MultiChoiceArg,
  PlasmicDataSourceContextProvider as PlasmicDataSourceContextProvider__,
  PlasmicIcon as PlasmicIcon__,
  PlasmicImg as PlasmicImg__,
  PlasmicLink as PlasmicLink__,
  PlasmicPageGuard as PlasmicPageGuard__,
  SingleBooleanChoiceArg,
  SingleChoiceArg,
  Stack as Stack__,
  StrictProps,
  Trans as Trans__,
  classNames,
  createPlasmicElementProxy,
  deriveRenderOpts,
  ensureGlobalVariants,
  generateOnMutateForSpec,
  generateStateOnChangeProp,
  generateStateOnChangePropForCodeComponents,
  generateStateValueProp,
  get as $stateGet,
  hasVariant,
  initializeCodeComponentStates,
  initializePlasmicStates,
  makeFragment,
  omit,
  pick,
  renderPlasmicSlot,
  set as $stateSet,
  useCurrentUser,
  useDollarState,
  usePlasmicTranslator,
  useTrigger,
  wrapWithClassName
} from "@plasmicapp/react-web";
import {
  DataCtxReader as DataCtxReader__,
  useDataEnv,
  useGlobalActions
} from "@plasmicapp/host";

import SubcomponentAddSectionButton from "../../SubcomponentAddSectionButton"; // plasmic-import: vFlsxZd2-jZM/component

import "@plasmicapp/react-web/lib/plasmic.css";

import projectcss from "./plasmic.module.css"; // plasmic-import: nZeWKcn21KijvC7eT8B3c7/projectcss
import sty from "./PlasmicProfileSectionsProfileSectionHeading.module.css"; // plasmic-import: Uz656rZzIxzC/css

createPlasmicElementProxy;

export type PlasmicProfileSectionsProfileSectionHeading__VariantMembers = {
  noUnderline: "noUnderline";
  overviewGrid: "overviewGrid";
  editable: "editable";
};
export type PlasmicProfileSectionsProfileSectionHeading__VariantsArgs = {
  noUnderline?: SingleBooleanChoiceArg<"noUnderline">;
  overviewGrid?: SingleBooleanChoiceArg<"overviewGrid">;
  editable?: SingleBooleanChoiceArg<"editable">;
};
type VariantPropType =
  keyof PlasmicProfileSectionsProfileSectionHeading__VariantsArgs;
export const PlasmicProfileSectionsProfileSectionHeading__VariantProps =
  new Array<VariantPropType>("noUnderline", "overviewGrid", "editable");

export type PlasmicProfileSectionsProfileSectionHeading__ArgsType = {
  children?: React.ReactNode;
  addButtonBaseOnClick?: (event: any) => void;
  addButtonPatentOnClick?: (event: any) => void;
  addButtonTrademarkOnClick?: (event: any) => void;
  addButtonLicenseOnClick?: (event: any) => void;
  addButtonCertificationOnClick?: (event: any) => void;
  addButtonSectionType?: string;
};
type ArgPropType = keyof PlasmicProfileSectionsProfileSectionHeading__ArgsType;
export const PlasmicProfileSectionsProfileSectionHeading__ArgProps =
  new Array<ArgPropType>(
    "children",
    "addButtonBaseOnClick",
    "addButtonPatentOnClick",
    "addButtonTrademarkOnClick",
    "addButtonLicenseOnClick",
    "addButtonCertificationOnClick",
    "addButtonSectionType"
  );

export type PlasmicProfileSectionsProfileSectionHeading__OverridesType = {
  headerContainer?: Flex__<"div">;
  headerWorks?: Flex__<"div">;
  subcomponentAddSectionButton?: Flex__<typeof SubcomponentAddSectionButton>;
  underline?: Flex__<"section">;
};

export interface DefaultProfileSectionsProfileSectionHeadingProps {
  children?: React.ReactNode;
  addButtonBaseOnClick?: (event: any) => void;
  addButtonPatentOnClick?: (event: any) => void;
  addButtonTrademarkOnClick?: (event: any) => void;
  addButtonLicenseOnClick?: (event: any) => void;
  addButtonCertificationOnClick?: (event: any) => void;
  addButtonSectionType?: string;
  noUnderline?: SingleBooleanChoiceArg<"noUnderline">;
  overviewGrid?: SingleBooleanChoiceArg<"overviewGrid">;
  editable?: SingleBooleanChoiceArg<"editable">;
  className?: string;
}

const $$ = {};

function useNextRouter() {
  try {
    return useRouter();
  } catch {}
  return undefined;
}

function PlasmicProfileSectionsProfileSectionHeading__RenderFunc(props: {
  variants: PlasmicProfileSectionsProfileSectionHeading__VariantsArgs;
  args: PlasmicProfileSectionsProfileSectionHeading__ArgsType;
  overrides: PlasmicProfileSectionsProfileSectionHeading__OverridesType;
  forNode?: string;
}) {
  const { variants, overrides, forNode } = props;

  const args = React.useMemo(
    () =>
      Object.assign(
        {},
        Object.fromEntries(
          Object.entries(props.args).filter(([_, v]) => v !== undefined)
        )
      ),
    [props.args]
  );

  const $props = {
    ...args,
    ...variants
  };

  const __nextRouter = useNextRouter();
  const $ctx = useDataEnv?.() || {};
  const refsRef = React.useRef({});
  const $refs = refsRef.current;

  let [$queries, setDollarQueries] = React.useState<
    Record<string, ReturnType<typeof usePlasmicDataOp>>
  >({});
  const stateSpecs: Parameters<typeof useDollarState>[0] = React.useMemo(
    () => [
      {
        path: "noUnderline",
        type: "private",
        variableType: "variant",
        initFunc: ({ $props, $state, $queries, $ctx }) => $props.noUnderline
      },
      {
        path: "overviewGrid",
        type: "private",
        variableType: "variant",
        initFunc: ({ $props, $state, $queries, $ctx }) => $props.overviewGrid
      },
      {
        path: "editable",
        type: "private",
        variableType: "variant",
        initFunc: ({ $props, $state, $queries, $ctx }) => $props.editable
      }
    ],
    [$props, $ctx, $refs]
  );
  const $state = useDollarState(stateSpecs, {
    $props,
    $ctx,
    $queries: $queries,
    $refs
  });

  const new$Queries: Record<string, ReturnType<typeof usePlasmicDataOp>> = {};
  if (Object.keys(new$Queries).some(k => new$Queries[k] !== $queries[k])) {
    setDollarQueries(new$Queries);

    $queries = new$Queries;
  }

  return (
    <div
      data-plasmic-name={"headerContainer"}
      data-plasmic-override={overrides.headerContainer}
      data-plasmic-root={true}
      data-plasmic-for-node={forNode}
      className={classNames(
        projectcss.all,
        projectcss.root_reset,
        projectcss.plasmic_default_styles,
        projectcss.plasmic_mixins,
        projectcss.plasmic_tokens,
        sty.headerContainer,
        {
          [sty.headerContainereditable]: hasVariant(
            $state,
            "editable",
            "editable"
          ),
          [sty.headerContainernoUnderline]: hasVariant(
            $state,
            "noUnderline",
            "noUnderline"
          ),
          [sty.headerContaineroverviewGrid]: hasVariant(
            $state,
            "overviewGrid",
            "overviewGrid"
          )
        }
      )}
    >
      <div
        data-plasmic-name={"headerWorks"}
        data-plasmic-override={overrides.headerWorks}
        className={classNames(projectcss.all, sty.headerWorks, {
          [sty.headerWorkseditable]: hasVariant($state, "editable", "editable"),
          [sty.headerWorksnoUnderline]: hasVariant(
            $state,
            "noUnderline",
            "noUnderline"
          ),
          [sty.headerWorksoverviewGrid]: hasVariant(
            $state,
            "overviewGrid",
            "overviewGrid"
          )
        })}
      >
        {renderPlasmicSlot({
          defaultContents: "Header Name",
          value: args.children,
          className: classNames(sty.slotTargetChildren, {
            [sty.slotTargetChildreneditable]: hasVariant(
              $state,
              "editable",
              "editable"
            ),
            [sty.slotTargetChildrennoUnderline]: hasVariant(
              $state,
              "noUnderline",
              "noUnderline"
            ),
            [sty.slotTargetChildrenoverviewGrid]: hasVariant(
              $state,
              "overviewGrid",
              "overviewGrid"
            )
          })
        })}
        <SubcomponentAddSectionButton
          data-plasmic-name={"subcomponentAddSectionButton"}
          data-plasmic-override={overrides.subcomponentAddSectionButton}
          className={classNames(
            "__wab_instance",
            sty.subcomponentAddSectionButton,
            {
              [sty.subcomponentAddSectionButtoneditable]: hasVariant(
                $state,
                "editable",
                "editable"
              )
            }
          )}
          licenseOrCertification={(() => {
            try {
              return $props.addButtonSectionType === "licenseorcertification"
                ? "collapsed"
                : undefined;
            } catch (e) {
              if (
                e instanceof TypeError ||
                e?.plasmicType === "PlasmicUndefinedDataError"
              ) {
                return [];
              }
              throw e;
            }
          })()}
          onClickBase={args.addButtonBaseOnClick}
          onClickCertification={args.addButtonCertificationOnClick}
          onClickLicense={args.addButtonLicenseOnClick}
          onClickPatent={args.addButtonPatentOnClick}
          onClickTrademark={args.addButtonTrademarkOnClick}
          patentOrTrademark={(() => {
            try {
              return $props.addButtonSectionType === "patentortrademark"
                ? "collapsed"
                : undefined;
            } catch (e) {
              if (
                e instanceof TypeError ||
                e?.plasmicType === "PlasmicUndefinedDataError"
              ) {
                return [];
              }
              throw e;
            }
          })()}
        />
      </div>
      <section
        data-plasmic-name={"underline"}
        data-plasmic-override={overrides.underline}
        className={classNames(projectcss.all, sty.underline, {
          [sty.underlinenoUnderline]: hasVariant(
            $state,
            "noUnderline",
            "noUnderline"
          ),
          [sty.underlineoverviewGrid]: hasVariant(
            $state,
            "overviewGrid",
            "overviewGrid"
          )
        })}
      />
    </div>
  ) as React.ReactElement | null;
}

const PlasmicDescendants = {
  headerContainer: [
    "headerContainer",
    "headerWorks",
    "subcomponentAddSectionButton",
    "underline"
  ],
  headerWorks: ["headerWorks", "subcomponentAddSectionButton"],
  subcomponentAddSectionButton: ["subcomponentAddSectionButton"],
  underline: ["underline"]
} as const;
type NodeNameType = keyof typeof PlasmicDescendants;
type DescendantsType<T extends NodeNameType> =
  (typeof PlasmicDescendants)[T][number];
type NodeDefaultElementType = {
  headerContainer: "div";
  headerWorks: "div";
  subcomponentAddSectionButton: typeof SubcomponentAddSectionButton;
  underline: "section";
};

type ReservedPropsType = "variants" | "args" | "overrides";
type NodeOverridesType<T extends NodeNameType> = Pick<
  PlasmicProfileSectionsProfileSectionHeading__OverridesType,
  DescendantsType<T>
>;
type NodeComponentProps<T extends NodeNameType> =
  // Explicitly specify variants, args, and overrides as objects
  {
    variants?: PlasmicProfileSectionsProfileSectionHeading__VariantsArgs;
    args?: PlasmicProfileSectionsProfileSectionHeading__ArgsType;
    overrides?: NodeOverridesType<T>;
  } & Omit< // Specify variants directly as props
    PlasmicProfileSectionsProfileSectionHeading__VariantsArgs,
    ReservedPropsType
  > &
    /* Specify args directly as props*/ Omit<
      PlasmicProfileSectionsProfileSectionHeading__ArgsType,
      ReservedPropsType
    > &
    /* Specify overrides for each element directly as props*/ Omit<
      NodeOverridesType<T>,
      ReservedPropsType | VariantPropType | ArgPropType
    > &
    /* Specify props for the root element*/ Omit<
      Partial<React.ComponentProps<NodeDefaultElementType[T]>>,
      ReservedPropsType | VariantPropType | ArgPropType | DescendantsType<T>
    >;

function makeNodeComponent<NodeName extends NodeNameType>(nodeName: NodeName) {
  type PropsType = NodeComponentProps<NodeName> & { key?: React.Key };
  const func = function <T extends PropsType>(
    props: T & StrictProps<T, PropsType>
  ) {
    const { variants, args, overrides } = React.useMemo(
      () =>
        deriveRenderOpts(props, {
          name: nodeName,
          descendantNames: PlasmicDescendants[nodeName],
          internalArgPropNames:
            PlasmicProfileSectionsProfileSectionHeading__ArgProps,
          internalVariantPropNames:
            PlasmicProfileSectionsProfileSectionHeading__VariantProps
        }),
      [props, nodeName]
    );
    return PlasmicProfileSectionsProfileSectionHeading__RenderFunc({
      variants,
      args,
      overrides,
      forNode: nodeName
    });
  };
  if (nodeName === "headerContainer") {
    func.displayName = "PlasmicProfileSectionsProfileSectionHeading";
  } else {
    func.displayName = `PlasmicProfileSectionsProfileSectionHeading.${nodeName}`;
  }
  return func;
}

export const PlasmicProfileSectionsProfileSectionHeading = Object.assign(
  // Top-level PlasmicProfileSectionsProfileSectionHeading renders the root element
  makeNodeComponent("headerContainer"),
  {
    // Helper components rendering sub-elements
    headerWorks: makeNodeComponent("headerWorks"),
    subcomponentAddSectionButton: makeNodeComponent(
      "subcomponentAddSectionButton"
    ),
    underline: makeNodeComponent("underline"),

    // Metadata about props expected for PlasmicProfileSectionsProfileSectionHeading
    internalVariantProps:
      PlasmicProfileSectionsProfileSectionHeading__VariantProps,
    internalArgProps: PlasmicProfileSectionsProfileSectionHeading__ArgProps
  }
);

export default PlasmicProfileSectionsProfileSectionHeading;
/* prettier-ignore-end */
