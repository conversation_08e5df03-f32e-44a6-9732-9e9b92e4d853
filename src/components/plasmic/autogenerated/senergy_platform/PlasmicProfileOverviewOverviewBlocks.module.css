.formattingContainer {
  box-shadow: 0px 2px 20px -2px #8b8b8b33;
  display: inline-flex;
  flex-direction: row;
  position: relative;
  justify-content: flex-start;
  align-items: flex-start;
  transition-property: transform;
  transition-duration: 0.05s;
  justify-self: flex-start;
  flex-wrap: nowrap;
  align-content: stretch;
  overflow: hidden;
  flex-shrink: 1;
  flex-grow: 0;
  max-height: 500px;
  max-width: 550px;
  height: auto;
  width: auto;
  transform: translateX(0px) translateY(0px) translateZ(0px);
  -webkit-transition-property: transform;
  -webkit-transition-duration: 0.05s;
  padding: var(--token-j0qnbpah5w9U);
  margin: 0px;
  border: 0.01cm solid #e4e4e7;
}
@media (max-width: 860px) {
  .formattingContainer {
    max-height: none;
  }
}
.formattingContainersectionContent_introduction {
  min-width: auto;
}
.formattingContainersectionContent_skills {
  align-items: flex-start;
  justify-content: flex-start;
  margin-bottom: 0px;
}
.formattingContainersectionContent_tools {
  align-items: flex-start;
  justify-content: flex-start;
}
.formattingContainercomingSoon {
  width: 100%;
  height: 100%;
  min-width: 0;
  min-height: 0;
}
.formattingContainersectionContent_skills_comingSoon {
  width: auto;
  justify-self: flex-start;
  display: inline-flex;
}
.comingSoonContainer {
  display: none;
  flex-direction: column;
  background: linear-gradient(0deg, #ededed8c 0%, #ededed0d 100%);
  width: 100%;
  height: 100%;
  backdrop-filter: blur(3px);
  position: absolute;
  align-items: center;
  justify-content: center;
  left: 0px;
  top: 0px;
  min-width: 0;
  min-height: 0;
  -webkit-backdrop-filter: blur(3px);
  padding: var(--token-sazGmnf7GWAk);
  border: 1px solid var(--token-1AMvw6c2eIK7);
}
.comingSoonContainercomingSoon {
  position: absolute;
  left: 0px;
  top: 0px;
  align-items: center;
  justify-content: center;
  display: flex;
  flex-direction: column;
  z-index: 1;
  width: 100%;
  height: 100%;
  min-width: 0;
  min-height: 0;
}
.text {
  position: relative;
  width: 100%;
  height: auto;
  max-width: 100%;
  text-align: center;
  font-family: var(--token-z1yrQVi72Nj1);
  font-size: var(--token-_-i82ElPHE7I);
  font-weight: 700;
  transform: rotateX(0deg) rotateY(0deg) rotateZ(-4deg);
  margin-top: 24px;
  align-self: auto;
  min-width: 0;
}
.textcomingSoon {
  margin-top: 0px;
  position: relative;
  left: auto;
  top: auto;
  width: 100%;
  min-width: 0;
}
.pSectionsEducation:global(.__wab_instance) {
  max-width: 100%;
  position: relative;
  display: none;
}
.pSectionsEducationsectionContent_education:global(.__wab_instance) {
  display: flex;
}
.pSectionsExperience:global(.__wab_instance) {
  max-width: 100%;
  position: relative;
  display: none;
}
.pSectionsExperiencesectionContent_experience:global(.__wab_instance) {
  display: flex;
}
.profileSectionsIntroductionBlock:global(.__wab_instance) {
  max-width: 100%;
  position: relative;
  display: none;
}
.profileSectionsIntroductionBlocksectionContent_introduction:global(
    .__wab_instance
  ) {
  display: flex;
}
.profileSectionsLanguageBlock:global(.__wab_instance) {
  max-width: 100%;
  position: relative;
  display: none;
}
.pSectionsPublications:global(.__wab_instance) {
  max-width: 100%;
  position: relative;
  display: none;
}
.pSectionsPublicationssectionContent_publications:global(.__wab_instance) {
  display: flex;
}
.profileSectionsSkillsBlock:global(.__wab_instance) {
  max-width: 100%;
  position: relative;
  display: none;
}
.profileSectionsSkillsBlocksectionContent_skills:global(.__wab_instance) {
  display: flex;
}
.profileSectionsToolsBlock:global(.__wab_instance) {
  max-width: 100%;
  position: relative;
  display: none;
}
.profileSectionsToolsBlocksectionContent_tools:global(.__wab_instance) {
  display: flex;
}
.pSectionsPatents:global(.__wab_instance) {
  max-width: 100%;
  position: relative;
  display: none;
}
.pSectionsPatentssectionContent_patents:global(.__wab_instance) {
  display: flex;
}
.pSectionsPatentssectionContent_trademarks:global(.__wab_instance) {
  display: none;
}
.pSectionsTrademarks:global(.__wab_instance) {
  max-width: 100%;
  position: relative;
  display: none;
}
.pSectionsTrademarkssectionContent_patents:global(.__wab_instance) {
  display: none;
}
.pSectionsTrademarkssectionContent_trademarks:global(.__wab_instance) {
  display: flex;
}
.pSectionsLicenses:global(.__wab_instance) {
  max-width: 100%;
  position: relative;
  display: none;
}
.pSectionsLicensessectionContent_licenses:global(.__wab_instance) {
  display: flex;
}
.pSectionsCertifications:global(.__wab_instance) {
  max-width: 100%;
  position: relative;
  display: none;
}
.pSectionsCertificationssectionContent_certifications:global(.__wab_instance) {
  display: flex;
}
