.navTopButtonAssembly {
  display: flex;
  flex-direction: column;
  position: relative;
  width: 100%;
  height: auto;
  justify-content: flex-start;
  align-items: center;
  justify-self: flex-start;
  grid-column-start: 1 !important;
  grid-column-end: -1 !important;
}
.navigationActiveSlotSlider:global(.__wab_instance) {
  position: absolute;
  width: 100%;
  margin-left: 2px;
  margin-right: 2px;
  z-index: 2;
  min-width: 0;
  flex-shrink: 0;
  display: none;
}
.navigationActiveSlotSliderhoverTab_slot1:global(.__wab_instance) {
  flex-shrink: 0;
  display: none;
}
.navigationActiveSlotSliderhoverTab_slot2:global(.__wab_instance) {
  flex-shrink: 0;
  display: none;
}
.navigationActiveSlotSliderhoverTab_slot3:global(.__wab_instance) {
  flex-shrink: 0;
  display: none;
}
.navigationActiveSlotSlideractiveTab_slot1:global(.__wab_instance) {
  flex-shrink: 0;
  display: flex;
}
.navigationActiveSlotSlideractiveTab_slot2:global(.__wab_instance) {
  flex-shrink: 0;
  display: flex;
}
.navigationActiveSlotSlideractiveTab_slot3:global(.__wab_instance) {
  flex-shrink: 0;
  display: flex;
}
.navigationActiveSlotSlidercollapsed:global(.__wab_instance) {
  flex-shrink: 0;
}
.navigationActiveSlotSlideractiveTab_slot1_hoverTab_slot1:global(
    .__wab_instance
  ) {
  flex-shrink: 0;
}
.navigationActiveSlotSlideractiveTab_slot2_hoverTab_slot2:global(
    .__wab_instance
  ) {
  flex-shrink: 0;
}
.navigationActiveSlotSlideractiveTab_slot3_hoverTab_slot3:global(
    .__wab_instance
  ) {
  flex-shrink: 0;
}
.navigationHoverSlotSlider:global(.__wab_instance) {
  position: absolute;
  width: 100%;
  margin-right: 2px;
  margin-left: 2px;
  z-index: 1;
  min-width: 0;
  flex-shrink: 0;
}
.navigationHoverSlotSliderhoverTab_slot1:global(.__wab_instance) {
  flex-shrink: 0;
}
.navigationHoverSlotSliderhoverTab_slot2:global(.__wab_instance) {
  flex-shrink: 0;
}
.navigationHoverSlotSliderhoverTab_slot3:global(.__wab_instance) {
  flex-shrink: 0;
}
.navigationHoverSlotSlideractiveTab_slot1:global(.__wab_instance) {
  flex-shrink: 0;
}
.navigationHoverSlotSlideractiveTab_slot2:global(.__wab_instance) {
  flex-shrink: 0;
}
.navigationHoverSlotSlideractiveTab_slot3:global(.__wab_instance) {
  flex-shrink: 0;
}
.navigationHoverSlotSlideractiveTab_slot1_hoverTab_slot1:global(
    .__wab_instance
  ) {
  flex-shrink: 0;
}
.navigationHoverSlotSlideractiveTab_slot1_hoverTab_slot2:global(
    .__wab_instance
  ) {
  flex-shrink: 0;
}
.navigationHoverSlotSlideractiveTab_slot2_hoverTab_slot1:global(
    .__wab_instance
  ) {
  flex-shrink: 0;
}
.navigationHoverSlotSlideractiveTab_slot1_hoverTab_slot3:global(
    .__wab_instance
  ) {
  flex-shrink: 0;
}
.navigationHoverSlotSliderhoverTab_slot1_activeTab_slot3:global(
    .__wab_instance
  ) {
  flex-shrink: 0;
}
.navigationHoverSlotSlideractiveTab_slot2_hoverTab_slot3:global(
    .__wab_instance
  ) {
  flex-shrink: 0;
}
.navigationHoverSlotSlideractiveTab_slot3_hoverTab_slot2:global(
    .__wab_instance
  ) {
  flex-shrink: 0;
}
.searchButton:global(.__wab_instance) {
  max-width: 100%;
  position: relative;
  z-index: 5;
  flex-shrink: 0;
}
.searchButtonhoverTab_slot1:global(.__wab_instance) {
  flex-shrink: 0;
}
.searchButtonhoverTab_slot3:global(.__wab_instance) {
  flex-shrink: 0;
}
.searchButtonactiveTab_slot1:global(.__wab_instance) {
  flex-shrink: 0;
}
.searchButtonactiveTab_slot3:global(.__wab_instance) {
  flex-shrink: 0;
}
.searchButtoncollapsed:global(.__wab_instance) {
  flex-shrink: 0;
}
.searchButtonactiveTab_slot1_hoverTab_slot1:global(.__wab_instance) {
  flex-shrink: 0;
}
.icon {
  position: relative;
  object-fit: cover;
  max-width: 100%;
  width: var(--token-j0qnbpah5w9U);
  height: var(--token-j0qnbpah5w9U);
  margin-right: 0;
}
.bookmarkButton:global(.__wab_instance) {
  max-width: 100%;
  position: relative;
  z-index: 5;
  flex-shrink: 0;
}
.bookmarkButtonhoverTab_slot1:global(.__wab_instance) {
  flex-shrink: 0;
}
.bookmarkButtonhoverTab_slot2:global(.__wab_instance) {
  flex-shrink: 0;
}
.bookmarkButtonhoverTab_slot3:global(.__wab_instance) {
  flex-shrink: 0;
}
.bookmarkButtonactiveTab_slot1:global(.__wab_instance) {
  flex-shrink: 0;
}
.bookmarkButtonactiveTab_slot2:global(.__wab_instance) {
  flex-shrink: 0;
}
.bookmarkButtonactiveTab_slot3:global(.__wab_instance) {
  flex-shrink: 0;
}
.bookmarkButtoncollapsed:global(.__wab_instance) {
  flex-shrink: 0;
}
.bookmarkButtonunAuthed:global(.__wab_instance) {
  flex-shrink: 0;
  display: none;
}
.bookmarkButtonactiveTab_slot2_hoverTab_slot1:global(.__wab_instance) {
  flex-shrink: 0;
}
.bookmarkButtonactiveTab_slot2_hoverTab_slot2:global(.__wab_instance) {
  flex-shrink: 0;
}
.icon2 {
  position: relative;
  object-fit: cover;
  max-width: 100%;
  width: var(--token-j0qnbpah5w9U);
  height: var(--token-j0qnbpah5w9U);
  margin-right: 0;
}
.messagesButton:global(.__wab_instance) {
  max-width: 100%;
  position: relative;
  z-index: 5;
  flex-shrink: 0;
  display: none;
}
.messagesButtonhoverTab_slot1:global(.__wab_instance) {
  flex-shrink: 0;
}
.messagesButtonhoverTab_slot3:global(.__wab_instance) {
  flex-shrink: 0;
}
.messagesButtonactiveTab_slot1:global(.__wab_instance) {
  flex-shrink: 0;
}
.messagesButtonactiveTab_slot3:global(.__wab_instance) {
  flex-shrink: 0;
}
.messagesButtoncollapsed:global(.__wab_instance) {
  flex-shrink: 0;
}
.messagesButtonunAuthed:global(.__wab_instance) {
  flex-shrink: 0;
  display: none;
}
.messagesButtonactiveTab_slot3_hoverTab_slot3:global(.__wab_instance) {
  flex-shrink: 0;
}
.icon3 {
  position: relative;
  object-fit: cover;
  max-width: 100%;
  width: var(--token-j0qnbpah5w9U);
  height: var(--token-j0qnbpah5w9U);
  margin-right: 0;
}
