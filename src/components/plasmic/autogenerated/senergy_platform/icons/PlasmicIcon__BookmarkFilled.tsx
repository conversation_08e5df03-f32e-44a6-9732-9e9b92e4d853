/* eslint-disable */
/* tslint:disable */
// @ts-nocheck
/* prettier-ignore-start */
import React from "react";
import { classNames } from "@plasmicapp/react-web";

export type BookmarkFilledIconProps = React.ComponentProps<"svg"> & {
  title?: string;
};

export function BookmarkFilledIcon(props: BookmarkFilledIconProps) {
  const { className, style, title, ...restProps } = props;
  return (
    <svg
      xmlns={"http://www.w3.org/2000/svg"}
      className={classNames(
        "plasmic-default__svg",
        className,
        "icon icon-tabler icon-tabler-bookmark-filled"
      )}
      viewBox={"0 0 24 24"}
      strokeWidth={"1.5"}
      stroke={"#292929"}
      fill={"none"}
      strokeLinecap={"round"}
      strokeLinejoin={"round"}
      height={"1em"}
      style={style}
      {...restProps}
    >
      {title && <title>{title}</title>}

      <path d={"M0 0h24v24H0z"} fill={"none"} stroke={"none"}></path>

      <path
        d={
          "M14 2a5 5 0 015 5v14a1 1 0 01-1.555.832L12 18.202l-5.444 3.63a1 1 0 01-1.55-.72L5 21V7a5 5 0 015-5h4z"
        }
        fill={"currentColor"}
        stroke={"none"}
      ></path>
    </svg>
  );
}

export default BookmarkFilledIcon;
/* prettier-ignore-end */
