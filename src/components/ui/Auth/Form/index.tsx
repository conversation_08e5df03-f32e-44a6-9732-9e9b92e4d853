'use client';

import { zod<PERSON>esolver } from '@hookform/resolvers/zod';
import { Loader2 } from 'lucide-react';
import { useRouter } from 'next/navigation';
import React, { useState } from 'react';
import {
  type ControllerRenderProps,
  type UseFormReturn,
  useForm,
} from 'react-hook-form';
import * as z from 'zod';
import { useAuthRedirect, useMagicLink } from '@/auth/hooks';
// Import shared auth utilities
import {
  type AuthLoadingType,
  passkeySignIn,
  sendMagicLink,
  socialSignIn,
} from '@/auth/service';
import { formatTimeLeft } from '@/auth/utils';
import { SendPaperPlaneIcon } from '@/components/plasmic/autogenerated/senergy_platform/icons/PlasmicIcon__SendPaperPlane';
import { Button } from '@/components/ui/button';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';

const loginSchema = z.object({
  email: z.string().email({ message: 'Please enter a valid email address' }),
});

type FormValues = z.infer<typeof loginSchema>;

export interface AuthFormProps {
  onSuccess?: () => void;
  redirectPath?: string;
  closeOnMagicLink?: boolean;
}

// LinkedIn button component
const LinkedInButton = ({
  loadingType,
  onSignIn,
}: {
  loadingType: AuthLoadingType;
  onSignIn: () => Promise<void>;
}) => (
  <Button className="w-full" disabled={loadingType !== null} onClick={onSignIn}>
    {loadingType === 'linkedin' ? (
      <Loader2 className="h-4 w-4 animate-spin" />
    ) : (
      <svg
        aria-labelledby="linkedinIconTitle"
        height="1em"
        viewBox="0 0 24 24"
        width="1em"
        xmlns="http://www.w3.org/2000/svg"
      >
        <title id="linkedinIconTitle">LinkedIn icon</title>
        <path
          d="M19 3a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2zm-.5 15.5v-5.3a3.26 3.26 0 0 0-3.26-3.26c-.85 0-1.84.52-2.32 1.3v-1.11h-2.79v8.37h2.79v-4.93c0-.77.62-1.4 1.39-1.4a1.4 1.4 0 0 1 1.4 1.4v4.93zM6.88 8.56a1.68 1.68 0 0 0 1.68-1.68c0-.93-.75-1.69-1.68-1.69a1.69 1.69 0 0 0-1.69 1.69c0 .93.76 1.68 1.69 1.68m1.39 9.94v-8.37H5.5v8.37z"
          fill="currentColor"
        />
      </svg>
    )}
    Continue with LinkedIn
  </Button>
);

// Magic link sent view
const MagicLinkSentView = ({
  isExpired,
  sentEmail,
  secondsLeft,
  loadingType,
  handleResendLink,
  clearMagicLink,
  isLoading,
  form,
}: {
  isExpired: boolean;
  sentEmail: string;
  secondsLeft: number;
  loadingType: AuthLoadingType;
  handleResendLink: () => void;
  clearMagicLink: () => void;
  isLoading: boolean;
  form: UseFormReturn<FormValues>;
}) => (
  <div className="space-y-6 p-4 text-center">
    <div className="mx-auto flex h-12 w-12 items-center justify-center rounded-full bg-primary/10">
      <SendPaperPlaneIcon className="h-6 w-6 text-primary" />
    </div>
    <div className="space-y-2">
      <h3 className="font-semibold text-xl tracking-tight">
        {isExpired ? 'Link expired' : 'Check your email'}
      </h3>
      <p className="text-muted-foreground text-sm">
        {isExpired ? (
          'Your magic link has expired.'
        ) : (
          <>
            We've sent a magic link to{' '}
            <span className="font-medium text-foreground">{sentEmail}</span>
          </>
        )}
      </p>
      {!isExpired && (
        <p className="inline-flex items-center gap-1.5 text-muted-foreground text-xs">
          <span
            className={`inline-block h-2 w-2 ${secondsLeft > 60 ? 'animate-pulse' : 'animate-ping'} rounded-full ${secondsLeft > 60 ? 'bg-primary/50' : 'bg-orange-500'}`}
          />
          Link expires in {formatTimeLeft(secondsLeft)}
        </p>
      )}
    </div>
    <div className="flex flex-col gap-2 pt-4">
      {isExpired ? (
        <Button disabled={isLoading} onClick={handleResendLink}>
          {loadingType === 'email' ? (
            <Loader2 className="mr-2 h-4 w-4 animate-spin" />
          ) : null}
          Resend magic link
        </Button>
      ) : (
        secondsLeft < 60 && (
          <Button
            disabled={isLoading}
            onClick={handleResendLink}
            variant="outline"
          >
            {loadingType === 'email' ? (
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
            ) : null}
            Resend magic link
          </Button>
        )
      )}
      <Button
        onClick={() => {
          clearMagicLink();
          form.reset();
        }}
        variant="outline"
      >
        Use a different email
      </Button>
    </div>
  </div>
);

// Email login form
const EmailLoginForm = ({
  form,
  onSubmit,
  isLoading,
  loadingType,
  renderFormField,
}: {
  form: UseFormReturn<FormValues>;
  onSubmit: (values: FormValues) => void;
  isLoading: boolean;
  loadingType: AuthLoadingType;
  renderFormField: (props: {
    field: ControllerRenderProps<FormValues, 'email'>;
  }) => React.ReactElement;
}) => (
  <Form {...form}>
    <form className="space-y-4" onSubmit={form.handleSubmit(onSubmit)}>
      <FormField control={form.control} name="email" render={renderFormField} />

      <Button className="mt-2 w-full" disabled={isLoading} type="submit">
        {loadingType === 'email' ? (
          <Loader2 className="mr-2 h-4 w-4 animate-spin" />
        ) : null}
        Continue with Email
      </Button>
    </form>
  </Form>
);

// Divider component
const Divider = () => (
  <div
    style={{
      display: 'flex',
      alignItems: 'center',
      gap: '10px',
    }}
  >
    <div style={{ flex: 1, height: '1px', background: '#e0e0e0' }} />
    <span style={{ color: '#666', fontSize: '14px' }}>or</span>
    <div style={{ flex: 1, height: '1px', background: '#e0e0e0' }} />
  </div>
);

export default function AuthForm({
  onSuccess,
  redirectPath = '/settings',
  closeOnMagicLink = true,
}: AuthFormProps) {
  const [loadingType, setLoadingType] = useState<AuthLoadingType>(null);
  const router = useRouter();

  // Listen for authentication changes across tabs and redirect if needed
  useAuthRedirect();

  // Use the shared magic link hook
  const {
    magicLinkSent,
    sentEmail,
    isExpired,
    secondsLeft,
    clearMagicLink,
    syncWithStorage,
  } = useMagicLink();

  const form = useForm<FormValues>({
    resolver: zodResolver(loginSchema),
    defaultValues: {
      email: sentEmail || '',
    },
  });

  const isLoading = loadingType !== null;

  function onSubmit(_values: FormValues) {
    login();
  }

  const login = async () => {
    const email = form.getValues('email');

    await sendMagicLink({
      email,
      callbackURL: redirectPath,
      onRequest: () => setLoadingType('email'),
      onSuccess: () => {
        setLoadingType(null);
        // Sync with localStorage to update our UI state
        syncWithStorage();
        if (onSuccess && closeOnMagicLink) {
          onSuccess();
        }
      },
      onError: () => setLoadingType(null),
    });
  };

  const _handlePasskeySignIn = async () => {
    await passkeySignIn({
      onRequest: () => setLoadingType('passkey'),
      onSuccess: () => {
        if (onSuccess) {
          onSuccess();
        } else {
          router.push(redirectPath);
        }
      },
      onError: () => setLoadingType(null),
    });
  };

  const handleLinkedInSignIn = async () => {
    await socialSignIn({
      provider: 'linkedin',
      callbackURL: redirectPath,
      errorCallbackURL: '/login',
      newUserCallbackURL: '/onboarding',
      onRequest: () => setLoadingType('linkedin'),
      onError: () => setLoadingType(null),
    });
  };

  const renderFormField = React.useCallback(
    ({ field }: { field: ControllerRenderProps<FormValues, 'email'> }) => (
      <FormItem>
        <FormLabel>Email</FormLabel>
        <FormControl>
          <Input
            {...field}
            disabled={isLoading}
            placeholder="<EMAIL>"
          />
        </FormControl>
        <FormMessage />
      </FormItem>
    ),
    [isLoading]
  );

  const handleResendLink = () => {
    clearMagicLink();
    form.setValue('email', sentEmail);
    login();
  };

  if (magicLinkSent) {
    return (
      <MagicLinkSentView
        clearMagicLink={clearMagicLink}
        form={form}
        handleResendLink={handleResendLink}
        isExpired={isExpired}
        isLoading={isLoading}
        loadingType={loadingType}
        secondsLeft={secondsLeft}
        sentEmail={sentEmail}
      />
    );
  }

  return (
    <div className="mb-1 space-y-4">
      <h2 className="font-bold text-2xl tracking-tight">Login</h2>

      {/*      <EmailLoginForm
        form={form}
        isLoading={isLoading}
        loadingType={loadingType}
        onSubmit={onSubmit}
        renderFormField={renderFormField}
      />

      <Divider />*/}

      <LinkedInButton
        loadingType={loadingType}
        onSignIn={handleLinkedInSignIn}
      />
    </div>
  );
}
