/* eslint-disable */
/* tslint:disable */
// @ts-nocheck
/* prettier-ignore-start */

/** @jsxRuntime classic */
/** @jsx createPlasmicElementProxy */
/** @jsxFrag React.Fragment */

// This class is auto-generated by Plasmic; please do not edit!
// Plasmic Project: nZeWKcn21KijvC7eT8B3c7
// Component: 9Evwwahu_Iga

"use client";

import * as React from "react";

import Head from "next/head";
import Link, { LinkProps } from "next/link";
import { useRouter } from "next/navigation";

import {
  Flex as Flex__,
  MultiChoiceArg,
  PlasmicDataSourceContextProvider as PlasmicDataSourceContextProvider__,
  PlasmicIcon as PlasmicIcon__,
  PlasmicImg as PlasmicImg__,
  PlasmicLink as PlasmicLink__,
  PlasmicPageGuard as PlasmicPageGuard__,
  SingleBooleanChoiceArg,
  SingleChoiceArg,
  Stack as Stack__,
  StrictProps,
  Trans as Trans__,
  classNames,
  createPlasmicElementProxy,
  deriveRenderOpts,
  ensureGlobalVariants,
  generateOnMutateForSpec,
  generateStateOnChangeProp,
  generateStateOnChangePropForCodeComponents,
  generateStateValueProp,
  get as $stateGet,
  hasVariant,
  initializeCodeComponentStates,
  initializePlasmicStates,
  makeFragment,
  omit,
  pick,
  renderPlasmicSlot,
  set as $stateSet,
  useCurrentUser,
  useDollarState,
  usePlasmicTranslator,
  useTrigger,
  wrapWithClassName
} from "@plasmicapp/react-web";
import {
  DataCtxReader as DataCtxReader__,
  useDataEnv,
  useGlobalActions
} from "@plasmicapp/host";

import ProfileSectionsProfileSectionHeading from "../../ProfileSectionsProfileSectionHeading"; // plasmic-import: Uz656rZzIxzC/component
import ProfileTileExperiencesTile from "../../ProfileTileExperiencesTile"; // plasmic-import: HKIN54Ptec2v/component

import "@plasmicapp/react-web/lib/plasmic.css";

import projectcss from "./plasmic.module.css"; // plasmic-import: nZeWKcn21KijvC7eT8B3c7/projectcss
import sty from "./PlasmicProfileSectionsExperienceBlock.module.css"; // plasmic-import: 9Evwwahu_Iga/css

createPlasmicElementProxy;

export type PlasmicProfileSectionsExperienceBlock__VariantMembers = {
  overviewGrid: "overvierwGrid";
  editable: "editable";
};
export type PlasmicProfileSectionsExperienceBlock__VariantsArgs = {
  overviewGrid?: SingleChoiceArg<"overvierwGrid">;
  editable?: SingleBooleanChoiceArg<"editable">;
};
type VariantPropType =
  keyof PlasmicProfileSectionsExperienceBlock__VariantsArgs;
export const PlasmicProfileSectionsExperienceBlock__VariantProps =
  new Array<VariantPropType>("overviewGrid", "editable");

export type PlasmicProfileSectionsExperienceBlock__ArgsType = {
  allExperience?: any;
  addButtonOnClick?: (event: any) => void;
  onAllExperienceChange?: (val: string) => void;
};
type ArgPropType = keyof PlasmicProfileSectionsExperienceBlock__ArgsType;
export const PlasmicProfileSectionsExperienceBlock__ArgProps =
  new Array<ArgPropType>(
    "allExperience",
    "addButtonOnClick",
    "onAllExperienceChange"
  );

export type PlasmicProfileSectionsExperienceBlock__OverridesType = {
  experiencesSection?: Flex__<"div">;
  profileSectionsProfileSectionHeading?: Flex__<
    typeof ProfileSectionsProfileSectionHeading
  >;
  experiencesDisplayContainer?: Flex__<"section">;
  experienceTile?: Flex__<typeof ProfileTileExperiencesTile>;
};

export interface DefaultProfileSectionsExperienceBlockProps {
  allExperience?: any;
  addButtonOnClick?: (event: any) => void;
  onAllExperienceChange?: (val: string) => void;
  overviewGrid?: SingleChoiceArg<"overvierwGrid">;
  editable?: SingleBooleanChoiceArg<"editable">;
  className?: string;
}

const $$ = {};

function useNextRouter() {
  try {
    return useRouter();
  } catch {}
  return undefined;
}

function PlasmicProfileSectionsExperienceBlock__RenderFunc(props: {
  variants: PlasmicProfileSectionsExperienceBlock__VariantsArgs;
  args: PlasmicProfileSectionsExperienceBlock__ArgsType;
  overrides: PlasmicProfileSectionsExperienceBlock__OverridesType;
  forNode?: string;
}) {
  const { variants, overrides, forNode } = props;

  const args = React.useMemo(
    () =>
      Object.assign(
        {},
        Object.fromEntries(
          Object.entries(props.args).filter(([_, v]) => v !== undefined)
        )
      ),
    [props.args]
  );

  const $props = {
    ...args,
    ...variants
  };

  const __nextRouter = useNextRouter();
  const $ctx = useDataEnv?.() || {};
  const refsRef = React.useRef({});
  const $refs = refsRef.current;

  let [$queries, setDollarQueries] = React.useState<
    Record<string, ReturnType<typeof usePlasmicDataOp>>
  >({});
  const stateSpecs: Parameters<typeof useDollarState>[0] = React.useMemo(
    () => [
      {
        path: "overviewGrid",
        type: "private",
        variableType: "variant",
        initFunc: ({ $props, $state, $queries, $ctx }) => $props.overviewGrid
      },
      {
        path: "editable",
        type: "private",
        variableType: "variant",
        initFunc: ({ $props, $state, $queries, $ctx }) => $props.editable
      },
      {
        path: "experienceTile[].jobTitleInputValue",
        type: "private",
        variableType: "text"
      },
      {
        path: "experienceTile[].companyNameInputValue",
        type: "private",
        variableType: "text"
      },
      {
        path: "experienceTile[].locationInputValue",
        type: "private",
        variableType: "text"
      },
      {
        path: "experienceTile[].startDateInputValue",
        type: "private",
        variableType: "text"
      },
      {
        path: "experienceTile[].endDateInputValue",
        type: "private",
        variableType: "text"
      },
      {
        path: "experienceTile[].presentStateInputValue",
        type: "private",
        variableType: "boolean"
      },
      {
        path: "experienceTile[].deleteButtonClickStage",
        type: "private",
        variableType: "number"
      },
      {
        path: "experienceTile[].deleteButtonDisabled",
        type: "private",
        variableType: "text"
      },
      {
        path: "allExperience",
        type: "writable",
        variableType: "object",

        valueProp: "allExperience",
        onChangeProp: "onAllExperienceChange"
      },
      {
        path: "experienceTile[].descriptionInputValue",
        type: "private",
        variableType: "text"
      }
    ],
    [$props, $ctx, $refs]
  );
  const $state = useDollarState(stateSpecs, {
    $props,
    $ctx,
    $queries: $queries,
    $refs
  });

  const new$Queries: Record<string, ReturnType<typeof usePlasmicDataOp>> = {};
  if (Object.keys(new$Queries).some(k => new$Queries[k] !== $queries[k])) {
    setDollarQueries(new$Queries);

    $queries = new$Queries;
  }

  return (
    <div
      data-plasmic-name={"experiencesSection"}
      data-plasmic-override={overrides.experiencesSection}
      data-plasmic-root={true}
      data-plasmic-for-node={forNode}
      className={classNames(
        projectcss.all,
        projectcss.root_reset,
        projectcss.plasmic_default_styles,
        projectcss.plasmic_mixins,
        projectcss.plasmic_tokens,
        sty.experiencesSection,
        {
          [sty.experiencesSectioneditable]: hasVariant(
            $state,
            "editable",
            "editable"
          ),
          [sty.experiencesSectionoverviewGrid_overvierwGrid]: hasVariant(
            $state,
            "overviewGrid",
            "overvierwGrid"
          )
        }
      )}
    >
      <ProfileSectionsProfileSectionHeading
        data-plasmic-name={"profileSectionsProfileSectionHeading"}
        data-plasmic-override={overrides.profileSectionsProfileSectionHeading}
        addButtonBaseOnClick={args.addButtonOnClick}
        className={classNames(
          "__wab_instance",
          sty.profileSectionsProfileSectionHeading,
          {
            [sty.profileSectionsProfileSectionHeadingeditable]: hasVariant(
              $state,
              "editable",
              "editable"
            ),
            [sty.profileSectionsProfileSectionHeadingoverviewGrid_overvierwGrid]:
              hasVariant($state, "overviewGrid", "overvierwGrid")
          }
        )}
        editable={hasVariant($state, "editable", "editable") ? true : undefined}
        overviewGrid={
          hasVariant($state, "overviewGrid", "overvierwGrid") ? true : undefined
        }
      >
        {"Experience"}
      </ProfileSectionsProfileSectionHeading>
      <section
        data-plasmic-name={"experiencesDisplayContainer"}
        data-plasmic-override={overrides.experiencesDisplayContainer}
        className={classNames(projectcss.all, sty.experiencesDisplayContainer, {
          [sty.experiencesDisplayContainereditable]: hasVariant(
            $state,
            "editable",
            "editable"
          ),
          [sty.experiencesDisplayContaineroverviewGrid_overvierwGrid]:
            hasVariant($state, "overviewGrid", "overvierwGrid")
        })}
      >
        {(_par => (!_par ? [] : Array.isArray(_par) ? _par : [_par]))(
          (() => {
            try {
              return $state.allExperience.data;
            } catch (e) {
              if (
                e instanceof TypeError ||
                e?.plasmicType === "PlasmicUndefinedDataError"
              ) {
                return [];
              }
              throw e;
            }
          })()
        ).map((__plasmic_item_0, __plasmic_idx_0) => {
          const currentItem = __plasmic_item_0;
          const currentIndex = __plasmic_idx_0;
          return (() => {
            const child$Props = {
              className: classNames("__wab_instance", sty.experienceTile, {
                [sty.experienceTileeditable]: hasVariant(
                  $state,
                  "editable",
                  "editable"
                ),
                [sty.experienceTileoverviewGrid_overvierwGrid]: hasVariant(
                  $state,
                  "overviewGrid",
                  "overvierwGrid"
                )
              }),
              companyNameInputValue: generateStateValueProp($state, [
                "experienceTile",
                __plasmic_idx_0,
                "companyNameInputValue"
              ]),
              deleteButtonClickStage: generateStateValueProp($state, [
                "experienceTile",
                __plasmic_idx_0,
                "deleteButtonClickStage"
              ]),
              deleteButtonDisabled: generateStateValueProp($state, [
                "experienceTile",
                __plasmic_idx_0,
                "deleteButtonDisabled"
              ]),
              descriptionInputValue: generateStateValueProp($state, [
                "experienceTile",
                __plasmic_idx_0,
                "descriptionInputValue"
              ]),
              editable: (() => {
                try {
                  return $state.editable;
                } catch (e) {
                  if (
                    e instanceof TypeError ||
                    e?.plasmicType === "PlasmicUndefinedDataError"
                  ) {
                    return [];
                  }
                  throw e;
                }
              })(),
              endDateInputValue: generateStateValueProp($state, [
                "experienceTile",
                __plasmic_idx_0,
                "endDateInputValue"
              ]),
              experienceId: (() => {
                try {
                  return currentItem.id;
                } catch (e) {
                  if (
                    e instanceof TypeError ||
                    e?.plasmicType === "PlasmicUndefinedDataError"
                  ) {
                    return undefined;
                  }
                  throw e;
                }
              })(),
              jobTitleInputValue: generateStateValueProp($state, [
                "experienceTile",
                __plasmic_idx_0,
                "jobTitleInputValue"
              ]),
              key: currentIndex,
              locationInputValue: generateStateValueProp($state, [
                "experienceTile",
                __plasmic_idx_0,
                "locationInputValue"
              ]),
              onCompanyNameInputValueChange: async (...eventArgs: any) => {
                generateStateOnChangeProp($state, [
                  "experienceTile",
                  __plasmic_idx_0,
                  "companyNameInputValue"
                ]).apply(null, eventArgs);

                if (
                  eventArgs.length > 1 &&
                  eventArgs[1] &&
                  eventArgs[1]._plasmic_state_init_
                ) {
                  return;
                }
              },
              onDeleteButtonClickStageChange: async (...eventArgs: any) => {
                generateStateOnChangeProp($state, [
                  "experienceTile",
                  __plasmic_idx_0,
                  "deleteButtonClickStage"
                ]).apply(null, eventArgs);

                if (
                  eventArgs.length > 1 &&
                  eventArgs[1] &&
                  eventArgs[1]._plasmic_state_init_
                ) {
                  return;
                }
              },
              onDeleteButtonDisabledChange: async (...eventArgs: any) => {
                generateStateOnChangeProp($state, [
                  "experienceTile",
                  __plasmic_idx_0,
                  "deleteButtonDisabled"
                ]).apply(null, eventArgs);

                if (
                  eventArgs.length > 1 &&
                  eventArgs[1] &&
                  eventArgs[1]._plasmic_state_init_
                ) {
                  return;
                }
              },
              onDescriptionInputValueChange: async (...eventArgs: any) => {
                generateStateOnChangeProp($state, [
                  "experienceTile",
                  __plasmic_idx_0,
                  "descriptionInputValue"
                ]).apply(null, eventArgs);

                if (
                  eventArgs.length > 1 &&
                  eventArgs[1] &&
                  eventArgs[1]._plasmic_state_init_
                ) {
                  return;
                }
              },
              onEndDateInputValueChange: async (...eventArgs: any) => {
                generateStateOnChangeProp($state, [
                  "experienceTile",
                  __plasmic_idx_0,
                  "endDateInputValue"
                ]).apply(null, eventArgs);

                if (
                  eventArgs.length > 1 &&
                  eventArgs[1] &&
                  eventArgs[1]._plasmic_state_init_
                ) {
                  return;
                }
              },
              onJobTitleInputValueChange: async (...eventArgs: any) => {
                generateStateOnChangeProp($state, [
                  "experienceTile",
                  __plasmic_idx_0,
                  "jobTitleInputValue"
                ]).apply(null, eventArgs);

                if (
                  eventArgs.length > 1 &&
                  eventArgs[1] &&
                  eventArgs[1]._plasmic_state_init_
                ) {
                  return;
                }
              },
              onLocationInputValueChange: async (...eventArgs: any) => {
                generateStateOnChangeProp($state, [
                  "experienceTile",
                  __plasmic_idx_0,
                  "locationInputValue"
                ]).apply(null, eventArgs);

                if (
                  eventArgs.length > 1 &&
                  eventArgs[1] &&
                  eventArgs[1]._plasmic_state_init_
                ) {
                  return;
                }
              },
              onPresentStateInputValueChange: async (...eventArgs: any) => {
                generateStateOnChangeProp($state, [
                  "experienceTile",
                  __plasmic_idx_0,
                  "presentStateInputValue"
                ]).apply(null, eventArgs);

                if (
                  eventArgs.length > 1 &&
                  eventArgs[1] &&
                  eventArgs[1]._plasmic_state_init_
                ) {
                  return;
                }
              },
              onStartDateInputValueChange: async (...eventArgs: any) => {
                generateStateOnChangeProp($state, [
                  "experienceTile",
                  __plasmic_idx_0,
                  "startDateInputValue"
                ]).apply(null, eventArgs);

                if (
                  eventArgs.length > 1 &&
                  eventArgs[1] &&
                  eventArgs[1]._plasmic_state_init_
                ) {
                  return;
                }
              },
              overview: hasVariant($state, "overviewGrid", "overvierwGrid")
                ? true
                : undefined,
              presentStateInputValue: generateStateValueProp($state, [
                "experienceTile",
                __plasmic_idx_0,
                "presentStateInputValue"
              ]),
              startDateInputValue: generateStateValueProp($state, [
                "experienceTile",
                __plasmic_idx_0,
                "startDateInputValue"
              ])
            };

            initializePlasmicStates(
              $state,
              [
                {
                  name: "experienceTile[].jobTitleInputValue",
                  initFunc: ({ $props, $state, $queries }) =>
                    (() => {
                      try {
                        return currentItem.title;
                      } catch (e) {
                        if (
                          e instanceof TypeError ||
                          e?.plasmicType === "PlasmicUndefinedDataError"
                        ) {
                          return undefined;
                        }
                        throw e;
                      }
                    })()
                },
                {
                  name: "experienceTile[].companyNameInputValue",
                  initFunc: ({ $props, $state, $queries }) =>
                    (() => {
                      try {
                        return currentItem.company;
                      } catch (e) {
                        if (
                          e instanceof TypeError ||
                          e?.plasmicType === "PlasmicUndefinedDataError"
                        ) {
                          return undefined;
                        }
                        throw e;
                      }
                    })()
                },
                {
                  name: "experienceTile[].locationInputValue",
                  initFunc: ({ $props, $state, $queries }) =>
                    (() => {
                      try {
                        return currentItem.location;
                      } catch (e) {
                        if (
                          e instanceof TypeError ||
                          e?.plasmicType === "PlasmicUndefinedDataError"
                        ) {
                          return undefined;
                        }
                        throw e;
                      }
                    })()
                },
                {
                  name: "experienceTile[].startDateInputValue",
                  initFunc: ({ $props, $state, $queries }) =>
                    (() => {
                      try {
                        return currentItem.start_date
                          ? currentItem.start_date
                              .split("T")[0]
                              .split("-")
                              .join("-")
                          : null;
                      } catch (e) {
                        if (
                          e instanceof TypeError ||
                          e?.plasmicType === "PlasmicUndefinedDataError"
                        ) {
                          return undefined;
                        }
                        throw e;
                      }
                    })()
                },
                {
                  name: "experienceTile[].endDateInputValue",
                  initFunc: ({ $props, $state, $queries }) =>
                    (() => {
                      try {
                        return currentItem.end_date
                          ? currentItem.end_date
                              .split("T")[0]
                              .split("-")
                              .join("-")
                          : null;
                      } catch (e) {
                        if (
                          e instanceof TypeError ||
                          e?.plasmicType === "PlasmicUndefinedDataError"
                        ) {
                          return undefined;
                        }
                        throw e;
                      }
                    })()
                },
                {
                  name: "experienceTile[].presentStateInputValue",
                  initFunc: ({ $props, $state, $queries }) =>
                    (() => {
                      try {
                        return currentItem.present;
                      } catch (e) {
                        if (
                          e instanceof TypeError ||
                          e?.plasmicType === "PlasmicUndefinedDataError"
                        ) {
                          return undefined;
                        }
                        throw e;
                      }
                    })()
                },
                {
                  name: "experienceTile[].deleteButtonClickStage",
                  initFunc: ({ $props, $state, $queries }) => 0
                },
                {
                  name: "experienceTile[].deleteButtonDisabled",
                  initFunc: ({ $props, $state, $queries }) => undefined
                },
                {
                  name: "experienceTile[].descriptionInputValue",
                  initFunc: ({ $props, $state, $queries }) =>
                    (() => {
                      try {
                        return currentItem.description;
                      } catch (e) {
                        if (
                          e instanceof TypeError ||
                          e?.plasmicType === "PlasmicUndefinedDataError"
                        ) {
                          return undefined;
                        }
                        throw e;
                      }
                    })()
                }
              ],
              [__plasmic_idx_0]
            );
            return (
              <ProfileTileExperiencesTile
                data-plasmic-name={"experienceTile"}
                data-plasmic-override={overrides.experienceTile}
                {...child$Props}
              />
            );
          })();
        })}
      </section>
    </div>
  ) as React.ReactElement | null;
}

const PlasmicDescendants = {
  experiencesSection: [
    "experiencesSection",
    "profileSectionsProfileSectionHeading",
    "experiencesDisplayContainer",
    "experienceTile"
  ],
  profileSectionsProfileSectionHeading: [
    "profileSectionsProfileSectionHeading"
  ],
  experiencesDisplayContainer: [
    "experiencesDisplayContainer",
    "experienceTile"
  ],
  experienceTile: ["experienceTile"]
} as const;
type NodeNameType = keyof typeof PlasmicDescendants;
type DescendantsType<T extends NodeNameType> =
  (typeof PlasmicDescendants)[T][number];
type NodeDefaultElementType = {
  experiencesSection: "div";
  profileSectionsProfileSectionHeading: typeof ProfileSectionsProfileSectionHeading;
  experiencesDisplayContainer: "section";
  experienceTile: typeof ProfileTileExperiencesTile;
};

type ReservedPropsType = "variants" | "args" | "overrides";
type NodeOverridesType<T extends NodeNameType> = Pick<
  PlasmicProfileSectionsExperienceBlock__OverridesType,
  DescendantsType<T>
>;
type NodeComponentProps<T extends NodeNameType> =
  // Explicitly specify variants, args, and overrides as objects
  {
    variants?: PlasmicProfileSectionsExperienceBlock__VariantsArgs;
    args?: PlasmicProfileSectionsExperienceBlock__ArgsType;
    overrides?: NodeOverridesType<T>;
  } & Omit< // Specify variants directly as props
    PlasmicProfileSectionsExperienceBlock__VariantsArgs,
    ReservedPropsType
  > &
    /* Specify args directly as props*/ Omit<
      PlasmicProfileSectionsExperienceBlock__ArgsType,
      ReservedPropsType
    > &
    /* Specify overrides for each element directly as props*/ Omit<
      NodeOverridesType<T>,
      ReservedPropsType | VariantPropType | ArgPropType
    > &
    /* Specify props for the root element*/ Omit<
      Partial<React.ComponentProps<NodeDefaultElementType[T]>>,
      ReservedPropsType | VariantPropType | ArgPropType | DescendantsType<T>
    >;

function makeNodeComponent<NodeName extends NodeNameType>(nodeName: NodeName) {
  type PropsType = NodeComponentProps<NodeName> & { key?: React.Key };
  const func = function <T extends PropsType>(
    props: T & StrictProps<T, PropsType>
  ) {
    const { variants, args, overrides } = React.useMemo(
      () =>
        deriveRenderOpts(props, {
          name: nodeName,
          descendantNames: PlasmicDescendants[nodeName],
          internalArgPropNames: PlasmicProfileSectionsExperienceBlock__ArgProps,
          internalVariantPropNames:
            PlasmicProfileSectionsExperienceBlock__VariantProps
        }),
      [props, nodeName]
    );
    return PlasmicProfileSectionsExperienceBlock__RenderFunc({
      variants,
      args,
      overrides,
      forNode: nodeName
    });
  };
  if (nodeName === "experiencesSection") {
    func.displayName = "PlasmicProfileSectionsExperienceBlock";
  } else {
    func.displayName = `PlasmicProfileSectionsExperienceBlock.${nodeName}`;
  }
  return func;
}

export const PlasmicProfileSectionsExperienceBlock = Object.assign(
  // Top-level PlasmicProfileSectionsExperienceBlock renders the root element
  makeNodeComponent("experiencesSection"),
  {
    // Helper components rendering sub-elements
    profileSectionsProfileSectionHeading: makeNodeComponent(
      "profileSectionsProfileSectionHeading"
    ),
    experiencesDisplayContainer: makeNodeComponent(
      "experiencesDisplayContainer"
    ),
    experienceTile: makeNodeComponent("experienceTile"),

    // Metadata about props expected for PlasmicProfileSectionsExperienceBlock
    internalVariantProps: PlasmicProfileSectionsExperienceBlock__VariantProps,
    internalArgProps: PlasmicProfileSectionsExperienceBlock__ArgProps
  }
);

export default PlasmicProfileSectionsExperienceBlock;
/* prettier-ignore-end */
