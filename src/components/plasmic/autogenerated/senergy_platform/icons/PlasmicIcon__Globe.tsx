/* eslint-disable */
/* tslint:disable */
// @ts-nocheck
/* prettier-ignore-start */
import React from "react";
import { classNames } from "@plasmicapp/react-web";

export type GlobeIconProps = React.ComponentProps<"svg"> & {
  title?: string;
};

export function GlobeIcon(props: GlobeIconProps) {
  const { className, style, title, ...restProps } = props;
  return (
    <svg
      xmlns={"http://www.w3.org/2000/svg"}
      className={classNames(
        "plasmic-default__svg",
        className,
        "icon icon-tabler icon-tabler-globe"
      )}
      viewBox={"0 0 24 24"}
      strokeWidth={"1.5"}
      stroke={"currentColor"}
      fill={"none"}
      strokeLinecap={"round"}
      strokeLinejoin={"round"}
      height={"1em"}
      style={style}
      {...restProps}
    >
      {title && <title>{title}</title>}

      <path d={"M0 0h24v24H0z"} fill={"none"} stroke={"none"}></path>

      <path
        d={
          "M7 9a4 4 0 108 0 4 4 0 00-8 0m-1.25 6A8.015 8.015 0 1015 2m-4 15v4m-4 0h8"
        }
      ></path>
    </svg>
  );
}

export default GlobeIcon;
/* prettier-ignore-end */
