.logoStack {
  display: inline-flex;
  cursor: pointer;
  pointer-events: auto;
  position: relative;
  flex-shrink: 0;
  align-items: center;
  justify-content: flex-start;
  overflow: hidden;
  width: auto;
  justify-self: flex-start;
  padding: 8px;
}
.logoStacktest {
  max-width: 26px;
  width: 100%;
  min-width: 0;
}
.img {
  width: 40px;
  height: 53px;
  margin-right: 0px;
  pointer-events: none;
  object-fit: contain;
  min-width: 40px;
  min-height: 53px;
  flex-shrink: 0;
}
.img > picture > img {
  object-fit: contain;
}
.text {
  position: relative;
  width: auto;
  height: auto;
  max-width: 100%;
  text-align: right;
  font-family: var(--token-IfwtVZvVvF7g);
  font-weight: 700;
  user-select: none;
  line-height: 1.65;
  pointer-events: none;
  transition-property: color;
  transition-duration: 0.25s;
  -webkit-transition-property: color;
  -webkit-transition-duration: 0.25s;
}
.textcollapsed {
  color: #29292900;
}
