'use client';

import { useRouter } from 'next/navigation';
import { useState } from 'react';
import TypeIt from 'typeit-react';
import { Button } from '@/components/ui/button';

export default function WaitlistedPage() {
  const router = useRouter();
  const [showContent, setShowContent] = useState(false);

  return (
    <div className="flex min-h-svh flex-col items-center justify-center gap-8 bg-clay60 p-6 md:p-10">
      <div className="flex w-full max-w-2xl flex-col items-center gap-8 rounded-lg bg-clay80 p-8 text-center">
        <TypeIt
          className="font-bold text-3xl md:text-4xl"
          options={{
            strings: ["You're on the waitlist!"],
            speed: 50,
            waitUntilVisible: true,
            lifeLike: true,
            cursor: false,
            afterComplete: () => {
              setTimeout(() => {
                setShowContent(true);
              }, 1000);
            },
          }}
        />

        <div
          className={`space-y-4 text-lg transition-opacity duration-500 ${showContent ? 'opacity-100' : 'opacity-0'}`}
        >
          <p className="text-gray-700">
            Thank you for your interest in joining our platform.
          </p>
          <p className="text-gray-700">
            We're currently in a closed alpha phase, carefully onboarding teams
            and individuals to ensure the best possible experience for everyone.
          </p>
          <p className="text-gray-700">
            We'll notify you as soon as you have access!
          </p>
        </div>

        <Button
          className={`mt-4 transition-opacity duration-500 ${showContent ? 'opacity-100' : 'opacity-0'}`}
          onClick={() => router.push('/')}
          variant="outline"
        >
          Return Home
        </Button>
      </div>
    </div>
  );
}
