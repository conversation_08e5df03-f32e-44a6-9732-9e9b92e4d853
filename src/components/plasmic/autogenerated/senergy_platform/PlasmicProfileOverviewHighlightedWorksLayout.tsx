/* eslint-disable */
/* tslint:disable */
// @ts-nocheck
/* prettier-ignore-start */

/** @jsxRuntime classic */
/** @jsx createPlasmicElementProxy */
/** @jsxFrag React.Fragment */

// This class is auto-generated by Plasmic; please do not edit!
// Plasmic Project: nZeWKcn21KijvC7eT8B3c7
// Component: Vq_8v2qcFNSB

"use client";

import * as React from "react";

import Head from "next/head";
import Link, { LinkProps } from "next/link";
import { useRouter } from "next/navigation";

import {
  Flex as Flex__,
  MultiChoiceArg,
  PlasmicDataSourceContextProvider as PlasmicDataSourceContextProvider__,
  PlasmicIcon as PlasmicIcon__,
  PlasmicImg as PlasmicImg__,
  PlasmicLink as PlasmicLink__,
  PlasmicPageGuard as PlasmicPageGuard__,
  SingleBooleanChoiceArg,
  SingleChoiceArg,
  Stack as Stack__,
  StrictProps,
  Trans as Trans__,
  classNames,
  createPlasmicElementProxy,
  deriveRenderOpts,
  ensureGlobalVariants,
  generateOnMutateForSpec,
  generateStateOnChangeProp,
  generateStateOnChangePropForCodeComponents,
  generateStateValueProp,
  get as $stateGet,
  hasVariant,
  initializeCodeComponentStates,
  initializePlasmicStates,
  makeFragment,
  omit,
  pick,
  renderPlasmicSlot,
  set as $stateSet,
  useCurrentUser,
  useDollarState,
  usePlasmicTranslator,
  useTrigger,
  wrapWithClassName
} from "@plasmicapp/react-web";
import {
  DataCtxReader as DataCtxReader__,
  useDataEnv,
  useGlobalActions
} from "@plasmicapp/host";

import ProfileSectionsProfileSectionHeading from "../../ProfileSectionsProfileSectionHeading"; // plasmic-import: Uz656rZzIxzC/component
import ProfileTileCaseStudyTile from "../../ProfileTileCaseStudyTile"; // plasmic-import: msZSdug4VrvS/component

import "@plasmicapp/react-web/lib/plasmic.css";

import projectcss from "./plasmic.module.css"; // plasmic-import: nZeWKcn21KijvC7eT8B3c7/projectcss
import sty from "./PlasmicProfileOverviewHighlightedWorksLayout.module.css"; // plasmic-import: Vq_8v2qcFNSB/css

createPlasmicElementProxy;

export type PlasmicProfileOverviewHighlightedWorksLayout__VariantMembers = {};
export type PlasmicProfileOverviewHighlightedWorksLayout__VariantsArgs = {};
type VariantPropType =
  keyof PlasmicProfileOverviewHighlightedWorksLayout__VariantsArgs;
export const PlasmicProfileOverviewHighlightedWorksLayout__VariantProps =
  new Array<VariantPropType>();

export type PlasmicProfileOverviewHighlightedWorksLayout__ArgsType = {
  clicked?: (event: any) => void;
};
type ArgPropType = keyof PlasmicProfileOverviewHighlightedWorksLayout__ArgsType;
export const PlasmicProfileOverviewHighlightedWorksLayout__ArgProps =
  new Array<ArgPropType>("clicked");

export type PlasmicProfileOverviewHighlightedWorksLayout__OverridesType = {
  myWorksSection?: Flex__<"div">;
  myWorksHeader?: Flex__<typeof ProfileSectionsProfileSectionHeading>;
  text?: Flex__<"div">;
  displayWrapper?: Flex__<"div">;
  overflowContainer?: Flex__<"section">;
  profileTileCaseStudyTile?: Flex__<typeof ProfileTileCaseStudyTile>;
};

export interface DefaultProfileOverviewHighlightedWorksLayoutProps {
  clicked?: (event: any) => void;
  className?: string;
}

const $$ = {};

function useNextRouter() {
  try {
    return useRouter();
  } catch {}
  return undefined;
}

function PlasmicProfileOverviewHighlightedWorksLayout__RenderFunc(props: {
  variants: PlasmicProfileOverviewHighlightedWorksLayout__VariantsArgs;
  args: PlasmicProfileOverviewHighlightedWorksLayout__ArgsType;
  overrides: PlasmicProfileOverviewHighlightedWorksLayout__OverridesType;
  forNode?: string;
}) {
  const { variants, overrides, forNode } = props;

  const args = React.useMemo(
    () =>
      Object.assign(
        {},
        Object.fromEntries(
          Object.entries(props.args).filter(([_, v]) => v !== undefined)
        )
      ),
    [props.args]
  );

  const $props = {
    ...args,
    ...variants
  };

  const __nextRouter = useNextRouter();
  const $ctx = useDataEnv?.() || {};
  const refsRef = React.useRef({});
  const $refs = refsRef.current;

  let [$queries, setDollarQueries] = React.useState<
    Record<string, ReturnType<typeof usePlasmicDataOp>>
  >({});

  const new$Queries: Record<string, ReturnType<typeof usePlasmicDataOp>> = {};
  if (Object.keys(new$Queries).some(k => new$Queries[k] !== $queries[k])) {
    setDollarQueries(new$Queries);

    $queries = new$Queries;
  }

  return (
    <div
      data-plasmic-name={"myWorksSection"}
      data-plasmic-override={overrides.myWorksSection}
      data-plasmic-root={true}
      data-plasmic-for-node={forNode}
      className={classNames(
        projectcss.all,
        projectcss.root_reset,
        projectcss.plasmic_default_styles,
        projectcss.plasmic_mixins,
        projectcss.plasmic_tokens,
        sty.myWorksSection
      )}
    >
      <ProfileSectionsProfileSectionHeading
        data-plasmic-name={"myWorksHeader"}
        data-plasmic-override={overrides.myWorksHeader}
        className={classNames("__wab_instance", sty.myWorksHeader)}
      >
        <div
          data-plasmic-name={"text"}
          data-plasmic-override={overrides.text}
          className={classNames(
            projectcss.all,
            projectcss.__wab_text,
            sty.text
          )}
        >
          {"My Work"}
        </div>
      </ProfileSectionsProfileSectionHeading>
      <div
        data-plasmic-name={"displayWrapper"}
        data-plasmic-override={overrides.displayWrapper}
        className={classNames(projectcss.all, sty.displayWrapper)}
      >
        <Stack__
          as={"section"}
          data-plasmic-name={"overflowContainer"}
          data-plasmic-override={overrides.overflowContainer}
          hasGap={true}
          className={classNames(projectcss.all, sty.overflowContainer)}
        >
          {(_par => (!_par ? [] : Array.isArray(_par) ? _par : [_par]))([
            2, 3, 4
          ]).map((__plasmic_item_0, __plasmic_idx_0) => {
            const currentItem = __plasmic_item_0;
            const currentIndex = __plasmic_idx_0;
            return (
              <ProfileTileCaseStudyTile
                data-plasmic-name={"profileTileCaseStudyTile"}
                data-plasmic-override={overrides.profileTileCaseStudyTile}
                className={classNames(
                  "__wab_instance",
                  sty.profileTileCaseStudyTile
                )}
                comingSoon={true}
                content={"photo"}
                key={currentIndex}
              />
            );
          })}
        </Stack__>
      </div>
    </div>
  ) as React.ReactElement | null;
}

const PlasmicDescendants = {
  myWorksSection: [
    "myWorksSection",
    "myWorksHeader",
    "text",
    "displayWrapper",
    "overflowContainer",
    "profileTileCaseStudyTile"
  ],
  myWorksHeader: ["myWorksHeader", "text"],
  text: ["text"],
  displayWrapper: [
    "displayWrapper",
    "overflowContainer",
    "profileTileCaseStudyTile"
  ],
  overflowContainer: ["overflowContainer", "profileTileCaseStudyTile"],
  profileTileCaseStudyTile: ["profileTileCaseStudyTile"]
} as const;
type NodeNameType = keyof typeof PlasmicDescendants;
type DescendantsType<T extends NodeNameType> =
  (typeof PlasmicDescendants)[T][number];
type NodeDefaultElementType = {
  myWorksSection: "div";
  myWorksHeader: typeof ProfileSectionsProfileSectionHeading;
  text: "div";
  displayWrapper: "div";
  overflowContainer: "section";
  profileTileCaseStudyTile: typeof ProfileTileCaseStudyTile;
};

type ReservedPropsType = "variants" | "args" | "overrides";
type NodeOverridesType<T extends NodeNameType> = Pick<
  PlasmicProfileOverviewHighlightedWorksLayout__OverridesType,
  DescendantsType<T>
>;
type NodeComponentProps<T extends NodeNameType> =
  // Explicitly specify variants, args, and overrides as objects
  {
    variants?: PlasmicProfileOverviewHighlightedWorksLayout__VariantsArgs;
    args?: PlasmicProfileOverviewHighlightedWorksLayout__ArgsType;
    overrides?: NodeOverridesType<T>;
  } & Omit< // Specify variants directly as props
    PlasmicProfileOverviewHighlightedWorksLayout__VariantsArgs,
    ReservedPropsType
  > &
    /* Specify args directly as props*/ Omit<
      PlasmicProfileOverviewHighlightedWorksLayout__ArgsType,
      ReservedPropsType
    > &
    /* Specify overrides for each element directly as props*/ Omit<
      NodeOverridesType<T>,
      ReservedPropsType | VariantPropType | ArgPropType
    > &
    /* Specify props for the root element*/ Omit<
      Partial<React.ComponentProps<NodeDefaultElementType[T]>>,
      ReservedPropsType | VariantPropType | ArgPropType | DescendantsType<T>
    >;

function makeNodeComponent<NodeName extends NodeNameType>(nodeName: NodeName) {
  type PropsType = NodeComponentProps<NodeName> & { key?: React.Key };
  const func = function <T extends PropsType>(
    props: T & StrictProps<T, PropsType>
  ) {
    const { variants, args, overrides } = React.useMemo(
      () =>
        deriveRenderOpts(props, {
          name: nodeName,
          descendantNames: PlasmicDescendants[nodeName],
          internalArgPropNames:
            PlasmicProfileOverviewHighlightedWorksLayout__ArgProps,
          internalVariantPropNames:
            PlasmicProfileOverviewHighlightedWorksLayout__VariantProps
        }),
      [props, nodeName]
    );
    return PlasmicProfileOverviewHighlightedWorksLayout__RenderFunc({
      variants,
      args,
      overrides,
      forNode: nodeName
    });
  };
  if (nodeName === "myWorksSection") {
    func.displayName = "PlasmicProfileOverviewHighlightedWorksLayout";
  } else {
    func.displayName = `PlasmicProfileOverviewHighlightedWorksLayout.${nodeName}`;
  }
  return func;
}

export const PlasmicProfileOverviewHighlightedWorksLayout = Object.assign(
  // Top-level PlasmicProfileOverviewHighlightedWorksLayout renders the root element
  makeNodeComponent("myWorksSection"),
  {
    // Helper components rendering sub-elements
    myWorksHeader: makeNodeComponent("myWorksHeader"),
    text: makeNodeComponent("text"),
    displayWrapper: makeNodeComponent("displayWrapper"),
    overflowContainer: makeNodeComponent("overflowContainer"),
    profileTileCaseStudyTile: makeNodeComponent("profileTileCaseStudyTile"),

    // Metadata about props expected for PlasmicProfileOverviewHighlightedWorksLayout
    internalVariantProps:
      PlasmicProfileOverviewHighlightedWorksLayout__VariantProps,
    internalArgProps: PlasmicProfileOverviewHighlightedWorksLayout__ArgProps
  }
);

export default PlasmicProfileOverviewHighlightedWorksLayout;
/* prettier-ignore-end */
