'use client';

import { createContext, type ReactNode, useContext } from 'react';
import type { Profile } from '@/payload-types';

interface ProfileContextType {
  profile: Profile | null;
}

const ProfileContext = createContext<ProfileContextType>({ profile: null });

export const ProfileProvider = ({
  children,
  profile,
}: {
  children: ReactNode;
  profile: Profile;
}) => {
  return (
    <ProfileContext.Provider value={{ profile }}>
      {children}
    </ProfileContext.Provider>
  );
};

export const useProfile = () => useContext(ProfileContext);
