import type { Field } from 'payload';

export const educationFields: Field[] = [
  {
    type: 'row',
    fields: [
      {
        name: 'degree_type',
        label: 'Degree Type',
        type: 'text',
        //type: 'relationship',
        //relationTo: 'Degreetypes',
        required: true,
      },
      {
        name: 'degree_name',
        label: 'Degree Name',
        type: 'text',
        required: true,
      },
    ],
  },
  {
    type: 'row',
    fields: [
      {
        name: 'institution_name',
        label: 'Educational Institution',
        type: 'text',
        required: true,
      },
      {
        name: 'institution_location',
        label: 'Location of Institution',
        type: 'text',
      },
    ],
  },
  {
    type: 'row',
    fields: [
      {
        name: 'start_date',
        label: 'Start Date',
        type: 'date',
        required: true,
      },
      {
        name: 'end_date',
        label: 'End Date',
        type: 'date',
        required: false,
        //admin: {
        //  condition: (data) => data.graduation_status == true,
        //},
      },
      {
        name: 'graduation_status',
        label: 'Graduated',
        type: 'checkbox',
        required: true,
      },
    ],
  },
  {
    name: 'visible',
    type: 'checkbox',
    required: false,
    defaultValue: true,
  },
];
