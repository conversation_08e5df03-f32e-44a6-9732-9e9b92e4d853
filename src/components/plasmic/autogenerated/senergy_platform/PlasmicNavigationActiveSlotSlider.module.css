.activeSlotSlider {
  display: flex;
  width: 100%;
  flex-direction: column;
  height: 36px;
  cursor: pointer;
  transition-property: transform, background;
  background: var(--token-5_Q90hFZ9CmK);
  transition-duration: 0.2s, 0.15s;
  box-shadow: 0px 4px 16px -10px #00000033;
  position: relative;
  max-width: 95%;
  min-width: 0;
  -webkit-transition-property: transform, background;
  -webkit-transition-duration: 0.2s, 0.15s;
  padding: var(--token-sazGmnf7GWAk);
  border: 0.5px solid #d7d7d7;
}
.activeSlotSlideractiveTab_slot1 {
  transform: translateX(0px) translateY(0px) translateZ(0px);
  display: flex;
}
.activeSlotSlideractiveTab_slot2 {
  transform: translateX(0px) translateY(36px) translateZ(0px);
  display: flex;
}
.activeSlotSlideractiveTab_slot3 {
  transform: translateX(0px) translateY(72px) translateZ(0px);
  display: flex;
}
.activeSlotSliderhoverTab_slot1 {
  display: none;
}
.activeSlotSliderhoverTab_slot2 {
  display: none;
}
.activeSlotSliderhoverTab_slot3 {
  display: none;
}
