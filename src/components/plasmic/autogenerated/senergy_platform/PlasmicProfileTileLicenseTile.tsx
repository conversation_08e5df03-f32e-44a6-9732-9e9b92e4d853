/* eslint-disable */
/* tslint:disable */
// @ts-nocheck
/* prettier-ignore-start */

/** @jsxRuntime classic */
/** @jsx createPlasmicElementProxy */
/** @jsxFrag React.Fragment */

// This class is auto-generated by Plasmic; please do not edit!
// Plasmic Project: nZeWKcn21KijvC7eT8B3c7
// Component: NlwHt2k50uIY

"use client";

import * as React from "react";

import Head from "next/head";
import Link, { LinkProps } from "next/link";
import { useRouter } from "next/navigation";

import {
  Flex as Flex__,
  MultiChoiceArg,
  PlasmicDataSourceContextProvider as PlasmicDataSourceContextProvider__,
  PlasmicIcon as PlasmicIcon__,
  PlasmicImg as PlasmicImg__,
  PlasmicLink as PlasmicLink__,
  PlasmicPageGuard as PlasmicPageGuard__,
  SingleBooleanChoiceArg,
  SingleChoiceArg,
  Stack as Stack__,
  StrictProps,
  Trans as Trans__,
  classNames,
  createPlasmicElementProxy,
  deriveRenderOpts,
  ensureGlobalVariants,
  generateOnMutateForSpec,
  generateStateOnChangeProp,
  generateStateOnChangePropForCodeComponents,
  generateStateValueProp,
  get as $stateGet,
  hasVariant,
  initializeCodeComponentStates,
  initializePlasmicStates,
  makeFragment,
  omit,
  pick,
  renderPlasmicSlot,
  set as $stateSet,
  useCurrentUser,
  useDollarState,
  usePlasmicTranslator,
  useTrigger,
  wrapWithClassName
} from "@plasmicapp/react-web";
import {
  DataCtxReader as DataCtxReader__,
  useDataEnv,
  useGlobalActions
} from "@plasmicapp/host";

import SubcomponentTextInput from "../../SubcomponentTextInput"; // plasmic-import: Pt5FPuEzirSe/component
import SubcomponentIconWithText from "../../SubcomponentIconWithText"; // plasmic-import: _eFlbiSm6hZU/component
import SubcomponentDeleteButton from "../../SubcomponentDeleteButton"; // plasmic-import: WMfLdq1qoZyo/component

import "@plasmicapp/react-web/lib/plasmic.css";

import projectcss from "./plasmic.module.css"; // plasmic-import: nZeWKcn21KijvC7eT8B3c7/projectcss
import sty from "./PlasmicProfileTileLicenseTile.module.css"; // plasmic-import: NlwHt2k50uIY/css

import LicenseIcon from "./icons/PlasmicIcon__License"; // plasmic-import: j-ct0zISt5hl/icon
import MapPinIcon from "./icons/PlasmicIcon__MapPin"; // plasmic-import: Q_wDLzMm5l2Y/icon
import HashtagIcon from "./icons/PlasmicIcon__Hashtag"; // plasmic-import: 7fTFQT6QY_o8/icon
import UserIcon from "./icons/PlasmicIcon__User"; // plasmic-import: hcR42vt5qkrz/icon
import CalendarIcon from "./icons/PlasmicIcon__Calendar"; // plasmic-import: -xP51745fXLc/icon
import CalendarExpirationIcon from "./icons/PlasmicIcon__CalendarExpiration"; // plasmic-import: Ra-yugMEa7AY/icon

createPlasmicElementProxy;

export type PlasmicProfileTileLicenseTile__VariantMembers = {
  editable: "editable";
  overview: "overview";
};
export type PlasmicProfileTileLicenseTile__VariantsArgs = {
  editable?: SingleBooleanChoiceArg<"editable">;
  overview?: SingleBooleanChoiceArg<"overview">;
};
type VariantPropType = keyof PlasmicProfileTileLicenseTile__VariantsArgs;
export const PlasmicProfileTileLicenseTile__VariantProps =
  new Array<VariantPropType>("editable", "overview");

export type PlasmicProfileTileLicenseTile__ArgsType = {
  licenseId?: string;
  titleInputValue?: string;
  licensingBodyInputValue?: string;
  issueDateInputValue?: string;
  registrationNumberInputValue?: string;
  instructorInputValue?: string;
  issueLocationInputValue?: string;
  expirationDateInputValue?: string;
  descriptionInputValue?: string;
  onTitleInputValueChange?: (val: string) => void;
  onIssueDateInputValueChange?: (val: string) => void;
  onRegistrationNumberInputValueChange?: (val: string) => void;
  onInstructorInputValueChange?: (val: string) => void;
  onIssueLocationInputValueChange?: (val: string) => void;
  onExpirationDateInputValueChange?: (val: string) => void;
  onLicensingBodyInputValueChange?: (val: string) => void;
  onDescriptionInputValueChange?: (val: string) => void;
  deleteButtonClickStage?: number;
  onDeleteButtonClickStageChange?: (val: number) => void;
  deleteButtonDisabled?: any;
  onDeleteButtonDisabledChange?: (val: any) => void;
  deleteButtonOnClick?: (event: any) => void;
};
type ArgPropType = keyof PlasmicProfileTileLicenseTile__ArgsType;
export const PlasmicProfileTileLicenseTile__ArgProps = new Array<ArgPropType>(
  "licenseId",
  "titleInputValue",
  "licensingBodyInputValue",
  "issueDateInputValue",
  "registrationNumberInputValue",
  "instructorInputValue",
  "issueLocationInputValue",
  "expirationDateInputValue",
  "descriptionInputValue",
  "onTitleInputValueChange",
  "onIssueDateInputValueChange",
  "onRegistrationNumberInputValueChange",
  "onInstructorInputValueChange",
  "onIssueLocationInputValueChange",
  "onExpirationDateInputValueChange",
  "onLicensingBodyInputValueChange",
  "onDescriptionInputValueChange",
  "deleteButtonClickStage",
  "onDeleteButtonClickStageChange",
  "deleteButtonDisabled",
  "onDeleteButtonDisabledChange",
  "deleteButtonOnClick"
);

export type PlasmicProfileTileLicenseTile__OverridesType = {
  licenseSpacingContainer?: Flex__<"div">;
  licenseIcon?: Flex__<"svg">;
  informationStack?: Flex__<"div">;
  titleInput?: Flex__<typeof SubcomponentTextInput>;
  licensingBodyInput?: Flex__<typeof SubcomponentTextInput>;
  infoBar?: Flex__<"section">;
  issueLocationInput?: Flex__<typeof SubcomponentIconWithText>;
  iconSpot5?: Flex__<"svg">;
  registrationNumberInput?: Flex__<typeof SubcomponentIconWithText>;
  iconSpot3?: Flex__<"svg">;
  instructorInput?: Flex__<typeof SubcomponentIconWithText>;
  iconSpot4?: Flex__<"svg">;
  issueDateInput?: Flex__<typeof SubcomponentIconWithText>;
  iconSpot2?: Flex__<"svg">;
  expirationDateInput?: Flex__<typeof SubcomponentIconWithText>;
  iconSpot6?: Flex__<"svg">;
  description?: Flex__<typeof SubcomponentTextInput>;
  subDeleteButton?: Flex__<typeof SubcomponentDeleteButton>;
};

export interface DefaultProfileTileLicenseTileProps {
  licenseId?: string;
  titleInputValue?: string;
  licensingBodyInputValue?: string;
  issueDateInputValue?: string;
  registrationNumberInputValue?: string;
  instructorInputValue?: string;
  issueLocationInputValue?: string;
  expirationDateInputValue?: string;
  descriptionInputValue?: string;
  onTitleInputValueChange?: (val: string) => void;
  onIssueDateInputValueChange?: (val: string) => void;
  onRegistrationNumberInputValueChange?: (val: string) => void;
  onInstructorInputValueChange?: (val: string) => void;
  onIssueLocationInputValueChange?: (val: string) => void;
  onExpirationDateInputValueChange?: (val: string) => void;
  onLicensingBodyInputValueChange?: (val: string) => void;
  onDescriptionInputValueChange?: (val: string) => void;
  deleteButtonClickStage?: number;
  onDeleteButtonClickStageChange?: (val: number) => void;
  deleteButtonDisabled?: any;
  onDeleteButtonDisabledChange?: (val: any) => void;
  deleteButtonOnClick?: (event: any) => void;
  editable?: SingleBooleanChoiceArg<"editable">;
  overview?: SingleBooleanChoiceArg<"overview">;
  className?: string;
}

const $$ = {};

function useNextRouter() {
  try {
    return useRouter();
  } catch {}
  return undefined;
}

function PlasmicProfileTileLicenseTile__RenderFunc(props: {
  variants: PlasmicProfileTileLicenseTile__VariantsArgs;
  args: PlasmicProfileTileLicenseTile__ArgsType;
  overrides: PlasmicProfileTileLicenseTile__OverridesType;
  forNode?: string;
}) {
  const { variants, overrides, forNode } = props;

  const args = React.useMemo(
    () =>
      Object.assign(
        {},
        Object.fromEntries(
          Object.entries(props.args).filter(([_, v]) => v !== undefined)
        )
      ),
    [props.args]
  );

  const $props = {
    ...args,
    ...variants
  };

  const __nextRouter = useNextRouter();
  const $ctx = useDataEnv?.() || {};
  const refsRef = React.useRef({});
  const $refs = refsRef.current;

  let [$queries, setDollarQueries] = React.useState<
    Record<string, ReturnType<typeof usePlasmicDataOp>>
  >({});
  const stateSpecs: Parameters<typeof useDollarState>[0] = React.useMemo(
    () => [
      {
        path: "titleInput.value",
        type: "writable",
        variableType: "text",

        valueProp: "titleInputValue",
        onChangeProp: "onTitleInputValueChange"
      },
      {
        path: "issueDateInput.inputValue",
        type: "writable",
        variableType: "text",

        valueProp: "issueDateInputValue",
        onChangeProp: "onIssueDateInputValueChange"
      },
      {
        path: "registrationNumberInput.inputValue",
        type: "writable",
        variableType: "text",

        valueProp: "registrationNumberInputValue",
        onChangeProp: "onRegistrationNumberInputValueChange"
      },
      {
        path: "instructorInput.inputValue",
        type: "writable",
        variableType: "text",

        valueProp: "instructorInputValue",
        onChangeProp: "onInstructorInputValueChange"
      },
      {
        path: "issueLocationInput.inputValue",
        type: "writable",
        variableType: "text",

        valueProp: "issueLocationInputValue",
        onChangeProp: "onIssueLocationInputValueChange"
      },
      {
        path: "expirationDateInput.inputValue",
        type: "writable",
        variableType: "text",

        valueProp: "expirationDateInputValue",
        onChangeProp: "onExpirationDateInputValueChange"
      },
      {
        path: "editable",
        type: "private",
        variableType: "variant",
        initFunc: ({ $props, $state, $queries, $ctx }) => $props.editable
      },
      {
        path: "overview",
        type: "private",
        variableType: "variant",
        initFunc: ({ $props, $state, $queries, $ctx }) => $props.overview
      },
      {
        path: "licensingBodyInput.value",
        type: "writable",
        variableType: "text",

        valueProp: "licensingBodyInputValue",
        onChangeProp: "onLicensingBodyInputValueChange"
      },
      {
        path: "subDeleteButton.clickStage",
        type: "writable",
        variableType: "number",

        valueProp: "deleteButtonClickStage",
        onChangeProp: "onDeleteButtonClickStageChange"
      },
      {
        path: "subDeleteButton.disabled",
        type: "writable",
        variableType: "text",

        valueProp: "deleteButtonDisabled",
        onChangeProp: "onDeleteButtonDisabledChange"
      },
      {
        path: "pendingUpdate",
        type: "private",
        variableType: "boolean",
        initFunc: ({ $props, $state, $queries, $ctx }) => false
      },
      {
        path: "description.value",
        type: "writable",
        variableType: "text",

        valueProp: "descriptionInputValue",
        onChangeProp: "onDescriptionInputValueChange"
      },
      {
        path: "titleInput.errorMessage",
        type: "private",
        variableType: "text",
        initFunc: ({ $props, $state, $queries, $ctx }) => ""
      },
      {
        path: "licensingBodyInput.errorMessage",
        type: "private",
        variableType: "text",
        initFunc: ({ $props, $state, $queries, $ctx }) => ""
      },
      {
        path: "description.errorMessage",
        type: "private",
        variableType: "text",
        initFunc: ({ $props, $state, $queries, $ctx }) => ""
      }
    ],
    [$props, $ctx, $refs]
  );
  const $state = useDollarState(stateSpecs, {
    $props,
    $ctx,
    $queries: $queries,
    $refs
  });

  const new$Queries: Record<string, ReturnType<typeof usePlasmicDataOp>> = {};
  if (Object.keys(new$Queries).some(k => new$Queries[k] !== $queries[k])) {
    setDollarQueries(new$Queries);

    $queries = new$Queries;
  }

  return (
    <div
      data-plasmic-name={"licenseSpacingContainer"}
      data-plasmic-override={overrides.licenseSpacingContainer}
      data-plasmic-root={true}
      data-plasmic-for-node={forNode}
      className={classNames(
        projectcss.all,
        projectcss.root_reset,
        projectcss.plasmic_default_styles,
        projectcss.plasmic_mixins,
        projectcss.plasmic_tokens,
        sty.licenseSpacingContainer,
        {
          [sty.licenseSpacingContainereditable]: hasVariant(
            $state,
            "editable",
            "editable"
          ),
          [sty.licenseSpacingContaineroverview]: hasVariant(
            $state,
            "overview",
            "overview"
          )
        }
      )}
    >
      <LicenseIcon
        data-plasmic-name={"licenseIcon"}
        data-plasmic-override={overrides.licenseIcon}
        className={classNames(projectcss.all, sty.licenseIcon, {
          [sty.licenseIconeditable]: hasVariant($state, "editable", "editable"),
          [sty.licenseIconoverview]: hasVariant($state, "overview", "overview")
        })}
        role={"img"}
      />

      <div
        data-plasmic-name={"informationStack"}
        data-plasmic-override={overrides.informationStack}
        className={classNames(projectcss.all, sty.informationStack, {
          [sty.informationStackeditable]: hasVariant(
            $state,
            "editable",
            "editable"
          ),
          [sty.informationStackoverview]: hasVariant(
            $state,
            "overview",
            "overview"
          )
        })}
      >
        <SubcomponentTextInput
          data-plasmic-name={"titleInput"}
          data-plasmic-override={overrides.titleInput}
          className={classNames("__wab_instance", sty.titleInput, {
            [sty.titleInputeditable]: hasVariant(
              $state,
              "editable",
              "editable"
            ),
            [sty.titleInputoverview]: hasVariant($state, "overview", "overview")
          })}
          displayText={(() => {
            try {
              return $state.titleInput.value;
            } catch (e) {
              if (
                e instanceof TypeError ||
                e?.plasmicType === "PlasmicUndefinedDataError"
              ) {
                return undefined;
              }
              throw e;
            }
          })()}
          editView={"heading3"}
          errorMessage={generateStateValueProp($state, [
            "titleInput",
            "errorMessage"
          ])}
          inputHoverText={"License Name"}
          inputName={"License Name"}
          inputPlaceholder={"License Name"}
          inputValue={generateStateValueProp($state, ["titleInput", "value"])}
          onErrorMessageChange={async (...eventArgs: any) => {
            generateStateOnChangeProp($state, [
              "titleInput",
              "errorMessage"
            ]).apply(null, eventArgs);

            if (
              eventArgs.length > 1 &&
              eventArgs[1] &&
              eventArgs[1]._plasmic_state_init_
            ) {
              return;
            }
          }}
          onInputValueChange={async (...eventArgs: any) => {
            generateStateOnChangeProp($state, ["titleInput", "value"]).apply(
              null,
              eventArgs
            );

            if (
              eventArgs.length > 1 &&
              eventArgs[1] &&
              eventArgs[1]._plasmic_state_init_
            ) {
              return;
            }
          }}
          viewOnly={
            hasVariant($state, "editable", "editable") ? undefined : true
          }
        />

        <SubcomponentTextInput
          data-plasmic-name={"licensingBodyInput"}
          data-plasmic-override={overrides.licensingBodyInput}
          className={classNames("__wab_instance", sty.licensingBodyInput, {
            [sty.licensingBodyInputeditable]: hasVariant(
              $state,
              "editable",
              "editable"
            ),
            [sty.licensingBodyInputoverview]: hasVariant(
              $state,
              "overview",
              "overview"
            )
          })}
          displayText={(() => {
            try {
              return $state.licensingBodyInput.value;
            } catch (e) {
              if (
                e instanceof TypeError ||
                e?.plasmicType === "PlasmicUndefinedDataError"
              ) {
                return undefined;
              }
              throw e;
            }
          })()}
          editView={"subHeading"}
          errorMessage={generateStateValueProp($state, [
            "licensingBodyInput",
            "errorMessage"
          ])}
          inputHoverText={"Licensing Body"}
          inputName={"Licensing Body"}
          inputPlaceholder={"Licensing Body"}
          inputValue={generateStateValueProp($state, [
            "licensingBodyInput",
            "value"
          ])}
          onErrorMessageChange={async (...eventArgs: any) => {
            generateStateOnChangeProp($state, [
              "licensingBodyInput",
              "errorMessage"
            ]).apply(null, eventArgs);

            if (
              eventArgs.length > 1 &&
              eventArgs[1] &&
              eventArgs[1]._plasmic_state_init_
            ) {
              return;
            }
          }}
          onInputValueChange={async (...eventArgs: any) => {
            generateStateOnChangeProp($state, [
              "licensingBodyInput",
              "value"
            ]).apply(null, eventArgs);

            if (
              eventArgs.length > 1 &&
              eventArgs[1] &&
              eventArgs[1]._plasmic_state_init_
            ) {
              return;
            }
          }}
          viewOnly={
            hasVariant($state, "editable", "editable") ? undefined : true
          }
        />

        <Stack__
          as={"section"}
          data-plasmic-name={"infoBar"}
          data-plasmic-override={overrides.infoBar}
          hasGap={true}
          className={classNames(projectcss.all, sty.infoBar, {
            [sty.infoBareditable]: hasVariant($state, "editable", "editable"),
            [sty.infoBaroverview]: hasVariant($state, "overview", "overview")
          })}
        >
          {(
            hasVariant($state, "editable", "editable")
              ? true
              : (() => {
                  try {
                    return !!$state.issueLocationInput.inputValue;
                  } catch (e) {
                    if (
                      e instanceof TypeError ||
                      e?.plasmicType === "PlasmicUndefinedDataError"
                    ) {
                      return true;
                    }
                    throw e;
                  }
                })()
          ) ? (
            <SubcomponentIconWithText
              data-plasmic-name={"issueLocationInput"}
              data-plasmic-override={overrides.issueLocationInput}
              className={classNames("__wab_instance", sty.issueLocationInput, {
                [sty.issueLocationInputeditable]: hasVariant(
                  $state,
                  "editable",
                  "editable"
                ),
                [sty.issueLocationInputoverview]: hasVariant(
                  $state,
                  "overview",
                  "overview"
                )
              })}
              displayText={(() => {
                try {
                  return $state.issueLocationInput.inputValue;
                } catch (e) {
                  if (
                    e instanceof TypeError ||
                    e?.plasmicType === "PlasmicUndefinedDataError"
                  ) {
                    return undefined;
                  }
                  throw e;
                }
              })()}
              editable={
                hasVariant($state, "editable", "editable")
                  ? "editableText"
                  : undefined
              }
              iconSpot2={
                <MapPinIcon
                  data-plasmic-name={"iconSpot5"}
                  data-plasmic-override={overrides.iconSpot5}
                  className={classNames(projectcss.all, sty.iconSpot5)}
                  role={"img"}
                />
              }
              inputHoverText={"Issue Location"}
              inputName={"Issue Location"}
              inputPlaceholder={"Issue Location"}
              inputValue={generateStateValueProp($state, [
                "issueLocationInput",
                "inputValue"
              ])}
              onInputValueChange={async (...eventArgs: any) => {
                generateStateOnChangeProp($state, [
                  "issueLocationInput",
                  "inputValue"
                ]).apply(null, eventArgs);

                if (
                  eventArgs.length > 1 &&
                  eventArgs[1] &&
                  eventArgs[1]._plasmic_state_init_
                ) {
                  return;
                }
              }}
            />
          ) : null}
          {(
            hasVariant($state, "overview", "overview")
              ? true
              : hasVariant($state, "editable", "editable")
              ? true
              : (() => {
                  try {
                    return !!$state.registrationNumberInput.inputValue;
                  } catch (e) {
                    if (
                      e instanceof TypeError ||
                      e?.plasmicType === "PlasmicUndefinedDataError"
                    ) {
                      return true;
                    }
                    throw e;
                  }
                })()
          ) ? (
            <SubcomponentIconWithText
              data-plasmic-name={"registrationNumberInput"}
              data-plasmic-override={overrides.registrationNumberInput}
              className={classNames(
                "__wab_instance",
                sty.registrationNumberInput,
                {
                  [sty.registrationNumberInputeditable]: hasVariant(
                    $state,
                    "editable",
                    "editable"
                  ),
                  [sty.registrationNumberInputoverview]: hasVariant(
                    $state,
                    "overview",
                    "overview"
                  )
                }
              )}
              displayText={(() => {
                try {
                  return $state.registrationNumberInput.inputValue;
                } catch (e) {
                  if (
                    e instanceof TypeError ||
                    e?.plasmicType === "PlasmicUndefinedDataError"
                  ) {
                    return undefined;
                  }
                  throw e;
                }
              })()}
              editable={
                hasVariant($state, "editable", "editable")
                  ? "editableText"
                  : undefined
              }
              iconSpot2={
                <HashtagIcon
                  data-plasmic-name={"iconSpot3"}
                  data-plasmic-override={overrides.iconSpot3}
                  className={classNames(projectcss.all, sty.iconSpot3)}
                  role={"img"}
                />
              }
              inputHoverText={"Registration Number"}
              inputName={"Registration Number"}
              inputPlaceholder={"Registration Number"}
              inputValue={generateStateValueProp($state, [
                "registrationNumberInput",
                "inputValue"
              ])}
              onInputValueChange={async (...eventArgs: any) => {
                generateStateOnChangeProp($state, [
                  "registrationNumberInput",
                  "inputValue"
                ]).apply(null, eventArgs);

                if (
                  eventArgs.length > 1 &&
                  eventArgs[1] &&
                  eventArgs[1]._plasmic_state_init_
                ) {
                  return;
                }
              }}
            />
          ) : null}
          {(
            hasVariant($state, "overview", "overview")
              ? true
              : hasVariant($state, "editable", "editable")
              ? true
              : (() => {
                  try {
                    return !!$state.instructorInput.inputValue;
                  } catch (e) {
                    if (
                      e instanceof TypeError ||
                      e?.plasmicType === "PlasmicUndefinedDataError"
                    ) {
                      return true;
                    }
                    throw e;
                  }
                })()
          ) ? (
            <SubcomponentIconWithText
              data-plasmic-name={"instructorInput"}
              data-plasmic-override={overrides.instructorInput}
              className={classNames("__wab_instance", sty.instructorInput, {
                [sty.instructorInputeditable]: hasVariant(
                  $state,
                  "editable",
                  "editable"
                ),
                [sty.instructorInputoverview]: hasVariant(
                  $state,
                  "overview",
                  "overview"
                )
              })}
              displayText={(() => {
                try {
                  return $state.instructorInput.inputValue;
                } catch (e) {
                  if (
                    e instanceof TypeError ||
                    e?.plasmicType === "PlasmicUndefinedDataError"
                  ) {
                    return undefined;
                  }
                  throw e;
                }
              })()}
              editable={
                hasVariant($state, "editable", "editable")
                  ? "editableText"
                  : undefined
              }
              iconSpot2={
                <UserIcon
                  data-plasmic-name={"iconSpot4"}
                  data-plasmic-override={overrides.iconSpot4}
                  className={classNames(projectcss.all, sty.iconSpot4)}
                  role={"img"}
                />
              }
              inputHoverText={"Instructor"}
              inputName={"Instructor"}
              inputPlaceholder={"Instructor"}
              inputValue={generateStateValueProp($state, [
                "instructorInput",
                "inputValue"
              ])}
              onInputValueChange={async (...eventArgs: any) => {
                generateStateOnChangeProp($state, [
                  "instructorInput",
                  "inputValue"
                ]).apply(null, eventArgs);

                if (
                  eventArgs.length > 1 &&
                  eventArgs[1] &&
                  eventArgs[1]._plasmic_state_init_
                ) {
                  return;
                }
              }}
            />
          ) : null}
          {(
            hasVariant($state, "editable", "editable")
              ? true
              : (() => {
                  try {
                    return !!$state.issueDateInput.inputValue;
                  } catch (e) {
                    if (
                      e instanceof TypeError ||
                      e?.plasmicType === "PlasmicUndefinedDataError"
                    ) {
                      return true;
                    }
                    throw e;
                  }
                })()
          ) ? (
            <SubcomponentIconWithText
              data-plasmic-name={"issueDateInput"}
              data-plasmic-override={overrides.issueDateInput}
              className={classNames("__wab_instance", sty.issueDateInput, {
                [sty.issueDateInputeditable]: hasVariant(
                  $state,
                  "editable",
                  "editable"
                ),
                [sty.issueDateInputoverview]: hasVariant(
                  $state,
                  "overview",
                  "overview"
                )
              })}
              displayText={(() => {
                try {
                  return $state.issueDateInput.inputValue;
                } catch (e) {
                  if (
                    e instanceof TypeError ||
                    e?.plasmicType === "PlasmicUndefinedDataError"
                  ) {
                    return undefined;
                  }
                  throw e;
                }
              })()}
              editable={
                hasVariant($state, "editable", "editable")
                  ? "editableText"
                  : undefined
              }
              iconSpot2={
                <CalendarIcon
                  data-plasmic-name={"iconSpot2"}
                  data-plasmic-override={overrides.iconSpot2}
                  className={classNames(projectcss.all, sty.iconSpot2)}
                  role={"img"}
                />
              }
              inputHoverText={"Issue Date"}
              inputName={"Issue Date"}
              inputPlaceholder={"Issue Date"}
              inputType={"date"}
              inputValue={generateStateValueProp($state, [
                "issueDateInput",
                "inputValue"
              ])}
              onInputValueChange={async (...eventArgs: any) => {
                generateStateOnChangeProp($state, [
                  "issueDateInput",
                  "inputValue"
                ]).apply(null, eventArgs);

                if (
                  eventArgs.length > 1 &&
                  eventArgs[1] &&
                  eventArgs[1]._plasmic_state_init_
                ) {
                  return;
                }
              }}
              withoutIcon={
                hasVariant($state, "editable", "editable") ? true : undefined
              }
            />
          ) : null}
          {(
            hasVariant($state, "editable", "editable")
              ? true
              : (() => {
                  try {
                    return !!$state.expirationDateInput.inputValue;
                  } catch (e) {
                    if (
                      e instanceof TypeError ||
                      e?.plasmicType === "PlasmicUndefinedDataError"
                    ) {
                      return true;
                    }
                    throw e;
                  }
                })()
          ) ? (
            <SubcomponentIconWithText
              data-plasmic-name={"expirationDateInput"}
              data-plasmic-override={overrides.expirationDateInput}
              className={classNames("__wab_instance", sty.expirationDateInput, {
                [sty.expirationDateInputeditable]: hasVariant(
                  $state,
                  "editable",
                  "editable"
                ),
                [sty.expirationDateInputoverview]: hasVariant(
                  $state,
                  "overview",
                  "overview"
                )
              })}
              displayText={(() => {
                try {
                  return $state.expirationDateInput.inputValue;
                } catch (e) {
                  if (
                    e instanceof TypeError ||
                    e?.plasmicType === "PlasmicUndefinedDataError"
                  ) {
                    return undefined;
                  }
                  throw e;
                }
              })()}
              editable={
                hasVariant($state, "editable", "editable")
                  ? "editableText"
                  : undefined
              }
              iconSpot2={
                <CalendarExpirationIcon
                  data-plasmic-name={"iconSpot6"}
                  data-plasmic-override={overrides.iconSpot6}
                  className={classNames(projectcss.all, sty.iconSpot6, {
                    [sty.iconSpot6editable]: hasVariant(
                      $state,
                      "editable",
                      "editable"
                    )
                  })}
                  role={"img"}
                />
              }
              inputHoverText={"Expiration Date"}
              inputName={"Expiration Date"}
              inputPlaceholder={"Expiration Date"}
              inputType={"date"}
              inputValue={generateStateValueProp($state, [
                "expirationDateInput",
                "inputValue"
              ])}
              onInputValueChange={async (...eventArgs: any) => {
                generateStateOnChangeProp($state, [
                  "expirationDateInput",
                  "inputValue"
                ]).apply(null, eventArgs);

                if (
                  eventArgs.length > 1 &&
                  eventArgs[1] &&
                  eventArgs[1]._plasmic_state_init_
                ) {
                  return;
                }
              }}
              withoutIcon={
                hasVariant($state, "editable", "editable") ? true : undefined
              }
            />
          ) : null}
        </Stack__>
        <SubcomponentTextInput
          data-plasmic-name={"description"}
          data-plasmic-override={overrides.description}
          className={classNames("__wab_instance", sty.description, {
            [sty.descriptioneditable]: hasVariant(
              $state,
              "editable",
              "editable"
            )
          })}
          displayText={(() => {
            try {
              return $state.description.value.length > 160
                ? $state.description.value.slice(0, 160) + "..."
                : $state.description.value;
            } catch (e) {
              if (
                e instanceof TypeError ||
                e?.plasmicType === "PlasmicUndefinedDataError"
              ) {
                return undefined;
              }
              throw e;
            }
          })()}
          editView={"core"}
          errorMessage={generateStateValueProp($state, [
            "description",
            "errorMessage"
          ])}
          inputHoverText={"Summary of License"}
          inputName={"Summary of License"}
          inputPlaceholder={"Summary of License"}
          inputValue={generateStateValueProp($state, ["description", "value"])}
          onErrorMessageChange={async (...eventArgs: any) => {
            generateStateOnChangeProp($state, [
              "description",
              "errorMessage"
            ]).apply(null, eventArgs);

            if (
              eventArgs.length > 1 &&
              eventArgs[1] &&
              eventArgs[1]._plasmic_state_init_
            ) {
              return;
            }
          }}
          onInputValueChange={async (...eventArgs: any) => {
            generateStateOnChangeProp($state, ["description", "value"]).apply(
              null,
              eventArgs
            );

            if (
              eventArgs.length > 1 &&
              eventArgs[1] &&
              eventArgs[1]._plasmic_state_init_
            ) {
              return;
            }
          }}
          viewOnly={
            hasVariant($state, "editable", "editable") ? undefined : true
          }
        />
      </div>
      <SubcomponentDeleteButton
        data-plasmic-name={"subDeleteButton"}
        data-plasmic-override={overrides.subDeleteButton}
        className={classNames("__wab_instance", sty.subDeleteButton, {
          [sty.subDeleteButtoneditable]: hasVariant(
            $state,
            "editable",
            "editable"
          )
        })}
        onClick={args.deleteButtonOnClick}
        onClickStageChange={async (...eventArgs: any) => {
          generateStateOnChangeProp($state, [
            "subDeleteButton",
            "clickStage"
          ]).apply(null, eventArgs);

          if (
            eventArgs.length > 1 &&
            eventArgs[1] &&
            eventArgs[1]._plasmic_state_init_
          ) {
            return;
          }
        }}
        onDisabledChange={async (...eventArgs: any) => {
          generateStateOnChangeProp($state, [
            "subDeleteButton",
            "disabled"
          ]).apply(null, eventArgs);

          if (
            eventArgs.length > 1 &&
            eventArgs[1] &&
            eventArgs[1]._plasmic_state_init_
          ) {
            return;
          }
        }}
      />
    </div>
  ) as React.ReactElement | null;
}

const PlasmicDescendants = {
  licenseSpacingContainer: [
    "licenseSpacingContainer",
    "licenseIcon",
    "informationStack",
    "titleInput",
    "licensingBodyInput",
    "infoBar",
    "issueLocationInput",
    "iconSpot5",
    "registrationNumberInput",
    "iconSpot3",
    "instructorInput",
    "iconSpot4",
    "issueDateInput",
    "iconSpot2",
    "expirationDateInput",
    "iconSpot6",
    "description",
    "subDeleteButton"
  ],
  licenseIcon: ["licenseIcon"],
  informationStack: [
    "informationStack",
    "titleInput",
    "licensingBodyInput",
    "infoBar",
    "issueLocationInput",
    "iconSpot5",
    "registrationNumberInput",
    "iconSpot3",
    "instructorInput",
    "iconSpot4",
    "issueDateInput",
    "iconSpot2",
    "expirationDateInput",
    "iconSpot6",
    "description"
  ],
  titleInput: ["titleInput"],
  licensingBodyInput: ["licensingBodyInput"],
  infoBar: [
    "infoBar",
    "issueLocationInput",
    "iconSpot5",
    "registrationNumberInput",
    "iconSpot3",
    "instructorInput",
    "iconSpot4",
    "issueDateInput",
    "iconSpot2",
    "expirationDateInput",
    "iconSpot6"
  ],
  issueLocationInput: ["issueLocationInput", "iconSpot5"],
  iconSpot5: ["iconSpot5"],
  registrationNumberInput: ["registrationNumberInput", "iconSpot3"],
  iconSpot3: ["iconSpot3"],
  instructorInput: ["instructorInput", "iconSpot4"],
  iconSpot4: ["iconSpot4"],
  issueDateInput: ["issueDateInput", "iconSpot2"],
  iconSpot2: ["iconSpot2"],
  expirationDateInput: ["expirationDateInput", "iconSpot6"],
  iconSpot6: ["iconSpot6"],
  description: ["description"],
  subDeleteButton: ["subDeleteButton"]
} as const;
type NodeNameType = keyof typeof PlasmicDescendants;
type DescendantsType<T extends NodeNameType> =
  (typeof PlasmicDescendants)[T][number];
type NodeDefaultElementType = {
  licenseSpacingContainer: "div";
  licenseIcon: "svg";
  informationStack: "div";
  titleInput: typeof SubcomponentTextInput;
  licensingBodyInput: typeof SubcomponentTextInput;
  infoBar: "section";
  issueLocationInput: typeof SubcomponentIconWithText;
  iconSpot5: "svg";
  registrationNumberInput: typeof SubcomponentIconWithText;
  iconSpot3: "svg";
  instructorInput: typeof SubcomponentIconWithText;
  iconSpot4: "svg";
  issueDateInput: typeof SubcomponentIconWithText;
  iconSpot2: "svg";
  expirationDateInput: typeof SubcomponentIconWithText;
  iconSpot6: "svg";
  description: typeof SubcomponentTextInput;
  subDeleteButton: typeof SubcomponentDeleteButton;
};

type ReservedPropsType = "variants" | "args" | "overrides";
type NodeOverridesType<T extends NodeNameType> = Pick<
  PlasmicProfileTileLicenseTile__OverridesType,
  DescendantsType<T>
>;
type NodeComponentProps<T extends NodeNameType> =
  // Explicitly specify variants, args, and overrides as objects
  {
    variants?: PlasmicProfileTileLicenseTile__VariantsArgs;
    args?: PlasmicProfileTileLicenseTile__ArgsType;
    overrides?: NodeOverridesType<T>;
  } & Omit<PlasmicProfileTileLicenseTile__VariantsArgs, ReservedPropsType> & // Specify variants directly as props
    /* Specify args directly as props*/ Omit<
      PlasmicProfileTileLicenseTile__ArgsType,
      ReservedPropsType
    > &
    /* Specify overrides for each element directly as props*/ Omit<
      NodeOverridesType<T>,
      ReservedPropsType | VariantPropType | ArgPropType
    > &
    /* Specify props for the root element*/ Omit<
      Partial<React.ComponentProps<NodeDefaultElementType[T]>>,
      ReservedPropsType | VariantPropType | ArgPropType | DescendantsType<T>
    >;

function makeNodeComponent<NodeName extends NodeNameType>(nodeName: NodeName) {
  type PropsType = NodeComponentProps<NodeName> & { key?: React.Key };
  const func = function <T extends PropsType>(
    props: T & StrictProps<T, PropsType>
  ) {
    const { variants, args, overrides } = React.useMemo(
      () =>
        deriveRenderOpts(props, {
          name: nodeName,
          descendantNames: PlasmicDescendants[nodeName],
          internalArgPropNames: PlasmicProfileTileLicenseTile__ArgProps,
          internalVariantPropNames: PlasmicProfileTileLicenseTile__VariantProps
        }),
      [props, nodeName]
    );
    return PlasmicProfileTileLicenseTile__RenderFunc({
      variants,
      args,
      overrides,
      forNode: nodeName
    });
  };
  if (nodeName === "licenseSpacingContainer") {
    func.displayName = "PlasmicProfileTileLicenseTile";
  } else {
    func.displayName = `PlasmicProfileTileLicenseTile.${nodeName}`;
  }
  return func;
}

export const PlasmicProfileTileLicenseTile = Object.assign(
  // Top-level PlasmicProfileTileLicenseTile renders the root element
  makeNodeComponent("licenseSpacingContainer"),
  {
    // Helper components rendering sub-elements
    licenseIcon: makeNodeComponent("licenseIcon"),
    informationStack: makeNodeComponent("informationStack"),
    titleInput: makeNodeComponent("titleInput"),
    licensingBodyInput: makeNodeComponent("licensingBodyInput"),
    infoBar: makeNodeComponent("infoBar"),
    issueLocationInput: makeNodeComponent("issueLocationInput"),
    iconSpot5: makeNodeComponent("iconSpot5"),
    registrationNumberInput: makeNodeComponent("registrationNumberInput"),
    iconSpot3: makeNodeComponent("iconSpot3"),
    instructorInput: makeNodeComponent("instructorInput"),
    iconSpot4: makeNodeComponent("iconSpot4"),
    issueDateInput: makeNodeComponent("issueDateInput"),
    iconSpot2: makeNodeComponent("iconSpot2"),
    expirationDateInput: makeNodeComponent("expirationDateInput"),
    iconSpot6: makeNodeComponent("iconSpot6"),
    description: makeNodeComponent("description"),
    subDeleteButton: makeNodeComponent("subDeleteButton"),

    // Metadata about props expected for PlasmicProfileTileLicenseTile
    internalVariantProps: PlasmicProfileTileLicenseTile__VariantProps,
    internalArgProps: PlasmicProfileTileLicenseTile__ArgProps
  }
);

export default PlasmicProfileTileLicenseTile;
/* prettier-ignore-end */
