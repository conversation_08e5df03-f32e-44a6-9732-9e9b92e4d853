/* eslint-disable */
/* tslint:disable */
// @ts-nocheck
/* prettier-ignore-start */

/** @jsxRuntime classic */
/** @jsx createPlasmicElementProxy */
/** @jsxFrag React.Fragment */

// This class is auto-generated by Plasmic; please do not edit!
// Plasmic Project: nZeWKcn21KijvC7eT8B3c7
// Component: po-xZOF5f8ym

"use client";

import * as React from "react";

import Head from "next/head";
import Link, { LinkProps } from "next/link";
import { useRouter } from "next/navigation";

import {
  Flex as Flex__,
  MultiChoiceArg,
  PlasmicDataSourceContextProvider as PlasmicDataSourceContextProvider__,
  PlasmicIcon as PlasmicIcon__,
  PlasmicImg as PlasmicImg__,
  PlasmicLink as PlasmicLink__,
  PlasmicPageGuard as PlasmicPageGuard__,
  SingleBooleanChoiceArg,
  SingleChoiceArg,
  Stack as Stack__,
  StrictProps,
  Trans as Trans__,
  classNames,
  createPlasmicElementProxy,
  deriveRenderOpts,
  ensureGlobalVariants,
  generateOnMutateForSpec,
  generateStateOnChangeProp,
  generateStateOnChangePropForCodeComponents,
  generateStateValueProp,
  get as $stateGet,
  hasVariant,
  initializeCodeComponentStates,
  initializePlasmicStates,
  makeFragment,
  omit,
  pick,
  renderPlasmicSlot,
  set as $stateSet,
  useCurrentUser,
  useDollarState,
  usePlasmicTranslator,
  useTrigger,
  wrapWithClassName
} from "@plasmicapp/react-web";
import {
  DataCtxReader as DataCtxReader__,
  useDataEnv,
  useGlobalActions
} from "@plasmicapp/host";

import ProfileSectionsProfileSectionHeading from "../../ProfileSectionsProfileSectionHeading"; // plasmic-import: Uz656rZzIxzC/component
import ProfileTileLanguagesTile from "../../ProfileTileLanguagesTile"; // plasmic-import: tlsYnN8cn_ki/component

import "@plasmicapp/react-web/lib/plasmic.css";

import projectcss from "./plasmic.module.css"; // plasmic-import: nZeWKcn21KijvC7eT8B3c7/projectcss
import sty from "./PlasmicProfileSectionsLanguageBlock.module.css"; // plasmic-import: po-xZOF5f8ym/css

createPlasmicElementProxy;

export type PlasmicProfileSectionsLanguageBlock__VariantMembers = {
  overviewGrid: "overviewGrid";
  editable: "editable";
};
export type PlasmicProfileSectionsLanguageBlock__VariantsArgs = {
  overviewGrid?: SingleBooleanChoiceArg<"overviewGrid">;
  editable?: SingleBooleanChoiceArg<"editable">;
};
type VariantPropType = keyof PlasmicProfileSectionsLanguageBlock__VariantsArgs;
export const PlasmicProfileSectionsLanguageBlock__VariantProps =
  new Array<VariantPropType>("overviewGrid", "editable");

export type PlasmicProfileSectionsLanguageBlock__ArgsType = {
  languageProficiency?: any;
};
type ArgPropType = keyof PlasmicProfileSectionsLanguageBlock__ArgsType;
export const PlasmicProfileSectionsLanguageBlock__ArgProps =
  new Array<ArgPropType>("languageProficiency");

export type PlasmicProfileSectionsLanguageBlock__OverridesType = {
  root?: Flex__<"div">;
  profileSectionsProfileSectionHeading?: Flex__<
    typeof ProfileSectionsProfileSectionHeading
  >;
  langDisplayContainer?: Flex__<"section">;
  freeBox?: Flex__<"div">;
};

export interface DefaultProfileSectionsLanguageBlockProps {
  languageProficiency?: any;
  overviewGrid?: SingleBooleanChoiceArg<"overviewGrid">;
  editable?: SingleBooleanChoiceArg<"editable">;
  className?: string;
}

const $$ = {};

function useNextRouter() {
  try {
    return useRouter();
  } catch {}
  return undefined;
}

function PlasmicProfileSectionsLanguageBlock__RenderFunc(props: {
  variants: PlasmicProfileSectionsLanguageBlock__VariantsArgs;
  args: PlasmicProfileSectionsLanguageBlock__ArgsType;
  overrides: PlasmicProfileSectionsLanguageBlock__OverridesType;
  forNode?: string;
}) {
  const { variants, overrides, forNode } = props;

  const args = React.useMemo(
    () =>
      Object.assign(
        {},
        Object.fromEntries(
          Object.entries(props.args).filter(([_, v]) => v !== undefined)
        )
      ),
    [props.args]
  );

  const $props = {
    ...args,
    ...variants
  };

  const __nextRouter = useNextRouter();
  const $ctx = useDataEnv?.() || {};
  const refsRef = React.useRef({});
  const $refs = refsRef.current;

  let [$queries, setDollarQueries] = React.useState<
    Record<string, ReturnType<typeof usePlasmicDataOp>>
  >({});
  const stateSpecs: Parameters<typeof useDollarState>[0] = React.useMemo(
    () => [
      {
        path: "overviewGrid",
        type: "private",
        variableType: "variant",
        initFunc: ({ $props, $state, $queries, $ctx }) => $props.overviewGrid
      },
      {
        path: "editable",
        type: "private",
        variableType: "variant",
        initFunc: ({ $props, $state, $queries, $ctx }) => $props.editable
      }
    ],
    [$props, $ctx, $refs]
  );
  const $state = useDollarState(stateSpecs, {
    $props,
    $ctx,
    $queries: $queries,
    $refs
  });

  const new$Queries: Record<string, ReturnType<typeof usePlasmicDataOp>> = {};
  if (Object.keys(new$Queries).some(k => new$Queries[k] !== $queries[k])) {
    setDollarQueries(new$Queries);

    $queries = new$Queries;
  }

  return (
    <div
      data-plasmic-name={"root"}
      data-plasmic-override={overrides.root}
      data-plasmic-root={true}
      data-plasmic-for-node={forNode}
      className={classNames(
        projectcss.all,
        projectcss.root_reset,
        projectcss.plasmic_default_styles,
        projectcss.plasmic_mixins,
        projectcss.plasmic_tokens,
        sty.root,
        {
          [sty.rooteditable]: hasVariant($state, "editable", "editable"),
          [sty.rootoverviewGrid]: hasVariant(
            $state,
            "overviewGrid",
            "overviewGrid"
          )
        }
      )}
    >
      <ProfileSectionsProfileSectionHeading
        data-plasmic-name={"profileSectionsProfileSectionHeading"}
        data-plasmic-override={overrides.profileSectionsProfileSectionHeading}
        className={classNames(
          "__wab_instance",
          sty.profileSectionsProfileSectionHeading,
          {
            [sty.profileSectionsProfileSectionHeadingeditable]: hasVariant(
              $state,
              "editable",
              "editable"
            ),
            [sty.profileSectionsProfileSectionHeadingoverviewGrid]: hasVariant(
              $state,
              "overviewGrid",
              "overviewGrid"
            )
          }
        )}
        editable={hasVariant($state, "editable", "editable") ? true : undefined}
        overviewGrid={
          hasVariant($state, "overviewGrid", "overviewGrid") ? true : undefined
        }
      >
        {"Languages"}
      </ProfileSectionsProfileSectionHeading>
      <section
        data-plasmic-name={"langDisplayContainer"}
        data-plasmic-override={overrides.langDisplayContainer}
        className={classNames(projectcss.all, sty.langDisplayContainer, {
          [sty.langDisplayContainereditable]: hasVariant(
            $state,
            "editable",
            "editable"
          ),
          [sty.langDisplayContaineroverviewGrid]: hasVariant(
            $state,
            "overviewGrid",
            "overviewGrid"
          )
        })}
      >
        <div
          data-plasmic-name={"freeBox"}
          data-plasmic-override={overrides.freeBox}
          className={classNames(projectcss.all, sty.freeBox, {
            [sty.freeBoxeditable]: hasVariant($state, "editable", "editable"),
            [sty.freeBoxoverviewGrid]: hasVariant(
              $state,
              "overviewGrid",
              "overviewGrid"
            )
          })}
        >
          {(_par => (!_par ? [] : Array.isArray(_par) ? _par : [_par]))(
            (() => {
              try {
                return [2, 3, 4, 5, 6];
              } catch (e) {
                if (
                  e instanceof TypeError ||
                  e?.plasmicType === "PlasmicUndefinedDataError"
                ) {
                  return [];
                }
                throw e;
              }
            })()
          ).map((__plasmic_item_0, __plasmic_idx_0) => {
            const currentItem = __plasmic_item_0;
            const currentIndex = __plasmic_idx_0;
            return (
              <ProfileTileLanguagesTile
                className={classNames(
                  "__wab_instance",
                  sty.profileTileLanguagesTile__pgImg,
                  {
                    [sty.profileTileLanguagesTileeditable__pgImGbsXvo]:
                      hasVariant($state, "editable", "editable"),
                    [sty.profileTileLanguagesTileoverviewGrid__pgImg3Upgm]:
                      hasVariant($state, "overviewGrid", "overviewGrid")
                  }
                )}
                key={currentIndex}
              />
            );
          })}
        </div>
        <ProfileTileLanguagesTile
          className={classNames(
            "__wab_instance",
            sty.profileTileLanguagesTile__tEeSq,
            {
              [sty.profileTileLanguagesTileeditable__tEeSQbsXvo]: hasVariant(
                $state,
                "editable",
                "editable"
              ),
              [sty.profileTileLanguagesTileoverviewGrid__tEeSq3Upgm]:
                hasVariant($state, "overviewGrid", "overviewGrid")
            }
          )}
          overviewGrid={true}
        />
      </section>
    </div>
  ) as React.ReactElement | null;
}

const PlasmicDescendants = {
  root: [
    "root",
    "profileSectionsProfileSectionHeading",
    "langDisplayContainer",
    "freeBox"
  ],
  profileSectionsProfileSectionHeading: [
    "profileSectionsProfileSectionHeading"
  ],
  langDisplayContainer: ["langDisplayContainer", "freeBox"],
  freeBox: ["freeBox"]
} as const;
type NodeNameType = keyof typeof PlasmicDescendants;
type DescendantsType<T extends NodeNameType> =
  (typeof PlasmicDescendants)[T][number];
type NodeDefaultElementType = {
  root: "div";
  profileSectionsProfileSectionHeading: typeof ProfileSectionsProfileSectionHeading;
  langDisplayContainer: "section";
  freeBox: "div";
};

type ReservedPropsType = "variants" | "args" | "overrides";
type NodeOverridesType<T extends NodeNameType> = Pick<
  PlasmicProfileSectionsLanguageBlock__OverridesType,
  DescendantsType<T>
>;
type NodeComponentProps<T extends NodeNameType> =
  // Explicitly specify variants, args, and overrides as objects
  {
    variants?: PlasmicProfileSectionsLanguageBlock__VariantsArgs;
    args?: PlasmicProfileSectionsLanguageBlock__ArgsType;
    overrides?: NodeOverridesType<T>;
  } & Omit< // Specify variants directly as props
    PlasmicProfileSectionsLanguageBlock__VariantsArgs,
    ReservedPropsType
  > &
    /* Specify args directly as props*/ Omit<
      PlasmicProfileSectionsLanguageBlock__ArgsType,
      ReservedPropsType
    > &
    /* Specify overrides for each element directly as props*/ Omit<
      NodeOverridesType<T>,
      ReservedPropsType | VariantPropType | ArgPropType
    > &
    /* Specify props for the root element*/ Omit<
      Partial<React.ComponentProps<NodeDefaultElementType[T]>>,
      ReservedPropsType | VariantPropType | ArgPropType | DescendantsType<T>
    >;

function makeNodeComponent<NodeName extends NodeNameType>(nodeName: NodeName) {
  type PropsType = NodeComponentProps<NodeName> & { key?: React.Key };
  const func = function <T extends PropsType>(
    props: T & StrictProps<T, PropsType>
  ) {
    const { variants, args, overrides } = React.useMemo(
      () =>
        deriveRenderOpts(props, {
          name: nodeName,
          descendantNames: PlasmicDescendants[nodeName],
          internalArgPropNames: PlasmicProfileSectionsLanguageBlock__ArgProps,
          internalVariantPropNames:
            PlasmicProfileSectionsLanguageBlock__VariantProps
        }),
      [props, nodeName]
    );
    return PlasmicProfileSectionsLanguageBlock__RenderFunc({
      variants,
      args,
      overrides,
      forNode: nodeName
    });
  };
  if (nodeName === "root") {
    func.displayName = "PlasmicProfileSectionsLanguageBlock";
  } else {
    func.displayName = `PlasmicProfileSectionsLanguageBlock.${nodeName}`;
  }
  return func;
}

export const PlasmicProfileSectionsLanguageBlock = Object.assign(
  // Top-level PlasmicProfileSectionsLanguageBlock renders the root element
  makeNodeComponent("root"),
  {
    // Helper components rendering sub-elements
    profileSectionsProfileSectionHeading: makeNodeComponent(
      "profileSectionsProfileSectionHeading"
    ),
    langDisplayContainer: makeNodeComponent("langDisplayContainer"),
    freeBox: makeNodeComponent("freeBox"),

    // Metadata about props expected for PlasmicProfileSectionsLanguageBlock
    internalVariantProps: PlasmicProfileSectionsLanguageBlock__VariantProps,
    internalArgProps: PlasmicProfileSectionsLanguageBlock__ArgProps
  }
);

export default PlasmicProfileSectionsLanguageBlock;
/* prettier-ignore-end */
