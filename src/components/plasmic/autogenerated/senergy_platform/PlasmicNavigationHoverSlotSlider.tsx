/* eslint-disable */
/* tslint:disable */
// @ts-nocheck
/* prettier-ignore-start */

/** @jsxRuntime classic */
/** @jsx createPlasmicElementProxy */
/** @jsxFrag React.Fragment */

// This class is auto-generated by Plasmic; please do not edit!
// Plasmic Project: nZeWKcn21KijvC7eT8B3c7
// Component: 7r9yyvza21zd

"use client";

import * as React from "react";

import Head from "next/head";
import Link, { LinkProps } from "next/link";
import { useRouter } from "next/navigation";

import {
  Flex as Flex__,
  MultiChoiceArg,
  PlasmicDataSourceContextProvider as PlasmicDataSourceContextProvider__,
  PlasmicIcon as PlasmicIcon__,
  PlasmicImg as PlasmicImg__,
  PlasmicLink as PlasmicLink__,
  PlasmicPageGuard as PlasmicPageGuard__,
  SingleBooleanChoiceArg,
  SingleChoiceArg,
  Stack as Stack__,
  StrictProps,
  Trans as Trans__,
  classNames,
  createPlasmicElementProxy,
  deriveRenderOpts,
  ensureGlobalVariants,
  generateOnMutateForSpec,
  generateStateOnChangeProp,
  generateStateOnChangePropForCodeComponents,
  generateStateValueProp,
  get as $stateGet,
  hasVariant,
  initializeCodeComponentStates,
  initializePlasmicStates,
  makeFragment,
  omit,
  pick,
  renderPlasmicSlot,
  set as $stateSet,
  useCurrentUser,
  useDollarState,
  usePlasmicTranslator,
  useTrigger,
  wrapWithClassName
} from "@plasmicapp/react-web";
import {
  DataCtxReader as DataCtxReader__,
  useDataEnv,
  useGlobalActions
} from "@plasmicapp/host";

import "@plasmicapp/react-web/lib/plasmic.css";

import projectcss from "./plasmic.module.css"; // plasmic-import: nZeWKcn21KijvC7eT8B3c7/projectcss
import sty from "./PlasmicNavigationHoverSlotSlider.module.css"; // plasmic-import: 7r9yyvza21zd/css

createPlasmicElementProxy;

export type PlasmicNavigationHoverSlotSlider__VariantMembers = {
  activeTab: "slot1" | "slot2" | "slot3";
  hoverTab: "slot1" | "slot2" | "slot3";
};
export type PlasmicNavigationHoverSlotSlider__VariantsArgs = {
  activeTab?: SingleChoiceArg<"slot1" | "slot2" | "slot3">;
  hoverTab?: SingleChoiceArg<"slot1" | "slot2" | "slot3">;
};
type VariantPropType = keyof PlasmicNavigationHoverSlotSlider__VariantsArgs;
export const PlasmicNavigationHoverSlotSlider__VariantProps =
  new Array<VariantPropType>("activeTab", "hoverTab");

export type PlasmicNavigationHoverSlotSlider__ArgsType = {};
type ArgPropType = keyof PlasmicNavigationHoverSlotSlider__ArgsType;
export const PlasmicNavigationHoverSlotSlider__ArgProps =
  new Array<ArgPropType>();

export type PlasmicNavigationHoverSlotSlider__OverridesType = {
  hoverSlotSlider?: Flex__<"section">;
};

export interface DefaultNavigationHoverSlotSliderProps {
  activeTab?: SingleChoiceArg<"slot1" | "slot2" | "slot3">;
  hoverTab?: SingleChoiceArg<"slot1" | "slot2" | "slot3">;
  className?: string;
}

const $$ = {};

function useNextRouter() {
  try {
    return useRouter();
  } catch {}
  return undefined;
}

function PlasmicNavigationHoverSlotSlider__RenderFunc(props: {
  variants: PlasmicNavigationHoverSlotSlider__VariantsArgs;
  args: PlasmicNavigationHoverSlotSlider__ArgsType;
  overrides: PlasmicNavigationHoverSlotSlider__OverridesType;
  forNode?: string;
}) {
  const { variants, overrides, forNode } = props;

  const args = React.useMemo(
    () =>
      Object.assign(
        {},
        Object.fromEntries(
          Object.entries(props.args).filter(([_, v]) => v !== undefined)
        )
      ),
    [props.args]
  );

  const $props = {
    ...args,
    ...variants
  };

  const __nextRouter = useNextRouter();
  const $ctx = useDataEnv?.() || {};
  const refsRef = React.useRef({});
  const $refs = refsRef.current;

  let [$queries, setDollarQueries] = React.useState<
    Record<string, ReturnType<typeof usePlasmicDataOp>>
  >({});
  const stateSpecs: Parameters<typeof useDollarState>[0] = React.useMemo(
    () => [
      {
        path: "activeTab",
        type: "private",
        variableType: "variant",
        initFunc: ({ $props, $state, $queries, $ctx }) => $props.activeTab
      },
      {
        path: "hoverTab",
        type: "private",
        variableType: "variant",
        initFunc: ({ $props, $state, $queries, $ctx }) => $props.hoverTab
      }
    ],
    [$props, $ctx, $refs]
  );
  const $state = useDollarState(stateSpecs, {
    $props,
    $ctx,
    $queries: $queries,
    $refs
  });

  const new$Queries: Record<string, ReturnType<typeof usePlasmicDataOp>> = {};
  if (Object.keys(new$Queries).some(k => new$Queries[k] !== $queries[k])) {
    setDollarQueries(new$Queries);

    $queries = new$Queries;
  }

  return (
    <section
      data-plasmic-name={"hoverSlotSlider"}
      data-plasmic-override={overrides.hoverSlotSlider}
      data-plasmic-root={true}
      data-plasmic-for-node={forNode}
      className={classNames(
        projectcss.all,
        projectcss.root_reset,
        projectcss.plasmic_default_styles,
        projectcss.plasmic_mixins,
        projectcss.plasmic_tokens,
        sty.hoverSlotSlider,
        {
          [sty.hoverSlotSlideractiveTab_slot1]: hasVariant(
            $state,
            "activeTab",
            "slot1"
          ),
          [sty.hoverSlotSlideractiveTab_slot1_hoverTab_slot2]:
            hasVariant($state, "activeTab", "slot1") &&
            hasVariant($state, "hoverTab", "slot2"),
          [sty.hoverSlotSlideractiveTab_slot1_hoverTab_slot3]:
            hasVariant($state, "activeTab", "slot1") &&
            hasVariant($state, "hoverTab", "slot3"),
          [sty.hoverSlotSlideractiveTab_slot2]: hasVariant(
            $state,
            "activeTab",
            "slot2"
          ),
          [sty.hoverSlotSlideractiveTab_slot2_hoverTab_slot1]:
            hasVariant($state, "activeTab", "slot2") &&
            hasVariant($state, "hoverTab", "slot1"),
          [sty.hoverSlotSlideractiveTab_slot2_hoverTab_slot3]:
            hasVariant($state, "activeTab", "slot2") &&
            hasVariant($state, "hoverTab", "slot3"),
          [sty.hoverSlotSlideractiveTab_slot3]: hasVariant(
            $state,
            "activeTab",
            "slot3"
          ),
          [sty.hoverSlotSlideractiveTab_slot3_hoverTab_slot1]:
            hasVariant($state, "activeTab", "slot3") &&
            hasVariant($state, "hoverTab", "slot1"),
          [sty.hoverSlotSliderhoverTab_slot1]: hasVariant(
            $state,
            "hoverTab",
            "slot1"
          ),
          [sty.hoverSlotSliderhoverTab_slot2]: hasVariant(
            $state,
            "hoverTab",
            "slot2"
          ),
          [sty.hoverSlotSliderhoverTab_slot2_activeTab_slot3]:
            hasVariant($state, "activeTab", "slot3") &&
            hasVariant($state, "hoverTab", "slot2"),
          [sty.hoverSlotSliderhoverTab_slot3]: hasVariant(
            $state,
            "hoverTab",
            "slot3"
          )
        }
      )}
    />
  ) as React.ReactElement | null;
}

const PlasmicDescendants = {
  hoverSlotSlider: ["hoverSlotSlider"]
} as const;
type NodeNameType = keyof typeof PlasmicDescendants;
type DescendantsType<T extends NodeNameType> =
  (typeof PlasmicDescendants)[T][number];
type NodeDefaultElementType = {
  hoverSlotSlider: "section";
};

type ReservedPropsType = "variants" | "args" | "overrides";
type NodeOverridesType<T extends NodeNameType> = Pick<
  PlasmicNavigationHoverSlotSlider__OverridesType,
  DescendantsType<T>
>;
type NodeComponentProps<T extends NodeNameType> =
  // Explicitly specify variants, args, and overrides as objects
  {
    variants?: PlasmicNavigationHoverSlotSlider__VariantsArgs;
    args?: PlasmicNavigationHoverSlotSlider__ArgsType;
    overrides?: NodeOverridesType<T>;
  } & Omit<PlasmicNavigationHoverSlotSlider__VariantsArgs, ReservedPropsType> & // Specify variants directly as props
    /* Specify args directly as props*/ Omit<
      PlasmicNavigationHoverSlotSlider__ArgsType,
      ReservedPropsType
    > &
    /* Specify overrides for each element directly as props*/ Omit<
      NodeOverridesType<T>,
      ReservedPropsType | VariantPropType | ArgPropType
    > &
    /* Specify props for the root element*/ Omit<
      Partial<React.ComponentProps<NodeDefaultElementType[T]>>,
      ReservedPropsType | VariantPropType | ArgPropType | DescendantsType<T>
    >;

function makeNodeComponent<NodeName extends NodeNameType>(nodeName: NodeName) {
  type PropsType = NodeComponentProps<NodeName> & { key?: React.Key };
  const func = function <T extends PropsType>(
    props: T & StrictProps<T, PropsType>
  ) {
    const { variants, args, overrides } = React.useMemo(
      () =>
        deriveRenderOpts(props, {
          name: nodeName,
          descendantNames: PlasmicDescendants[nodeName],
          internalArgPropNames: PlasmicNavigationHoverSlotSlider__ArgProps,
          internalVariantPropNames:
            PlasmicNavigationHoverSlotSlider__VariantProps
        }),
      [props, nodeName]
    );
    return PlasmicNavigationHoverSlotSlider__RenderFunc({
      variants,
      args,
      overrides,
      forNode: nodeName
    });
  };
  if (nodeName === "hoverSlotSlider") {
    func.displayName = "PlasmicNavigationHoverSlotSlider";
  } else {
    func.displayName = `PlasmicNavigationHoverSlotSlider.${nodeName}`;
  }
  return func;
}

export const PlasmicNavigationHoverSlotSlider = Object.assign(
  // Top-level PlasmicNavigationHoverSlotSlider renders the root element
  makeNodeComponent("hoverSlotSlider"),
  {
    // Helper components rendering sub-elements

    // Metadata about props expected for PlasmicNavigationHoverSlotSlider
    internalVariantProps: PlasmicNavigationHoverSlotSlider__VariantProps,
    internalArgProps: PlasmicNavigationHoverSlotSlider__ArgProps
  }
);

export default PlasmicNavigationHoverSlotSlider;
/* prettier-ignore-end */
