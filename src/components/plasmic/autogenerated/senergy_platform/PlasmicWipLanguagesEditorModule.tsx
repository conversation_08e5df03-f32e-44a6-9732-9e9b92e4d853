/* eslint-disable */
/* tslint:disable */
// @ts-nocheck
/* prettier-ignore-start */

/** @jsxRuntime classic */
/** @jsx createPlasmicElementProxy */
/** @jsxFrag React.Fragment */

// This class is auto-generated by Plasmic; please do not edit!
// Plasmic Project: nZeWKcn21KijvC7eT8B3c7
// Component: N6mlKPJGFxDf

"use client";

import * as React from "react";

import Head from "next/head";
import Link, { LinkProps } from "next/link";
import { useRouter } from "next/navigation";

import {
  Flex as Flex__,
  MultiChoiceArg,
  PlasmicDataSourceContextProvider as PlasmicDataSourceContextProvider__,
  PlasmicIcon as PlasmicIcon__,
  PlasmicImg as PlasmicImg__,
  PlasmicLink as PlasmicLink__,
  PlasmicPageGuard as PlasmicPageGuard__,
  SingleBooleanChoiceArg,
  SingleC<PERSON><PERSON><PERSON>rg,
  Stack as Stack__,
  StrictProps,
  Trans as Trans__,
  classNames,
  createPlasmicElementProxy,
  deriveRenderOpts,
  ensureGlobalVariants,
  generateOnMutateForSpec,
  generateStateOnChangeProp,
  generateStateOnChangePropForCodeComponents,
  generateStateValueProp,
  get as $stateGet,
  hasVariant,
  initializeCodeComponentStates,
  initializePlasmicStates,
  makeFragment,
  omit,
  pick,
  renderPlasmicSlot,
  set as $stateSet,
  useCurrentUser,
  useDollarState,
  usePlasmicTranslator,
  useTrigger,
  wrapWithClassName
} from "@plasmicapp/react-web";
import {
  DataCtxReader as DataCtxReader__,
  useDataEnv,
  useGlobalActions
} from "@plasmicapp/host";

import SubcomponentTextInput2 from "../../SubcomponentTextInput2"; // plasmic-import: x5ySv6UMingi/component
import SubcomponentSelect from "../../SubcomponentSelect"; // plasmic-import: CvlbcsmPEnlZ/component
import SubcomponentCheckbox from "../../SubcomponentCheckbox"; // plasmic-import: OeFTSKgMult8/component

import "@plasmicapp/react-web/lib/plasmic.css";

import projectcss from "./plasmic.module.css"; // plasmic-import: nZeWKcn21KijvC7eT8B3c7/projectcss
import sty from "./PlasmicWipLanguagesEditorModule.module.css"; // plasmic-import: N6mlKPJGFxDf/css

import MenuIcon from "./icons/PlasmicIcon__Menu"; // plasmic-import: pjtzwEs6QILW/icon
import SearchSvgIcon from "./icons/PlasmicIcon__SearchSvg"; // plasmic-import: o4-xGtH6_4AB/icon
import Icon5Icon from "./icons/PlasmicIcon__Icon5"; // plasmic-import: 6Ni9R0Lyd72t/icon

createPlasmicElementProxy;

export type PlasmicWipLanguagesEditorModule__VariantMembers = {};
export type PlasmicWipLanguagesEditorModule__VariantsArgs = {};
type VariantPropType = keyof PlasmicWipLanguagesEditorModule__VariantsArgs;
export const PlasmicWipLanguagesEditorModule__VariantProps =
  new Array<VariantPropType>();

export type PlasmicWipLanguagesEditorModule__ArgsType = {};
type ArgPropType = keyof PlasmicWipLanguagesEditorModule__ArgsType;
export const PlasmicWipLanguagesEditorModule__ArgProps =
  new Array<ArgPropType>();

export type PlasmicWipLanguagesEditorModule__OverridesType = {
  langEditMod?: Flex__<"div">;
  textInput?: Flex__<typeof SubcomponentTextInput2>;
  subSelect?: Flex__<typeof SubcomponentSelect>;
  subCheckbox?: Flex__<typeof SubcomponentCheckbox>;
  textInput2?: Flex__<typeof SubcomponentTextInput2>;
  subSelect2?: Flex__<typeof SubcomponentSelect>;
  textInput3?: Flex__<typeof SubcomponentTextInput2>;
  subSelect3?: Flex__<typeof SubcomponentSelect>;
  textInput4?: Flex__<typeof SubcomponentTextInput2>;
  subSelect4?: Flex__<typeof SubcomponentSelect>;
};

export interface DefaultWipLanguagesEditorModuleProps {
  className?: string;
}

const $$ = {};

function useNextRouter() {
  try {
    return useRouter();
  } catch {}
  return undefined;
}

function PlasmicWipLanguagesEditorModule__RenderFunc(props: {
  variants: PlasmicWipLanguagesEditorModule__VariantsArgs;
  args: PlasmicWipLanguagesEditorModule__ArgsType;
  overrides: PlasmicWipLanguagesEditorModule__OverridesType;
  forNode?: string;
}) {
  const { variants, overrides, forNode } = props;

  const args = React.useMemo(
    () =>
      Object.assign(
        {},
        Object.fromEntries(
          Object.entries(props.args).filter(([_, v]) => v !== undefined)
        )
      ),
    [props.args]
  );

  const $props = {
    ...args,
    ...variants
  };

  const __nextRouter = useNextRouter();
  const $ctx = useDataEnv?.() || {};
  const refsRef = React.useRef({});
  const $refs = refsRef.current;

  let [$queries, setDollarQueries] = React.useState<
    Record<string, ReturnType<typeof usePlasmicDataOp>>
  >({});
  const stateSpecs: Parameters<typeof useDollarState>[0] = React.useMemo(
    () => [
      {
        path: "subSelect.value",
        type: "private",
        variableType: "text",
        initFunc: ({ $props, $state, $queries, $ctx }) => undefined
      },
      {
        path: "textInput.value",
        type: "private",
        variableType: "text",
        initFunc: ({ $props, $state, $queries, $ctx }) => ""
      },
      {
        path: "textInput2.value",
        type: "private",
        variableType: "text",
        initFunc: ({ $props, $state, $queries, $ctx }) => ""
      },
      {
        path: "subSelect2.value",
        type: "private",
        variableType: "text",
        initFunc: ({ $props, $state, $queries, $ctx }) => undefined
      },
      {
        path: "textInput3.value",
        type: "private",
        variableType: "text",
        initFunc: ({ $props, $state, $queries, $ctx }) => ""
      },
      {
        path: "subSelect3.value",
        type: "private",
        variableType: "text",
        initFunc: ({ $props, $state, $queries, $ctx }) => undefined
      },
      {
        path: "textInput4.value",
        type: "private",
        variableType: "text",
        initFunc: ({ $props, $state, $queries, $ctx }) => ""
      },
      {
        path: "subSelect4.value",
        type: "private",
        variableType: "text",
        initFunc: ({ $props, $state, $queries, $ctx }) => undefined
      },
      {
        path: "subCheckbox.isChecked",
        type: "private",
        variableType: "boolean",
        initFunc: ({ $props, $state, $queries, $ctx }) => undefined
      }
    ],
    [$props, $ctx, $refs]
  );
  const $state = useDollarState(stateSpecs, {
    $props,
    $ctx,
    $queries: $queries,
    $refs
  });

  const new$Queries: Record<string, ReturnType<typeof usePlasmicDataOp>> = {};
  if (Object.keys(new$Queries).some(k => new$Queries[k] !== $queries[k])) {
    setDollarQueries(new$Queries);

    $queries = new$Queries;
  }

  return (
    <Stack__
      as={"div"}
      data-plasmic-name={"langEditMod"}
      data-plasmic-override={overrides.langEditMod}
      data-plasmic-root={true}
      data-plasmic-for-node={forNode}
      hasGap={true}
      className={classNames(
        projectcss.all,
        projectcss.root_reset,
        projectcss.plasmic_default_styles,
        projectcss.plasmic_mixins,
        projectcss.plasmic_tokens,
        sty.langEditMod
      )}
    >
      <Stack__
        as={"section"}
        hasGap={true}
        className={classNames(projectcss.all, sty.section__rgbpm)}
      >
        <MenuIcon
          className={classNames(projectcss.all, sty.svg__i7SFc)}
          role={"img"}
        />

        <SubcomponentTextInput2
          data-plasmic-name={"textInput"}
          data-plasmic-override={overrides.textInput}
          className={classNames("__wab_instance", sty.textInput)}
          onChange={async (...eventArgs: any) => {
            ((...eventArgs) => {
              generateStateOnChangeProp($state, ["textInput", "value"])(
                (e => e.target?.value).apply(null, eventArgs)
              );
            }).apply(null, eventArgs);

            if (
              eventArgs.length > 1 &&
              eventArgs[1] &&
              eventArgs[1]._plasmic_state_init_
            ) {
              return;
            }
          }}
          value={generateStateValueProp($state, ["textInput", "value"]) ?? ""}
        />

        <SubcomponentSelect
          data-plasmic-name={"subSelect"}
          data-plasmic-override={overrides.subSelect}
          onChange={async (...eventArgs: any) => {
            ((...eventArgs) => {
              generateStateOnChangeProp($state, ["subSelect", "value"])(
                eventArgs[0]
              );
            }).apply(null, eventArgs);

            if (
              eventArgs.length > 1 &&
              eventArgs[1] &&
              eventArgs[1]._plasmic_state_init_
            ) {
              return;
            }
          }}
          options={[
            { value: "option1", label: "Option 1" },
            { value: "option2", label: "Option 2" }
          ]}
          value={generateStateValueProp($state, ["subSelect", "value"])}
        />

        <SubcomponentCheckbox
          data-plasmic-name={"subCheckbox"}
          data-plasmic-override={overrides.subCheckbox}
          className={classNames("__wab_instance", sty.subCheckbox)}
          isChecked={
            generateStateValueProp($state, ["subCheckbox", "isChecked"]) ??
            false
          }
          onChange={async (...eventArgs: any) => {
            ((...eventArgs) => {
              generateStateOnChangeProp($state, ["subCheckbox", "isChecked"])(
                eventArgs[0]
              );
            }).apply(null, eventArgs);

            if (
              eventArgs.length > 1 &&
              eventArgs[1] &&
              eventArgs[1]._plasmic_state_init_
            ) {
              return;
            }
          }}
        >
          {"Primary"}
        </SubcomponentCheckbox>
      </Stack__>
      <Stack__
        as={"section"}
        hasGap={true}
        className={classNames(projectcss.all, sty.section___0Sdm6)}
      >
        <MenuIcon
          className={classNames(projectcss.all, sty.svg__vOhgp)}
          role={"img"}
        />

        <SubcomponentTextInput2
          data-plasmic-name={"textInput2"}
          data-plasmic-override={overrides.textInput2}
          className={classNames("__wab_instance", sty.textInput2)}
          onChange={async (...eventArgs: any) => {
            ((...eventArgs) => {
              generateStateOnChangeProp($state, ["textInput2", "value"])(
                (e => e.target?.value).apply(null, eventArgs)
              );
            }).apply(null, eventArgs);

            if (
              eventArgs.length > 1 &&
              eventArgs[1] &&
              eventArgs[1]._plasmic_state_init_
            ) {
              return;
            }
          }}
          value={generateStateValueProp($state, ["textInput2", "value"]) ?? ""}
        />

        <SubcomponentSelect
          data-plasmic-name={"subSelect2"}
          data-plasmic-override={overrides.subSelect2}
          onChange={async (...eventArgs: any) => {
            ((...eventArgs) => {
              generateStateOnChangeProp($state, ["subSelect2", "value"])(
                eventArgs[0]
              );
            }).apply(null, eventArgs);

            if (
              eventArgs.length > 1 &&
              eventArgs[1] &&
              eventArgs[1]._plasmic_state_init_
            ) {
              return;
            }
          }}
          options={[
            { value: "option1", label: "Option 1" },
            { value: "option2", label: "Option 2" }
          ]}
          value={generateStateValueProp($state, ["subSelect2", "value"])}
        />
      </Stack__>
      <Stack__
        as={"section"}
        hasGap={true}
        className={classNames(projectcss.all, sty.section__ydPtq)}
      >
        <MenuIcon
          className={classNames(projectcss.all, sty.svg__dwf1S)}
          role={"img"}
        />

        <SubcomponentTextInput2
          data-plasmic-name={"textInput3"}
          data-plasmic-override={overrides.textInput3}
          className={classNames("__wab_instance", sty.textInput3)}
          onChange={async (...eventArgs: any) => {
            ((...eventArgs) => {
              generateStateOnChangeProp($state, ["textInput3", "value"])(
                (e => e.target?.value).apply(null, eventArgs)
              );
            }).apply(null, eventArgs);

            if (
              eventArgs.length > 1 &&
              eventArgs[1] &&
              eventArgs[1]._plasmic_state_init_
            ) {
              return;
            }
          }}
          value={generateStateValueProp($state, ["textInput3", "value"]) ?? ""}
        />

        <SubcomponentSelect
          data-plasmic-name={"subSelect3"}
          data-plasmic-override={overrides.subSelect3}
          onChange={async (...eventArgs: any) => {
            ((...eventArgs) => {
              generateStateOnChangeProp($state, ["subSelect3", "value"])(
                eventArgs[0]
              );
            }).apply(null, eventArgs);

            if (
              eventArgs.length > 1 &&
              eventArgs[1] &&
              eventArgs[1]._plasmic_state_init_
            ) {
              return;
            }
          }}
          options={[
            { value: "option1", label: "Option 1" },
            { value: "option2", label: "Option 2" }
          ]}
          value={generateStateValueProp($state, ["subSelect3", "value"])}
        />
      </Stack__>
      <Stack__
        as={"section"}
        hasGap={true}
        className={classNames(projectcss.all, sty.section__pIVrK)}
      >
        <MenuIcon
          className={classNames(projectcss.all, sty.svg___4YdcF)}
          role={"img"}
        />

        <SubcomponentTextInput2
          data-plasmic-name={"textInput4"}
          data-plasmic-override={overrides.textInput4}
          className={classNames("__wab_instance", sty.textInput4)}
          onChange={async (...eventArgs: any) => {
            ((...eventArgs) => {
              generateStateOnChangeProp($state, ["textInput4", "value"])(
                (e => e.target?.value).apply(null, eventArgs)
              );
            }).apply(null, eventArgs);

            if (
              eventArgs.length > 1 &&
              eventArgs[1] &&
              eventArgs[1]._plasmic_state_init_
            ) {
              return;
            }
          }}
          value={generateStateValueProp($state, ["textInput4", "value"]) ?? ""}
        />

        <SubcomponentSelect
          data-plasmic-name={"subSelect4"}
          data-plasmic-override={overrides.subSelect4}
          onChange={async (...eventArgs: any) => {
            ((...eventArgs) => {
              generateStateOnChangeProp($state, ["subSelect4", "value"])(
                eventArgs[0]
              );
            }).apply(null, eventArgs);

            if (
              eventArgs.length > 1 &&
              eventArgs[1] &&
              eventArgs[1]._plasmic_state_init_
            ) {
              return;
            }
          }}
          options={[
            { value: "option1", label: "Option 1" },
            { value: "option2", label: "Option 2" }
          ]}
          value={generateStateValueProp($state, ["subSelect4", "value"])}
        />
      </Stack__>
    </Stack__>
  ) as React.ReactElement | null;
}

const PlasmicDescendants = {
  langEditMod: [
    "langEditMod",
    "textInput",
    "subSelect",
    "subCheckbox",
    "textInput2",
    "subSelect2",
    "textInput3",
    "subSelect3",
    "textInput4",
    "subSelect4"
  ],
  textInput: ["textInput"],
  subSelect: ["subSelect"],
  subCheckbox: ["subCheckbox"],
  textInput2: ["textInput2"],
  subSelect2: ["subSelect2"],
  textInput3: ["textInput3"],
  subSelect3: ["subSelect3"],
  textInput4: ["textInput4"],
  subSelect4: ["subSelect4"]
} as const;
type NodeNameType = keyof typeof PlasmicDescendants;
type DescendantsType<T extends NodeNameType> =
  (typeof PlasmicDescendants)[T][number];
type NodeDefaultElementType = {
  langEditMod: "div";
  textInput: typeof SubcomponentTextInput2;
  subSelect: typeof SubcomponentSelect;
  subCheckbox: typeof SubcomponentCheckbox;
  textInput2: typeof SubcomponentTextInput2;
  subSelect2: typeof SubcomponentSelect;
  textInput3: typeof SubcomponentTextInput2;
  subSelect3: typeof SubcomponentSelect;
  textInput4: typeof SubcomponentTextInput2;
  subSelect4: typeof SubcomponentSelect;
};

type ReservedPropsType = "variants" | "args" | "overrides";
type NodeOverridesType<T extends NodeNameType> = Pick<
  PlasmicWipLanguagesEditorModule__OverridesType,
  DescendantsType<T>
>;
type NodeComponentProps<T extends NodeNameType> =
  // Explicitly specify variants, args, and overrides as objects
  {
    variants?: PlasmicWipLanguagesEditorModule__VariantsArgs;
    args?: PlasmicWipLanguagesEditorModule__ArgsType;
    overrides?: NodeOverridesType<T>;
  } & Omit<PlasmicWipLanguagesEditorModule__VariantsArgs, ReservedPropsType> & // Specify variants directly as props
    /* Specify args directly as props*/ Omit<
      PlasmicWipLanguagesEditorModule__ArgsType,
      ReservedPropsType
    > &
    /* Specify overrides for each element directly as props*/ Omit<
      NodeOverridesType<T>,
      ReservedPropsType | VariantPropType | ArgPropType
    > &
    /* Specify props for the root element*/ Omit<
      Partial<React.ComponentProps<NodeDefaultElementType[T]>>,
      ReservedPropsType | VariantPropType | ArgPropType | DescendantsType<T>
    >;

function makeNodeComponent<NodeName extends NodeNameType>(nodeName: NodeName) {
  type PropsType = NodeComponentProps<NodeName> & { key?: React.Key };
  const func = function <T extends PropsType>(
    props: T & StrictProps<T, PropsType>
  ) {
    const { variants, args, overrides } = React.useMemo(
      () =>
        deriveRenderOpts(props, {
          name: nodeName,
          descendantNames: PlasmicDescendants[nodeName],
          internalArgPropNames: PlasmicWipLanguagesEditorModule__ArgProps,
          internalVariantPropNames:
            PlasmicWipLanguagesEditorModule__VariantProps
        }),
      [props, nodeName]
    );
    return PlasmicWipLanguagesEditorModule__RenderFunc({
      variants,
      args,
      overrides,
      forNode: nodeName
    });
  };
  if (nodeName === "langEditMod") {
    func.displayName = "PlasmicWipLanguagesEditorModule";
  } else {
    func.displayName = `PlasmicWipLanguagesEditorModule.${nodeName}`;
  }
  return func;
}

export const PlasmicWipLanguagesEditorModule = Object.assign(
  // Top-level PlasmicWipLanguagesEditorModule renders the root element
  makeNodeComponent("langEditMod"),
  {
    // Helper components rendering sub-elements
    textInput: makeNodeComponent("textInput"),
    subSelect: makeNodeComponent("subSelect"),
    subCheckbox: makeNodeComponent("subCheckbox"),
    textInput2: makeNodeComponent("textInput2"),
    subSelect2: makeNodeComponent("subSelect2"),
    textInput3: makeNodeComponent("textInput3"),
    subSelect3: makeNodeComponent("subSelect3"),
    textInput4: makeNodeComponent("textInput4"),
    subSelect4: makeNodeComponent("subSelect4"),

    // Metadata about props expected for PlasmicWipLanguagesEditorModule
    internalVariantProps: PlasmicWipLanguagesEditorModule__VariantProps,
    internalArgProps: PlasmicWipLanguagesEditorModule__ArgProps
  }
);

export default PlasmicWipLanguagesEditorModule;
/* prettier-ignore-end */
