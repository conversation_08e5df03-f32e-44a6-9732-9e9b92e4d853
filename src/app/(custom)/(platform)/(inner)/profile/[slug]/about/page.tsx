'use client';
import { PlasmicProfileTileExperiencesTile } from '@/components/plasmic/autogenerated/senergy_platform/PlasmicProfileTileExperiencesTile';
import { useProfile } from '@/contexts/profile-context';

export default function ProfileAboutPage() {
  const { profile } = useProfile();

  // Get the location and ensure it's either a string or undefined (not null)
  const location = profile?.experience?.[0]?.company_location || undefined;
  const endDate = profile?.experience?.[0]?.end_date || undefined;

  return (
    <PlasmicProfileTileExperiencesTile
      companyNameInputValue={profile?.experience?.[0]?.company_name}
      endDateInputValue={endDate}
      jobTitleInputValue={profile?.experience?.[0]?.job_title}
      locationInputValue={location}
      overview="overview"
      startDateInputValue={profile?.experience?.[0]?.start_date}
    />
  );
}
