import { createAccessControl } from 'better-auth/plugins/access';
import { defaultStatements } from 'better-auth/plugins/admin/access';

const statement = {
  ...defaultStatements,
} as const;

export const ac = createAccessControl(statement);

export const superadmin = ac.newRole({
  user: [
    'create',
    'list',
    'set-role',
    'ban',
    'impersonate',
    'delete',
    'set-password',
  ],
  session: ['list', 'delete', 'revoke'],
});

export const admin = ac.newRole({
  user: ['create', 'list', 'set-role', 'ban', 'set-password'],
  session: ['list', 'delete', 'revoke'],
});

export const user = ac.newRole({
  user: [],
});

export const waitlisted = ac.newRole({
  user: [],
});
