import { HTMLElementRefOf } from "@plasmicapp/react-web";
// This is a skeleton starter React component generated by Plasmic.
// This file is owned by you, feel free to edit as you see fit.
import * as React from "react";
import {
  DefaultBookmarksBookmarkTilesProps,
  PlasmicBookmarksBookmarkTiles
} from "./autogenerated/senergy_platform/PlasmicBookmarksBookmarkTiles";

// Your component props start with props for variants and slots you defined
// in Plasmic, but you can add more here, like event handlers that you can
// attach to named nodes in your component.
//
// If you don't want to expose certain variants or slots as a prop, you can use
// Omit to hide them:
//
// interface BookmarksBookmarkTilesProps extends Omit<DefaultBookmarksBookmarkTilesProps, "hideProps1"|"hideProp2"> {
//   // etc.
// }
//
// You can also stop extending from DefaultBookmarksBookmarkTilesProps altogether and have
// total control over the props for your component.
export interface BookmarksBookmarkTilesProps
  extends DefaultBookmarksBookmarkTilesProps {}

function BookmarksBookmarkTiles_(
  props: BookmarksBookmarkTilesProps,
  ref: HTMLElementRefOf<"div">
) {
  // Use PlasmicBookmarksBookmarkTiles to render this component as it was
  // designed in Plasmic, by activating the appropriate variants,
  // attaching the appropriate event handlers, etc.  You
  // can also install whatever React hooks you need here to manage state or
  // fetch data.
  //
  // Props you can pass into PlasmicBookmarksBookmarkTiles are:
  // 1. Variants you want to activate,
  // 2. Contents for slots you want to fill,
  // 3. Overrides for any named node in the component to attach behavior and data,
  // 4. Props to set on the root node.
  //
  // By default, we are just piping all BookmarksBookmarkTilesProps here, but feel free
  // to do whatever works for you.

  return (
    <PlasmicBookmarksBookmarkTiles formattingAndShadow={{ ref }} {...props} />
  );
}

const BookmarksBookmarkTiles = React.forwardRef(BookmarksBookmarkTiles_);
export default BookmarksBookmarkTiles;
