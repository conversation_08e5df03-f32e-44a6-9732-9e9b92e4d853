.root {
  display: flex;
  flex-direction: column;
  position: relative;
  width: 176px;
  height: auto;
  justify-content: flex-start;
  align-items: center;
  background: var(--token-K5FbAPSIIrXM);
}
.addSectionButton:global(.__wab_instance) {
  max-width: 100%;
  position: relative;
  width: 100%;
  min-width: 0;
}
.addSectionButtonpatentOrTrademark_collapsed:global(.__wab_instance) {
  display: none;
}
.addSectionButtonpatentOrTrademark_expanded:global(.__wab_instance) {
  display: none;
}
.addSectionButtonlicenseOrCertification_collapsed:global(.__wab_instance) {
  display: none;
}
.addSectionButtonlicenseOrCertification_expanded:global(.__wab_instance) {
  display: none;
}
.svg__bcDkS {
  position: relative;
  object-fit: cover;
  width: auto;
  height: 1em;
}
.text__iatjy {
  padding-bottom: 0px;
  padding-left: 0px;
}
.svg__qvIpd {
  position: relative;
  object-fit: cover;
  width: auto;
  height: 1em;
}
.addSectionButtonPatentOrTrademark:global(.__wab_instance) {
  max-width: 100%;
  position: relative;
  width: 100%;
  min-width: 0;
  display: none;
}
.addSectionButtonPatentOrTrademarkpatentOrTrademark_collapsed:global(
    .__wab_instance
  ) {
  display: flex;
}
.addSectionButtonPatentOrTrademarkpatentOrTrademark_expanded:global(
    .__wab_instance
  ) {
  display: flex;
}
.addSectionButtonPatentOrTrademarklicenseOrCertification_collapsed:global(
    .__wab_instance
  ) {
  display: none;
}
.addSectionButtonPatentOrTrademarklicenseOrCertification_expanded:global(
    .__wab_instance
  ) {
  display: none;
}
.svg__v0ODt {
  position: relative;
  object-fit: cover;
  width: auto;
  height: 1em;
}
.text__xogXi {
  padding-bottom: 0px;
}
.svg__xTvqM {
  position: relative;
  object-fit: cover;
  width: auto;
  height: 1em;
}
.addSectionButtonLicenseOrCertification:global(.__wab_instance) {
  max-width: 100%;
  position: relative;
  width: 100%;
  min-width: 0;
  display: none;
}
.addSectionButtonLicenseOrCertificationpatentOrTrademark_collapsed:global(
    .__wab_instance
  ) {
  display: none;
}
.addSectionButtonLicenseOrCertificationpatentOrTrademark_expanded:global(
    .__wab_instance
  ) {
  display: none;
}
.addSectionButtonLicenseOrCertificationlicenseOrCertification_collapsed:global(
    .__wab_instance
  ) {
  display: flex;
}
.addSectionButtonLicenseOrCertificationlicenseOrCertification_expanded:global(
    .__wab_instance
  ) {
  display: flex;
}
.svg__pzKyD {
  position: relative;
  object-fit: cover;
  width: auto;
  height: 1em;
}
.text__rmqA {
  padding-bottom: 0px;
}
.svg__baRvG {
  position: relative;
  object-fit: cover;
  width: auto;
  height: 1em;
}
.freeBoxForDropDown {
  position: absolute;
  width: 100%;
  flex-direction: column;
  left: 0px;
  top: 44px;
  background: var(--token-K5FbAPSIIrXM);
  z-index: 20;
  min-width: 0;
  display: none;
  padding: var(--token-sazGmnf7GWAk);
}
.freeBoxForDropDownpatentOrTrademark_expanded {
  display: flex;
}
.freeBoxForDropDownlicenseOrCertification_expanded {
  display: flex;
}
.addPatent:global(.__wab_instance) {
  max-width: 100%;
  position: relative;
  width: 100%;
  min-width: 0;
}
.addPatentlicenseOrCertification_expanded:global(.__wab_instance) {
  display: none;
}
.svg__uUmnv {
  position: relative;
  object-fit: cover;
  width: auto;
  height: 1em;
}
.svg__r31FS {
  position: relative;
  object-fit: cover;
  width: auto;
  height: 1em;
}
.addTrademark:global(.__wab_instance) {
  max-width: 100%;
  position: relative;
  width: 100%;
  min-width: 0;
}
.addTrademarklicenseOrCertification_expanded:global(.__wab_instance) {
  display: none;
}
.svg__dcn1K {
  position: relative;
  object-fit: cover;
  width: auto;
  height: 1em;
}
.svg__vyUdg {
  position: relative;
  object-fit: cover;
  width: auto;
  height: 1em;
}
.addLicense:global(.__wab_instance) {
  max-width: 100%;
  position: relative;
  width: 100%;
  min-width: 0;
}
.addLicensepatentOrTrademark_expanded:global(.__wab_instance) {
  display: none;
}
.svg__eoCd6 {
  position: relative;
  object-fit: cover;
  width: auto;
  height: 1em;
}
.svg__wkxKc {
  position: relative;
  object-fit: cover;
  width: auto;
  height: 1em;
}
.addCertification:global(.__wab_instance) {
  max-width: 100%;
  position: relative;
  width: 100%;
  min-width: 0;
}
.addCertificationpatentOrTrademark_expanded:global(.__wab_instance) {
  display: none;
}
.svg___6E21M {
  position: relative;
  object-fit: cover;
  width: auto;
  height: 1em;
}
.svg__cdaRd {
  position: relative;
  object-fit: cover;
  width: auto;
  height: 1em;
}
