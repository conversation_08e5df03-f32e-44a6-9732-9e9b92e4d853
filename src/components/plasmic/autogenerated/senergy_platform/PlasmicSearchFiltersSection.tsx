/* eslint-disable */
/* tslint:disable */
// @ts-nocheck
/* prettier-ignore-start */

/** @jsxRuntime classic */
/** @jsx createPlasmicElementProxy */
/** @jsxFrag React.Fragment */

// This class is auto-generated by Plasmic; please do not edit!
// Plasmic Project: nZeWKcn21KijvC7eT8B3c7
// Component: o8dphkrS007N

"use client";

import * as React from "react";

import Head from "next/head";
import Link, { LinkProps } from "next/link";
import { useRouter } from "next/navigation";

import {
  Flex as Flex__,
  MultiChoiceArg,
  PlasmicDataSourceContextProvider as PlasmicDataSourceContextProvider__,
  PlasmicIcon as PlasmicIcon__,
  PlasmicImg as PlasmicImg__,
  PlasmicLink as PlasmicLink__,
  PlasmicPageGuard as PlasmicPageGuard__,
  SingleBooleanChoiceArg,
  SingleChoiceArg,
  Stack as Stack__,
  StrictProps,
  Trans as Trans__,
  classNames,
  createPlasmicElementProxy,
  deriveRenderOpts,
  ensureGlobalVariants,
  generateOnMutateForSpec,
  generateStateOnChangeProp,
  generateStateOnChangePropForCodeComponents,
  generateStateValueProp,
  get as $stateGet,
  hasVariant,
  initializeCodeComponentStates,
  initializePlasmicStates,
  makeFragment,
  omit,
  pick,
  renderPlasmicSlot,
  set as $stateSet,
  useCurrentUser,
  useDollarState,
  usePlasmicTranslator,
  useTrigger,
  wrapWithClassName
} from "@plasmicapp/react-web";
import {
  DataCtxReader as DataCtxReader__,
  useDataEnv,
  useGlobalActions
} from "@plasmicapp/host";

import SearchCoreFilterIcon from "../../SearchCoreFilterIcon"; // plasmic-import: cEGGrNaGJB_3/component

import "@plasmicapp/react-web/lib/plasmic.css";

import projectcss from "./plasmic.module.css"; // plasmic-import: nZeWKcn21KijvC7eT8B3c7/projectcss
import sty from "./PlasmicSearchFiltersSection.module.css"; // plasmic-import: o8dphkrS007N/css

createPlasmicElementProxy;

export type PlasmicSearchFiltersSection__VariantMembers = {};
export type PlasmicSearchFiltersSection__VariantsArgs = {};
type VariantPropType = keyof PlasmicSearchFiltersSection__VariantsArgs;
export const PlasmicSearchFiltersSection__VariantProps =
  new Array<VariantPropType>();

export type PlasmicSearchFiltersSection__ArgsType = {};
type ArgPropType = keyof PlasmicSearchFiltersSection__ArgsType;
export const PlasmicSearchFiltersSection__ArgProps = new Array<ArgPropType>();

export type PlasmicSearchFiltersSection__OverridesType = {
  root?: Flex__<"div">;
};

export interface DefaultSearchFiltersSectionProps {
  className?: string;
}

const $$ = {};

function useNextRouter() {
  try {
    return useRouter();
  } catch {}
  return undefined;
}

function PlasmicSearchFiltersSection__RenderFunc(props: {
  variants: PlasmicSearchFiltersSection__VariantsArgs;
  args: PlasmicSearchFiltersSection__ArgsType;
  overrides: PlasmicSearchFiltersSection__OverridesType;
  forNode?: string;
}) {
  const { variants, overrides, forNode } = props;

  const args = React.useMemo(
    () =>
      Object.assign(
        {},
        Object.fromEntries(
          Object.entries(props.args).filter(([_, v]) => v !== undefined)
        )
      ),
    [props.args]
  );

  const $props = {
    ...args,
    ...variants
  };

  const __nextRouter = useNextRouter();
  const $ctx = useDataEnv?.() || {};
  const refsRef = React.useRef({});
  const $refs = refsRef.current;

  let [$queries, setDollarQueries] = React.useState<
    Record<string, ReturnType<typeof usePlasmicDataOp>>
  >({});

  const new$Queries: Record<string, ReturnType<typeof usePlasmicDataOp>> = {};
  if (Object.keys(new$Queries).some(k => new$Queries[k] !== $queries[k])) {
    setDollarQueries(new$Queries);

    $queries = new$Queries;
  }

  return (
    <div
      data-plasmic-name={"root"}
      data-plasmic-override={overrides.root}
      data-plasmic-root={true}
      data-plasmic-for-node={forNode}
      className={classNames(
        projectcss.all,
        projectcss.root_reset,
        projectcss.plasmic_default_styles,
        projectcss.plasmic_mixins,
        projectcss.plasmic_tokens,
        sty.root
      )}
    >
      <SearchCoreFilterIcon
        className={classNames(
          "__wab_instance",
          sty.searchCoreFilterIcon__sv47I
        )}
      />

      <SearchCoreFilterIcon
        className={classNames(
          "__wab_instance",
          sty.searchCoreFilterIcon__yz2Er
        )}
      />

      <SearchCoreFilterIcon
        className={classNames(
          "__wab_instance",
          sty.searchCoreFilterIcon__jkStd
        )}
      />

      <SearchCoreFilterIcon
        className={classNames(
          "__wab_instance",
          sty.searchCoreFilterIcon__ryUyv
        )}
      />

      <SearchCoreFilterIcon
        className={classNames("__wab_instance", sty.searchCoreFilterIcon__coTr)}
      />

      <SearchCoreFilterIcon
        className={classNames(
          "__wab_instance",
          sty.searchCoreFilterIcon__f9I8W
        )}
      />

      <SearchCoreFilterIcon
        className={classNames(
          "__wab_instance",
          sty.searchCoreFilterIcon__eaPDp
        )}
      />

      <SearchCoreFilterIcon
        className={classNames("__wab_instance", sty.searchCoreFilterIcon___24B)}
      />

      <SearchCoreFilterIcon
        className={classNames(
          "__wab_instance",
          sty.searchCoreFilterIcon__xIMsq
        )}
      />

      <SearchCoreFilterIcon
        className={classNames(
          "__wab_instance",
          sty.searchCoreFilterIcon__s1Fj3
        )}
      />

      <SearchCoreFilterIcon
        className={classNames(
          "__wab_instance",
          sty.searchCoreFilterIcon__jnuYa
        )}
      />
    </div>
  ) as React.ReactElement | null;
}

const PlasmicDescendants = {
  root: ["root"]
} as const;
type NodeNameType = keyof typeof PlasmicDescendants;
type DescendantsType<T extends NodeNameType> =
  (typeof PlasmicDescendants)[T][number];
type NodeDefaultElementType = {
  root: "div";
};

type ReservedPropsType = "variants" | "args" | "overrides";
type NodeOverridesType<T extends NodeNameType> = Pick<
  PlasmicSearchFiltersSection__OverridesType,
  DescendantsType<T>
>;
type NodeComponentProps<T extends NodeNameType> =
  // Explicitly specify variants, args, and overrides as objects
  {
    variants?: PlasmicSearchFiltersSection__VariantsArgs;
    args?: PlasmicSearchFiltersSection__ArgsType;
    overrides?: NodeOverridesType<T>;
  } & Omit<PlasmicSearchFiltersSection__VariantsArgs, ReservedPropsType> & // Specify variants directly as props
    /* Specify args directly as props*/ Omit<
      PlasmicSearchFiltersSection__ArgsType,
      ReservedPropsType
    > &
    /* Specify overrides for each element directly as props*/ Omit<
      NodeOverridesType<T>,
      ReservedPropsType | VariantPropType | ArgPropType
    > &
    /* Specify props for the root element*/ Omit<
      Partial<React.ComponentProps<NodeDefaultElementType[T]>>,
      ReservedPropsType | VariantPropType | ArgPropType | DescendantsType<T>
    >;

function makeNodeComponent<NodeName extends NodeNameType>(nodeName: NodeName) {
  type PropsType = NodeComponentProps<NodeName> & { key?: React.Key };
  const func = function <T extends PropsType>(
    props: T & StrictProps<T, PropsType>
  ) {
    const { variants, args, overrides } = React.useMemo(
      () =>
        deriveRenderOpts(props, {
          name: nodeName,
          descendantNames: PlasmicDescendants[nodeName],
          internalArgPropNames: PlasmicSearchFiltersSection__ArgProps,
          internalVariantPropNames: PlasmicSearchFiltersSection__VariantProps
        }),
      [props, nodeName]
    );
    return PlasmicSearchFiltersSection__RenderFunc({
      variants,
      args,
      overrides,
      forNode: nodeName
    });
  };
  if (nodeName === "root") {
    func.displayName = "PlasmicSearchFiltersSection";
  } else {
    func.displayName = `PlasmicSearchFiltersSection.${nodeName}`;
  }
  return func;
}

export const PlasmicSearchFiltersSection = Object.assign(
  // Top-level PlasmicSearchFiltersSection renders the root element
  makeNodeComponent("root"),
  {
    // Helper components rendering sub-elements

    // Metadata about props expected for PlasmicSearchFiltersSection
    internalVariantProps: PlasmicSearchFiltersSection__VariantProps,
    internalArgProps: PlasmicSearchFiltersSection__ArgProps
  }
);

export default PlasmicSearchFiltersSection;
/* prettier-ignore-end */
