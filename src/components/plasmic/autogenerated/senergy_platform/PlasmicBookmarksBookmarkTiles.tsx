/* eslint-disable */
/* tslint:disable */
// @ts-nocheck
/* prettier-ignore-start */

/** @jsxRuntime classic */
/** @jsx createPlasmicElementProxy */
/** @jsxFrag React.Fragment */

// This class is auto-generated by Plasmic; please do not edit!
// Plasmic Project: nZeWKcn21KijvC7eT8B3c7
// Component: _UuKQc4xy7Sd

"use client";

import * as React from "react";

import Head from "next/head";
import Link, { LinkProps } from "next/link";
import { useRouter } from "next/navigation";

import {
  Flex as Flex__,
  MultiChoiceArg,
  PlasmicDataSourceContextProvider as PlasmicDataSourceContextProvider__,
  PlasmicIcon as PlasmicIcon__,
  PlasmicImg as PlasmicImg__,
  PlasmicLink as PlasmicLink__,
  PlasmicPageGuard as PlasmicPageGuard__,
  SingleBooleanChoiceArg,
  SingleChoiceArg,
  Stack as Stack__,
  StrictProps,
  Trans as Trans__,
  classNames,
  createPlasmicElementProxy,
  deriveRenderOpts,
  ensureGlobalVariants,
  generateOnMutateForSpec,
  generateStateOnChangeProp,
  generateStateOnChangePropForCodeComponents,
  generateStateValueProp,
  get as $stateGet,
  hasVariant,
  initializeCodeComponentStates,
  initializePlasmicStates,
  makeFragment,
  omit,
  pick,
  renderPlasmicSlot,
  set as $stateSet,
  useCurrentUser,
  useDollarState,
  usePlasmicTranslator,
  useTrigger,
  wrapWithClassName
} from "@plasmicapp/react-web";
import {
  DataCtxReader as DataCtxReader__,
  useDataEnv,
  useGlobalActions
} from "@plasmicapp/host";

import SearchCoreBookmarkButton from "../../SearchCoreBookmarkButton"; // plasmic-import: FSzKF-1XfOQy/component

import "@plasmicapp/react-web/lib/plasmic.css";

import projectcss from "./plasmic.module.css"; // plasmic-import: nZeWKcn21KijvC7eT8B3c7/projectcss
import sty from "./PlasmicBookmarksBookmarkTiles.module.css"; // plasmic-import: _UuKQc4xy7Sd/css

import InfoIcon from "./icons/PlasmicIcon__Info"; // plasmic-import: 3DUdjU3Or2Rw/icon

createPlasmicElementProxy;

export type PlasmicBookmarksBookmarkTiles__VariantMembers = {};
export type PlasmicBookmarksBookmarkTiles__VariantsArgs = {};
type VariantPropType = keyof PlasmicBookmarksBookmarkTiles__VariantsArgs;
export const PlasmicBookmarksBookmarkTiles__VariantProps =
  new Array<VariantPropType>();

export type PlasmicBookmarksBookmarkTiles__ArgsType = {};
type ArgPropType = keyof PlasmicBookmarksBookmarkTiles__ArgsType;
export const PlasmicBookmarksBookmarkTiles__ArgProps = new Array<ArgPropType>();

export type PlasmicBookmarksBookmarkTiles__OverridesType = {
  formattingAndShadow?: Flex__<"div">;
  headingBar?: Flex__<"div">;
  img?: Flex__<typeof PlasmicImg__>;
  userInfo?: Flex__<"div">;
  searchCoreBookmarkButton?: Flex__<typeof SearchCoreBookmarkButton>;
  skillsSection?: Flex__<"section">;
  skillsRepeat?: Flex__<"svg">;
};

export interface DefaultBookmarksBookmarkTilesProps {
  className?: string;
}

const $$ = {};

function useNextRouter() {
  try {
    return useRouter();
  } catch {}
  return undefined;
}

function PlasmicBookmarksBookmarkTiles__RenderFunc(props: {
  variants: PlasmicBookmarksBookmarkTiles__VariantsArgs;
  args: PlasmicBookmarksBookmarkTiles__ArgsType;
  overrides: PlasmicBookmarksBookmarkTiles__OverridesType;
  forNode?: string;
}) {
  const { variants, overrides, forNode } = props;

  const args = React.useMemo(
    () =>
      Object.assign(
        {},
        Object.fromEntries(
          Object.entries(props.args).filter(([_, v]) => v !== undefined)
        )
      ),
    [props.args]
  );

  const $props = {
    ...args,
    ...variants
  };

  const __nextRouter = useNextRouter();
  const $ctx = useDataEnv?.() || {};
  const refsRef = React.useRef({});
  const $refs = refsRef.current;

  let [$queries, setDollarQueries] = React.useState<
    Record<string, ReturnType<typeof usePlasmicDataOp>>
  >({});

  const new$Queries: Record<string, ReturnType<typeof usePlasmicDataOp>> = {};
  if (Object.keys(new$Queries).some(k => new$Queries[k] !== $queries[k])) {
    setDollarQueries(new$Queries);

    $queries = new$Queries;
  }

  const [isFormattingAndShadowHover, triggerFormattingAndShadowHoverProps] =
    useTrigger("useHover", {});
  const triggers = {
    hover_formattingAndShadow: isFormattingAndShadowHover
  };

  return (
    <div
      data-plasmic-name={"formattingAndShadow"}
      data-plasmic-override={overrides.formattingAndShadow}
      data-plasmic-root={true}
      data-plasmic-for-node={forNode}
      className={classNames(
        projectcss.all,
        projectcss.root_reset,
        projectcss.plasmic_default_styles,
        projectcss.plasmic_mixins,
        projectcss.plasmic_tokens,
        sty.formattingAndShadow
      )}
      data-plasmic-trigger-props={[triggerFormattingAndShadowHoverProps]}
    >
      <Stack__
        as={"div"}
        data-plasmic-name={"headingBar"}
        data-plasmic-override={overrides.headingBar}
        hasGap={true}
        className={classNames(projectcss.all, sty.headingBar)}
      >
        <PlasmicImg__
          data-plasmic-name={"img"}
          data-plasmic-override={overrides.img}
          alt={""}
          className={classNames(sty.img)}
          displayHeight={"30px"}
          displayMaxHeight={"none"}
          displayMaxWidth={"100%"}
          displayMinHeight={"0"}
          displayMinWidth={"0"}
          displayWidth={"30px"}
          loading={"lazy"}
          src={{
            src: "/plasmic/senergy_platform/images/imagePlaceholderPng.png",
            fullWidth: 800,
            fullHeight: 600,
            aspectRatio: undefined
          }}
        />

        <div
          data-plasmic-name={"userInfo"}
          data-plasmic-override={overrides.userInfo}
          className={classNames(projectcss.all, sty.userInfo)}
        >
          <div
            className={classNames(
              projectcss.all,
              projectcss.__wab_text,
              sty.text__cg2Wu
            )}
          >
            {"Firstname Lastname"}
          </div>
          <div
            className={classNames(
              projectcss.all,
              projectcss.__wab_text,
              sty.text__iow3U
            )}
          >
            {"Career Title"}
          </div>
        </div>
        <SearchCoreBookmarkButton
          data-plasmic-name={"searchCoreBookmarkButton"}
          data-plasmic-override={overrides.searchCoreBookmarkButton}
          className={classNames("__wab_instance", sty.searchCoreBookmarkButton)}
          selected={true}
        />
      </Stack__>
      <Stack__
        as={"section"}
        data-plasmic-name={"skillsSection"}
        data-plasmic-override={overrides.skillsSection}
        hasGap={true}
        className={classNames(projectcss.all, sty.skillsSection)}
      >
        {(_par => (!_par ? [] : Array.isArray(_par) ? _par : [_par]))([
          2, 3, 4
        ]).map((__plasmic_item_0, __plasmic_idx_0) => {
          const currentItem = __plasmic_item_0;
          const currentIndex = __plasmic_idx_0;
          return (
            <InfoIcon
              data-plasmic-name={"skillsRepeat"}
              data-plasmic-override={overrides.skillsRepeat}
              className={classNames(projectcss.all, sty.skillsRepeat)}
              key={currentIndex}
              role={"img"}
            />
          );
        })}
      </Stack__>
    </div>
  ) as React.ReactElement | null;
}

const PlasmicDescendants = {
  formattingAndShadow: [
    "formattingAndShadow",
    "headingBar",
    "img",
    "userInfo",
    "searchCoreBookmarkButton",
    "skillsSection",
    "skillsRepeat"
  ],
  headingBar: ["headingBar", "img", "userInfo", "searchCoreBookmarkButton"],
  img: ["img"],
  userInfo: ["userInfo"],
  searchCoreBookmarkButton: ["searchCoreBookmarkButton"],
  skillsSection: ["skillsSection", "skillsRepeat"],
  skillsRepeat: ["skillsRepeat"]
} as const;
type NodeNameType = keyof typeof PlasmicDescendants;
type DescendantsType<T extends NodeNameType> =
  (typeof PlasmicDescendants)[T][number];
type NodeDefaultElementType = {
  formattingAndShadow: "div";
  headingBar: "div";
  img: typeof PlasmicImg__;
  userInfo: "div";
  searchCoreBookmarkButton: typeof SearchCoreBookmarkButton;
  skillsSection: "section";
  skillsRepeat: "svg";
};

type ReservedPropsType = "variants" | "args" | "overrides";
type NodeOverridesType<T extends NodeNameType> = Pick<
  PlasmicBookmarksBookmarkTiles__OverridesType,
  DescendantsType<T>
>;
type NodeComponentProps<T extends NodeNameType> =
  // Explicitly specify variants, args, and overrides as objects
  {
    variants?: PlasmicBookmarksBookmarkTiles__VariantsArgs;
    args?: PlasmicBookmarksBookmarkTiles__ArgsType;
    overrides?: NodeOverridesType<T>;
  } & Omit<PlasmicBookmarksBookmarkTiles__VariantsArgs, ReservedPropsType> & // Specify variants directly as props
    /* Specify args directly as props*/ Omit<
      PlasmicBookmarksBookmarkTiles__ArgsType,
      ReservedPropsType
    > &
    /* Specify overrides for each element directly as props*/ Omit<
      NodeOverridesType<T>,
      ReservedPropsType | VariantPropType | ArgPropType
    > &
    /* Specify props for the root element*/ Omit<
      Partial<React.ComponentProps<NodeDefaultElementType[T]>>,
      ReservedPropsType | VariantPropType | ArgPropType | DescendantsType<T>
    >;

function makeNodeComponent<NodeName extends NodeNameType>(nodeName: NodeName) {
  type PropsType = NodeComponentProps<NodeName> & { key?: React.Key };
  const func = function <T extends PropsType>(
    props: T & StrictProps<T, PropsType>
  ) {
    const { variants, args, overrides } = React.useMemo(
      () =>
        deriveRenderOpts(props, {
          name: nodeName,
          descendantNames: PlasmicDescendants[nodeName],
          internalArgPropNames: PlasmicBookmarksBookmarkTiles__ArgProps,
          internalVariantPropNames: PlasmicBookmarksBookmarkTiles__VariantProps
        }),
      [props, nodeName]
    );
    return PlasmicBookmarksBookmarkTiles__RenderFunc({
      variants,
      args,
      overrides,
      forNode: nodeName
    });
  };
  if (nodeName === "formattingAndShadow") {
    func.displayName = "PlasmicBookmarksBookmarkTiles";
  } else {
    func.displayName = `PlasmicBookmarksBookmarkTiles.${nodeName}`;
  }
  return func;
}

export const PlasmicBookmarksBookmarkTiles = Object.assign(
  // Top-level PlasmicBookmarksBookmarkTiles renders the root element
  makeNodeComponent("formattingAndShadow"),
  {
    // Helper components rendering sub-elements
    headingBar: makeNodeComponent("headingBar"),
    img: makeNodeComponent("img"),
    userInfo: makeNodeComponent("userInfo"),
    searchCoreBookmarkButton: makeNodeComponent("searchCoreBookmarkButton"),
    skillsSection: makeNodeComponent("skillsSection"),
    skillsRepeat: makeNodeComponent("skillsRepeat"),

    // Metadata about props expected for PlasmicBookmarksBookmarkTiles
    internalVariantProps: PlasmicBookmarksBookmarkTiles__VariantProps,
    internalArgProps: PlasmicBookmarksBookmarkTiles__ArgProps
  }
);

export default PlasmicBookmarksBookmarkTiles;
/* prettier-ignore-end */
