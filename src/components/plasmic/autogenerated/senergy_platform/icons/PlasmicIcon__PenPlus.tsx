/* eslint-disable */
/* tslint:disable */
// @ts-nocheck
/* prettier-ignore-start */
import React from "react";
import { classNames } from "@plasmicapp/react-web";

export type PenPlusIconProps = React.ComponentProps<"svg"> & {
  title?: string;
};

export function PenPlusIcon(props: PenPlusIconProps) {
  const { className, style, title, ...restProps } = props;
  return (
    <svg
      xmlns={"http://www.w3.org/2000/svg"}
      className={classNames(
        "plasmic-default__svg",
        className,
        "icon icon-tabler icon-tabler-pencil-plus"
      )}
      viewBox={"0 0 24 24"}
      strokeWidth={"1.5"}
      stroke={"currentColor"}
      fill={"none"}
      strokeLinecap={"round"}
      strokeLinejoin={"round"}
      height={"1em"}
      style={style}
      {...restProps}
    >
      {title && <title>{title}</title>}

      <path d={"M0 0h24v24H0z"} fill={"none"} stroke={"none"}></path>

      <path
        d={
          "M4 20h4L18.5 9.5a2.828 2.828 0 10-4-4L4 16v4m9.5-13.5l4 4M16 19h6m-3-3v6"
        }
      ></path>
    </svg>
  );
}

export default PenPlusIcon;
/* prettier-ignore-end */
