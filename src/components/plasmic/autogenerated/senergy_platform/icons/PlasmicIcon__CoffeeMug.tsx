/* eslint-disable */
/* tslint:disable */
// @ts-nocheck
/* prettier-ignore-start */
import React from "react";
import { classNames } from "@plasmicapp/react-web";

export type CoffeeMugIconProps = React.ComponentProps<"svg"> & {
  title?: string;
};

export function CoffeeMugIcon(props: CoffeeMugIconProps) {
  const { className, style, title, ...restProps } = props;
  return (
    <svg
      xmlns={"http://www.w3.org/2000/svg"}
      className={classNames(
        "plasmic-default__svg",
        className,
        "icon icon-tabler icon-tabler-coffee"
      )}
      viewBox={"0 0 24 24"}
      strokeWidth={"1.5"}
      stroke={"currentColor"}
      fill={"none"}
      strokeLinecap={"round"}
      strokeLinejoin={"round"}
      height={"1em"}
      style={style}
      {...restProps}
    >
      {title && <title>{title}</title>}

      <path d={"M0 0h24v24H0z"} fill={"none"} stroke={"none"}></path>

      <path
        d={
          "M3 14c.83.642 2.077 1.017 3.5 1 1.423.017 2.67-.358 3.5-1 .83-.642 2.077-1.017 3.5-1 1.423-.017 2.67.358 3.5 1M8 3a2.4 2.4 0 00-1 2 2.4 2.4 0 001 2m4-4a2.4 2.4 0 00-1 2 2.4 2.4 0 001 2"
        }
      ></path>

      <path
        d={
          "M3 10h14v5a6 6 0 01-6 6H9a6 6 0 01-6-6v-5zm13.746 6.726a3 3 0 10.252-5.555"
        }
      ></path>
    </svg>
  );
}

export default CoffeeMugIcon;
/* prettier-ignore-end */
