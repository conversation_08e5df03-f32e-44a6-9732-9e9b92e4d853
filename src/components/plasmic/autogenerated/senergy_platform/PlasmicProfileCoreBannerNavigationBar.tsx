/* eslint-disable */
/* tslint:disable */
// @ts-nocheck
/* prettier-ignore-start */

/** @jsxRuntime classic */
/** @jsx createPlasmicElementProxy */
/** @jsxFrag React.Fragment */

// This class is auto-generated by Plasmic; please do not edit!
// Plasmic Project: nZeWKcn21KijvC7eT8B3c7
// Component: 5qBtmyZf-TyR

"use client";

import * as React from "react";

import Head from "next/head";
import Link, { LinkProps } from "next/link";
import { useRouter } from "next/navigation";

import {
  Flex as Flex__,
  MultiChoiceArg,
  PlasmicDataSourceContextProvider as PlasmicDataSourceContextProvider__,
  PlasmicIcon as PlasmicIcon__,
  PlasmicImg as PlasmicImg__,
  PlasmicLink as PlasmicLink__,
  PlasmicPageGuard as PlasmicPageGuard__,
  SingleBooleanChoiceArg,
  SingleChoiceArg,
  Stack as St<PERSON>__,
  StrictProps,
  Trans as Trans__,
  classNames,
  createPlasmicElementProxy,
  deriveRenderOpts,
  ensureGlobalVariants,
  generateOnMutateForSpec,
  generateStateOnChangeProp,
  generateStateOnChangePropForCodeComponents,
  generateStateValueProp,
  get as $stateGet,
  hasVariant,
  initializeCodeComponentStates,
  initializePlasmicStates,
  makeFragment,
  omit,
  pick,
  renderPlasmicSlot,
  set as $stateSet,
  useCurrentUser,
  useDollarState,
  usePlasmicTranslator,
  useTrigger,
  wrapWithClassName
} from "@plasmicapp/react-web";
import {
  DataCtxReader as DataCtxReader__,
  useDataEnv,
  useGlobalActions
} from "@plasmicapp/host";

import ProfileCoreButtonForProfileNavigation from "../../ProfileCoreButtonForProfileNavigation"; // plasmic-import: 9HLEjheuNhYo/component

import "@plasmicapp/react-web/lib/plasmic.css";

import projectcss from "./plasmic.module.css"; // plasmic-import: nZeWKcn21KijvC7eT8B3c7/projectcss
import sty from "./PlasmicProfileCoreBannerNavigationBar.module.css"; // plasmic-import: 5qBtmyZf-TyR/css

createPlasmicElementProxy;

export type PlasmicProfileCoreBannerNavigationBar__VariantMembers = {
  collapsedHeader: "collapsedHeader";
  selected: "overview" | "myWork" | "about";
};
export type PlasmicProfileCoreBannerNavigationBar__VariantsArgs = {
  collapsedHeader?: SingleBooleanChoiceArg<"collapsedHeader">;
  selected?: SingleChoiceArg<"overview" | "myWork" | "about">;
};
type VariantPropType =
  keyof PlasmicProfileCoreBannerNavigationBar__VariantsArgs;
export const PlasmicProfileCoreBannerNavigationBar__VariantProps =
  new Array<VariantPropType>("collapsedHeader", "selected");

export type PlasmicProfileCoreBannerNavigationBar__ArgsType = {
  onSelectedChange?: (val: any) => void;
};
type ArgPropType = keyof PlasmicProfileCoreBannerNavigationBar__ArgsType;
export const PlasmicProfileCoreBannerNavigationBar__ArgProps =
  new Array<ArgPropType>("onSelectedChange");

export type PlasmicProfileCoreBannerNavigationBar__OverridesType = {
  profileNavContainer?: Flex__<"div">;
  overviewButton?: Flex__<typeof ProfileCoreButtonForProfileNavigation>;
  myWorkButton?: Flex__<typeof ProfileCoreButtonForProfileNavigation>;
  aboutMeButton?: Flex__<typeof ProfileCoreButtonForProfileNavigation>;
};

export interface DefaultProfileCoreBannerNavigationBarProps {
  onSelectedChange?: (val: any) => void;
  collapsedHeader?: SingleBooleanChoiceArg<"collapsedHeader">;
  selected?: SingleChoiceArg<"overview" | "myWork" | "about">;
  className?: string;
}

const $$ = {};

function useNextRouter() {
  try {
    return useRouter();
  } catch {}
  return undefined;
}

function PlasmicProfileCoreBannerNavigationBar__RenderFunc(props: {
  variants: PlasmicProfileCoreBannerNavigationBar__VariantsArgs;
  args: PlasmicProfileCoreBannerNavigationBar__ArgsType;
  overrides: PlasmicProfileCoreBannerNavigationBar__OverridesType;
  forNode?: string;
}) {
  const { variants, overrides, forNode } = props;

  const args = React.useMemo(
    () =>
      Object.assign(
        {},
        Object.fromEntries(
          Object.entries(props.args).filter(([_, v]) => v !== undefined)
        )
      ),
    [props.args]
  );

  const $props = {
    ...args,
    ...variants
  };

  const __nextRouter = useNextRouter();
  const $ctx = useDataEnv?.() || {};
  const refsRef = React.useRef({});
  const $refs = refsRef.current;

  let [$queries, setDollarQueries] = React.useState<
    Record<string, ReturnType<typeof usePlasmicDataOp>>
  >({});
  const stateSpecs: Parameters<typeof useDollarState>[0] = React.useMemo(
    () => [
      {
        path: "collapsedHeader",
        type: "private",
        variableType: "variant",
        initFunc: ({ $props, $state, $queries, $ctx }) => $props.collapsedHeader
      },
      {
        path: "selected",
        type: "writable",
        variableType: "variant",

        valueProp: "selected",
        onChangeProp: "onSelectedChange"
      }
    ],
    [$props, $ctx, $refs]
  );
  const $state = useDollarState(stateSpecs, {
    $props,
    $ctx,
    $queries: $queries,
    $refs
  });

  const new$Queries: Record<string, ReturnType<typeof usePlasmicDataOp>> = {};
  if (Object.keys(new$Queries).some(k => new$Queries[k] !== $queries[k])) {
    setDollarQueries(new$Queries);

    $queries = new$Queries;
  }

  return (
    <div
      data-plasmic-name={"profileNavContainer"}
      data-plasmic-override={overrides.profileNavContainer}
      data-plasmic-root={true}
      data-plasmic-for-node={forNode}
      className={classNames(
        projectcss.all,
        projectcss.root_reset,
        projectcss.plasmic_default_styles,
        projectcss.plasmic_mixins,
        projectcss.plasmic_tokens,
        sty.profileNavContainer,
        {
          [sty.profileNavContainercollapsedHeader]: hasVariant(
            $state,
            "collapsedHeader",
            "collapsedHeader"
          )
        }
      )}
    >
      <ProfileCoreButtonForProfileNavigation
        data-plasmic-name={"overviewButton"}
        data-plasmic-override={overrides.overviewButton}
        className={classNames("__wab_instance", sty.overviewButton, {
          [sty.overviewButtoncollapsedHeader]: hasVariant(
            $state,
            "collapsedHeader",
            "collapsedHeader"
          ),
          [sty.overviewButtoncollapsedHeader_selected_overview]:
            hasVariant($state, "collapsedHeader", "collapsedHeader") &&
            hasVariant($state, "selected", "overview"),
          [sty.overviewButtonselected_about]: hasVariant(
            $state,
            "selected",
            "about"
          ),
          [sty.overviewButtonselected_overview]: hasVariant(
            $state,
            "selected",
            "overview"
          )
        })}
        endIcon={
          <svg
            className={classNames(projectcss.all, sty.svg__yQjoi)}
            role={"img"}
          />
        }
        onClick={async event => {
          const $steps = {};

          $steps["updateVariant"] = true
            ? (() => {
                const actionArgs = {
                  vgroup: "selected",
                  operation: 0,
                  value: "overview"
                };
                return (({ vgroup, value }) => {
                  if (typeof value === "string") {
                    value = [value];
                  }

                  $stateSet($state, vgroup, value);
                  return value;
                })?.apply(null, [actionArgs]);
              })()
            : undefined;
          if (
            $steps["updateVariant"] != null &&
            typeof $steps["updateVariant"] === "object" &&
            typeof $steps["updateVariant"].then === "function"
          ) {
            $steps["updateVariant"] = await $steps["updateVariant"];
          }
        }}
        selected={hasVariant($state, "selected", "overview") ? true : undefined}
        startIcon={
          <svg
            className={classNames(projectcss.all, sty.svg__ev7Pu)}
            role={"img"}
          />
        }
        styling={
          hasVariant($state, "collapsedHeader", "collapsedHeader")
            ? ["typewriter"]
            : ["nittiWColor"]
        }
      >
        {"Overview"}
      </ProfileCoreButtonForProfileNavigation>
      <ProfileCoreButtonForProfileNavigation
        data-plasmic-name={"myWorkButton"}
        data-plasmic-override={overrides.myWorkButton}
        className={classNames("__wab_instance", sty.myWorkButton, {
          [sty.myWorkButtoncollapsedHeader]: hasVariant(
            $state,
            "collapsedHeader",
            "collapsedHeader"
          ),
          [sty.myWorkButtonselected_myWork]: hasVariant(
            $state,
            "selected",
            "myWork"
          ),
          [sty.myWorkButtonselected_overview]: hasVariant(
            $state,
            "selected",
            "overview"
          )
        })}
        endIcon={
          <svg
            className={classNames(projectcss.all, sty.svg___8InK)}
            role={"img"}
          />
        }
        onClick={async event => {
          const $steps = {};

          $steps["updateSelected"] = true
            ? (() => {
                const actionArgs = {
                  vgroup: "selected",
                  operation: 0,
                  value: "myWork"
                };
                return (({ vgroup, value }) => {
                  if (typeof value === "string") {
                    value = [value];
                  }

                  $stateSet($state, vgroup, value);
                  return value;
                })?.apply(null, [actionArgs]);
              })()
            : undefined;
          if (
            $steps["updateSelected"] != null &&
            typeof $steps["updateSelected"] === "object" &&
            typeof $steps["updateSelected"].then === "function"
          ) {
            $steps["updateSelected"] = await $steps["updateSelected"];
          }
        }}
        selected={hasVariant($state, "selected", "myWork") ? true : undefined}
        startIcon={
          <svg
            className={classNames(projectcss.all, sty.svg___4OXku)}
            role={"img"}
          />
        }
        styling={
          hasVariant($state, "collapsedHeader", "collapsedHeader")
            ? ["typewriter"]
            : ["nittiWColor"]
        }
      >
        {"My Work"}
      </ProfileCoreButtonForProfileNavigation>
      <ProfileCoreButtonForProfileNavigation
        data-plasmic-name={"aboutMeButton"}
        data-plasmic-override={overrides.aboutMeButton}
        className={classNames("__wab_instance", sty.aboutMeButton, {
          [sty.aboutMeButtoncollapsedHeader]: hasVariant(
            $state,
            "collapsedHeader",
            "collapsedHeader"
          ),
          [sty.aboutMeButtoncollapsedHeader_selected_overview]:
            hasVariant($state, "collapsedHeader", "collapsedHeader") &&
            hasVariant($state, "selected", "overview"),
          [sty.aboutMeButtonselected_about]: hasVariant(
            $state,
            "selected",
            "about"
          ),
          [sty.aboutMeButtonselected_overview]: hasVariant(
            $state,
            "selected",
            "overview"
          )
        })}
        endIcon={
          <svg
            className={classNames(projectcss.all, sty.svg__gpDjr)}
            role={"img"}
          />
        }
        onClick={async event => {
          const $steps = {};

          $steps["updateSelected"] = true
            ? (() => {
                const actionArgs = {
                  vgroup: "selected",
                  operation: 0,
                  value: "about"
                };
                return (({ vgroup, value }) => {
                  if (typeof value === "string") {
                    value = [value];
                  }

                  $stateSet($state, vgroup, value);
                  return value;
                })?.apply(null, [actionArgs]);
              })()
            : undefined;
          if (
            $steps["updateSelected"] != null &&
            typeof $steps["updateSelected"] === "object" &&
            typeof $steps["updateSelected"].then === "function"
          ) {
            $steps["updateSelected"] = await $steps["updateSelected"];
          }
        }}
        selected={hasVariant($state, "selected", "about") ? true : undefined}
        startIcon={
          <svg
            className={classNames(projectcss.all, sty.svg__kYkg3)}
            role={"img"}
          />
        }
        styling={
          hasVariant($state, "collapsedHeader", "collapsedHeader")
            ? ["typewriter"]
            : ["nittiWColor"]
        }
      >
        {"About Me"}
      </ProfileCoreButtonForProfileNavigation>
    </div>
  ) as React.ReactElement | null;
}

const PlasmicDescendants = {
  profileNavContainer: [
    "profileNavContainer",
    "overviewButton",
    "myWorkButton",
    "aboutMeButton"
  ],
  overviewButton: ["overviewButton"],
  myWorkButton: ["myWorkButton"],
  aboutMeButton: ["aboutMeButton"]
} as const;
type NodeNameType = keyof typeof PlasmicDescendants;
type DescendantsType<T extends NodeNameType> =
  (typeof PlasmicDescendants)[T][number];
type NodeDefaultElementType = {
  profileNavContainer: "div";
  overviewButton: typeof ProfileCoreButtonForProfileNavigation;
  myWorkButton: typeof ProfileCoreButtonForProfileNavigation;
  aboutMeButton: typeof ProfileCoreButtonForProfileNavigation;
};

type ReservedPropsType = "variants" | "args" | "overrides";
type NodeOverridesType<T extends NodeNameType> = Pick<
  PlasmicProfileCoreBannerNavigationBar__OverridesType,
  DescendantsType<T>
>;
type NodeComponentProps<T extends NodeNameType> =
  // Explicitly specify variants, args, and overrides as objects
  {
    variants?: PlasmicProfileCoreBannerNavigationBar__VariantsArgs;
    args?: PlasmicProfileCoreBannerNavigationBar__ArgsType;
    overrides?: NodeOverridesType<T>;
  } & Omit< // Specify variants directly as props
    PlasmicProfileCoreBannerNavigationBar__VariantsArgs,
    ReservedPropsType
  > &
    /* Specify args directly as props*/ Omit<
      PlasmicProfileCoreBannerNavigationBar__ArgsType,
      ReservedPropsType
    > &
    /* Specify overrides for each element directly as props*/ Omit<
      NodeOverridesType<T>,
      ReservedPropsType | VariantPropType | ArgPropType
    > &
    /* Specify props for the root element*/ Omit<
      Partial<React.ComponentProps<NodeDefaultElementType[T]>>,
      ReservedPropsType | VariantPropType | ArgPropType | DescendantsType<T>
    >;

function makeNodeComponent<NodeName extends NodeNameType>(nodeName: NodeName) {
  type PropsType = NodeComponentProps<NodeName> & { key?: React.Key };
  const func = function <T extends PropsType>(
    props: T & StrictProps<T, PropsType>
  ) {
    const { variants, args, overrides } = React.useMemo(
      () =>
        deriveRenderOpts(props, {
          name: nodeName,
          descendantNames: PlasmicDescendants[nodeName],
          internalArgPropNames: PlasmicProfileCoreBannerNavigationBar__ArgProps,
          internalVariantPropNames:
            PlasmicProfileCoreBannerNavigationBar__VariantProps
        }),
      [props, nodeName]
    );
    return PlasmicProfileCoreBannerNavigationBar__RenderFunc({
      variants,
      args,
      overrides,
      forNode: nodeName
    });
  };
  if (nodeName === "profileNavContainer") {
    func.displayName = "PlasmicProfileCoreBannerNavigationBar";
  } else {
    func.displayName = `PlasmicProfileCoreBannerNavigationBar.${nodeName}`;
  }
  return func;
}

export const PlasmicProfileCoreBannerNavigationBar = Object.assign(
  // Top-level PlasmicProfileCoreBannerNavigationBar renders the root element
  makeNodeComponent("profileNavContainer"),
  {
    // Helper components rendering sub-elements
    overviewButton: makeNodeComponent("overviewButton"),
    myWorkButton: makeNodeComponent("myWorkButton"),
    aboutMeButton: makeNodeComponent("aboutMeButton"),

    // Metadata about props expected for PlasmicProfileCoreBannerNavigationBar
    internalVariantProps: PlasmicProfileCoreBannerNavigationBar__VariantProps,
    internalArgProps: PlasmicProfileCoreBannerNavigationBar__ArgProps
  }
);

export default PlasmicProfileCoreBannerNavigationBar;
/* prettier-ignore-end */
