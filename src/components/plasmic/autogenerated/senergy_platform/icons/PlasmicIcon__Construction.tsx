/* eslint-disable */
/* tslint:disable */
// @ts-nocheck
/* prettier-ignore-start */
import React from "react";
import { classNames } from "@plasmicapp/react-web";

export type ConstructionIconProps = React.ComponentProps<"svg"> & {
  title?: string;
};

export function ConstructionIcon(props: ConstructionIconProps) {
  const { className, style, title, ...restProps } = props;
  return (
    <svg
      xmlns={"http://www.w3.org/2000/svg"}
      className={classNames(
        "plasmic-default__svg",
        className,
        "icon icon-tabler icon-tabler-backhoe"
      )}
      viewBox={"0 0 24 24"}
      strokeWidth={"1.5"}
      stroke={"currentColor"}
      fill={"none"}
      strokeLinecap={"round"}
      strokeLinejoin={"round"}
      height={"1em"}
      style={style}
      {...restProps}
    >
      {title && <title>{title}</title>}

      <path d={"M0 0h24v24H0z"} fill={"none"} stroke={"none"}></path>

      <path
        d={
          "M2 17a2 2 0 104 0 2 2 0 10-4 0m9 0a2 2 0 104 0 2 2 0 10-4 0m2 2H4m0-4h9"
        }
      ></path>

      <path d={"M8 12V7h2a3 3 0 013 3v5"}></path>

      <path
        d={
          "M5 15v-2a1 1 0 011-1h7m8.12-2.12L18 5l-5 5m8.12-.12A3 3 0 0119 15a3 3 0 01-2.12-.88l4.24-4.24z"
        }
      ></path>
    </svg>
  );
}

export default ConstructionIcon;
/* prettier-ignore-end */
