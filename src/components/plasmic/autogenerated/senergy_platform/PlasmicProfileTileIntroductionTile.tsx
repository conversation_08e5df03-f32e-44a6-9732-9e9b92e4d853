/* eslint-disable */
/* tslint:disable */
// @ts-nocheck
/* prettier-ignore-start */

/** @jsxRuntime classic */
/** @jsx createPlasmicElementProxy */
/** @jsxFrag React.Fragment */

// This class is auto-generated by Plasmic; please do not edit!
// Plasmic Project: nZeWKcn21KijvC7eT8B3c7
// Component: ZpGWQx6DTU7R

"use client";

import * as React from "react";

import Head from "next/head";
import Link, { LinkProps } from "next/link";
import { useRouter } from "next/navigation";

import {
  Flex as Flex__,
  MultiChoiceArg,
  PlasmicDataSourceContextProvider as PlasmicDataSourceContextProvider__,
  PlasmicIcon as PlasmicIcon__,
  PlasmicImg as PlasmicImg__,
  PlasmicLink as PlasmicLink__,
  PlasmicPageGuard as PlasmicPageGuard__,
  SingleBooleanChoiceArg,
  SingleChoiceArg,
  Stack as Stack__,
  StrictProps,
  Trans as Trans__,
  classNames,
  createPlasmicElementProxy,
  deriveRenderOpts,
  ensureGlobalVariants,
  generateOnMutateForSpec,
  generateStateOnChangeProp,
  generateStateOnChangePropForCodeComponents,
  generateStateValueProp,
  get as $stateGet,
  hasVariant,
  initializeCodeComponentStates,
  initializePlasmicStates,
  makeFragment,
  omit,
  pick,
  renderPlasmicSlot,
  set as $stateSet,
  useCurrentUser,
  useDollarState,
  usePlasmicTranslator,
  useTrigger,
  wrapWithClassName
} from "@plasmicapp/react-web";
import {
  DataCtxReader as DataCtxReader__,
  useDataEnv,
  useGlobalActions
} from "@plasmicapp/host";

import SubcomponentUploadButton from "../../SubcomponentUploadButton"; // plasmic-import: q4dIf6ELFdYO/component

import { useScreenVariants as useScreenVariants_4Hrhi5G5ANwQ } from "./PlasmicGlobalVariant__FormattingBreakPoint"; // plasmic-import: 4Hrhi5G5aNwQ/globalVariant

import "@plasmicapp/react-web/lib/plasmic.css";

import projectcss from "./plasmic.module.css"; // plasmic-import: nZeWKcn21KijvC7eT8B3c7/projectcss
import sty from "./PlasmicProfileTileIntroductionTile.module.css"; // plasmic-import: ZpGWQx6DTU7R/css

createPlasmicElementProxy;

export type PlasmicProfileTileIntroductionTile__VariantMembers = {
  overview: "introOverview";
  editable: "editable";
  introOnboarding: "introComposition" | "imageUpload" | "review";
};
export type PlasmicProfileTileIntroductionTile__VariantsArgs = {
  overview?: SingleChoiceArg<"introOverview">;
  editable?: SingleBooleanChoiceArg<"editable">;
  introOnboarding?: SingleChoiceArg<
    "introComposition" | "imageUpload" | "review"
  >;
};
type VariantPropType = keyof PlasmicProfileTileIntroductionTile__VariantsArgs;
export const PlasmicProfileTileIntroductionTile__VariantProps =
  new Array<VariantPropType>("overview", "editable", "introOnboarding");

export type PlasmicProfileTileIntroductionTile__ArgsType = {
  introImage?: string;
  imageUploadButton?: string;
};
type ArgPropType = keyof PlasmicProfileTileIntroductionTile__ArgsType;
export const PlasmicProfileTileIntroductionTile__ArgProps =
  new Array<ArgPropType>("introImage", "imageUploadButton");

export type PlasmicProfileTileIntroductionTile__OverridesType = {
  introductionFormatting?: Flex__<"div">;
  introSummaryDisplay?: Flex__<"div">;
  imageContainerWithUpload?: Flex__<"section">;
  introductionImageFile?: Flex__<typeof PlasmicImg__>;
  subcomponentUploadButton?: Flex__<typeof SubcomponentUploadButton>;
};

export interface DefaultProfileTileIntroductionTileProps {
  introImage?: string;
  imageUploadButton?: string;
  overview?: SingleChoiceArg<"introOverview">;
  editable?: SingleBooleanChoiceArg<"editable">;
  introOnboarding?: SingleChoiceArg<
    "introComposition" | "imageUpload" | "review"
  >;
  className?: string;
}

const $$ = {};

function useNextRouter() {
  try {
    return useRouter();
  } catch {}
  return undefined;
}

function PlasmicProfileTileIntroductionTile__RenderFunc(props: {
  variants: PlasmicProfileTileIntroductionTile__VariantsArgs;
  args: PlasmicProfileTileIntroductionTile__ArgsType;
  overrides: PlasmicProfileTileIntroductionTile__OverridesType;
  forNode?: string;
}) {
  const { variants, overrides, forNode } = props;

  const args = React.useMemo(
    () =>
      Object.assign(
        {},
        Object.fromEntries(
          Object.entries(props.args).filter(([_, v]) => v !== undefined)
        )
      ),
    [props.args]
  );

  const $props = {
    ...args,
    ...variants
  };

  const __nextRouter = useNextRouter();
  const $ctx = useDataEnv?.() || {};
  const refsRef = React.useRef({});
  const $refs = refsRef.current;

  let [$queries, setDollarQueries] = React.useState<
    Record<string, ReturnType<typeof usePlasmicDataOp>>
  >({});
  const stateSpecs: Parameters<typeof useDollarState>[0] = React.useMemo(
    () => [
      {
        path: "overview",
        type: "private",
        variableType: "variant",
        initFunc: ({ $props, $state, $queries, $ctx }) => $props.overview
      },
      {
        path: "editable",
        type: "private",
        variableType: "variant",
        initFunc: ({ $props, $state, $queries, $ctx }) => $props.editable
      },
      {
        path: "introOnboarding",
        type: "private",
        variableType: "variant",
        initFunc: ({ $props, $state, $queries, $ctx }) => $props.introOnboarding
      }
    ],
    [$props, $ctx, $refs]
  );
  const $state = useDollarState(stateSpecs, {
    $props,
    $ctx,
    $queries: $queries,
    $refs
  });

  const new$Queries: Record<string, ReturnType<typeof usePlasmicDataOp>> = {};
  if (Object.keys(new$Queries).some(k => new$Queries[k] !== $queries[k])) {
    setDollarQueries(new$Queries);

    $queries = new$Queries;
  }

  const globalVariants = ensureGlobalVariants({
    formattingBreakPoint: useScreenVariants_4Hrhi5G5ANwQ()
  });

  return (
    <Stack__
      as={"div"}
      data-plasmic-name={"introductionFormatting"}
      data-plasmic-override={overrides.introductionFormatting}
      data-plasmic-root={true}
      data-plasmic-for-node={forNode}
      hasGap={true}
      className={classNames(
        projectcss.all,
        projectcss.root_reset,
        projectcss.plasmic_default_styles,
        projectcss.plasmic_mixins,
        projectcss.plasmic_tokens,
        sty.introductionFormatting,
        {
          [sty.introductionFormattingeditable]: hasVariant(
            $state,
            "editable",
            "editable"
          ),
          [sty.introductionFormattingintroOnboarding_imageUpload]: hasVariant(
            $state,
            "introOnboarding",
            "imageUpload"
          ),
          [sty.introductionFormattingintroOnboarding_introComposition]:
            hasVariant($state, "introOnboarding", "introComposition"),
          [sty.introductionFormattingoverview_introOverview]: hasVariant(
            $state,
            "overview",
            "introOverview"
          )
        }
      )}
    >
      <div
        data-plasmic-name={"introSummaryDisplay"}
        data-plasmic-override={overrides.introSummaryDisplay}
        className={classNames(
          projectcss.all,
          projectcss.__wab_text,
          sty.introSummaryDisplay,
          {
            [sty.introSummaryDisplayeditable]: hasVariant(
              $state,
              "editable",
              "editable"
            ),
            [sty.introSummaryDisplayintroOnboarding_imageUpload]: hasVariant(
              $state,
              "introOnboarding",
              "imageUpload"
            ),
            [sty.introSummaryDisplayintroOnboarding_introComposition]:
              hasVariant($state, "introOnboarding", "introComposition"),
            [sty.introSummaryDisplayoverview_introOverview]: hasVariant(
              $state,
              "overview",
              "introOverview"
            )
          }
        )}
      >
        {hasVariant($state, "introOnboarding", "introComposition") ? (
          "In consectetur sunt laborum duis sit ex nostrud deserunt aliqua irure non eiusmod consectetur voluptate. Id nisi qui aute sunt consequat velit occaecat veniam voluptate aute ullamco officia ex quis. Ipsum eiusmod nulla eiusmod anim non laboris duis officia labore sit quis mollit. Laboris mollit veniam magna magna et sint ea aute laboris minim anim velit duis amet. Qui irure est elit minim velit dolor ea proident. Adipisicing exercitation velit excepteur nostrud nulla exercitation laborum excepteur ex excepteur mollit veniam veniam ea. In proident fugiat reprehenderit.In consectetur sunt laborum duis sit ex nostrud deserunt aliqua irure non eiusmod consectetur voluptate. Id nisi qui aute sunt consequat velit occaecat veniam voluptate aute ullamco officia ex quis. Ipsum eiusmod nulla eiusmod anim non laboris duis officia labore sit quis mollit. Laboris mollit veniam magna magna et sint ea aute laboris minim anim velit duis amet. Qui irure est elit minim velit dolor ea proident. Adipisicing exercitation velit excepteur nostrud nulla exercitation laborum excepteur ex excepteur mollit veniam veniam ea. In proident fugiat reprehenderit.In consectetur sunt laborum duis sit ex nostrud deserunt aliqua irure non eiusmod consectetur voluptate. Id nisi qui aute sunt consequat velit occaecat veniam "
        ) : hasVariant($state, "overview", "introOverview") ? (
          <React.Fragment>
            {(() => {
              try {
                return $props.introduction.slice(0, 500) + "...";
              } catch (e) {
                if (
                  e instanceof TypeError ||
                  e?.plasmicType === "PlasmicUndefinedDataError"
                ) {
                  return "Introduction Summary In consectetur sunt laborum duis sit ex nostrud deserunt aliqua irure non eiusmod consectetur voluptate. Id nisi qui aute sunt consequat velit occaecat veniam voluptate aute ullamco officia ex quis. Commodo dolor ullamco eu cupidatat ullamco.  Commodo dolor ullamco eu cupidatat ullamco.Commodo dolor ullamco eu cupidatat ullamco.Commodo dolor ullamco eu cupidatat ullamco.Commodo dolor ullamco eu cupidatat ullamco.Commodo dolor ullamco eu cupidatat ullamco.Commodo dolor ullamco eu cupidatat ullacom, lorem ipsum. ";
                }
                throw e;
              }
            })()}
          </React.Fragment>
        ) : (
          "In consectetur sunt laborum duis sit ex nostrud deserunt aliqua irure non eiusmod consectetur voluptate. Id nisi qui aute sunt consequat velit occaecat veniam voluptate aute ullamco officia ex quis. Ipsum eiusmod nulla eiusmod anim non laboris duis officia labore sit quis mollit. Laboris mollit veniam magna magna et sint ea aute laboris minim anim velit duis amet. Qui irure est elit minim velit dolor ea proident. Adipisicing exercitation velit excepteur nostrud nulla exercitation laborum excepteur ex excepteur mollit veniam veniam ea. In proident fugiat reprehenderit.In consectetur sunt laborum duis sit ex nostrud deserunt aliqua irure non eiusmod consectetur voluptate. Id nisi qui aute sunt consequat velit occaecat veniam voluptate aute ullamco officia ex quis. Ipsum eiusmod nulla eiusmod anim non laboris duis officia labore sit quis mollit. Laboris mollit veniam magna magna et sint ea aute laboris minim anim velit duis amet. Qui irure est elit minim velit dolor ea proident. Adipisicing exercitation velit excepteur nostrud nulla exercitation laborum excepteur ex excepteur mollit veniam veniam ea. In proident fugiat reprehenderit.In consectetur sunt laborum duis sit ex nostrud deserunt aliqua irure non eiusmod consectetur voluptate. Id nisi qui aute sunt consequat velit occaecat veniam "
        )}
      </div>
      <section
        data-plasmic-name={"imageContainerWithUpload"}
        data-plasmic-override={overrides.imageContainerWithUpload}
        className={classNames(projectcss.all, sty.imageContainerWithUpload, {
          [sty.imageContainerWithUploadeditable]: hasVariant(
            $state,
            "editable",
            "editable"
          ),
          [sty.imageContainerWithUploadintroOnboarding_introComposition]:
            hasVariant($state, "introOnboarding", "introComposition")
        })}
      >
        <PlasmicImg__
          data-plasmic-name={"introductionImageFile"}
          data-plasmic-override={overrides.introductionImageFile}
          alt={""}
          className={classNames(sty.introductionImageFile, {
            [sty.introductionImageFileeditable]: hasVariant(
              $state,
              "editable",
              "editable"
            ),
            [sty.introductionImageFileintroOnboarding_imageUpload]: hasVariant(
              $state,
              "introOnboarding",
              "imageUpload"
            ),
            [sty.introductionImageFileintroOnboarding_introComposition]:
              hasVariant($state, "introOnboarding", "introComposition"),
            [sty.introductionImageFileoverview_introOverview]: hasVariant(
              $state,
              "overview",
              "introOverview"
            )
          })}
          displayHeight={"400px"}
          displayMaxHeight={"none"}
          displayMaxWidth={"none"}
          displayMinHeight={"0"}
          displayMinWidth={"0"}
          displayWidth={"350px"}
          loading={"lazy"}
          src={{
            src: "/plasmic/senergy_platform/images/imagePlaceholderPng.png",
            fullWidth: 800,
            fullHeight: 600,
            aspectRatio: undefined
          }}
        />

        <SubcomponentUploadButton
          data-plasmic-name={"subcomponentUploadButton"}
          data-plasmic-override={overrides.subcomponentUploadButton}
          className={classNames(
            "__wab_instance",
            sty.subcomponentUploadButton,
            {
              [sty.subcomponentUploadButtoneditable]: hasVariant(
                $state,
                "editable",
                "editable"
              ),
              [sty.subcomponentUploadButtonintroOnboarding_imageUpload]:
                hasVariant($state, "introOnboarding", "imageUpload"),
              [sty.subcomponentUploadButtonintroOnboarding_introComposition]:
                hasVariant($state, "introOnboarding", "introComposition")
            }
          )}
          editable={
            hasVariant($state, "introOnboarding", "imageUpload")
              ? "photoCover"
              : hasVariant($state, "introOnboarding", "introComposition")
              ? "photoCover"
              : hasVariant($state, "editable", "editable")
              ? "photoCover"
              : undefined
          }
        />
      </section>
    </Stack__>
  ) as React.ReactElement | null;
}

const PlasmicDescendants = {
  introductionFormatting: [
    "introductionFormatting",
    "introSummaryDisplay",
    "imageContainerWithUpload",
    "introductionImageFile",
    "subcomponentUploadButton"
  ],
  introSummaryDisplay: ["introSummaryDisplay"],
  imageContainerWithUpload: [
    "imageContainerWithUpload",
    "introductionImageFile",
    "subcomponentUploadButton"
  ],
  introductionImageFile: ["introductionImageFile"],
  subcomponentUploadButton: ["subcomponentUploadButton"]
} as const;
type NodeNameType = keyof typeof PlasmicDescendants;
type DescendantsType<T extends NodeNameType> =
  (typeof PlasmicDescendants)[T][number];
type NodeDefaultElementType = {
  introductionFormatting: "div";
  introSummaryDisplay: "div";
  imageContainerWithUpload: "section";
  introductionImageFile: typeof PlasmicImg__;
  subcomponentUploadButton: typeof SubcomponentUploadButton;
};

type ReservedPropsType = "variants" | "args" | "overrides";
type NodeOverridesType<T extends NodeNameType> = Pick<
  PlasmicProfileTileIntroductionTile__OverridesType,
  DescendantsType<T>
>;
type NodeComponentProps<T extends NodeNameType> =
  // Explicitly specify variants, args, and overrides as objects
  {
    variants?: PlasmicProfileTileIntroductionTile__VariantsArgs;
    args?: PlasmicProfileTileIntroductionTile__ArgsType;
    overrides?: NodeOverridesType<T>;
  } & Omit< // Specify variants directly as props
    PlasmicProfileTileIntroductionTile__VariantsArgs,
    ReservedPropsType
  > &
    /* Specify args directly as props*/ Omit<
      PlasmicProfileTileIntroductionTile__ArgsType,
      ReservedPropsType
    > &
    /* Specify overrides for each element directly as props*/ Omit<
      NodeOverridesType<T>,
      ReservedPropsType | VariantPropType | ArgPropType
    > &
    /* Specify props for the root element*/ Omit<
      Partial<React.ComponentProps<NodeDefaultElementType[T]>>,
      ReservedPropsType | VariantPropType | ArgPropType | DescendantsType<T>
    >;

function makeNodeComponent<NodeName extends NodeNameType>(nodeName: NodeName) {
  type PropsType = NodeComponentProps<NodeName> & { key?: React.Key };
  const func = function <T extends PropsType>(
    props: T & StrictProps<T, PropsType>
  ) {
    const { variants, args, overrides } = React.useMemo(
      () =>
        deriveRenderOpts(props, {
          name: nodeName,
          descendantNames: PlasmicDescendants[nodeName],
          internalArgPropNames: PlasmicProfileTileIntroductionTile__ArgProps,
          internalVariantPropNames:
            PlasmicProfileTileIntroductionTile__VariantProps
        }),
      [props, nodeName]
    );
    return PlasmicProfileTileIntroductionTile__RenderFunc({
      variants,
      args,
      overrides,
      forNode: nodeName
    });
  };
  if (nodeName === "introductionFormatting") {
    func.displayName = "PlasmicProfileTileIntroductionTile";
  } else {
    func.displayName = `PlasmicProfileTileIntroductionTile.${nodeName}`;
  }
  return func;
}

export const PlasmicProfileTileIntroductionTile = Object.assign(
  // Top-level PlasmicProfileTileIntroductionTile renders the root element
  makeNodeComponent("introductionFormatting"),
  {
    // Helper components rendering sub-elements
    introSummaryDisplay: makeNodeComponent("introSummaryDisplay"),
    imageContainerWithUpload: makeNodeComponent("imageContainerWithUpload"),
    introductionImageFile: makeNodeComponent("introductionImageFile"),
    subcomponentUploadButton: makeNodeComponent("subcomponentUploadButton"),

    // Metadata about props expected for PlasmicProfileTileIntroductionTile
    internalVariantProps: PlasmicProfileTileIntroductionTile__VariantProps,
    internalArgProps: PlasmicProfileTileIntroductionTile__ArgProps
  }
);

export default PlasmicProfileTileIntroductionTile;
/* prettier-ignore-end */
