/* eslint-disable */
/* tslint:disable */
// @ts-nocheck
/* prettier-ignore-start */

/** @jsxRuntime classic */
/** @jsx createPlasmicElementProxy */
/** @jsxFrag React.Fragment */

// This class is auto-generated by Plasmic; please do not edit!
// Plasmic Project: nZeWKcn21KijvC7eT8B3c7
// Component: MDX1x1PMdyNM

"use client";

import * as React from "react";

import Head from "next/head";
import Link, { LinkProps } from "next/link";
import { useRouter } from "next/navigation";

import {
  Flex as Flex__,
  MultiChoiceArg,
  PlasmicDataSourceContextProvider as PlasmicDataSourceContextProvider__,
  PlasmicIcon as PlasmicIcon__,
  PlasmicImg as PlasmicImg__,
  PlasmicLink as PlasmicLink__,
  PlasmicPageGuard as PlasmicPageGuard__,
  SingleBooleanChoiceArg,
  SingleChoiceArg,
  Stack as Stack__,
  StrictProps,
  Trans as Trans__,
  classNames,
  createPlasmicElementProxy,
  deriveRenderOpts,
  ensureGlobalVariants,
  generateOnMutateForSpec,
  generateStateOnChangeProp,
  generateStateOnChangePropForCodeComponents,
  generateStateValueProp,
  get as $stateGet,
  hasVariant,
  initializeCodeComponentStates,
  initializePlasmicStates,
  makeFragment,
  omit,
  pick,
  renderPlasmicSlot,
  set as $stateSet,
  useCurrentUser,
  useDollarState,
  usePlasmicTranslator,
  useTrigger,
  wrapWithClassName
} from "@plasmicapp/react-web";
import {
  DataCtxReader as DataCtxReader__,
  useDataEnv,
  useGlobalActions
} from "@plasmicapp/host";

import "@plasmicapp/react-web/lib/plasmic.css";

import projectcss from "./plasmic.module.css"; // plasmic-import: nZeWKcn21KijvC7eT8B3c7/projectcss
import sty from "./PlasmicSubcomponentChevrons.module.css"; // plasmic-import: MDX1x1PMdyNM/css

import ChevronRightIcon from "./icons/PlasmicIcon__ChevronRight"; // plasmic-import: IE1Jvgi--DTA/icon
import ChevronLeftIcon from "./icons/PlasmicIcon__ChevronLeft"; // plasmic-import: Xs7YR_mpBuGG/icon
import ChevronUpIcon from "./icons/PlasmicIcon__ChevronUp"; // plasmic-import: e6s2KgA0Bmlu/icon
import ChevronDownIcon from "./icons/PlasmicIcon__ChevronDown"; // plasmic-import: UW2ddwYm4xaW/icon

createPlasmicElementProxy;

export type PlasmicSubcomponentChevrons__VariantMembers = {
  direction: "left" | "right" | "up" | "down";
};
export type PlasmicSubcomponentChevrons__VariantsArgs = {
  direction?: SingleChoiceArg<"left" | "right" | "up" | "down">;
};
type VariantPropType = keyof PlasmicSubcomponentChevrons__VariantsArgs;
export const PlasmicSubcomponentChevrons__VariantProps =
  new Array<VariantPropType>("direction");

export type PlasmicSubcomponentChevrons__ArgsType = {};
type ArgPropType = keyof PlasmicSubcomponentChevrons__ArgsType;
export const PlasmicSubcomponentChevrons__ArgProps = new Array<ArgPropType>();

export type PlasmicSubcomponentChevrons__OverridesType = {
  dot?: Flex__<"div">;
  svg?: Flex__<"svg">;
};

export interface DefaultSubcomponentChevronsProps {
  direction?: SingleChoiceArg<"left" | "right" | "up" | "down">;
  className?: string;
}

const $$ = {};

function useNextRouter() {
  try {
    return useRouter();
  } catch {}
  return undefined;
}

function PlasmicSubcomponentChevrons__RenderFunc(props: {
  variants: PlasmicSubcomponentChevrons__VariantsArgs;
  args: PlasmicSubcomponentChevrons__ArgsType;
  overrides: PlasmicSubcomponentChevrons__OverridesType;
  forNode?: string;
}) {
  const { variants, overrides, forNode } = props;

  const args = React.useMemo(
    () =>
      Object.assign(
        {},
        Object.fromEntries(
          Object.entries(props.args).filter(([_, v]) => v !== undefined)
        )
      ),
    [props.args]
  );

  const $props = {
    ...args,
    ...variants
  };

  const __nextRouter = useNextRouter();
  const $ctx = useDataEnv?.() || {};
  const refsRef = React.useRef({});
  const $refs = refsRef.current;

  let [$queries, setDollarQueries] = React.useState<
    Record<string, ReturnType<typeof usePlasmicDataOp>>
  >({});
  const stateSpecs: Parameters<typeof useDollarState>[0] = React.useMemo(
    () => [
      {
        path: "direction",
        type: "private",
        variableType: "variant",
        initFunc: ({ $props, $state, $queries, $ctx }) => $props.direction
      }
    ],
    [$props, $ctx, $refs]
  );
  const $state = useDollarState(stateSpecs, {
    $props,
    $ctx,
    $queries: $queries,
    $refs
  });

  const new$Queries: Record<string, ReturnType<typeof usePlasmicDataOp>> = {};
  if (Object.keys(new$Queries).some(k => new$Queries[k] !== $queries[k])) {
    setDollarQueries(new$Queries);

    $queries = new$Queries;
  }

  return (
    <div
      data-plasmic-name={"dot"}
      data-plasmic-override={overrides.dot}
      data-plasmic-root={true}
      data-plasmic-for-node={forNode}
      className={classNames(
        projectcss.all,
        projectcss.root_reset,
        projectcss.plasmic_default_styles,
        projectcss.plasmic_mixins,
        projectcss.plasmic_tokens,
        sty.dot,
        {
          [sty.dotdirection_down]: hasVariant($state, "direction", "down"),
          [sty.dotdirection_left]: hasVariant($state, "direction", "left"),
          [sty.dotdirection_right]: hasVariant($state, "direction", "right"),
          [sty.dotdirection_up]: hasVariant($state, "direction", "up")
        }
      )}
    >
      <PlasmicIcon__
        data-plasmic-name={"svg"}
        data-plasmic-override={overrides.svg}
        PlasmicIconType={
          hasVariant($state, "direction", "down")
            ? ChevronDownIcon
            : hasVariant($state, "direction", "up")
            ? ChevronUpIcon
            : hasVariant($state, "direction", "left")
            ? ChevronLeftIcon
            : ChevronRightIcon
        }
        className={classNames(projectcss.all, sty.svg, {
          [sty.svgdirection_down]: hasVariant($state, "direction", "down"),
          [sty.svgdirection_left]: hasVariant($state, "direction", "left"),
          [sty.svgdirection_up]: hasVariant($state, "direction", "up")
        })}
        role={"img"}
      />
    </div>
  ) as React.ReactElement | null;
}

const PlasmicDescendants = {
  dot: ["dot", "svg"],
  svg: ["svg"]
} as const;
type NodeNameType = keyof typeof PlasmicDescendants;
type DescendantsType<T extends NodeNameType> =
  (typeof PlasmicDescendants)[T][number];
type NodeDefaultElementType = {
  dot: "div";
  svg: "svg";
};

type ReservedPropsType = "variants" | "args" | "overrides";
type NodeOverridesType<T extends NodeNameType> = Pick<
  PlasmicSubcomponentChevrons__OverridesType,
  DescendantsType<T>
>;
type NodeComponentProps<T extends NodeNameType> =
  // Explicitly specify variants, args, and overrides as objects
  {
    variants?: PlasmicSubcomponentChevrons__VariantsArgs;
    args?: PlasmicSubcomponentChevrons__ArgsType;
    overrides?: NodeOverridesType<T>;
  } & Omit<PlasmicSubcomponentChevrons__VariantsArgs, ReservedPropsType> & // Specify variants directly as props
    /* Specify args directly as props*/ Omit<
      PlasmicSubcomponentChevrons__ArgsType,
      ReservedPropsType
    > &
    /* Specify overrides for each element directly as props*/ Omit<
      NodeOverridesType<T>,
      ReservedPropsType | VariantPropType | ArgPropType
    > &
    /* Specify props for the root element*/ Omit<
      Partial<React.ComponentProps<NodeDefaultElementType[T]>>,
      ReservedPropsType | VariantPropType | ArgPropType | DescendantsType<T>
    >;

function makeNodeComponent<NodeName extends NodeNameType>(nodeName: NodeName) {
  type PropsType = NodeComponentProps<NodeName> & { key?: React.Key };
  const func = function <T extends PropsType>(
    props: T & StrictProps<T, PropsType>
  ) {
    const { variants, args, overrides } = React.useMemo(
      () =>
        deriveRenderOpts(props, {
          name: nodeName,
          descendantNames: PlasmicDescendants[nodeName],
          internalArgPropNames: PlasmicSubcomponentChevrons__ArgProps,
          internalVariantPropNames: PlasmicSubcomponentChevrons__VariantProps
        }),
      [props, nodeName]
    );
    return PlasmicSubcomponentChevrons__RenderFunc({
      variants,
      args,
      overrides,
      forNode: nodeName
    });
  };
  if (nodeName === "dot") {
    func.displayName = "PlasmicSubcomponentChevrons";
  } else {
    func.displayName = `PlasmicSubcomponentChevrons.${nodeName}`;
  }
  return func;
}

export const PlasmicSubcomponentChevrons = Object.assign(
  // Top-level PlasmicSubcomponentChevrons renders the root element
  makeNodeComponent("dot"),
  {
    // Helper components rendering sub-elements
    svg: makeNodeComponent("svg"),

    // Metadata about props expected for PlasmicSubcomponentChevrons
    internalVariantProps: PlasmicSubcomponentChevrons__VariantProps,
    internalArgProps: PlasmicSubcomponentChevrons__ArgProps
  }
);

export default PlasmicSubcomponentChevrons;
/* prettier-ignore-end */
