import * as React from "react";
import {
  DefaultSubcomponentLogOutButtonProps,
  PlasmicSubcomponentLogOutButton
} from "./autogenerated/senergy_platform/PlasmicSubcomponentLogOutButton";

import {
  ButtonRef,
  HtmlAnchorOnlyProps,
  HtmlButtonOnlyProps
} from "@plasmicapp/react-web";

export interface SubcomponentLogOutButtonProps
  extends DefaultSubcomponentLogOutButtonProps {
  // Feel free to add any additional props that this component should receive
}
function SubcomponentLogOutButton_(
  props: SubcomponentLogOutButtonProps,
  ref: ButtonRef
) {
  const { plasmicProps } =
    PlasmicSubcomponentLogOutButton.useBehavior<SubcomponentLogOutButtonProps>(
      props,
      ref
    );
  return <PlasmicSubcomponentLogOutButton {...plasmicProps} />;
}

export type ButtonComponentType = {
  (
    props: Omit<SubcomponentLogOutButtonProps, HtmlAnchorOnlyProps> & {
      ref?: React.Ref<HTMLButtonElement>;
    }
  ): React.ReactElement;
  (
    props: Omit<SubcomponentLogOutButtonProps, HtmlButtonOnlyProps> & {
      ref?: React.Ref<HTMLAnchorElement>;
    }
  ): React.ReactElement;
};
const SubcomponentLogOutButton = React.forwardRef(
  SubcomponentLogOutButton_
) as any as ButtonComponentType;

export default Object.assign(SubcomponentLogOutButton, {
  __plumeType: "button"
});
