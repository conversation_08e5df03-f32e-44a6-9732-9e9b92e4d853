/* eslint-disable */
/* tslint:disable */
// @ts-nocheck
/* prettier-ignore-start */
import React from "react";
import { classNames } from "@plasmicapp/react-web";

export type PatentPendingIconProps = React.ComponentProps<"svg"> & {
  title?: string;
};

export function PatentPendingIcon(props: PatentPendingIconProps) {
  const { className, style, title, ...restProps } = props;
  return (
    <svg
      xmlns={"http://www.w3.org/2000/svg"}
      className={classNames(
        "plasmic-default__svg",
        className,
        "icon icon-tabler icon-tabler-progress-check"
      )}
      viewBox={"0 0 24 24"}
      strokeWidth={"1.5"}
      stroke={"currentColor"}
      fill={"none"}
      strokeLinecap={"round"}
      strokeLinejoin={"round"}
      height={"1em"}
      style={style}
      {...restProps}
    >
      {title && <title>{title}</title>}

      <path d={"M0 0h24v24H0z"} fill={"none"} stroke={"none"}></path>

      <path
        d={
          "M10 20.777a8.942 8.942 0 01-2.48-.969M14 3.223a9.003 9.003 0 010 17.554m-9.421-3.684a8.961 8.961 0 01-1.227-2.592M3.124 10.5c.16-.95.468-1.85.9-2.675l.169-.305m2.714-2.941A8.954 8.954 0 0110 3.223"
        }
      ></path>

      <path d={"M9 12l2 2 4-4"}></path>
    </svg>
  );
}

export default PatentPendingIcon;
/* prettier-ignore-end */
