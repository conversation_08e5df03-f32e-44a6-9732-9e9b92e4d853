.subNavBarContainer {
  box-shadow: 0px 4px 16px -8px rgba(0, 0, 0, 0.2);
  display: flex;
  flex-direction: column;
  position: relative;
  width: 300px;
  height: 100%;
  justify-content: flex-start;
  align-items: center;
  justify-self: flex-start;
  background: var(--token-5_Q90hFZ9CmK);
  min-height: 100%;
  border-right: 1px solid var(--token-6c2Z9MBevRAq);
  border-left: 1px solid var(--token-6c2Z9MBevRAq);
}
.headerContainer {
  display: flex;
  width: 100%;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start;
  min-width: 0;
  padding: var(--token-sazGmnf7GWAk) var(--token-sazGmnf7GWAk) 0px;
  border-bottom: 0.05em solid var(--token-K5FbAPSIIrXM);
}
.headerContainersubMenuContent_messages {
  border-bottom-style: solid;
  border-top-style: none;
  border-left-style: none;
  border-right-style: none;
  border-width: 1px;
  border-color: var(--token-v4jufhOu3lt9);
}
.headerFormat {
  display: flex;
  position: relative;
  flex-direction: row;
  width: 100%;
  height: auto;
  max-width: 100%;
  min-width: 0;
  padding: var(--token-4Wrp9mDZCSCQ);
  margin: var(--token-sazGmnf7GWAk) 0px 0px var(--token-sazGmnf7GWAk);
}
.headerFormat > :global(.__wab_flex-container) {
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  min-width: 0;
  margin-left: calc(0px - var(--token-4Wrp9mDZCSCQ));
  width: calc(100% + var(--token-4Wrp9mDZCSCQ));
}
.headerFormat > :global(.__wab_flex-container) > *,
.headerFormat > :global(.__wab_flex-container) > :global(.__wab_slot) > *,
.headerFormat > :global(.__wab_flex-container) > picture > img,
.headerFormat
  > :global(.__wab_flex-container)
  > :global(.__wab_slot)
  > picture
  > img {
  margin-left: var(--token-4Wrp9mDZCSCQ);
}
.text {
  width: auto;
  height: auto;
  max-width: 100%;
  font-family: var(--token-z1yrQVi72Nj1);
  font-size: var(--token-9KumB6TRpaad);
  padding-left: 0px;
  padding-top: 0px;
  margin-left: calc(4px + var(--token-4Wrp9mDZCSCQ)) !important;
  padding-bottom: 0px;
}
.textsubMenuContent_messages {
  margin-left: calc(4px + var(--token-4Wrp9mDZCSCQ)) !important;
}
.textsubMenuContent_search {
  margin-left: calc(4px + var(--token-4Wrp9mDZCSCQ)) !important;
}
.textsubMenuContent_bookmarks {
  margin-left: calc(4px + var(--token-4Wrp9mDZCSCQ)) !important;
}
.subcomponentIconButton:global(.__wab_instance) {
  position: relative;
  max-width: 100%;
  width: 32px;
  height: 32px;
  flex-shrink: 0;
}
.contentContainer {
  display: flex;
  position: relative;
  width: 100%;
  height: 100%;
  flex-direction: column;
  min-width: 0;
  min-height: 0;
  padding: var(--token-sazGmnf7GWAk);
}
.contentContainersubMenuContent_messages {
  border-width: 1px;
  border-style: none;
}
.searchFormatting {
  display: flex;
  position: relative;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: auto;
  max-width: 100%;
  border-bottom-style: solid;
  border-bottom-width: 1px;
  min-width: 0;
  padding: 2px var(--token-4Wrp9mDZCSCQ);
}
.searchFormattingsubMenuContent_messages {
  border-bottom-style: solid;
  border-right-style: none;
  border-left-style: none;
  border-top-style: none;
  padding-top: 0px;
  padding-bottom: 0px;
  justify-content: center;
  align-items: center;
  border-width: 1px;
}
.searchFormattingsubMenuContent_bookmarks {
  border-bottom-width: 0.5px;
  border-bottom-style: solid;
}
.searchBar:global(.__wab_instance) {
  max-width: 100%;
  width: 86%;
}
.searchBarsubMenuContent_messages:global(.__wab_instance) {
  width: 86%;
}
.icon {
  position: relative;
  object-fit: cover;
  max-width: 100%;
  color: var(--token-3OLw18f8JRN3);
  transform: none;
  border-bottom-style: solid;
  border-bottom-width: 1px;
  margin-right: 0px;
  margin-top: 4px;
  height: 1em;
}
.glassOverlay {
  display: none;
  flex-direction: column;
  background: linear-gradient(0deg, #ededed8c 0%, #ededed0d 100%);
  width: 100%;
  height: 100%;
  backdrop-filter: blur(3px);
  position: absolute;
  align-content: flex-start;
  justify-items: center;
  left: 0px;
  top: 0px;
  cursor: not-allowed;
  pointer-events: none;
  z-index: 5;
  min-width: 0;
  min-height: 0;
  -webkit-backdrop-filter: blur(3px);
  padding: var(--token-sazGmnf7GWAk);
}
.glassOverlaycomingSoon {
  display: block;
}
.h3 {
  width: 100%;
  height: auto;
  max-width: 100%;
  font-family: var(--token-z1yrQVi72Nj1);
  text-align: center;
  color: var(--token-v4jufhOu3lt9);
  font-weight: 700;
  transform: rotateX(0deg) rotateY(0deg) rotateZ(-2deg);
  margin-top: 42px;
  z-index: 6;
  left: 0px;
  top: 0px;
  position: absolute;
  min-width: 0;
  display: none;
  padding: var(--token-4Wrp9mDZCSCQ);
}
.h3comingSoon {
  display: block;
}
.bookmarksBookmarkTiles:global(.__wab_instance) {
  position: relative;
  display: none;
}
.bookmarksBookmarkTilessubMenuContent_bookmarks:global(.__wab_instance) {
  display: flex;
}
.messagesMessagePreview:global(.__wab_instance) {
  position: relative;
  left: auto;
  top: auto;
  display: none;
}
.messagesMessagePreviewsubMenuContent_messages:global(.__wab_instance) {
  display: flex;
}
