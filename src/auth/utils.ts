// Magic link storage helpers
const STORAGE_KEYS = {
  SENT: 'magicLinkSent',
  EMAIL: 'magicLinkSentEmail',
  EXPIRES_AT: 'magicLinkExpiresAt',
};

export const storeMagicLinkData = (email: string) => {
  const expiresAt = Date.now() + 300_000; // 5 minutes (300 seconds) from now
  localStorage.setItem(STORAGE_KEYS.SENT, 'true');
  localStorage.setItem(STORAGE_KEYS.EMAIL, email);
  localStorage.setItem(STORAGE_KEYS.EXPIRES_AT, expiresAt.toString());
  return expiresAt;
};

export const clearMagicLinkData = () => {
  localStorage.removeItem(STORAGE_KEYS.SENT);
  localStorage.removeItem(STORAGE_KEYS.EMAIL);
  localStorage.removeItem(STORAGE_KEYS.EXPIRES_AT);
};

export const getMagicLinkData = () => {
  if (typeof window === 'undefined') {
    return null;
  }

  const sent = localStorage.getItem(STORAGE_KEYS.SENT);
  const email = localStorage.getItem(STORAGE_KEYS.EMAIL);
  const expiresAtStr = localStorage.getItem(STORAGE_KEYS.EXPIRES_AT);

  if (!(sent && email && expiresAtStr)) {
    return null;
  }

  const expiresAt = Number.parseInt(expiresAtStr, 10);
  const isExpired = Date.now() >= expiresAt;
  const secondsLeft = Math.max(0, Math.ceil((expiresAt - Date.now()) / 1000));

  return {
    sent: true,
    email,
    expiresAt,
    isExpired,
    secondsLeft,
  };
};

export const formatTimeLeft = (seconds: number) => {
  if (seconds <= 0) {
    return 'Expired';
  }

  const minutes = Math.floor(seconds / 60);
  const remainingSeconds = seconds % 60;

  // Show "5 minutes" for any time between 4:30 and 5:00
  if (seconds >= 270) {
    return '5 minutes';
  }

  // Show full minutes when more than 2 minutes left
  if (minutes >= 2) {
    return `${minutes} minutes`;
  }

  // Show minutes and seconds when under 2 minutes
  if (minutes >= 1) {
    return `${minutes} minute ${remainingSeconds} seconds`;
  }

  return `${remainingSeconds} second${remainingSeconds !== 1 ? 's' : ''}`;
};

export const MAGIC_LINK_EXPIRY_TIME = 300; // 5 minutes in seconds
