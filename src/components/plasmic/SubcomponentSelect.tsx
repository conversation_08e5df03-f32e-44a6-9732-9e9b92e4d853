import * as React from "react";
import {
  DefaultSubcomponentSelectProps,
  PlasmicSubcomponentSelect
} from "./autogenerated/senergy_platform/PlasmicSubcomponentSelect";

import { SelectRef } from "@plasmicapp/react-web";
import SubcomponentSelect__Option from "./SubcomponentSelect__Option";
import SubcomponentSelect__OptionGroup from "./SubcomponentSelect__OptionGroup";

export interface SubcomponentSelectProps
  extends DefaultSubcomponentSelectProps {
  // Feel free to add any additional props that this component should receive
}
function SubcomponentSelect_(props: SubcomponentSelectProps, ref: SelectRef) {
  const { plasmicProps, state } = PlasmicSubcomponentSelect.useBehavior(
    props,
    ref
  );
  return <PlasmicSubcomponentSelect {...plasmicProps} />;
}

const SubcomponentSelect = React.forwardRef(SubcomponentSelect_);

export default Object.assign(
  SubcomponentSelect,

  {
    Option: SubcomponentSelect__Option,
    OptionGroup: SubcomponentSelect__OptionGroup,
    __plumeType: "select"
  }
);
