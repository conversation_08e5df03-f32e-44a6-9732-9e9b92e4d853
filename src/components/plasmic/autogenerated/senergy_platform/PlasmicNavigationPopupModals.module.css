.formattingContainer {
  display: flex;
  flex-direction: column;
  position: relative;
  width: 250px;
  height: auto;
  justify-content: flex-start;
  align-items: flex-start;
  background: var(--token-xo_r2w5pebq-);
  box-shadow: 0px 2px 20px -2px #8b8b8b33;
  overflow: hidden;
  justify-self: flex-start;
  padding: var(--token-sazGmnf7GWAk);
  margin: 0px;
  border: 0.01cm solid #e4e4e7;
}
.header {
  display: flex;
  position: sticky;
  width: 100%;
  flex-direction: column;
  left: 0px;
  top: 0px;
  z-index: 1;
  min-width: 0;
  padding: var(--token-sazGmnf7GWAk);
}
.text {
  position: relative;
  width: auto;
  height: auto;
  max-width: 100%;
  font-family: var(--token-z1yrQVi72Nj1);
  padding: var(--token-4Wrp9mDZCSCQ) var(--token-4Wrp9mDZCSCQ)
    var(--token-4Wrp9mDZCSCQ) 7px;
}
.textnavBarModals_messages {
  padding-left: 7px;
}
.section {
  display: grid;
  position: relative;
  align-content: flex-start;
  justify-items: center;
  width: 100%;
  height: 1px;
  background: var(--token-p09LDPmbF81_);
  margin-bottom: 0px;
  min-width: 0;
  flex-shrink: 0;
  grid-template-columns:
    var(--plsmc-viewport-gap) 1fr minmax(0, var(--plsmc-wide-chunk))
    min(
      var(--plsmc-standard-width),
      calc(100% - var(--plsmc-viewport-gap) - var(--plsmc-viewport-gap))
    )
    minmax(0, var(--plsmc-wide-chunk)) 1fr var(--plsmc-viewport-gap);
  padding: var(--token-sazGmnf7GWAk);
}
.section > * {
  grid-column: 4;
}
.dynamicContent {
  display: flex;
  position: relative;
  width: 100%;
  flex-direction: column;
  min-width: 0;
  padding: var(--token-sazGmnf7GWAk);
}
.messagesSlot {
  position: relative;
  width: 100%;
  flex-direction: column;
  min-width: 0;
  display: none;
  padding: var(--token-sazGmnf7GWAk);
}
.messagesSlotnavBarModals_messages {
  display: flex;
}
.messagesMessagePreview___5E1Kk:global(.__wab_instance) {
  max-width: 100%;
  position: relative;
}
.bookmarkSlot {
  position: relative;
  align-content: flex-start;
  justify-items: center;
  width: 100%;
  flex-direction: column;
  min-width: 0;
  display: none;
  padding: var(--token-sazGmnf7GWAk);
}
.bookmarkSlotnavBarModals_bookmarks {
  display: flex;
}
.bookmarksBookmarkTiles__vujno:global(.__wab_instance) {
  max-width: 100%;
  position: relative;
}
.legalStack {
  position: relative;
  height: auto;
  width: 270px;
  justify-content: flex-start;
  align-items: flex-start;
  flex-direction: column;
  background: none;
  padding-left: 24px;
  padding-bottom: 8px;
  padding-top: 0px;
  max-width: 100%;
  display: none;
  margin: var(--token-sazGmnf7GWAk);
}
.legalStacknavBarModals_legal {
  display: flex;
}
.termsOfUse:global(.__wab_instance) {
  position: relative;
  max-width: 100%;
  flex-shrink: 0;
}
.privacyPolicy:global(.__wab_instance) {
  max-width: 100%;
  position: relative;
  flex-shrink: 0;
}
.careers:global(.__wab_instance) {
  max-width: 100%;
  position: relative;
  flex-shrink: 0;
  display: none;
}
.careersnavBarModals_legal:global(.__wab_instance) {
  flex-shrink: 0;
}
.contactUs:global(.__wab_instance) {
  max-width: 100%;
  position: relative;
  flex-shrink: 0;
}
.logoutButton:global(.__wab_instance) {
  max-width: 100%;
  position: relative;
  flex-shrink: 0;
}
.logoutButtonnavBarModals_legal:global(.__wab_instance) {
  flex-shrink: 0;
}
