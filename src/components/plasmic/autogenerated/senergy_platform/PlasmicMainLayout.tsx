/* eslint-disable */
/* tslint:disable */
// @ts-nocheck
/* prettier-ignore-start */

/** @jsxRuntime classic */
/** @jsx createPlasmicElementProxy */
/** @jsxFrag React.Fragment */

// This class is auto-generated by Plasmic; please do not edit!
// Plasmic Project: nZeWKcn21KijvC7eT8B3c7
// Component: 0PT5smaq5Bm6

"use client";

import * as React from "react";

import Head from "next/head";
import Link, { LinkProps } from "next/link";
import { useRouter } from "next/navigation";

import {
  Flex as Flex__,
  MultiChoiceArg,
  PlasmicDataSourceContextProvider as PlasmicDataSourceContextProvider__,
  PlasmicIcon as PlasmicIcon__,
  PlasmicImg as PlasmicImg__,
  PlasmicLink as PlasmicLink__,
  PlasmicPageGuard as PlasmicPageGuard__,
  SingleBooleanChoiceArg,
  SingleChoiceArg,
  Stack as Stack__,
  StrictProps,
  Trans as Trans__,
  classNames,
  createPlasmicElementProxy,
  deriveRenderOpts,
  ensureGlobalVariants,
  generateOnMutateForSpec,
  generateStateOnChangeProp,
  generateStateOnChangePropForCodeComponents,
  generateStateValueProp,
  get as $stateGet,
  hasVariant,
  initializeCodeComponentStates,
  initializePlasmicStates,
  makeFragment,
  omit,
  pick,
  renderPlasmicSlot,
  set as $stateSet,
  useCurrentUser,
  useDollarState,
  usePlasmicTranslator,
  useTrigger,
  wrapWithClassName
} from "@plasmicapp/react-web";
import {
  DataCtxReader as DataCtxReader__,
  useDataEnv,
  useGlobalActions
} from "@plasmicapp/host";

import NavigationSidebar from "../../NavigationSidebar"; // plasmic-import: t4FaTc0WFZv3/component

import { useScreenVariants as useScreenVariants_4Hrhi5G5ANwQ } from "./PlasmicGlobalVariant__FormattingBreakPoint"; // plasmic-import: 4Hrhi5G5aNwQ/globalVariant

import "@plasmicapp/react-web/lib/plasmic.css";

import projectcss from "./plasmic.module.css"; // plasmic-import: nZeWKcn21KijvC7eT8B3c7/projectcss
import sty from "./PlasmicMainLayout.module.css"; // plasmic-import: 0PT5smaq5Bm6/css

createPlasmicElementProxy;

export type PlasmicMainLayout__VariantMembers = {};
export type PlasmicMainLayout__VariantsArgs = {};
type VariantPropType = keyof PlasmicMainLayout__VariantsArgs;
export const PlasmicMainLayout__VariantProps = new Array<VariantPropType>();

export type PlasmicMainLayout__ArgsType = {
  children?: React.ReactNode;
  navbarActivePage?: string;
};
type ArgPropType = keyof PlasmicMainLayout__ArgsType;
export const PlasmicMainLayout__ArgProps = new Array<ArgPropType>(
  "children",
  "navbarActivePage"
);

export type PlasmicMainLayout__OverridesType = {
  root?: Flex__<"div">;
  pageMasterFormatting?: Flex__<"div">;
  sideBarFormattingContainer?: Flex__<"section">;
  navigationSidebar?: Flex__<typeof NavigationSidebar>;
  masterContainerForCentering?: Flex__<"div">;
  scrollableContentContainer?: Flex__<"div">;
  pageContent?: Flex__<"section">;
  main?: Flex__<"section">;
  textWrappingSection?: Flex__<"section">;
  h5?: Flex__<"h5">;
};

export interface DefaultMainLayoutProps {
  children?: React.ReactNode;
  navbarActivePage?: string;
  className?: string;
}

const $$ = {};

function useNextRouter() {
  try {
    return useRouter();
  } catch {}
  return undefined;
}

function PlasmicMainLayout__RenderFunc(props: {
  variants: PlasmicMainLayout__VariantsArgs;
  args: PlasmicMainLayout__ArgsType;
  overrides: PlasmicMainLayout__OverridesType;
  forNode?: string;
}) {
  const { variants, overrides, forNode } = props;

  const args = React.useMemo(
    () =>
      Object.assign(
        {},
        Object.fromEntries(
          Object.entries(props.args).filter(([_, v]) => v !== undefined)
        )
      ),
    [props.args]
  );

  const $props = {
    ...args,
    ...variants
  };

  const __nextRouter = useNextRouter();
  const $ctx = useDataEnv?.() || {};
  const refsRef = React.useRef({});
  const $refs = refsRef.current;

  let [$queries, setDollarQueries] = React.useState<
    Record<string, ReturnType<typeof usePlasmicDataOp>>
  >({});

  const new$Queries: Record<string, ReturnType<typeof usePlasmicDataOp>> = {};
  if (Object.keys(new$Queries).some(k => new$Queries[k] !== $queries[k])) {
    setDollarQueries(new$Queries);

    $queries = new$Queries;
  }

  const globalVariants = ensureGlobalVariants({
    formattingBreakPoint: useScreenVariants_4Hrhi5G5ANwQ()
  });

  return (
    <div
      data-plasmic-name={"root"}
      data-plasmic-override={overrides.root}
      data-plasmic-root={true}
      data-plasmic-for-node={forNode}
      className={classNames(
        projectcss.all,
        projectcss.root_reset,
        projectcss.plasmic_default_styles,
        projectcss.plasmic_mixins,
        projectcss.plasmic_tokens,
        sty.root
      )}
    >
      <div
        data-plasmic-name={"pageMasterFormatting"}
        data-plasmic-override={overrides.pageMasterFormatting}
        className={classNames(projectcss.all, sty.pageMasterFormatting)}
      >
        <section
          data-plasmic-name={"sideBarFormattingContainer"}
          data-plasmic-override={overrides.sideBarFormattingContainer}
          className={classNames(projectcss.all, sty.sideBarFormattingContainer)}
        >
          <NavigationSidebar
            data-plasmic-name={"navigationSidebar"}
            data-plasmic-override={overrides.navigationSidebar}
            className={classNames("__wab_instance", sty.navigationSidebar)}
          />
        </section>
        <div
          data-plasmic-name={"masterContainerForCentering"}
          data-plasmic-override={overrides.masterContainerForCentering}
          className={classNames(
            projectcss.all,
            sty.masterContainerForCentering
          )}
        >
          <div
            data-plasmic-name={"scrollableContentContainer"}
            data-plasmic-override={overrides.scrollableContentContainer}
            className={classNames(
              projectcss.all,
              sty.scrollableContentContainer
            )}
          >
            <section
              data-plasmic-name={"pageContent"}
              data-plasmic-override={overrides.pageContent}
              className={classNames(projectcss.all, sty.pageContent)}
            >
              {renderPlasmicSlot({
                defaultContents: null,
                value: args.children
              })}
            </section>
          </div>
        </div>
        <Stack__
          as={"section"}
          data-plasmic-name={"main"}
          data-plasmic-override={overrides.main}
          hasGap={true}
          className={classNames(projectcss.all, sty.main)}
        >
          <section
            data-plasmic-name={"textWrappingSection"}
            data-plasmic-override={overrides.textWrappingSection}
            className={classNames(projectcss.all, sty.textWrappingSection)}
          >
            <div
              className={classNames(
                projectcss.all,
                projectcss.__wab_text,
                sty.text__n0T1R
              )}
            >
              {"SENERGY"}
            </div>
            <div
              className={classNames(
                projectcss.all,
                projectcss.__wab_text,
                sty.text__kht0C
              )}
            >
              {".WORKS"}
            </div>
          </section>
          <h5
            data-plasmic-name={"h5"}
            data-plasmic-override={overrides.h5}
            className={classNames(
              projectcss.all,
              projectcss.h5,
              projectcss.__wab_text,
              sty.h5
            )}
          >
            <React.Fragment>
              <React.Fragment>
                {"The platform is not yet available on mobile devices. \n\n"}
              </React.Fragment>
              <span
                className={"plasmic_default__all plasmic_default__span"}
                style={{ textDecorationLine: "underline" }}
              >
                {"Please visit the page on a larger display"}
              </span>
            </React.Fragment>
          </h5>
        </Stack__>
      </div>
    </div>
  ) as React.ReactElement | null;
}

const PlasmicDescendants = {
  root: [
    "root",
    "pageMasterFormatting",
    "sideBarFormattingContainer",
    "navigationSidebar",
    "masterContainerForCentering",
    "scrollableContentContainer",
    "pageContent",
    "main",
    "textWrappingSection",
    "h5"
  ],
  pageMasterFormatting: [
    "pageMasterFormatting",
    "sideBarFormattingContainer",
    "navigationSidebar",
    "masterContainerForCentering",
    "scrollableContentContainer",
    "pageContent",
    "main",
    "textWrappingSection",
    "h5"
  ],
  sideBarFormattingContainer: [
    "sideBarFormattingContainer",
    "navigationSidebar"
  ],
  navigationSidebar: ["navigationSidebar"],
  masterContainerForCentering: [
    "masterContainerForCentering",
    "scrollableContentContainer",
    "pageContent"
  ],
  scrollableContentContainer: ["scrollableContentContainer", "pageContent"],
  pageContent: ["pageContent"],
  main: ["main", "textWrappingSection", "h5"],
  textWrappingSection: ["textWrappingSection"],
  h5: ["h5"]
} as const;
type NodeNameType = keyof typeof PlasmicDescendants;
type DescendantsType<T extends NodeNameType> =
  (typeof PlasmicDescendants)[T][number];
type NodeDefaultElementType = {
  root: "div";
  pageMasterFormatting: "div";
  sideBarFormattingContainer: "section";
  navigationSidebar: typeof NavigationSidebar;
  masterContainerForCentering: "div";
  scrollableContentContainer: "div";
  pageContent: "section";
  main: "section";
  textWrappingSection: "section";
  h5: "h5";
};

type ReservedPropsType = "variants" | "args" | "overrides";
type NodeOverridesType<T extends NodeNameType> = Pick<
  PlasmicMainLayout__OverridesType,
  DescendantsType<T>
>;
type NodeComponentProps<T extends NodeNameType> =
  // Explicitly specify variants, args, and overrides as objects
  {
    variants?: PlasmicMainLayout__VariantsArgs;
    args?: PlasmicMainLayout__ArgsType;
    overrides?: NodeOverridesType<T>;
  } & Omit<PlasmicMainLayout__VariantsArgs, ReservedPropsType> & // Specify variants directly as props
    /* Specify args directly as props*/ Omit<
      PlasmicMainLayout__ArgsType,
      ReservedPropsType
    > &
    /* Specify overrides for each element directly as props*/ Omit<
      NodeOverridesType<T>,
      ReservedPropsType | VariantPropType | ArgPropType
    > &
    /* Specify props for the root element*/ Omit<
      Partial<React.ComponentProps<NodeDefaultElementType[T]>>,
      ReservedPropsType | VariantPropType | ArgPropType | DescendantsType<T>
    >;

function makeNodeComponent<NodeName extends NodeNameType>(nodeName: NodeName) {
  type PropsType = NodeComponentProps<NodeName> & { key?: React.Key };
  const func = function <T extends PropsType>(
    props: T & StrictProps<T, PropsType>
  ) {
    const { variants, args, overrides } = React.useMemo(
      () =>
        deriveRenderOpts(props, {
          name: nodeName,
          descendantNames: PlasmicDescendants[nodeName],
          internalArgPropNames: PlasmicMainLayout__ArgProps,
          internalVariantPropNames: PlasmicMainLayout__VariantProps
        }),
      [props, nodeName]
    );
    return PlasmicMainLayout__RenderFunc({
      variants,
      args,
      overrides,
      forNode: nodeName
    });
  };
  if (nodeName === "root") {
    func.displayName = "PlasmicMainLayout";
  } else {
    func.displayName = `PlasmicMainLayout.${nodeName}`;
  }
  return func;
}

export const PlasmicMainLayout = Object.assign(
  // Top-level PlasmicMainLayout renders the root element
  makeNodeComponent("root"),
  {
    // Helper components rendering sub-elements
    pageMasterFormatting: makeNodeComponent("pageMasterFormatting"),
    sideBarFormattingContainer: makeNodeComponent("sideBarFormattingContainer"),
    navigationSidebar: makeNodeComponent("navigationSidebar"),
    masterContainerForCentering: makeNodeComponent(
      "masterContainerForCentering"
    ),
    scrollableContentContainer: makeNodeComponent("scrollableContentContainer"),
    pageContent: makeNodeComponent("pageContent"),
    main: makeNodeComponent("main"),
    textWrappingSection: makeNodeComponent("textWrappingSection"),
    h5: makeNodeComponent("h5"),

    // Metadata about props expected for PlasmicMainLayout
    internalVariantProps: PlasmicMainLayout__VariantProps,
    internalArgProps: PlasmicMainLayout__ArgProps
  }
);

export default PlasmicMainLayout;
/* prettier-ignore-end */
