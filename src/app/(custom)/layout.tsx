import type React from 'react';
import { PostHogProvider } from '@/app/(custom)/providers';
import { Toaster } from '@/components/ui/sonner';
import { SessionProvider } from '@/contexts/session-context';

export default function Layout({ children }: { children: React.ReactNode }) {
  return (
    <html lang="en">
      <body>
        <SessionProvider>
          <PostHogProvider>{children}</PostHogProvider>
        </SessionProvider>
        <Toaster />
      </body>
    </html>
  );
}
