/* eslint-disable */
/* tslint:disable */
// @ts-nocheck
/* prettier-ignore-start */
import React from "react";
import { classNames } from "@plasmicapp/react-web";

export type ConstructionCraneIconProps = React.ComponentProps<"svg"> & {
  title?: string;
};

export function ConstructionCraneIcon(props: ConstructionCraneIconProps) {
  const { className, style, title, ...restProps } = props;
  return (
    <svg
      xmlns={"http://www.w3.org/2000/svg"}
      className={classNames(
        "plasmic-default__svg",
        className,
        "icon icon-tabler icon-tabler-crane"
      )}
      viewBox={"0 0 24 24"}
      strokeWidth={"1.5"}
      stroke={"currentColor"}
      fill={"none"}
      strokeLinecap={"round"}
      strokeLinejoin={"round"}
      height={"1em"}
      style={style}
      {...restProps}
    >
      {title && <title>{title}</title>}

      <path d={"M0 0h24v24H0z"} fill={"none"} stroke={"none"}></path>

      <path d={"M6 21h6m-3 0V3L3 9h18M9 3l10 6"}></path>

      <path d={"M17 9v4a2 2 0 11-2 2"}></path>
    </svg>
  );
}

export default ConstructionCraneIcon;
/* prettier-ignore-end */
