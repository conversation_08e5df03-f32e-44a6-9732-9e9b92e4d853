/* eslint-disable */
/* tslint:disable */
// @ts-nocheck
/* prettier-ignore-start */

/** @jsxRuntime classic */
/** @jsx createPlasmicElementProxy */
/** @jsxFrag React.Fragment */

// This class is auto-generated by Plasmic; please do not edit!
// Plasmic Project: nZeWKcn21KijvC7eT8B3c7
// Component: NQLRskEW8iAu

"use client";

import * as React from "react";

import Head from "next/head";
import Link, { LinkProps } from "next/link";
import { useRouter } from "next/navigation";

import {
  Flex as Flex__,
  MultiChoiceArg,
  PlasmicDataSourceContextProvider as PlasmicDataSourceContextProvider__,
  PlasmicIcon as PlasmicIcon__,
  PlasmicImg as PlasmicImg__,
  PlasmicLink as PlasmicLink__,
  PlasmicPageGuard as PlasmicPageGuard__,
  SingleBooleanChoiceArg,
  SingleChoiceArg,
  Stack as Stack__,
  StrictProps,
  Trans as Trans__,
  classNames,
  createPlasmicElementProxy,
  deriveRenderOpts,
  ensureGlobalVariants,
  generateOnMutateForSpec,
  generateStateOnChangeProp,
  generateStateOnChangePropForCodeComponents,
  generateStateValueProp,
  get as $stateGet,
  hasVariant,
  initializeCodeComponentStates,
  initializePlasmicStates,
  makeFragment,
  omit,
  pick,
  renderPlasmicSlot,
  set as $stateSet,
  useCurrentUser,
  useDollarState,
  usePlasmicTranslator,
  useTrigger,
  wrapWithClassName
} from "@plasmicapp/react-web";
import {
  DataCtxReader as DataCtxReader__,
  useDataEnv,
  useGlobalActions
} from "@plasmicapp/host";

import LogoStack from "../../LogoStack"; // plasmic-import: MY4cHaJaVAVx/component
import SubcomponentTextInput from "../../SubcomponentTextInput"; // plasmic-import: Pt5FPuEzirSe/component
import SubcomponentButton from "../../SubcomponentButton"; // plasmic-import: ezhuRZvm_fH9/component

import "@plasmicapp/react-web/lib/plasmic.css";

import projectcss from "./plasmic.module.css"; // plasmic-import: nZeWKcn21KijvC7eT8B3c7/projectcss
import sty from "./PlasmicLoginLoginModal.module.css"; // plasmic-import: NQLRskEW8iAu/css

import LoadingDotsIcon from "./icons/PlasmicIcon__LoadingDots"; // plasmic-import: _Z1nsWZI7LH8/icon

createPlasmicElementProxy;

export type PlasmicLoginLoginModal__VariantMembers = {
  isLoading: "isLoading";
  isSignUp: "isSignUp";
};
export type PlasmicLoginLoginModal__VariantsArgs = {
  isLoading?: SingleBooleanChoiceArg<"isLoading">;
  isSignUp?: SingleBooleanChoiceArg<"isSignUp">;
};
type VariantPropType = keyof PlasmicLoginLoginModal__VariantsArgs;
export const PlasmicLoginLoginModal__VariantProps = new Array<VariantPropType>(
  "isLoading",
  "isSignUp"
);

export type PlasmicLoginLoginModal__ArgsType = {
  errorResponse?: string;
  onErrorResponseChange?: (val: string) => void;
};
type ArgPropType = keyof PlasmicLoginLoginModal__ArgsType;
export const PlasmicLoginLoginModal__ArgProps = new Array<ArgPropType>(
  "errorResponse",
  "onErrorResponseChange"
);

export type PlasmicLoginLoginModal__OverridesType = {
  root?: Flex__<"div">;
  logoStack?: Flex__<typeof LogoStack>;
  form?: Flex__<"form">;
  emailInput?: Flex__<typeof SubcomponentTextInput>;
  passwordInput?: Flex__<typeof SubcomponentTextInput>;
  nameBoxes?: Flex__<"div">;
  firstNameInput?: Flex__<typeof SubcomponentTextInput>;
  lastNameInput?: Flex__<typeof SubcomponentTextInput>;
  passwordCreationInput?: Flex__<typeof SubcomponentTextInput>;
  passwordConfirmationInput?: Flex__<typeof SubcomponentTextInput>;
  errorText?: Flex__<"div">;
  submitButton?: Flex__<typeof SubcomponentButton>;
  text?: Flex__<"div">;
  swapButton?: Flex__<"button">;
};

export interface DefaultLoginLoginModalProps {
  errorResponse?: string;
  onErrorResponseChange?: (val: string) => void;
  isLoading?: SingleBooleanChoiceArg<"isLoading">;
  isSignUp?: SingleBooleanChoiceArg<"isSignUp">;
  className?: string;
}

const $$ = {};

function useNextRouter() {
  try {
    return useRouter();
  } catch {}
  return undefined;
}

function PlasmicLoginLoginModal__RenderFunc(props: {
  variants: PlasmicLoginLoginModal__VariantsArgs;
  args: PlasmicLoginLoginModal__ArgsType;
  overrides: PlasmicLoginLoginModal__OverridesType;
  forNode?: string;
}) {
  const { variants, overrides, forNode } = props;

  const args = React.useMemo(
    () =>
      Object.assign(
        {},
        Object.fromEntries(
          Object.entries(props.args).filter(([_, v]) => v !== undefined)
        )
      ),
    [props.args]
  );

  const $props = {
    ...args,
    ...variants
  };

  const __nextRouter = useNextRouter();
  const $ctx = useDataEnv?.() || {};
  const refsRef = React.useRef({});
  const $refs = refsRef.current;

  let [$queries, setDollarQueries] = React.useState<
    Record<string, ReturnType<typeof usePlasmicDataOp>>
  >({});
  const stateSpecs: Parameters<typeof useDollarState>[0] = React.useMemo(
    () => [
      {
        path: "isLoading",
        type: "private",
        variableType: "variant",
        initFunc: ({ $props, $state, $queries, $ctx }) => $props.isLoading
      },
      {
        path: "errorResponse",
        type: "writable",
        variableType: "text",

        valueProp: "errorResponse",
        onChangeProp: "onErrorResponseChange"
      },
      {
        path: "emailInput.value",
        type: "private",
        variableType: "text",
        initFunc: ({ $props, $state, $queries, $ctx }) => undefined
      },
      {
        path: "emailInput.errorMessage",
        type: "private",
        variableType: "text",
        initFunc: ({ $props, $state, $queries, $ctx }) => ""
      },
      {
        path: "passwordInput.value",
        type: "private",
        variableType: "text",
        initFunc: ({ $props, $state, $queries, $ctx }) => undefined
      },
      {
        path: "passwordInput.errorMessage",
        type: "private",
        variableType: "text",
        initFunc: ({ $props, $state, $queries, $ctx }) => ""
      },
      {
        path: "firstNameInput.value",
        type: "private",
        variableType: "text",
        initFunc: ({ $props, $state, $queries, $ctx }) => undefined
      },
      {
        path: "firstNameInput.errorMessage",
        type: "private",
        variableType: "text",
        initFunc: ({ $props, $state, $queries, $ctx }) => ""
      },
      {
        path: "lastNameInput.value",
        type: "private",
        variableType: "text",
        initFunc: ({ $props, $state, $queries, $ctx }) => undefined
      },
      {
        path: "lastNameInput.errorMessage",
        type: "private",
        variableType: "text",
        initFunc: ({ $props, $state, $queries, $ctx }) => ""
      },
      {
        path: "passwordCreationInput.value",
        type: "private",
        variableType: "text",
        initFunc: ({ $props, $state, $queries, $ctx }) => undefined
      },
      {
        path: "passwordCreationInput.errorMessage",
        type: "private",
        variableType: "text",
        initFunc: ({ $props, $state, $queries, $ctx }) => ""
      },
      {
        path: "passwordConfirmationInput.value",
        type: "private",
        variableType: "text",
        initFunc: ({ $props, $state, $queries, $ctx }) => undefined
      },
      {
        path: "passwordConfirmationInput.errorMessage",
        type: "private",
        variableType: "text",
        initFunc: ({ $props, $state, $queries, $ctx }) => ""
      },
      {
        path: "isSignUp",
        type: "private",
        variableType: "variant",
        initFunc: ({ $props, $state, $queries, $ctx }) => $props.isSignUp
      }
    ],
    [$props, $ctx, $refs]
  );
  const $state = useDollarState(stateSpecs, {
    $props,
    $ctx,
    $queries: $queries,
    $refs
  });

  const new$Queries: Record<string, ReturnType<typeof usePlasmicDataOp>> = {};
  if (Object.keys(new$Queries).some(k => new$Queries[k] !== $queries[k])) {
    setDollarQueries(new$Queries);

    $queries = new$Queries;
  }

  return (
    <Stack__
      as={"div"}
      data-plasmic-name={"root"}
      data-plasmic-override={overrides.root}
      data-plasmic-root={true}
      data-plasmic-for-node={forNode}
      hasGap={true}
      className={classNames(
        projectcss.all,
        projectcss.root_reset,
        projectcss.plasmic_default_styles,
        projectcss.plasmic_mixins,
        projectcss.plasmic_tokens,
        sty.root,
        {
          [sty.rootisLoading]: hasVariant($state, "isLoading", "isLoading"),
          [sty.rootisSignUp]: hasVariant($state, "isSignUp", "isSignUp")
        }
      )}
    >
      <LogoStack
        data-plasmic-name={"logoStack"}
        data-plasmic-override={overrides.logoStack}
        className={classNames("__wab_instance", sty.logoStack)}
      />

      <Stack__
        as={"form"}
        data-plasmic-name={"form"}
        data-plasmic-override={overrides.form}
        hasGap={true}
        className={classNames(projectcss.all, sty.form, {
          [sty.formisLoading]: hasVariant($state, "isLoading", "isLoading"),
          [sty.formisSignUp]: hasVariant($state, "isSignUp", "isSignUp")
        })}
      >
        <SubcomponentTextInput
          data-plasmic-name={"emailInput"}
          data-plasmic-override={overrides.emailInput}
          className={classNames("__wab_instance", sty.emailInput, {
            [sty.emailInputisSignUp]: hasVariant($state, "isSignUp", "isSignUp")
          })}
          errorMessage={generateStateValueProp($state, [
            "emailInput",
            "errorMessage"
          ])}
          inputAutoComplete={"email"}
          inputName={"Email Address"}
          inputTabIndex={1}
          inputType={"email"}
          inputValue={generateStateValueProp($state, ["emailInput", "value"])}
          onErrorMessageChange={async (...eventArgs: any) => {
            generateStateOnChangeProp($state, [
              "emailInput",
              "errorMessage"
            ]).apply(null, eventArgs);

            if (
              eventArgs.length > 1 &&
              eventArgs[1] &&
              eventArgs[1]._plasmic_state_init_
            ) {
              return;
            }
          }}
          onInputValueChange={async (...eventArgs: any) => {
            generateStateOnChangeProp($state, ["emailInput", "value"]).apply(
              null,
              eventArgs
            );

            if (
              eventArgs.length > 1 &&
              eventArgs[1] &&
              eventArgs[1]._plasmic_state_init_
            ) {
              return;
            }
          }}
          required={true}
        />

        <SubcomponentTextInput
          data-plasmic-name={"passwordInput"}
          data-plasmic-override={overrides.passwordInput}
          className={classNames("__wab_instance", sty.passwordInput, {
            [sty.passwordInputisSignUp]: hasVariant(
              $state,
              "isSignUp",
              "isSignUp"
            )
          })}
          displayText={(() => {
            try {
              return undefined;
            } catch (e) {
              if (
                e instanceof TypeError ||
                e?.plasmicType === "PlasmicUndefinedDataError"
              ) {
                return undefined;
              }
              throw e;
            }
          })()}
          errorMessage={generateStateValueProp($state, [
            "passwordInput",
            "errorMessage"
          ])}
          inputAutoComplete={"current-password"}
          inputName={"Password"}
          inputTabIndex={2}
          inputType={"password"}
          inputValue={generateStateValueProp($state, [
            "passwordInput",
            "value"
          ])}
          onErrorMessageChange={async (...eventArgs: any) => {
            generateStateOnChangeProp($state, [
              "passwordInput",
              "errorMessage"
            ]).apply(null, eventArgs);

            if (
              eventArgs.length > 1 &&
              eventArgs[1] &&
              eventArgs[1]._plasmic_state_init_
            ) {
              return;
            }
          }}
          onInputValueChange={async (...eventArgs: any) => {
            generateStateOnChangeProp($state, ["passwordInput", "value"]).apply(
              null,
              eventArgs
            );

            if (
              eventArgs.length > 1 &&
              eventArgs[1] &&
              eventArgs[1]._plasmic_state_init_
            ) {
              return;
            }
          }}
          required={true}
        />

        <Stack__
          as={"div"}
          data-plasmic-name={"nameBoxes"}
          data-plasmic-override={overrides.nameBoxes}
          hasGap={true}
          className={classNames(projectcss.all, sty.nameBoxes, {
            [sty.nameBoxesisSignUp]: hasVariant($state, "isSignUp", "isSignUp")
          })}
        >
          <SubcomponentTextInput
            data-plasmic-name={"firstNameInput"}
            data-plasmic-override={overrides.firstNameInput}
            className={classNames("__wab_instance", sty.firstNameInput, {
              [sty.firstNameInputisSignUp]: hasVariant(
                $state,
                "isSignUp",
                "isSignUp"
              )
            })}
            errorMessage={generateStateValueProp($state, [
              "firstNameInput",
              "errorMessage"
            ])}
            inputAutoComplete={"given-name"}
            inputName={"First Name"}
            inputTabIndex={2}
            inputType={"text"}
            inputValue={generateStateValueProp($state, [
              "firstNameInput",
              "value"
            ])}
            onErrorMessageChange={async (...eventArgs: any) => {
              generateStateOnChangeProp($state, [
                "firstNameInput",
                "errorMessage"
              ]).apply(null, eventArgs);

              if (
                eventArgs.length > 1 &&
                eventArgs[1] &&
                eventArgs[1]._plasmic_state_init_
              ) {
                return;
              }
            }}
            onInputValueChange={async (...eventArgs: any) => {
              generateStateOnChangeProp($state, [
                "firstNameInput",
                "value"
              ]).apply(null, eventArgs);

              if (
                eventArgs.length > 1 &&
                eventArgs[1] &&
                eventArgs[1]._plasmic_state_init_
              ) {
                return;
              }
            }}
          />

          {(hasVariant($state, "isSignUp", "isSignUp") ? true : false) ? (
            <SubcomponentTextInput
              data-plasmic-name={"lastNameInput"}
              data-plasmic-override={overrides.lastNameInput}
              className={classNames("__wab_instance", sty.lastNameInput, {
                [sty.lastNameInputisSignUp]: hasVariant(
                  $state,
                  "isSignUp",
                  "isSignUp"
                )
              })}
              errorMessage={generateStateValueProp($state, [
                "lastNameInput",
                "errorMessage"
              ])}
              inputAutoComplete={"family-name"}
              inputName={"Last Name"}
              inputTabIndex={3}
              inputType={"text"}
              inputValue={generateStateValueProp($state, [
                "lastNameInput",
                "value"
              ])}
              onErrorMessageChange={async (...eventArgs: any) => {
                generateStateOnChangeProp($state, [
                  "lastNameInput",
                  "errorMessage"
                ]).apply(null, eventArgs);

                if (
                  eventArgs.length > 1 &&
                  eventArgs[1] &&
                  eventArgs[1]._plasmic_state_init_
                ) {
                  return;
                }
              }}
              onInputValueChange={async (...eventArgs: any) => {
                generateStateOnChangeProp($state, [
                  "lastNameInput",
                  "value"
                ]).apply(null, eventArgs);

                if (
                  eventArgs.length > 1 &&
                  eventArgs[1] &&
                  eventArgs[1]._plasmic_state_init_
                ) {
                  return;
                }
              }}
            />
          ) : null}
        </Stack__>
        {(hasVariant($state, "isSignUp", "isSignUp") ? true : false) ? (
          <SubcomponentTextInput
            data-plasmic-name={"passwordCreationInput"}
            data-plasmic-override={overrides.passwordCreationInput}
            className={classNames("__wab_instance", sty.passwordCreationInput, {
              [sty.passwordCreationInputisSignUp]: hasVariant(
                $state,
                "isSignUp",
                "isSignUp"
              )
            })}
            errorMessage={generateStateValueProp($state, [
              "passwordCreationInput",
              "errorMessage"
            ])}
            inputAutoComplete={"new-password"}
            inputName={"New Password"}
            inputTabIndex={4}
            inputType={"password"}
            inputValue={generateStateValueProp($state, [
              "passwordCreationInput",
              "value"
            ])}
            onErrorMessageChange={async (...eventArgs: any) => {
              generateStateOnChangeProp($state, [
                "passwordCreationInput",
                "errorMessage"
              ]).apply(null, eventArgs);

              if (
                eventArgs.length > 1 &&
                eventArgs[1] &&
                eventArgs[1]._plasmic_state_init_
              ) {
                return;
              }
            }}
            onInputValueChange={async (...eventArgs: any) => {
              generateStateOnChangeProp($state, [
                "passwordCreationInput",
                "value"
              ]).apply(null, eventArgs);

              if (
                eventArgs.length > 1 &&
                eventArgs[1] &&
                eventArgs[1]._plasmic_state_init_
              ) {
                return;
              }
            }}
          />
        ) : null}
        {(hasVariant($state, "isSignUp", "isSignUp") ? true : false) ? (
          <SubcomponentTextInput
            data-plasmic-name={"passwordConfirmationInput"}
            data-plasmic-override={overrides.passwordConfirmationInput}
            className={classNames(
              "__wab_instance",
              sty.passwordConfirmationInput,
              {
                [sty.passwordConfirmationInputisSignUp]: hasVariant(
                  $state,
                  "isSignUp",
                  "isSignUp"
                )
              }
            )}
            errorMessage={generateStateValueProp($state, [
              "passwordConfirmationInput",
              "errorMessage"
            ])}
            inputAutoComplete={"new-password"}
            inputName={"Confirm Password"}
            inputTabIndex={5}
            inputType={"password"}
            inputValue={generateStateValueProp($state, [
              "passwordConfirmationInput",
              "value"
            ])}
            onErrorMessageChange={async (...eventArgs: any) => {
              generateStateOnChangeProp($state, [
                "passwordConfirmationInput",
                "errorMessage"
              ]).apply(null, eventArgs);

              if (
                eventArgs.length > 1 &&
                eventArgs[1] &&
                eventArgs[1]._plasmic_state_init_
              ) {
                return;
              }
            }}
            onInputValueChange={async (...eventArgs: any) => {
              generateStateOnChangeProp($state, [
                "passwordConfirmationInput",
                "value"
              ]).apply(null, eventArgs);

              if (
                eventArgs.length > 1 &&
                eventArgs[1] &&
                eventArgs[1]._plasmic_state_init_
              ) {
                return;
              }
            }}
          />
        ) : null}
        <div
          data-plasmic-name={"errorText"}
          data-plasmic-override={overrides.errorText}
          className={classNames(
            projectcss.all,
            projectcss.__wab_text,
            sty.errorText,
            {
              [sty.errorTextisLoading]: hasVariant(
                $state,
                "isLoading",
                "isLoading"
              ),
              [sty.errorTextisSignUp]: hasVariant(
                $state,
                "isSignUp",
                "isSignUp"
              )
            }
          )}
        >
          <React.Fragment>
            {(() => {
              try {
                return $state.errorResponse;
              } catch (e) {
                if (
                  e instanceof TypeError ||
                  e?.plasmicType === "PlasmicUndefinedDataError"
                ) {
                  return "";
                }
                throw e;
              }
            })()}
          </React.Fragment>
        </div>
        <SubcomponentButton
          data-plasmic-name={"submitButton"}
          data-plasmic-override={overrides.submitButton}
          className={classNames("__wab_instance", sty.submitButton, {
            [sty.submitButtonisLoading]: hasVariant(
              $state,
              "isLoading",
              "isLoading"
            ),
            [sty.submitButtonisSignUp]: hasVariant(
              $state,
              "isSignUp",
              "isSignUp"
            ),
            [sty.submitButtonisSignUp_isLoading]:
              hasVariant($state, "isLoading", "isLoading") &&
              hasVariant($state, "isSignUp", "isSignUp")
          })}
          endIcon={
            <svg
              className={classNames(projectcss.all, sty.svg__oGgeK)}
              role={"img"}
            />
          }
          onClick={async event => {
            const $steps = {};

            $steps["updateErrorResponse"] = true
              ? (() => {
                  const actionArgs = {
                    variable: {
                      objRoot: $state,
                      variablePath: ["errorResponse"]
                    },
                    operation: 0
                  };
                  return (({ variable, value, startIndex, deleteCount }) => {
                    if (!variable) {
                      return;
                    }
                    const { objRoot, variablePath } = variable;

                    $stateSet(objRoot, variablePath, value);
                    return value;
                  })?.apply(null, [actionArgs]);
                })()
              : undefined;
            if (
              $steps["updateErrorResponse"] != null &&
              typeof $steps["updateErrorResponse"] === "object" &&
              typeof $steps["updateErrorResponse"].then === "function"
            ) {
              $steps["updateErrorResponse"] = await $steps[
                "updateErrorResponse"
              ];
            }
          }}
          startIcon={
            <svg
              className={classNames(projectcss.all, sty.svg__hsRrM)}
              role={"img"}
            />
          }
        >
          <div
            data-plasmic-name={"text"}
            data-plasmic-override={overrides.text}
            className={classNames(
              projectcss.all,
              projectcss.__wab_text,
              sty.text,
              {
                [sty.textisLoading]: hasVariant(
                  $state,
                  "isLoading",
                  "isLoading"
                ),
                [sty.textisSignUp]: hasVariant($state, "isSignUp", "isSignUp")
              }
            )}
          >
            {hasVariant($state, "isSignUp", "isSignUp")
              ? "Create Account"
              : "Login"}
          </div>
          <LoadingDotsIcon
            className={classNames(projectcss.all, sty.svg__kaHgv, {
              [sty.svgisLoading__kaHgVphA88]: hasVariant(
                $state,
                "isLoading",
                "isLoading"
              )
            })}
            role={"img"}
          />
        </SubcomponentButton>
      </Stack__>
      <button
        data-plasmic-name={"swapButton"}
        data-plasmic-override={overrides.swapButton}
        className={classNames(
          projectcss.all,
          projectcss.button,
          projectcss.__wab_text,
          sty.swapButton,
          {
            [sty.swapButtonisLoading]: hasVariant(
              $state,
              "isLoading",
              "isLoading"
            ),
            [sty.swapButtonisSignUp]: hasVariant(
              $state,
              "isSignUp",
              "isSignUp"
            ),
            [sty.swapButtonisSignUp_isLoading]:
              hasVariant($state, "isLoading", "isLoading") &&
              hasVariant($state, "isSignUp", "isSignUp")
          }
        )}
        disabled={hasVariant($state, "isLoading", "isLoading") ? true : false}
        onClick={async event => {
          const $steps = {};

          $steps["updateIsSignUp"] = true
            ? (() => {
                const actionArgs = { vgroup: "isSignUp", operation: 2 };
                return (({ vgroup, value }) => {
                  if (typeof value === "string") {
                    value = [value];
                  }

                  const oldValue = $stateGet($state, vgroup);
                  $stateSet($state, vgroup, !oldValue);
                  return !oldValue;
                })?.apply(null, [actionArgs]);
              })()
            : undefined;
          if (
            $steps["updateIsSignUp"] != null &&
            typeof $steps["updateIsSignUp"] === "object" &&
            typeof $steps["updateIsSignUp"].then === "function"
          ) {
            $steps["updateIsSignUp"] = await $steps["updateIsSignUp"];
          }
        }}
        ref={ref => {
          $refs["swapButton"] = ref;
        }}
      >
        {hasVariant($state, "isSignUp", "isSignUp") ? (
          <React.Fragment>
            <React.Fragment>{"Already have an account?\n"}</React.Fragment>
            <span
              className={"plasmic_default__all plasmic_default__span"}
              style={{ textDecorationLine: "underline" }}
            >
              {"Log In"}
            </span>
          </React.Fragment>
        ) : (
          <React.Fragment>
            <React.Fragment>{"Don't have an account yet?\n"}</React.Fragment>
            <span
              className={"plasmic_default__all plasmic_default__span"}
              style={{ textDecorationLine: "underline" }}
            >
              {"Sign Up"}
            </span>
          </React.Fragment>
        )}
      </button>
    </Stack__>
  ) as React.ReactElement | null;
}

const PlasmicDescendants = {
  root: [
    "root",
    "logoStack",
    "form",
    "emailInput",
    "passwordInput",
    "nameBoxes",
    "firstNameInput",
    "lastNameInput",
    "passwordCreationInput",
    "passwordConfirmationInput",
    "errorText",
    "submitButton",
    "text",
    "swapButton"
  ],
  logoStack: ["logoStack"],
  form: [
    "form",
    "emailInput",
    "passwordInput",
    "nameBoxes",
    "firstNameInput",
    "lastNameInput",
    "passwordCreationInput",
    "passwordConfirmationInput",
    "errorText",
    "submitButton",
    "text"
  ],
  emailInput: ["emailInput"],
  passwordInput: ["passwordInput"],
  nameBoxes: ["nameBoxes", "firstNameInput", "lastNameInput"],
  firstNameInput: ["firstNameInput"],
  lastNameInput: ["lastNameInput"],
  passwordCreationInput: ["passwordCreationInput"],
  passwordConfirmationInput: ["passwordConfirmationInput"],
  errorText: ["errorText"],
  submitButton: ["submitButton", "text"],
  text: ["text"],
  swapButton: ["swapButton"]
} as const;
type NodeNameType = keyof typeof PlasmicDescendants;
type DescendantsType<T extends NodeNameType> =
  (typeof PlasmicDescendants)[T][number];
type NodeDefaultElementType = {
  root: "div";
  logoStack: typeof LogoStack;
  form: "form";
  emailInput: typeof SubcomponentTextInput;
  passwordInput: typeof SubcomponentTextInput;
  nameBoxes: "div";
  firstNameInput: typeof SubcomponentTextInput;
  lastNameInput: typeof SubcomponentTextInput;
  passwordCreationInput: typeof SubcomponentTextInput;
  passwordConfirmationInput: typeof SubcomponentTextInput;
  errorText: "div";
  submitButton: typeof SubcomponentButton;
  text: "div";
  swapButton: "button";
};

type ReservedPropsType = "variants" | "args" | "overrides";
type NodeOverridesType<T extends NodeNameType> = Pick<
  PlasmicLoginLoginModal__OverridesType,
  DescendantsType<T>
>;
type NodeComponentProps<T extends NodeNameType> =
  // Explicitly specify variants, args, and overrides as objects
  {
    variants?: PlasmicLoginLoginModal__VariantsArgs;
    args?: PlasmicLoginLoginModal__ArgsType;
    overrides?: NodeOverridesType<T>;
  } & Omit<PlasmicLoginLoginModal__VariantsArgs, ReservedPropsType> & // Specify variants directly as props
    /* Specify args directly as props*/ Omit<
      PlasmicLoginLoginModal__ArgsType,
      ReservedPropsType
    > &
    /* Specify overrides for each element directly as props*/ Omit<
      NodeOverridesType<T>,
      ReservedPropsType | VariantPropType | ArgPropType
    > &
    /* Specify props for the root element*/ Omit<
      Partial<React.ComponentProps<NodeDefaultElementType[T]>>,
      ReservedPropsType | VariantPropType | ArgPropType | DescendantsType<T>
    >;

function makeNodeComponent<NodeName extends NodeNameType>(nodeName: NodeName) {
  type PropsType = NodeComponentProps<NodeName> & { key?: React.Key };
  const func = function <T extends PropsType>(
    props: T & StrictProps<T, PropsType>
  ) {
    const { variants, args, overrides } = React.useMemo(
      () =>
        deriveRenderOpts(props, {
          name: nodeName,
          descendantNames: PlasmicDescendants[nodeName],
          internalArgPropNames: PlasmicLoginLoginModal__ArgProps,
          internalVariantPropNames: PlasmicLoginLoginModal__VariantProps
        }),
      [props, nodeName]
    );
    return PlasmicLoginLoginModal__RenderFunc({
      variants,
      args,
      overrides,
      forNode: nodeName
    });
  };
  if (nodeName === "root") {
    func.displayName = "PlasmicLoginLoginModal";
  } else {
    func.displayName = `PlasmicLoginLoginModal.${nodeName}`;
  }
  return func;
}

export const PlasmicLoginLoginModal = Object.assign(
  // Top-level PlasmicLoginLoginModal renders the root element
  makeNodeComponent("root"),
  {
    // Helper components rendering sub-elements
    logoStack: makeNodeComponent("logoStack"),
    form: makeNodeComponent("form"),
    emailInput: makeNodeComponent("emailInput"),
    passwordInput: makeNodeComponent("passwordInput"),
    nameBoxes: makeNodeComponent("nameBoxes"),
    firstNameInput: makeNodeComponent("firstNameInput"),
    lastNameInput: makeNodeComponent("lastNameInput"),
    passwordCreationInput: makeNodeComponent("passwordCreationInput"),
    passwordConfirmationInput: makeNodeComponent("passwordConfirmationInput"),
    errorText: makeNodeComponent("errorText"),
    submitButton: makeNodeComponent("submitButton"),
    text: makeNodeComponent("text"),
    swapButton: makeNodeComponent("swapButton"),

    // Metadata about props expected for PlasmicLoginLoginModal
    internalVariantProps: PlasmicLoginLoginModal__VariantProps,
    internalArgProps: PlasmicLoginLoginModal__ArgProps
  }
);

export default PlasmicLoginLoginModal;
/* prettier-ignore-end */
