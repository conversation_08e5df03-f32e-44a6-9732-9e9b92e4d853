/* eslint-disable */
/* tslint:disable */
// @ts-nocheck
/* prettier-ignore-start */

/** @jsxRuntime classic */
/** @jsx createPlasmicElementProxy */
/** @jsxFrag React.Fragment */

// This class is auto-generated by Plasmic; please do not edit!
// Plasmic Project: nZeWKcn21KijvC7eT8B3c7
// Component: 8ZiaPQ4apvBM

"use client";

import * as React from "react";

import Head from "next/head";
import Link, { LinkProps } from "next/link";
import { useRouter } from "next/navigation";

import {
  Flex as Flex__,
  MultiChoiceArg,
  PlasmicDataSourceContextProvider as PlasmicDataSourceContextProvider__,
  PlasmicIcon as PlasmicIcon__,
  PlasmicImg as PlasmicImg__,
  PlasmicLink as PlasmicLink__,
  PlasmicPageGuard as PlasmicPageGuard__,
  SingleBooleanChoiceArg,
  SingleChoiceArg,
  Stack as Stack__,
  StrictProps,
  Trans as Trans__,
  classNames,
  createPlasmicElementProxy,
  deriveRenderOpts,
  ensureGlobalVariants,
  generateOnMutateForSpec,
  generateStateOnChangeProp,
  generateStateOnChangePropForCodeComponents,
  generateStateValueProp,
  get as $stateGet,
  hasVariant,
  initializeCodeComponentStates,
  initializePlasmicStates,
  makeFragment,
  omit,
  pick,
  renderPlasmicSlot,
  set as $stateSet,
  useCurrentUser,
  useDollarState,
  usePlasmicTranslator,
  useTrigger,
  wrapWithClassName
} from "@plasmicapp/react-web";
import {
  DataCtxReader as DataCtxReader__,
  useDataEnv,
  useGlobalActions
} from "@plasmicapp/host";

import "@plasmicapp/react-web/lib/plasmic.css";

import projectcss from "./plasmic.module.css"; // plasmic-import: nZeWKcn21KijvC7eT8B3c7/projectcss
import sty from "./PlasmicNavigationLogoStack.module.css"; // plasmic-import: 8ZiaPQ4apvBM/css

createPlasmicElementProxy;

export type PlasmicNavigationLogoStack__VariantMembers = {
  collapsed: "collapsed";
  test: "test";
};
export type PlasmicNavigationLogoStack__VariantsArgs = {
  collapsed?: SingleBooleanChoiceArg<"collapsed">;
  test?: SingleBooleanChoiceArg<"test">;
};
type VariantPropType = keyof PlasmicNavigationLogoStack__VariantsArgs;
export const PlasmicNavigationLogoStack__VariantProps =
  new Array<VariantPropType>("collapsed", "test");

export type PlasmicNavigationLogoStack__ArgsType = {};
type ArgPropType = keyof PlasmicNavigationLogoStack__ArgsType;
export const PlasmicNavigationLogoStack__ArgProps = new Array<ArgPropType>();

export type PlasmicNavigationLogoStack__OverridesType = {
  logoStack?: Flex__<"div">;
  img?: Flex__<typeof PlasmicImg__>;
  text?: Flex__<"div">;
};

export interface DefaultNavigationLogoStackProps {
  collapsed?: SingleBooleanChoiceArg<"collapsed">;
  test?: SingleBooleanChoiceArg<"test">;
  className?: string;
}

const $$ = {};

function useNextRouter() {
  try {
    return useRouter();
  } catch {}
  return undefined;
}

function PlasmicNavigationLogoStack__RenderFunc(props: {
  variants: PlasmicNavigationLogoStack__VariantsArgs;
  args: PlasmicNavigationLogoStack__ArgsType;
  overrides: PlasmicNavigationLogoStack__OverridesType;
  forNode?: string;
}) {
  const { variants, overrides, forNode } = props;

  const args = React.useMemo(
    () =>
      Object.assign(
        {},
        Object.fromEntries(
          Object.entries(props.args).filter(([_, v]) => v !== undefined)
        )
      ),
    [props.args]
  );

  const $props = {
    ...args,
    ...variants
  };

  const __nextRouter = useNextRouter();
  const $ctx = useDataEnv?.() || {};
  const refsRef = React.useRef({});
  const $refs = refsRef.current;

  let [$queries, setDollarQueries] = React.useState<
    Record<string, ReturnType<typeof usePlasmicDataOp>>
  >({});
  const stateSpecs: Parameters<typeof useDollarState>[0] = React.useMemo(
    () => [
      {
        path: "collapsed",
        type: "private",
        variableType: "variant",
        initFunc: ({ $props, $state, $queries, $ctx }) => $props.collapsed
      },
      {
        path: "test",
        type: "private",
        variableType: "variant",
        initFunc: ({ $props, $state, $queries, $ctx }) => $props.test
      }
    ],
    [$props, $ctx, $refs]
  );
  const $state = useDollarState(stateSpecs, {
    $props,
    $ctx,
    $queries: $queries,
    $refs
  });

  const new$Queries: Record<string, ReturnType<typeof usePlasmicDataOp>> = {};
  if (Object.keys(new$Queries).some(k => new$Queries[k] !== $queries[k])) {
    setDollarQueries(new$Queries);

    $queries = new$Queries;
  }

  return (
    <div
      data-plasmic-name={"logoStack"}
      data-plasmic-override={overrides.logoStack}
      data-plasmic-root={true}
      data-plasmic-for-node={forNode}
      className={classNames(
        projectcss.all,
        projectcss.root_reset,
        projectcss.plasmic_default_styles,
        projectcss.plasmic_mixins,
        projectcss.plasmic_tokens,
        sty.logoStack,
        {
          [sty.logoStackcollapsed]: hasVariant(
            $state,
            "collapsed",
            "collapsed"
          ),
          [sty.logoStacktest]: hasVariant($state, "test", "test")
        }
      )}
    >
      <PlasmicImg__
        data-plasmic-name={"img"}
        data-plasmic-override={overrides.img}
        alt={""}
        className={classNames(sty.img, {
          [sty.imgcollapsed]: hasVariant($state, "collapsed", "collapsed"),
          [sty.imgtest]: hasVariant($state, "test", "test")
        })}
        displayHeight={"53px"}
        displayMaxHeight={"none"}
        displayMaxWidth={"none"}
        displayMinHeight={"53px"}
        displayMinWidth={"40px"}
        displayWidth={"40px"}
        loading={"eager"}
        src={{
          src: "/plasmic/senergy_platform/images/placeholderLogoSmSvg.svg",
          fullWidth: 149,
          fullHeight: 150,
          aspectRatio: 0.991071
        }}
      />

      <div
        data-plasmic-name={"text"}
        data-plasmic-override={overrides.text}
        className={classNames(projectcss.all, projectcss.__wab_text, sty.text, {
          [sty.textcollapsed]: hasVariant($state, "collapsed", "collapsed"),
          [sty.texttest]: hasVariant($state, "test", "test")
        })}
      >
        {"SENERGY\n.WORKS"}
      </div>
    </div>
  ) as React.ReactElement | null;
}

const PlasmicDescendants = {
  logoStack: ["logoStack", "img", "text"],
  img: ["img"],
  text: ["text"]
} as const;
type NodeNameType = keyof typeof PlasmicDescendants;
type DescendantsType<T extends NodeNameType> =
  (typeof PlasmicDescendants)[T][number];
type NodeDefaultElementType = {
  logoStack: "div";
  img: typeof PlasmicImg__;
  text: "div";
};

type ReservedPropsType = "variants" | "args" | "overrides";
type NodeOverridesType<T extends NodeNameType> = Pick<
  PlasmicNavigationLogoStack__OverridesType,
  DescendantsType<T>
>;
type NodeComponentProps<T extends NodeNameType> =
  // Explicitly specify variants, args, and overrides as objects
  {
    variants?: PlasmicNavigationLogoStack__VariantsArgs;
    args?: PlasmicNavigationLogoStack__ArgsType;
    overrides?: NodeOverridesType<T>;
  } & Omit<PlasmicNavigationLogoStack__VariantsArgs, ReservedPropsType> & // Specify variants directly as props
    /* Specify args directly as props*/ Omit<
      PlasmicNavigationLogoStack__ArgsType,
      ReservedPropsType
    > &
    /* Specify overrides for each element directly as props*/ Omit<
      NodeOverridesType<T>,
      ReservedPropsType | VariantPropType | ArgPropType
    > &
    /* Specify props for the root element*/ Omit<
      Partial<React.ComponentProps<NodeDefaultElementType[T]>>,
      ReservedPropsType | VariantPropType | ArgPropType | DescendantsType<T>
    >;

function makeNodeComponent<NodeName extends NodeNameType>(nodeName: NodeName) {
  type PropsType = NodeComponentProps<NodeName> & { key?: React.Key };
  const func = function <T extends PropsType>(
    props: T & StrictProps<T, PropsType>
  ) {
    const { variants, args, overrides } = React.useMemo(
      () =>
        deriveRenderOpts(props, {
          name: nodeName,
          descendantNames: PlasmicDescendants[nodeName],
          internalArgPropNames: PlasmicNavigationLogoStack__ArgProps,
          internalVariantPropNames: PlasmicNavigationLogoStack__VariantProps
        }),
      [props, nodeName]
    );
    return PlasmicNavigationLogoStack__RenderFunc({
      variants,
      args,
      overrides,
      forNode: nodeName
    });
  };
  if (nodeName === "logoStack") {
    func.displayName = "PlasmicNavigationLogoStack";
  } else {
    func.displayName = `PlasmicNavigationLogoStack.${nodeName}`;
  }
  return func;
}

export const PlasmicNavigationLogoStack = Object.assign(
  // Top-level PlasmicNavigationLogoStack renders the root element
  makeNodeComponent("logoStack"),
  {
    // Helper components rendering sub-elements
    img: makeNodeComponent("img"),
    text: makeNodeComponent("text"),

    // Metadata about props expected for PlasmicNavigationLogoStack
    internalVariantProps: PlasmicNavigationLogoStack__VariantProps,
    internalArgProps: PlasmicNavigationLogoStack__ArgProps
  }
);

export default PlasmicNavigationLogoStack;
/* prettier-ignore-end */
