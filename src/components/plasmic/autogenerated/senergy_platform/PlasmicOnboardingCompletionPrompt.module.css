.formattingContainer {
  box-shadow: 0px 4px 4px 0px var(--token-p09LDPmbF81_);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start;
  width: 100%;
  height: auto;
  max-width: 100%;
  position: relative;
  justify-self: flex-start;
  margin-bottom: 12px;
  background: var(--token-1AMvw6c2eIK7);
  grid-column-start: 3 !important;
  grid-column-end: -3 !important;
  padding: var(--token-M1l4keX1sfKm) var(--token-M1l4keX1sfKm) 55px;
  border: 1px solid var(--token-5_Q90hFZ9CmK);
}
.text__wDnWi {
  position: relative;
  width: auto;
  height: auto;
  max-width: 100%;
  font-size: var(--token-AMP1angFPBtf);
  text-align: center;
  margin-left: 30px;
  margin-right: 30px;
  margin-bottom: 30px;
  font-family: var(--token-z1yrQVi72Nj1);
}
.mainSectionButtons {
  display: flex;
  position: relative;
  flex-direction: row;
  width: 100%;
  height: auto;
  max-width: 100%;
  min-width: 0;
  padding: var(--token-j0qnbpah5w9U);
  margin: var(--token-sazGmnf7GWAk);
}
.mainSectionButtons > :global(.__wab_flex-container) {
  flex-direction: row;
  align-items: stretch;
  justify-content: center;
  flex-wrap: nowrap;
  align-content: stretch;
  min-width: 0;
  margin-top: calc(0px - var(--token-M1cOQzpe0467));
  height: calc(100% + var(--token-M1cOQzpe0467));
}
.mainSectionButtons > :global(.__wab_flex-container) > *,
.mainSectionButtons > :global(.__wab_flex-container) > :global(.__wab_slot) > *,
.mainSectionButtons > :global(.__wab_flex-container) > picture > img,
.mainSectionButtons
  > :global(.__wab_flex-container)
  > :global(.__wab_slot)
  > picture
  > img {
  margin-top: var(--token-M1cOQzpe0467);
}
.profileBanner:global(.__wab_instance) {
  max-width: 100%;
  position: relative;
  flex-shrink: 0;
  display: none;
}
.svg__uwlry {
  position: relative;
  object-fit: cover;
  max-width: 100%;
  width: 100px;
  height: 100px;
}
.text__p8YGn {
  position: relative;
  width: auto;
  height: auto;
  max-width: 100%;
  padding-right: 0px;
  font-family: var(--token-z1yrQVi72Nj1);
}
.introduction:global(.__wab_instance) {
  max-width: 100%;
  position: relative;
  margin-top: calc(10px + var(--token-M1cOQzpe0467)) !important;
  flex-shrink: 0;
}
.introductionsectionOnboarding_optionSelected:global(.__wab_instance) {
  margin-top: calc(10px + var(--token-M1cOQzpe0467)) !important;
  flex-shrink: 0;
}
.svg__gwW2E {
  position: relative;
  object-fit: cover;
  max-width: 100%;
  width: 100px;
  height: 100px;
}
.text__bYdD {
  position: relative;
  width: auto;
  height: auto;
  max-width: 100%;
  padding-right: 0px;
  font-family: var(--token-z1yrQVi72Nj1);
}
.caseStudy:global(.__wab_instance) {
  max-width: 100%;
  position: relative;
  margin-top: calc(10px + var(--token-M1cOQzpe0467)) !important;
  margin-left: 30px;
  flex-shrink: 0;
}
.svg__suXy {
  position: relative;
  object-fit: cover;
  max-width: 100%;
  width: 100px;
  height: 100px;
}
.text__syE9N {
  position: relative;
  width: auto;
  height: auto;
  max-width: 100%;
  font-family: var(--token-z1yrQVi72Nj1);
  font-size: var(--token-2UEfYzPsoOY0);
}
.scrollingOptions {
  display: flex;
  position: relative;
  flex-direction: row;
  width: 100%;
  height: auto;
  max-width: 100%;
  overflow: auto;
  padding-left: 10px;
  padding-bottom: 10px;
  padding-top: 10px;
  margin-left: 20px;
  min-width: 0;
  border-left: 1px solid var(--token-PJrtmRUMFAJb);
}
.scrollingOptions > :global(.__wab_flex-container) {
  flex-direction: row;
  align-items: center;
  justify-content: flex-start;
  min-width: 0;
  margin-left: calc(0px - var(--token-M1l4keX1sfKm));
  width: calc(100% + var(--token-M1l4keX1sfKm));
}
.scrollingOptions > :global(.__wab_flex-container) > *,
.scrollingOptions > :global(.__wab_flex-container) > :global(.__wab_slot) > *,
.scrollingOptions > :global(.__wab_flex-container) > picture > img,
.scrollingOptions
  > :global(.__wab_flex-container)
  > :global(.__wab_slot)
  > picture
  > img {
  margin-left: var(--token-M1l4keX1sfKm);
}
.educationButton2:global(.__wab_instance) {
  max-width: 100%;
  margin-left: calc(0px + var(--token-M1l4keX1sfKm)) !important;
  flex-shrink: 0;
}
.svg__ksPs {
  position: relative;
  object-fit: cover;
  max-width: 100%;
  width: 100px;
  height: 100px;
}
.text__nceaG {
  position: relative;
  width: auto;
  height: auto;
  max-width: 100%;
  font-family: var(--token-z1yrQVi72Nj1);
}
.experienceButton:global(.__wab_instance) {
  max-width: 100%;
  position: relative;
  flex-shrink: 0;
}
.experienceButtonsectionOnboarding_optionSelected:global(.__wab_instance) {
  flex-shrink: 0;
}
.svg__uWgOp {
  position: relative;
  object-fit: cover;
  max-width: 100%;
  width: 100px;
  height: 100px;
}
.text__lTjbi {
  position: relative;
  width: auto;
  height: auto;
  max-width: 100%;
  padding-right: 0px;
  font-family: var(--token-z1yrQVi72Nj1);
}
.toolsButton:global(.__wab_instance) {
  max-width: 100%;
  position: relative;
  flex-shrink: 0;
}
.toolsButtonsectionOnboarding_optionSelected:global(.__wab_instance) {
  flex-shrink: 0;
}
.svg__ixs3E {
  position: relative;
  object-fit: cover;
  max-width: 100%;
  width: 100px;
  height: 100px;
}
.text__q6Er7 {
  position: relative;
  width: auto;
  height: auto;
  max-width: 100%;
  padding-right: 0px;
  font-family: var(--token-z1yrQVi72Nj1);
}
.skillsButton:global(.__wab_instance) {
  max-width: 100%;
  position: relative;
  flex-shrink: 0;
}
.svg__g0Bgy {
  position: relative;
  object-fit: cover;
  max-width: 100%;
  width: 100px;
  height: 100px;
}
.text__rRmR2 {
  position: relative;
  width: auto;
  height: auto;
  max-width: 100%;
  padding-right: 0px;
  font-family: var(--token-z1yrQVi72Nj1);
}
.languagesButton:global(.__wab_instance) {
  max-width: 100%;
  position: relative;
  flex-shrink: 0;
}
.svg__fN0Kd {
  position: relative;
  object-fit: cover;
  max-width: 100%;
  width: 100px;
  height: 100px;
}
.text__hskqN {
  position: relative;
  width: auto;
  height: auto;
  max-width: 100%;
  padding-right: 0px;
  font-family: var(--token-z1yrQVi72Nj1);
}
.patentsButton:global(.__wab_instance) {
  max-width: 100%;
  position: relative;
  flex-shrink: 0;
}
.svg__p7Ibt {
  position: relative;
  object-fit: cover;
  max-width: 100%;
  width: 100px;
  height: 100px;
}
.text___7PMtU {
  position: relative;
  width: auto;
  height: auto;
  max-width: 100%;
  padding-bottom: 0px;
  font-family: var(--token-z1yrQVi72Nj1);
}
.publicationsButton:global(.__wab_instance) {
  max-width: 100%;
  position: relative;
  flex-shrink: 0;
}
.svg___0FRm5 {
  position: relative;
  object-fit: cover;
  max-width: 100%;
  width: 100px;
  height: 100px;
}
.text__iw92K {
  position: relative;
  width: auto;
  height: auto;
  max-width: 100%;
  font-family: var(--token-z1yrQVi72Nj1);
}
.certificationsButton:global(.__wab_instance) {
  max-width: 100%;
  position: relative;
  flex-shrink: 0;
}
.svg__vHrl {
  position: relative;
  object-fit: cover;
  max-width: 100%;
  width: 100px;
  height: 100px;
}
.text__pXmTw {
  position: relative;
  width: auto;
  height: auto;
  max-width: 100%;
  font-family: var(--token-z1yrQVi72Nj1);
}
.licensesButton:global(.__wab_instance) {
  max-width: 100%;
  position: relative;
  flex-shrink: 0;
}
.svg__nUxZb {
  position: relative;
  object-fit: cover;
  max-width: 100%;
  width: 100px;
  height: 100px;
}
.text__nst7H {
  position: relative;
  width: auto;
  height: auto;
  max-width: 100%;
  font-family: var(--token-z1yrQVi72Nj1);
}
.trademarksButton:global(.__wab_instance) {
  max-width: 100%;
  position: relative;
  flex-shrink: 0;
  display: none;
}
.svg__hn490 {
  position: relative;
  object-fit: cover;
  max-width: 100%;
  width: 100px;
  height: 100px;
}
.text__nVZzM {
  position: relative;
  width: auto;
  height: auto;
  max-width: 100%;
  padding-top: 0px;
  padding-bottom: 0px;
  font-family: var(--token-z1yrQVi72Nj1);
}
.seeMore:global(.__wab_instance) {
  max-width: 100%;
  position: relative;
  flex-shrink: 0;
  display: none;
}
.svg__k3Amc {
  position: relative;
  object-fit: cover;
  max-width: 100%;
  width: 100px;
  height: 100px;
  color: var(--token-XQQdLIBrONt7);
}
.text__vXze {
  position: relative;
  width: auto;
  height: auto;
  max-width: 100%;
  padding-right: 0px;
  font-family: var(--token-z1yrQVi72Nj1);
  color: var(--token-XQQdLIBrONt7);
}
.seeLessButton:global(.__wab_instance) {
  max-width: 100%;
  position: relative;
  flex-shrink: 0;
  display: none;
}
.svg__ithXd {
  position: relative;
  object-fit: cover;
  max-width: 100%;
  width: 100px;
  height: 100px;
  color: var(--token-v4jufhOu3lt9);
}
.text__msbTx {
  position: relative;
  width: auto;
  height: auto;
  max-width: 100%;
  padding-right: 0px;
  font-family: var(--token-z1yrQVi72Nj1);
  color: var(--token-v4jufhOu3lt9);
}
.completedSections {
  position: relative;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start;
  width: 100%;
  height: auto;
  max-width: 100%;
  min-width: 0;
  display: none;
  padding: var(--token-sazGmnf7GWAk);
  margin: var(--token-sazGmnf7GWAk);
}
.text__xLbR5 {
  padding-top: 24px;
  padding-bottom: 24px;
  padding-left: 0px;
  font-family: var(--token-z1yrQVi72Nj1);
  width: 100%;
  height: auto;
  max-width: 100%;
  font-size: var(--token-AMP1angFPBtf);
  min-width: 0;
}
.sectionsWithContent {
  display: flex;
  position: relative;
  flex-direction: row;
  width: 100%;
  height: auto;
  max-width: 100%;
  min-width: 0;
  padding: var(--token-sazGmnf7GWAk);
  margin: var(--token-sazGmnf7GWAk) var(--token-sazGmnf7GWAk) 30px;
}
.sectionsWithContent > :global(.__wab_flex-container) {
  flex-direction: row;
  align-items: stretch;
  justify-content: flex-start;
  flex-wrap: wrap;
  align-content: flex-start;
  min-width: 0;
  margin-left: calc(0px - var(--token-M1l4keX1sfKm));
  width: calc(100% + var(--token-M1l4keX1sfKm));
  margin-top: calc(0px - var(--token-M1l4keX1sfKm));
  height: calc(100% + var(--token-M1l4keX1sfKm));
}
.sectionsWithContent > :global(.__wab_flex-container) > *,
.sectionsWithContent
  > :global(.__wab_flex-container)
  > :global(.__wab_slot)
  > *,
.sectionsWithContent > :global(.__wab_flex-container) > picture > img,
.sectionsWithContent
  > :global(.__wab_flex-container)
  > :global(.__wab_slot)
  > picture
  > img {
  margin-left: var(--token-M1l4keX1sfKm);
  margin-top: var(--token-M1l4keX1sfKm);
}
.introductionButtonEntry:global(.__wab_instance) {
  max-width: 100%;
  position: relative;
  flex-shrink: 0;
}
.svg__rkK5Y {
  position: relative;
  object-fit: cover;
  max-width: 100%;
  width: 50px;
  height: 50px;
}
.text__uoy1A {
  position: relative;
  width: auto;
  height: auto;
  max-width: 100%;
  padding-right: 0px;
  font-family: var(--token-z1yrQVi72Nj1);
}
.educationButtonEntry:global(.__wab_instance) {
  max-width: 100%;
  position: relative;
  flex-shrink: 0;
}
.svg__yp2V8 {
  position: relative;
  object-fit: cover;
  max-width: 100%;
  width: 50px;
  height: 50px;
}
.text__zuDGh {
  position: relative;
  width: auto;
  height: auto;
  max-width: 100%;
  font-family: var(--token-z1yrQVi72Nj1);
}
.experienceButtonEntry:global(.__wab_instance) {
  max-width: 100%;
  position: relative;
  flex-shrink: 0;
}
.svg__es8Wx {
  position: relative;
  object-fit: cover;
  max-width: 100%;
  width: 50px;
  height: 50px;
}
.text__zjjO5 {
  position: relative;
  width: auto;
  height: auto;
  max-width: 100%;
  padding-right: 0px;
  font-family: var(--token-z1yrQVi72Nj1);
}
.skillsButtonEntry:global(.__wab_instance) {
  max-width: 100%;
  position: relative;
  flex-shrink: 0;
}
.svg__xlduR {
  position: relative;
  object-fit: cover;
  max-width: 100%;
  width: 50px;
  height: 50px;
}
.text__eHdVo {
  position: relative;
  width: auto;
  height: auto;
  max-width: 100%;
  padding-right: 0px;
  font-family: var(--token-z1yrQVi72Nj1);
}
.toolsButtonEntry:global(.__wab_instance) {
  max-width: 100%;
  position: relative;
  flex-shrink: 0;
}
.svg__vfsY {
  position: relative;
  object-fit: cover;
  max-width: 100%;
  width: 50px;
  height: 50px;
}
.text__cClDm {
  position: relative;
  width: auto;
  height: auto;
  max-width: 100%;
  padding-right: 0px;
  font-family: var(--token-z1yrQVi72Nj1);
}
.languagesButtonEntry:global(.__wab_instance) {
  max-width: 100%;
  position: relative;
  flex-shrink: 0;
}
.svg__ePhQ {
  position: relative;
  object-fit: cover;
  max-width: 100%;
  width: 50px;
  height: 50px;
}
.text___18ODu {
  position: relative;
  width: auto;
  height: auto;
  max-width: 100%;
  padding-right: 0px;
  font-family: var(--token-z1yrQVi72Nj1);
}
.publicationsButtonEntry:global(.__wab_instance) {
  max-width: 100%;
  position: relative;
  flex-shrink: 0;
}
.svg__na0HL {
  position: relative;
  object-fit: cover;
  max-width: 100%;
  width: 50px;
  height: 50px;
}
.text__pcTp1 {
  position: relative;
  width: auto;
  height: auto;
  max-width: 100%;
  font-family: var(--token-z1yrQVi72Nj1);
}
.licensesButtonEntry:global(.__wab_instance) {
  max-width: 100%;
  position: relative;
  flex-shrink: 0;
}
.svg__dKWs {
  position: relative;
  object-fit: cover;
  max-width: 100%;
  width: 50px;
  height: 50px;
}
.text__jQbaN {
  position: relative;
  width: auto;
  height: auto;
  max-width: 100%;
  font-family: var(--token-z1yrQVi72Nj1);
}
.certificationsButtonEntry:global(.__wab_instance) {
  max-width: 100%;
  position: relative;
  flex-shrink: 0;
}
.svg__yTQx {
  position: relative;
  object-fit: cover;
  max-width: 100%;
  width: 50px;
  height: 50px;
}
.text__z3Es {
  position: relative;
  width: auto;
  height: auto;
  max-width: 100%;
  font-family: var(--token-z1yrQVi72Nj1);
}
.patentsButtonEntry:global(.__wab_instance) {
  max-width: 100%;
  position: relative;
  flex-shrink: 0;
}
.svg__g2ViT {
  position: relative;
  object-fit: cover;
  max-width: 100%;
  width: 50px;
  height: 50px;
}
.text__fGt78 {
  position: relative;
  width: auto;
  height: auto;
  max-width: 100%;
  padding-bottom: 0px;
  font-family: var(--token-z1yrQVi72Nj1);
}
.trademarksButtonEntry:global(.__wab_instance) {
  max-width: 100%;
  position: relative;
  flex-shrink: 0;
}
.svg__y6Amh {
  position: relative;
  object-fit: cover;
  max-width: 100%;
  width: 50px;
  height: 50px;
}
.text__zyLl4 {
  position: relative;
  width: auto;
  height: auto;
  max-width: 100%;
  padding-top: 0px;
  padding-bottom: 0px;
  font-family: var(--token-z1yrQVi72Nj1);
}
