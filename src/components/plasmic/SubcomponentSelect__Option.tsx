import * as React from "react";
import {
  DefaultSubcomponentSelect__OptionProps,
  PlasmicSubcomponentSelect__Option
} from "./autogenerated/senergy_platform/PlasmicSubcomponentSelect__Option";

import { SelectOptionRef } from "@plasmicapp/react-web";

export interface SubcomponentSelect__OptionProps
  extends DefaultSubcomponentSelect__OptionProps {
  // Feel free to add any additional props that this component should receive
}
function SubcomponentSelect__Option_(
  props: SubcomponentSelect__OptionProps,
  ref: SelectOptionRef
) {
  const { plasmicProps } = PlasmicSubcomponentSelect__Option.useBehavior(
    props,
    ref
  );
  return <PlasmicSubcomponentSelect__Option {...plasmicProps} />;
}

const SubcomponentSelect__Option = React.forwardRef(
  SubcomponentSelect__Option_
);

export default Object.assign(
  SubcomponentSelect__Option,

  {
    __plumeType: "select-option"
  }
);
