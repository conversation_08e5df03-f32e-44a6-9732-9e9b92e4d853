/* eslint-disable */
/* tslint:disable */
// @ts-nocheck
/* prettier-ignore-start */

/** @jsxRuntime classic */
/** @jsx createPlasmicElementProxy */
/** @jsxFrag React.Fragment */

// This class is auto-generated by Plasmic; please do not edit!
// Plasmic Project: nZeWKcn21KijvC7eT8B3c7
// Component: HKIN54Ptec2v

"use client";

import * as React from "react";

import Head from "next/head";
import Link, { LinkProps } from "next/link";
import { useRouter } from "next/navigation";

import {
  Flex as Flex__,
  MultiChoiceArg,
  PlasmicDataSourceContextProvider as PlasmicDataSourceContextProvider__,
  PlasmicIcon as PlasmicIcon__,
  PlasmicImg as PlasmicImg__,
  PlasmicLink as PlasmicLink__,
  PlasmicPageGuard as PlasmicPageGuard__,
  SingleBooleanChoiceArg,
  SingleChoiceArg,
  Stack as Stack__,
  StrictProps,
  Trans as Trans__,
  classNames,
  createPlasmicElementProxy,
  deriveRenderOpts,
  ensureGlobalVariants,
  generateOnMutateForSpec,
  generateStateOnChangeProp,
  generateStateOnChangePropForCodeComponents,
  generateStateValueProp,
  get as $stateGet,
  hasVariant,
  initializeCodeComponentStates,
  initializePlasmicStates,
  makeFragment,
  omit,
  pick,
  renderPlasmicSlot,
  set as $stateSet,
  useCurrentUser,
  useDollarState,
  usePlasmicTranslator,
  useTrigger,
  wrapWithClassName
} from "@plasmicapp/react-web";
import {
  DataCtxReader as DataCtxReader__,
  useDataEnv,
  useGlobalActions
} from "@plasmicapp/host";

import SubcomponentTextInput from "../../SubcomponentTextInput"; // plasmic-import: Pt5FPuEzirSe/component
import SubcomponentIconWithText from "../../SubcomponentIconWithText"; // plasmic-import: _eFlbiSm6hZU/component
import SubcomponentCheckbox from "../../SubcomponentCheckbox"; // plasmic-import: OeFTSKgMult8/component
import SubcomponentDeleteButton from "../../SubcomponentDeleteButton"; // plasmic-import: WMfLdq1qoZyo/component

import "@plasmicapp/react-web/lib/plasmic.css";

import projectcss from "./plasmic.module.css"; // plasmic-import: nZeWKcn21KijvC7eT8B3c7/projectcss
import sty from "./PlasmicProfileTileExperiencesTile.module.css"; // plasmic-import: HKIN54Ptec2v/css

import BuildingIcon from "./icons/PlasmicIcon__Building"; // plasmic-import: RuQrxOj-BhnB/icon
import CalendarIcon from "./icons/PlasmicIcon__Calendar"; // plasmic-import: -xP51745fXLc/icon
import MapPinIcon from "./icons/PlasmicIcon__MapPin"; // plasmic-import: Q_wDLzMm5l2Y/icon
import ContractTypeIcon from "./icons/PlasmicIcon__ContractType"; // plasmic-import: 8BfErdfvqP2t/icon

createPlasmicElementProxy;

export type PlasmicProfileTileExperiencesTile__VariantMembers = {
  editable: "editable";
  overview: "overview";
};
export type PlasmicProfileTileExperiencesTile__VariantsArgs = {
  editable?: SingleBooleanChoiceArg<"editable">;
  overview?: SingleBooleanChoiceArg<"overview">;
};
type VariantPropType = keyof PlasmicProfileTileExperiencesTile__VariantsArgs;
export const PlasmicProfileTileExperiencesTile__VariantProps =
  new Array<VariantPropType>("editable", "overview");

export type PlasmicProfileTileExperiencesTile__ArgsType = {
  experienceId?: string;
  jobTitleInputValue?: string;
  companyNameInputValue?: string;
  locationInputValue?: string;
  startDateInputValue?: string;
  endDateInputValue?: string;
  presentStateInputValue?: boolean;
  descriptionInputValue?: string;
  onJobTitleInputValueChange?: (val: string) => void;
  onCompanyNameInputValueChange?: (val: string) => void;
  onLocationInputValueChange?: (val: string) => void;
  onStartDateInputValueChange?: (val: string) => void;
  onEndDateInputValueChange?: (val: string) => void;
  onPresentStateInputValueChange?: (val: boolean) => void;
  onDescriptionInputValueChange?: (val: string) => void;
  deleteButtonOnClick?: (event: any) => void;
  deleteButtonDisabled?: any;
  deleteButtonClickStage?: number;
  onDeleteButtonDisabledChange?: (val: any) => void;
  onDeleteButtonClickStageChange?: (val: number) => void;
};
type ArgPropType = keyof PlasmicProfileTileExperiencesTile__ArgsType;
export const PlasmicProfileTileExperiencesTile__ArgProps =
  new Array<ArgPropType>(
    "experienceId",
    "jobTitleInputValue",
    "companyNameInputValue",
    "locationInputValue",
    "startDateInputValue",
    "endDateInputValue",
    "presentStateInputValue",
    "descriptionInputValue",
    "onJobTitleInputValueChange",
    "onCompanyNameInputValueChange",
    "onLocationInputValueChange",
    "onStartDateInputValueChange",
    "onEndDateInputValueChange",
    "onPresentStateInputValueChange",
    "onDescriptionInputValueChange",
    "deleteButtonOnClick",
    "deleteButtonDisabled",
    "deleteButtonClickStage",
    "onDeleteButtonDisabledChange",
    "onDeleteButtonClickStageChange"
  );

export type PlasmicProfileTileExperiencesTile__OverridesType = {
  experiencesSpacingContainer?: Flex__<"div">;
  companyIcon?: Flex__<"svg">;
  informationStack?: Flex__<"div">;
  jobTitle?: Flex__<typeof SubcomponentTextInput>;
  companyName?: Flex__<typeof SubcomponentTextInput>;
  displayDatesBox?: Flex__<typeof SubcomponentIconWithText>;
  iconSpot4?: Flex__<"svg">;
  infoBar?: Flex__<"section">;
  locationInput?: Flex__<typeof SubcomponentIconWithText>;
  iconSpot2?: Flex__<"svg">;
  employmentTypeSelector?: Flex__<typeof SubcomponentIconWithText>;
  iconSpot?: Flex__<"svg">;
  startDateInput?: Flex__<typeof SubcomponentIconWithText>;
  iconSpot7?: Flex__<"svg">;
  endDateInput?: Flex__<typeof SubcomponentIconWithText>;
  iconSpot8?: Flex__<"svg">;
  presentStateInput?: Flex__<typeof SubcomponentCheckbox>;
  description?: Flex__<typeof SubcomponentTextInput>;
  subDeleteButton?: Flex__<typeof SubcomponentDeleteButton>;
};

export interface DefaultProfileTileExperiencesTileProps {
  experienceId?: string;
  jobTitleInputValue?: string;
  companyNameInputValue?: string;
  locationInputValue?: string;
  startDateInputValue?: string;
  endDateInputValue?: string;
  presentStateInputValue?: boolean;
  descriptionInputValue?: string;
  onJobTitleInputValueChange?: (val: string) => void;
  onCompanyNameInputValueChange?: (val: string) => void;
  onLocationInputValueChange?: (val: string) => void;
  onStartDateInputValueChange?: (val: string) => void;
  onEndDateInputValueChange?: (val: string) => void;
  onPresentStateInputValueChange?: (val: boolean) => void;
  onDescriptionInputValueChange?: (val: string) => void;
  deleteButtonOnClick?: (event: any) => void;
  deleteButtonDisabled?: any;
  deleteButtonClickStage?: number;
  onDeleteButtonDisabledChange?: (val: any) => void;
  onDeleteButtonClickStageChange?: (val: number) => void;
  editable?: SingleBooleanChoiceArg<"editable">;
  overview?: SingleBooleanChoiceArg<"overview">;
  className?: string;
}

const $$ = {};

function useNextRouter() {
  try {
    return useRouter();
  } catch {}
  return undefined;
}

function PlasmicProfileTileExperiencesTile__RenderFunc(props: {
  variants: PlasmicProfileTileExperiencesTile__VariantsArgs;
  args: PlasmicProfileTileExperiencesTile__ArgsType;
  overrides: PlasmicProfileTileExperiencesTile__OverridesType;
  forNode?: string;
}) {
  const { variants, overrides, forNode } = props;

  const args = React.useMemo(
    () =>
      Object.assign(
        {},
        Object.fromEntries(
          Object.entries(props.args).filter(([_, v]) => v !== undefined)
        )
      ),
    [props.args]
  );

  const $props = {
    ...args,
    ...variants
  };

  const __nextRouter = useNextRouter();
  const $ctx = useDataEnv?.() || {};
  const refsRef = React.useRef({});
  const $refs = refsRef.current;

  let [$queries, setDollarQueries] = React.useState<
    Record<string, ReturnType<typeof usePlasmicDataOp>>
  >({});
  const stateSpecs: Parameters<typeof useDollarState>[0] = React.useMemo(
    () => [
      {
        path: "jobTitle.value",
        type: "writable",
        variableType: "text",

        valueProp: "jobTitleInputValue",
        onChangeProp: "onJobTitleInputValueChange"
      },
      {
        path: "employmentTypeSelector.inputValue",
        type: "private",
        variableType: "text",
        initFunc: ({ $props, $state, $queries, $ctx }) => undefined
      },
      {
        path: "locationInput.inputValue",
        type: "writable",
        variableType: "text",

        valueProp: "locationInputValue",
        onChangeProp: "onLocationInputValueChange"
      },
      {
        path: "displayDatesBox.inputValue",
        type: "private",
        variableType: "text",
        initFunc: ({ $props, $state, $queries, $ctx }) => undefined
      },
      {
        path: "editable",
        type: "private",
        variableType: "variant",
        initFunc: ({ $props, $state, $queries, $ctx }) => $props.editable
      },
      {
        path: "overview",
        type: "private",
        variableType: "variant",
        initFunc: ({ $props, $state, $queries, $ctx }) => $props.overview
      },
      {
        path: "companyName.value",
        type: "writable",
        variableType: "text",

        valueProp: "companyNameInputValue",
        onChangeProp: "onCompanyNameInputValueChange"
      },
      {
        path: "startDateInput.inputValue",
        type: "writable",
        variableType: "text",

        valueProp: "startDateInputValue",
        onChangeProp: "onStartDateInputValueChange"
      },
      {
        path: "endDateInput.inputValue",
        type: "writable",
        variableType: "text",

        valueProp: "endDateInputValue",
        onChangeProp: "onEndDateInputValueChange"
      },
      {
        path: "presentStateInput.isChecked",
        type: "writable",
        variableType: "boolean",

        valueProp: "presentStateInputValue",
        onChangeProp: "onPresentStateInputValueChange"
      },
      {
        path: "subDeleteButton.clickStage",
        type: "writable",
        variableType: "number",

        valueProp: "deleteButtonClickStage",
        onChangeProp: "onDeleteButtonClickStageChange"
      },
      {
        path: "subDeleteButton.disabled",
        type: "writable",
        variableType: "text",

        valueProp: "deleteButtonDisabled",
        onChangeProp: "onDeleteButtonDisabledChange"
      },
      {
        path: "description.value",
        type: "writable",
        variableType: "text",

        valueProp: "descriptionInputValue",
        onChangeProp: "onDescriptionInputValueChange"
      },
      {
        path: "jobTitle.errorMessage",
        type: "private",
        variableType: "text",
        initFunc: ({ $props, $state, $queries, $ctx }) => ""
      },
      {
        path: "companyName.errorMessage",
        type: "private",
        variableType: "text",
        initFunc: ({ $props, $state, $queries, $ctx }) => ""
      },
      {
        path: "description.errorMessage",
        type: "private",
        variableType: "text",
        initFunc: ({ $props, $state, $queries, $ctx }) => ""
      }
    ],
    [$props, $ctx, $refs]
  );
  const $state = useDollarState(stateSpecs, {
    $props,
    $ctx,
    $queries: $queries,
    $refs
  });

  const new$Queries: Record<string, ReturnType<typeof usePlasmicDataOp>> = {};
  if (Object.keys(new$Queries).some(k => new$Queries[k] !== $queries[k])) {
    setDollarQueries(new$Queries);

    $queries = new$Queries;
  }

  return (
    <div
      data-plasmic-name={"experiencesSpacingContainer"}
      data-plasmic-override={overrides.experiencesSpacingContainer}
      data-plasmic-root={true}
      data-plasmic-for-node={forNode}
      className={classNames(
        projectcss.all,
        projectcss.root_reset,
        projectcss.plasmic_default_styles,
        projectcss.plasmic_mixins,
        projectcss.plasmic_tokens,
        sty.experiencesSpacingContainer,
        {
          [sty.experiencesSpacingContainereditable]: hasVariant(
            $state,
            "editable",
            "editable"
          ),
          [sty.experiencesSpacingContaineroverview]: hasVariant(
            $state,
            "overview",
            "overview"
          )
        }
      )}
    >
      <BuildingIcon
        data-plasmic-name={"companyIcon"}
        data-plasmic-override={overrides.companyIcon}
        className={classNames(projectcss.all, sty.companyIcon, {
          [sty.companyIconoverview]: hasVariant($state, "overview", "overview")
        })}
        role={"img"}
      />

      <div
        data-plasmic-name={"informationStack"}
        data-plasmic-override={overrides.informationStack}
        className={classNames(projectcss.all, sty.informationStack, {
          [sty.informationStackeditable]: hasVariant(
            $state,
            "editable",
            "editable"
          ),
          [sty.informationStackoverview]: hasVariant(
            $state,
            "overview",
            "overview"
          )
        })}
      >
        <SubcomponentTextInput
          data-plasmic-name={"jobTitle"}
          data-plasmic-override={overrides.jobTitle}
          className={classNames("__wab_instance", sty.jobTitle, {
            [sty.jobTitleeditable]: hasVariant($state, "editable", "editable"),
            [sty.jobTitleoverview]: hasVariant($state, "overview", "overview")
          })}
          displayText={(() => {
            try {
              return $state.jobTitle.value;
            } catch (e) {
              if (
                e instanceof TypeError ||
                e?.plasmicType === "PlasmicUndefinedDataError"
              ) {
                return undefined;
              }
              throw e;
            }
          })()}
          editView={"heading3"}
          errorMessage={generateStateValueProp($state, [
            "jobTitle",
            "errorMessage"
          ])}
          fieldNameRemainVisible={
            hasVariant($state, "editable", "editable") ? true : false
          }
          inputHoverText={"Job Title"}
          inputName={"Job Title"}
          inputNameAsPlaceholder={false}
          inputPlaceholder={"Ex: Director of Operations"}
          inputValue={generateStateValueProp($state, ["jobTitle", "value"])}
          onErrorMessageChange={async (...eventArgs: any) => {
            generateStateOnChangeProp($state, [
              "jobTitle",
              "errorMessage"
            ]).apply(null, eventArgs);

            if (
              eventArgs.length > 1 &&
              eventArgs[1] &&
              eventArgs[1]._plasmic_state_init_
            ) {
              return;
            }
          }}
          onInputValueChange={async (...eventArgs: any) => {
            generateStateOnChangeProp($state, ["jobTitle", "value"]).apply(
              null,
              eventArgs
            );

            if (
              eventArgs.length > 1 &&
              eventArgs[1] &&
              eventArgs[1]._plasmic_state_init_
            ) {
              return;
            }
          }}
          viewOnly={
            hasVariant($state, "editable", "editable") ? undefined : true
          }
        />

        <SubcomponentTextInput
          data-plasmic-name={"companyName"}
          data-plasmic-override={overrides.companyName}
          className={classNames("__wab_instance", sty.companyName, {
            [sty.companyNameeditable]: hasVariant(
              $state,
              "editable",
              "editable"
            ),
            [sty.companyNameoverview]: hasVariant(
              $state,
              "overview",
              "overview"
            )
          })}
          displayText={(() => {
            try {
              return $state.companyName.value;
            } catch (e) {
              if (
                e instanceof TypeError ||
                e?.plasmicType === "PlasmicUndefinedDataError"
              ) {
                return undefined;
              }
              throw e;
            }
          })()}
          editView={"subHeading"}
          errorMessage={generateStateValueProp($state, [
            "companyName",
            "errorMessage"
          ])}
          fieldNameRemainVisible={
            hasVariant($state, "editable", "editable") ? true : false
          }
          inputHoverText={"Company Name"}
          inputName={"Company Name"}
          inputNameAsPlaceholder={false}
          inputPlaceholder={"Ex: Spherical"}
          inputValue={generateStateValueProp($state, ["companyName", "value"])}
          onErrorMessageChange={async (...eventArgs: any) => {
            generateStateOnChangeProp($state, [
              "companyName",
              "errorMessage"
            ]).apply(null, eventArgs);

            if (
              eventArgs.length > 1 &&
              eventArgs[1] &&
              eventArgs[1]._plasmic_state_init_
            ) {
              return;
            }
          }}
          onInputValueChange={async (...eventArgs: any) => {
            generateStateOnChangeProp($state, ["companyName", "value"]).apply(
              null,
              eventArgs
            );

            if (
              eventArgs.length > 1 &&
              eventArgs[1] &&
              eventArgs[1]._plasmic_state_init_
            ) {
              return;
            }
          }}
          viewOnly={
            hasVariant($state, "editable", "editable") ? undefined : true
          }
        />

        {(
          hasVariant($state, "editable", "editable")
            ? true
            : (() => {
                try {
                  return (() => {
                    if (!$state.startDateInput.inputValue) {
                      return false;
                    } else if (
                      $state.startDateInput.inputValue &&
                      ($state.endDateInput.inputValue ||
                        $state.presentStateInput.isChecked)
                    ) {
                      return true;
                    } else {
                      return false;
                    }
                  })();
                } catch (e) {
                  if (
                    e instanceof TypeError ||
                    e?.plasmicType === "PlasmicUndefinedDataError"
                  ) {
                    return false;
                  }
                  throw e;
                }
              })()
        ) ? (
          <SubcomponentIconWithText
            data-plasmic-name={"displayDatesBox"}
            data-plasmic-override={overrides.displayDatesBox}
            className={classNames("__wab_instance", sty.displayDatesBox, {
              [sty.displayDatesBoxeditable]: hasVariant(
                $state,
                "editable",
                "editable"
              ),
              [sty.displayDatesBoxoverview]: hasVariant(
                $state,
                "overview",
                "overview"
              )
            })}
            displayText={(() => {
              try {
                return (() => {
                  if (
                    !$state.startDateInput.inputValue ||
                    (!$state.endDateInput.inputValue &&
                      !$state.presentStateInput.isChecked)
                  ) {
                    return null;
                  } else {
                    const startDate = new Date(
                      $state.startDateInput.inputValue
                        .split("T")[0]
                        .split("-")
                        .join("/")
                    );
                    const formattedStartDate = `${startDate.toLocaleString(
                      "default",
                      { month: "long" }
                    )}, ${startDate.getFullYear()}`;
                    let formattedEndDate;
                    if ($state.presentStateInput.isChecked) {
                      formattedEndDate = "Present";
                    } else {
                      const endDate = new Date(
                        $state.endDateInput.inputValue
                          .split("T")[0]
                          .split("-")
                          .join("/")
                      );
                      formattedEndDate = `${endDate.toLocaleString("default", {
                        month: "long"
                      })}, ${endDate.getFullYear()}`;
                    }
                    return `${formattedStartDate} - ${formattedEndDate}`;
                  }
                })();
              } catch (e) {
                if (
                  e instanceof TypeError ||
                  e?.plasmicType === "PlasmicUndefinedDataError"
                ) {
                  return undefined;
                }
                throw e;
              }
            })()}
            editable={
              hasVariant($state, "editable", "editable")
                ? "editableText"
                : undefined
            }
            iconSpot2={
              <CalendarIcon
                data-plasmic-name={"iconSpot4"}
                data-plasmic-override={overrides.iconSpot4}
                className={classNames(projectcss.all, sty.iconSpot4)}
                role={"img"}
              />
            }
            inputHoverText={"Display Dates"}
            inputName={"Display Dates"}
            inputPlaceholder={"Display Dates"}
            inputValue={generateStateValueProp($state, [
              "displayDatesBox",
              "inputValue"
            ])}
            onInputValueChange={async (...eventArgs: any) => {
              generateStateOnChangeProp($state, [
                "displayDatesBox",
                "inputValue"
              ]).apply(null, eventArgs);

              if (
                eventArgs.length > 1 &&
                eventArgs[1] &&
                eventArgs[1]._plasmic_state_init_
              ) {
                return;
              }
            }}
          />
        ) : null}
        <Stack__
          as={"section"}
          data-plasmic-name={"infoBar"}
          data-plasmic-override={overrides.infoBar}
          hasGap={true}
          className={classNames(projectcss.all, sty.infoBar, {
            [sty.infoBareditable]: hasVariant($state, "editable", "editable"),
            [sty.infoBaroverview]: hasVariant($state, "overview", "overview")
          })}
        >
          {(
            hasVariant($state, "editable", "editable")
              ? true
              : (() => {
                  try {
                    return !!$state.locationInput.inputValue;
                  } catch (e) {
                    if (
                      e instanceof TypeError ||
                      e?.plasmicType === "PlasmicUndefinedDataError"
                    ) {
                      return true;
                    }
                    throw e;
                  }
                })()
          ) ? (
            <SubcomponentIconWithText
              data-plasmic-name={"locationInput"}
              data-plasmic-override={overrides.locationInput}
              className={classNames("__wab_instance", sty.locationInput, {
                [sty.locationInputeditable]: hasVariant(
                  $state,
                  "editable",
                  "editable"
                ),
                [sty.locationInputoverview]: hasVariant(
                  $state,
                  "overview",
                  "overview"
                )
              })}
              displayText={(() => {
                try {
                  return $state.locationInput.inputValue;
                } catch (e) {
                  if (
                    e instanceof TypeError ||
                    e?.plasmicType === "PlasmicUndefinedDataError"
                  ) {
                    return undefined;
                  }
                  throw e;
                }
              })()}
              editable={
                hasVariant($state, "editable", "editable")
                  ? "editableText"
                  : undefined
              }
              iconSpot2={
                <MapPinIcon
                  data-plasmic-name={"iconSpot2"}
                  data-plasmic-override={overrides.iconSpot2}
                  className={classNames(projectcss.all, sty.iconSpot2)}
                  role={"img"}
                />
              }
              inputHoverText={"Location"}
              inputName={"Location"}
              inputPlaceholder={"Ex: Chicago, IL"}
              inputValue={generateStateValueProp($state, [
                "locationInput",
                "inputValue"
              ])}
              onInputValueChange={async (...eventArgs: any) => {
                generateStateOnChangeProp($state, [
                  "locationInput",
                  "inputValue"
                ]).apply(null, eventArgs);

                if (
                  eventArgs.length > 1 &&
                  eventArgs[1] &&
                  eventArgs[1]._plasmic_state_init_
                ) {
                  return;
                }
              }}
            />
          ) : null}
          {(
            hasVariant($state, "editable", "editable")
              ? true
              : (() => {
                  try {
                    return undefined;
                  } catch (e) {
                    if (
                      e instanceof TypeError ||
                      e?.plasmicType === "PlasmicUndefinedDataError"
                    ) {
                      return true;
                    }
                    throw e;
                  }
                })()
          ) ? (
            <SubcomponentIconWithText
              data-plasmic-name={"employmentTypeSelector"}
              data-plasmic-override={overrides.employmentTypeSelector}
              className={classNames(
                "__wab_instance",
                sty.employmentTypeSelector,
                {
                  [sty.employmentTypeSelectoreditable]: hasVariant(
                    $state,
                    "editable",
                    "editable"
                  ),
                  [sty.employmentTypeSelectoroverview]: hasVariant(
                    $state,
                    "overview",
                    "overview"
                  )
                }
              )}
              displayText={(() => {
                try {
                  return undefined;
                } catch (e) {
                  if (
                    e instanceof TypeError ||
                    e?.plasmicType === "PlasmicUndefinedDataError"
                  ) {
                    return undefined;
                  }
                  throw e;
                }
              })()}
              dropdownName={"Employment Type"}
              dropdownOptions={(() => {
                const __composite = [
                  { value: null },
                  { value: null },
                  { value: null },
                  { value: null },
                  { value: null },
                  { value: null },
                  { value: null },
                  { value: null },
                  { value: null }
                ];
                __composite["0"]["value"] = "Full-time";
                __composite["1"]["value"] = "Part-time";
                __composite["2"]["value"] = "Self-employed";
                __composite["3"]["value"] = "Freelance";
                __composite["4"]["value"] = "Contract";
                __composite["5"]["value"] = "Internship";
                __composite["6"]["value"] = "Apprenticeship";
                __composite["7"]["value"] = "Temporary";
                __composite["8"]["value"] = "Seasonal";
                return __composite;
              })()}
              dropdownPlaceholderText={"Employment Type"}
              editable={
                hasVariant($state, "editable", "editable")
                  ? "editableSelector"
                  : undefined
              }
              iconSpot2={
                <ContractTypeIcon
                  data-plasmic-name={"iconSpot"}
                  data-plasmic-override={overrides.iconSpot}
                  className={classNames(projectcss.all, sty.iconSpot)}
                  role={"img"}
                />
              }
              inputHoverText={"Employment Type"}
              inputName={"Employment Type"}
              inputPlaceholder={"Employment Type"}
              inputValue={generateStateValueProp($state, [
                "employmentTypeSelector",
                "inputValue"
              ])}
              onInputValueChange={async (...eventArgs: any) => {
                generateStateOnChangeProp($state, [
                  "employmentTypeSelector",
                  "inputValue"
                ]).apply(null, eventArgs);

                if (
                  eventArgs.length > 1 &&
                  eventArgs[1] &&
                  eventArgs[1]._plasmic_state_init_
                ) {
                  return;
                }
              }}
            />
          ) : null}
          <SubcomponentIconWithText
            data-plasmic-name={"startDateInput"}
            data-plasmic-override={overrides.startDateInput}
            className={classNames("__wab_instance", sty.startDateInput, {
              [sty.startDateInputeditable]: hasVariant(
                $state,
                "editable",
                "editable"
              ),
              [sty.startDateInputoverview]: hasVariant(
                $state,
                "overview",
                "overview"
              )
            })}
            editable={
              hasVariant($state, "editable", "editable")
                ? "editableText"
                : undefined
            }
            iconSpot2={
              <CalendarIcon
                data-plasmic-name={"iconSpot7"}
                data-plasmic-override={overrides.iconSpot7}
                className={classNames(projectcss.all, sty.iconSpot7)}
                role={"img"}
              />
            }
            inputHoverText={"Start Date"}
            inputName={"Start Date"}
            inputType={"date"}
            inputValue={generateStateValueProp($state, [
              "startDateInput",
              "inputValue"
            ])}
            onInputValueChange={async (...eventArgs: any) => {
              generateStateOnChangeProp($state, [
                "startDateInput",
                "inputValue"
              ]).apply(null, eventArgs);

              if (
                eventArgs.length > 1 &&
                eventArgs[1] &&
                eventArgs[1]._plasmic_state_init_
              ) {
                return;
              }
            }}
            withoutIcon={
              hasVariant($state, "editable", "editable") ? true : undefined
            }
          />

          {(
            hasVariant($state, "editable", "editable")
              ? (() => {
                  try {
                    return (() => {
                      return $state.presentStateInput.isChecked ? false : true;
                    })();
                  } catch (e) {
                    if (
                      e instanceof TypeError ||
                      e?.plasmicType === "PlasmicUndefinedDataError"
                    ) {
                      return true;
                    }
                    throw e;
                  }
                })()
              : true
          ) ? (
            <SubcomponentIconWithText
              data-plasmic-name={"endDateInput"}
              data-plasmic-override={overrides.endDateInput}
              className={classNames("__wab_instance", sty.endDateInput, {
                [sty.endDateInputeditable]: hasVariant(
                  $state,
                  "editable",
                  "editable"
                ),
                [sty.endDateInputoverview]: hasVariant(
                  $state,
                  "overview",
                  "overview"
                )
              })}
              editable={
                hasVariant($state, "editable", "editable")
                  ? "editableText"
                  : undefined
              }
              iconSpot2={
                <svg
                  data-plasmic-name={"iconSpot8"}
                  data-plasmic-override={overrides.iconSpot8}
                  className={classNames(projectcss.all, sty.iconSpot8)}
                  role={"img"}
                />
              }
              inputHoverText={"End Date"}
              inputName={"End Date"}
              inputType={"date"}
              inputValue={generateStateValueProp($state, [
                "endDateInput",
                "inputValue"
              ])}
              onInputValueChange={async (...eventArgs: any) => {
                generateStateOnChangeProp($state, [
                  "endDateInput",
                  "inputValue"
                ]).apply(null, eventArgs);

                if (
                  eventArgs.length > 1 &&
                  eventArgs[1] &&
                  eventArgs[1]._plasmic_state_init_
                ) {
                  return;
                }
              }}
              withoutIcon={
                hasVariant($state, "editable", "editable") ? true : undefined
              }
            />
          ) : null}
          <SubcomponentCheckbox
            data-plasmic-name={"presentStateInput"}
            data-plasmic-override={overrides.presentStateInput}
            className={classNames("__wab_instance", sty.presentStateInput, {
              [sty.presentStateInputeditable]: hasVariant(
                $state,
                "editable",
                "editable"
              ),
              [sty.presentStateInputoverview]: hasVariant(
                $state,
                "overview",
                "overview"
              )
            })}
            isChecked={
              generateStateValueProp($state, [
                "presentStateInput",
                "isChecked"
              ]) ?? false
            }
            name={"Current Experience"}
            onChange={async (...eventArgs: any) => {
              ((...eventArgs) => {
                generateStateOnChangeProp($state, [
                  "presentStateInput",
                  "isChecked"
                ])(eventArgs[0]);
              }).apply(null, eventArgs);

              if (
                eventArgs.length > 1 &&
                eventArgs[1] &&
                eventArgs[1]._plasmic_state_init_
              ) {
                return;
              }
            }}
          >
            {"Present"}
          </SubcomponentCheckbox>
        </Stack__>
        <SubcomponentTextInput
          data-plasmic-name={"description"}
          data-plasmic-override={overrides.description}
          className={classNames("__wab_instance", sty.description, {
            [sty.descriptioneditable]: hasVariant(
              $state,
              "editable",
              "editable"
            ),
            [sty.descriptionoverview]: hasVariant(
              $state,
              "overview",
              "overview"
            )
          })}
          displayText={
            hasVariant($state, "overview", "overview")
              ? (() => {
                  try {
                    return $state.description.value.length > 160
                      ? $state.description.value.slice(0, 160) + "..."
                      : $state.description.value;
                  } catch (e) {
                    if (
                      e instanceof TypeError ||
                      e?.plasmicType === "PlasmicUndefinedDataError"
                    ) {
                      return undefined;
                    }
                    throw e;
                  }
                })()
              : (() => {
                  try {
                    return $state.description.value;
                  } catch (e) {
                    if (
                      e instanceof TypeError ||
                      e?.plasmicType === "PlasmicUndefinedDataError"
                    ) {
                      return undefined;
                    }
                    throw e;
                  }
                })()
          }
          editView={
            hasVariant($state, "editable", "editable") ? "core" : undefined
          }
          errorMessage={generateStateValueProp($state, [
            "description",
            "errorMessage"
          ])}
          inputHoverText={
            hasVariant($state, "editable", "editable")
              ? "Experience Summary"
              : undefined
          }
          inputName={
            hasVariant($state, "editable", "editable")
              ? "Experience Summary"
              : undefined
          }
          inputPlaceholder={
            hasVariant($state, "editable", "editable")
              ? "Experience Summary"
              : undefined
          }
          inputValue={generateStateValueProp($state, ["description", "value"])}
          onErrorMessageChange={async (...eventArgs: any) => {
            generateStateOnChangeProp($state, [
              "description",
              "errorMessage"
            ]).apply(null, eventArgs);

            if (
              eventArgs.length > 1 &&
              eventArgs[1] &&
              eventArgs[1]._plasmic_state_init_
            ) {
              return;
            }
          }}
          onInputValueChange={async (...eventArgs: any) => {
            generateStateOnChangeProp($state, ["description", "value"]).apply(
              null,
              eventArgs
            );

            if (
              eventArgs.length > 1 &&
              eventArgs[1] &&
              eventArgs[1]._plasmic_state_init_
            ) {
              return;
            }
          }}
          viewOnly={
            hasVariant($state, "editable", "editable") ? undefined : true
          }
        />
      </div>
      <SubcomponentDeleteButton
        data-plasmic-name={"subDeleteButton"}
        data-plasmic-override={overrides.subDeleteButton}
        className={classNames("__wab_instance", sty.subDeleteButton, {
          [sty.subDeleteButtoneditable]: hasVariant(
            $state,
            "editable",
            "editable"
          )
        })}
        onClick={args.deleteButtonOnClick}
        onClickStageChange={async (...eventArgs: any) => {
          generateStateOnChangeProp($state, [
            "subDeleteButton",
            "clickStage"
          ]).apply(null, eventArgs);

          if (
            eventArgs.length > 1 &&
            eventArgs[1] &&
            eventArgs[1]._plasmic_state_init_
          ) {
            return;
          }
        }}
        onDisabledChange={async (...eventArgs: any) => {
          generateStateOnChangeProp($state, [
            "subDeleteButton",
            "disabled"
          ]).apply(null, eventArgs);

          if (
            eventArgs.length > 1 &&
            eventArgs[1] &&
            eventArgs[1]._plasmic_state_init_
          ) {
            return;
          }
        }}
      />
    </div>
  ) as React.ReactElement | null;
}

const PlasmicDescendants = {
  experiencesSpacingContainer: [
    "experiencesSpacingContainer",
    "companyIcon",
    "informationStack",
    "jobTitle",
    "companyName",
    "displayDatesBox",
    "iconSpot4",
    "infoBar",
    "locationInput",
    "iconSpot2",
    "employmentTypeSelector",
    "iconSpot",
    "startDateInput",
    "iconSpot7",
    "endDateInput",
    "iconSpot8",
    "presentStateInput",
    "description",
    "subDeleteButton"
  ],
  companyIcon: ["companyIcon"],
  informationStack: [
    "informationStack",
    "jobTitle",
    "companyName",
    "displayDatesBox",
    "iconSpot4",
    "infoBar",
    "locationInput",
    "iconSpot2",
    "employmentTypeSelector",
    "iconSpot",
    "startDateInput",
    "iconSpot7",
    "endDateInput",
    "iconSpot8",
    "presentStateInput",
    "description"
  ],
  jobTitle: ["jobTitle"],
  companyName: ["companyName"],
  displayDatesBox: ["displayDatesBox", "iconSpot4"],
  iconSpot4: ["iconSpot4"],
  infoBar: [
    "infoBar",
    "locationInput",
    "iconSpot2",
    "employmentTypeSelector",
    "iconSpot",
    "startDateInput",
    "iconSpot7",
    "endDateInput",
    "iconSpot8",
    "presentStateInput"
  ],
  locationInput: ["locationInput", "iconSpot2"],
  iconSpot2: ["iconSpot2"],
  employmentTypeSelector: ["employmentTypeSelector", "iconSpot"],
  iconSpot: ["iconSpot"],
  startDateInput: ["startDateInput", "iconSpot7"],
  iconSpot7: ["iconSpot7"],
  endDateInput: ["endDateInput", "iconSpot8"],
  iconSpot8: ["iconSpot8"],
  presentStateInput: ["presentStateInput"],
  description: ["description"],
  subDeleteButton: ["subDeleteButton"]
} as const;
type NodeNameType = keyof typeof PlasmicDescendants;
type DescendantsType<T extends NodeNameType> =
  (typeof PlasmicDescendants)[T][number];
type NodeDefaultElementType = {
  experiencesSpacingContainer: "div";
  companyIcon: "svg";
  informationStack: "div";
  jobTitle: typeof SubcomponentTextInput;
  companyName: typeof SubcomponentTextInput;
  displayDatesBox: typeof SubcomponentIconWithText;
  iconSpot4: "svg";
  infoBar: "section";
  locationInput: typeof SubcomponentIconWithText;
  iconSpot2: "svg";
  employmentTypeSelector: typeof SubcomponentIconWithText;
  iconSpot: "svg";
  startDateInput: typeof SubcomponentIconWithText;
  iconSpot7: "svg";
  endDateInput: typeof SubcomponentIconWithText;
  iconSpot8: "svg";
  presentStateInput: typeof SubcomponentCheckbox;
  description: typeof SubcomponentTextInput;
  subDeleteButton: typeof SubcomponentDeleteButton;
};

type ReservedPropsType = "variants" | "args" | "overrides";
type NodeOverridesType<T extends NodeNameType> = Pick<
  PlasmicProfileTileExperiencesTile__OverridesType,
  DescendantsType<T>
>;
type NodeComponentProps<T extends NodeNameType> =
  // Explicitly specify variants, args, and overrides as objects
  {
    variants?: PlasmicProfileTileExperiencesTile__VariantsArgs;
    args?: PlasmicProfileTileExperiencesTile__ArgsType;
    overrides?: NodeOverridesType<T>;
  } & Omit<PlasmicProfileTileExperiencesTile__VariantsArgs, ReservedPropsType> & // Specify variants directly as props
    /* Specify args directly as props*/ Omit<
      PlasmicProfileTileExperiencesTile__ArgsType,
      ReservedPropsType
    > &
    /* Specify overrides for each element directly as props*/ Omit<
      NodeOverridesType<T>,
      ReservedPropsType | VariantPropType | ArgPropType
    > &
    /* Specify props for the root element*/ Omit<
      Partial<React.ComponentProps<NodeDefaultElementType[T]>>,
      ReservedPropsType | VariantPropType | ArgPropType | DescendantsType<T>
    >;

function makeNodeComponent<NodeName extends NodeNameType>(nodeName: NodeName) {
  type PropsType = NodeComponentProps<NodeName> & { key?: React.Key };
  const func = function <T extends PropsType>(
    props: T & StrictProps<T, PropsType>
  ) {
    const { variants, args, overrides } = React.useMemo(
      () =>
        deriveRenderOpts(props, {
          name: nodeName,
          descendantNames: PlasmicDescendants[nodeName],
          internalArgPropNames: PlasmicProfileTileExperiencesTile__ArgProps,
          internalVariantPropNames:
            PlasmicProfileTileExperiencesTile__VariantProps
        }),
      [props, nodeName]
    );
    return PlasmicProfileTileExperiencesTile__RenderFunc({
      variants,
      args,
      overrides,
      forNode: nodeName
    });
  };
  if (nodeName === "experiencesSpacingContainer") {
    func.displayName = "PlasmicProfileTileExperiencesTile";
  } else {
    func.displayName = `PlasmicProfileTileExperiencesTile.${nodeName}`;
  }
  return func;
}

export const PlasmicProfileTileExperiencesTile = Object.assign(
  // Top-level PlasmicProfileTileExperiencesTile renders the root element
  makeNodeComponent("experiencesSpacingContainer"),
  {
    // Helper components rendering sub-elements
    companyIcon: makeNodeComponent("companyIcon"),
    informationStack: makeNodeComponent("informationStack"),
    jobTitle: makeNodeComponent("jobTitle"),
    companyName: makeNodeComponent("companyName"),
    displayDatesBox: makeNodeComponent("displayDatesBox"),
    iconSpot4: makeNodeComponent("iconSpot4"),
    infoBar: makeNodeComponent("infoBar"),
    locationInput: makeNodeComponent("locationInput"),
    iconSpot2: makeNodeComponent("iconSpot2"),
    employmentTypeSelector: makeNodeComponent("employmentTypeSelector"),
    iconSpot: makeNodeComponent("iconSpot"),
    startDateInput: makeNodeComponent("startDateInput"),
    iconSpot7: makeNodeComponent("iconSpot7"),
    endDateInput: makeNodeComponent("endDateInput"),
    iconSpot8: makeNodeComponent("iconSpot8"),
    presentStateInput: makeNodeComponent("presentStateInput"),
    description: makeNodeComponent("description"),
    subDeleteButton: makeNodeComponent("subDeleteButton"),

    // Metadata about props expected for PlasmicProfileTileExperiencesTile
    internalVariantProps: PlasmicProfileTileExperiencesTile__VariantProps,
    internalArgProps: PlasmicProfileTileExperiencesTile__ArgProps
  }
);

export default PlasmicProfileTileExperiencesTile;
/* prettier-ignore-end */
