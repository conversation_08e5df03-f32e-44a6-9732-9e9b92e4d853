/* eslint-disable */
/* tslint:disable */
// @ts-nocheck
/* prettier-ignore-start */
import React from "react";
import { classNames } from "@plasmicapp/react-web";

export type SchoolBellIconProps = React.ComponentProps<"svg"> & {
  title?: string;
};

export function SchoolBellIcon(props: SchoolBellIconProps) {
  const { className, style, title, ...restProps } = props;
  return (
    <svg
      xmlns={"http://www.w3.org/2000/svg"}
      className={classNames(
        "plasmic-default__svg",
        className,
        "icon icon-tabler icon-tabler-bell-school"
      )}
      viewBox={"0 0 24 24"}
      strokeWidth={"1.5"}
      stroke={"currentColor"}
      fill={"none"}
      strokeLinecap={"round"}
      strokeLinejoin={"round"}
      height={"1em"}
      style={style}
      {...restProps}
    >
      {title && <title>{title}</title>}

      <path d={"M0 0h24v24H0z"} fill={"none"} stroke={"none"}></path>

      <path d={"M4 10a6 6 0 1012 0 6 6 0 10-12 0"}></path>

      <path
        d={
          "M13.5 15h.5a2 2 0 012 2v1a2 2 0 01-2 2H6a2 2 0 01-2-2v-1a2 2 0 012-2h.5m9.5 2a5.698 5.698 0 004.467-7.932L20 8m-10 2v.01"
        }
      ></path>

      <path d={"M19 8a1 1 0 102 0 1 1 0 10-2 0"}></path>
    </svg>
  );
}

export default SchoolBellIcon;
/* prettier-ignore-end */
