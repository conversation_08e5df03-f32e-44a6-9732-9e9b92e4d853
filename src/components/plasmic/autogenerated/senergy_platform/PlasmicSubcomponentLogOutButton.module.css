.root {
  display: inline-flex;
  position: relative;
  background: var(--token-K5FbAPSIIrXM);
  cursor: pointer;
  width: auto;
  justify-self: flex-start;
  flex-direction: column;
  height: auto;
  transition-property: all;
  transition-duration: 0.2s;
  -webkit-transition-property: all;
  -webkit-transition-duration: 0.2s;
  border-radius: 1px;
  padding: 10px 16px;
  border-width: 0px;
}
.root > :global(.__wab_flex-container) {
  flex-direction: column;
  justify-content: center;
  align-items: center;
  margin-top: calc(0px - var(--token-sazGmnf7GWAk));
  height: calc(100% + var(--token-sazGmnf7GWAk));
}
.root > :global(.__wab_flex-container) > *,
.root > :global(.__wab_flex-container) > :global(.__wab_slot) > *,
.root > :global(.__wab_flex-container) > picture > img,
.root > :global(.__wab_flex-container) > :global(.__wab_slot) > picture > img {
  margin-top: var(--token-sazGmnf7GWAk);
}
.rootshowStartIcon {
  padding-left: 16px;
}
.rootshowEndIcon {
  padding-right: 16px;
}
.rootisDisabled {
  opacity: 0.6;
}
.rootshape_rounded {
  padding-left: 20px;
  padding-right: 20px;
  min-width: 100px;
  border-radius: 999px;
}
.rootshape_sharp {
  border-radius: 0px;
}
.rootsize_compact {
  padding: 6px 16px;
}
.rootsize_minimal {
  padding: 0px 4px;
}
.rootstyling_cameo {
  background: var(--token-MPvkQ1rq8Due);
}
.rootstyling_typewriter {
  background: none;
}
.rootjustification_left > :global(.__wab_flex-container) {
  justify-content: center;
  align-items: flex-start;
}
.rootjustification_right > :global(.__wab_flex-container) {
  align-items: flex-end;
  justify-content: center;
}
.rootselected {
  display: flex;
  justify-self: flex-start;
}
.rootcolor_eggplant {
  background: var(--token-v4jufhOu3lt9);
}
.rootcolor_forest {
  background: var(--token-XQQdLIBrONt7);
}
.rootcolor_sage {
  background: var(--token-lUfRIntGAfk9);
}
.rootcolor_sunflower {
  background: var(--token-cXZCUAmMzuNV);
}
.rootdisabledAnimation_disabled {
  opacity: 0.6;
}
.rootdisabledAnimation_shakeLeft {
  opacity: 0.6;
  transform: translateX(-5px) translateY(0px) translateZ(0px);
}
.rootdisabledAnimation_shakeRight {
  opacity: 0.6;
  transform: translateX(5px) translateY(0px) translateZ(0px);
}
.rootdisabledAnimation_disabledText {
  opacity: 0.6;
}
.rootshape_rounded_showStartIcon {
  padding-left: 16px;
}
.rootshape_rounded_showEndIcon {
  padding-right: 16px;
}
.root:hover {
  background: #3d3d3d;
}
.root:active {
  background: #3d3d3d;
}
.freeBox {
  display: flex;
  position: relative;
  width: auto;
  height: auto;
  max-width: 100%;
  margin-top: var(--token-sazGmnf7GWAk) !important;
  margin-right: var(--token-sazGmnf7GWAk);
  margin-bottom: var(--token-sazGmnf7GWAk);
  margin-left: var(--token-sazGmnf7GWAk);
  flex-direction: row;
  left: auto;
  top: auto;
  padding: var(--token-sazGmnf7GWAk);
}
.freeBox > :global(.__wab_flex-container) {
  flex-direction: row;
  justify-content: center;
  align-items: center;
  margin-left: calc(0px - var(--token-4Wrp9mDZCSCQ));
  width: calc(100% + var(--token-4Wrp9mDZCSCQ));
}
.freeBox > :global(.__wab_flex-container) > *,
.freeBox > :global(.__wab_flex-container) > :global(.__wab_slot) > *,
.freeBox > :global(.__wab_flex-container) > picture > img,
.freeBox
  > :global(.__wab_flex-container)
  > :global(.__wab_slot)
  > picture
  > img {
  margin-left: var(--token-4Wrp9mDZCSCQ);
}
.freeBoxshowStartIcon {
  margin-top: var(--token-sazGmnf7GWAk) !important;
}
.freeBoxselected {
  width: auto;
  margin-top: var(--token-sazGmnf7GWAk) !important;
}
.freeBoxselected > :global(.__wab_flex-container) {
  justify-content: center;
  align-items: center;
  margin-left: calc(0px - var(--token-4Wrp9mDZCSCQ));
  width: calc(100% + var(--token-4Wrp9mDZCSCQ));
  margin-top: calc(0px - 0px);
  height: calc(100% + 0px);
}
.freeBoxselected > :global(.__wab_flex-container) > *,
.freeBoxselected > :global(.__wab_flex-container) > :global(.__wab_slot) > *,
.freeBoxselected > :global(.__wab_flex-container) > picture > img,
.freeBoxselected
  > :global(.__wab_flex-container)
  > :global(.__wab_slot)
  > picture
  > img {
  margin-left: var(--token-4Wrp9mDZCSCQ);
  margin-top: 0px;
}
.freeBoxdisabledAnimation_disabled {
  margin-top: var(--token-sazGmnf7GWAk) !important;
}
.freeBoxdisabledAnimation_shakeRight {
  margin-top: var(--token-sazGmnf7GWAk) !important;
}
.freeBoxdisabledAnimation_disabledText {
  margin-top: var(--token-sazGmnf7GWAk) !important;
}
.startIconContainer {
  display: flex;
  flex-direction: row;
  align-items: stretch;
  justify-content: flex-start;
  position: relative;
  left: auto;
  top: auto;
  bottom: auto;
  right: auto;
}
.startIconContainershowEndIcon {
  display: none;
}
.slotTargetStartIcon {
  color: #ededec;
}
.svg___73K3G {
  position: relative;
  object-fit: cover;
  width: auto;
  height: 1em;
}
.svgshowEndIcon___73K3GSqDuB {
  display: none;
}
.contentContainer {
  display: flex;
  position: relative;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  left: auto;
  top: auto;
  width: auto;
}
.contentContainerselected {
  width: auto;
}
.slotTargetChildren {
  color: var(--token-xo_r2w5pebq-);
  font-weight: 400;
  white-space: pre;
  text-align: center;
}
.slotTargetChildrenstyling_cameo {
  color: var(--token-K5FbAPSIIrXM);
  font-family: var(--token-z1yrQVi72Nj1);
  font-weight: 700;
}
.slotTargetChildrenstyling_typewriter {
  font-family: var(--token-z1yrQVi72Nj1);
  color: var(--token-K5FbAPSIIrXM);
  font-weight: 400;
}
.slotTargetChildrenstyling_nittiWColor {
  font-family: var(--token-z1yrQVi72Nj1);
}
.slotTargetChildrenselected_styling_nittiWColor {
  font-weight: 400;
}
.root:hover .slotTargetChildren {
  color: var(--token-xo_r2w5pebq-);
}
.root:hover .slotTargetChildren > :global(.__wab_text),
.root:hover .slotTargetChildren > :global(.__wab_expr_html_text),
.root:hover .slotTargetChildren > :global(.__wab_slot-string-wrapper),
.root:hover .slotTargetChildren > :global(.__wab_slot) > :global(.__wab_text),
.root:hover
  .slotTargetChildren
  > :global(.__wab_slot)
  > :global(.__wab_expr_html_text),
.root:hover
  .slotTargetChildren
  > :global(.__wab_slot)
  > :global(.__wab_slot-string-wrapper),
.root:hover
  .slotTargetChildren
  > :global(.__wab_slot)
  > :global(.__wab_slot)
  > :global(.__wab_text),
.root:hover
  .slotTargetChildren
  > :global(.__wab_slot)
  > :global(.__wab_slot)
  > :global(.__wab_expr_html_text),
.root:hover
  .slotTargetChildren
  > :global(.__wab_slot)
  > :global(.__wab_slot)
  > :global(.__wab_slot-string-wrapper),
.root:hover
  .slotTargetChildren
  > :global(.__wab_slot)
  > :global(.__wab_slot)
  > :global(.__wab_slot)
  > :global(.__wab_text),
.root:hover
  .slotTargetChildren
  > :global(.__wab_slot)
  > :global(.__wab_slot)
  > :global(.__wab_slot)
  > :global(.__wab_expr_html_text),
.root:hover
  .slotTargetChildren
  > :global(.__wab_slot)
  > :global(.__wab_slot)
  > :global(.__wab_slot)
  > :global(.__wab_slot-string-wrapper) {
  text-decoration-line: none;
}
.endIconContainer {
  display: flex;
  position: relative;
  flex-direction: row;
  align-items: stretch;
  justify-content: flex-start;
  left: auto;
  top: auto;
  bottom: auto;
  right: auto;
}
.endIconContainershowEndIcon {
  display: flex;
}
.slotTargetEndIcon {
  color: #ededec;
}
.svg__rQOv {
  position: relative;
  object-fit: cover;
  width: auto;
  height: 1em;
}
.section {
  position: relative;
  align-content: flex-start;
  justify-items: center;
  width: auto;
  left: auto;
  bottom: auto;
  right: auto;
  top: auto;
  z-index: 1;
  height: 1px;
  background: var(--token-xo_r2w5pebq-);
  margin-bottom: -1px;
  flex-shrink: 0;
  display: none;
  grid-template-columns:
    var(--plsmc-viewport-gap) 1fr minmax(0, var(--plsmc-wide-chunk))
    min(
      var(--plsmc-standard-width),
      calc(100% - var(--plsmc-viewport-gap) - var(--plsmc-viewport-gap))
    )
    minmax(0, var(--plsmc-wide-chunk)) 1fr var(--plsmc-viewport-gap);
  padding: var(--token-sazGmnf7GWAk);
}
.section > * {
  grid-column: 4;
}
.sectionselected {
  left: auto;
  top: auto;
  bottom: auto;
  right: auto;
  width: 100%;
  display: block;
  min-width: 0;
}
.sectionselected > * {
  grid-column: 4;
}
.sectiondisabledAnimation_shakeLeft > * {
  grid-column: 4;
}
.sectionstyling_typewriter_selected {
  background: var(--token-K5FbAPSIIrXM);
}
.sectionstyling_typewriter_selected > * {
  grid-column: 4;
}
.text {
  position: absolute;
  color: var(--token-3OLw18f8JRN3);
  text-align: center;
  top: 42px;
  z-index: 1;
  height: auto;
  display: none;
}
.textdisabledAnimation_disabled {
  display: none;
}
.textdisabledAnimation_shakeLeft {
  display: block;
}
.textdisabledAnimation_shakeRight {
  display: block;
}
.textdisabledAnimation_disabledText {
  display: block;
}
