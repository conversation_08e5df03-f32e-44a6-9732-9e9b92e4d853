'use client';

import TypeIt from 'typeit-react';

// Define an interface for the TypeIt instance
interface TypeItInstance {
  destroy: () => void;
}

export default function Page() {
  return (
    <div className="flex h-full min-h-screen w-full items-center justify-center bg-clay80">
      <div className="flex flex-col items-center text-center">
        <TypeIt
          className="justify-center font-bold text-3xl"
          getBeforeInit={(instance) => {
            return instance
              .type('welcome to SENERGY.WORKS', {
                delay: 300,
              })
              .move(-23)
              .pause(460)
              .delete(1)
              .pause(460)
              .type('W')
              .pause(200)
              .move(25, { instant: true })
              .break()
              .pause(500)
              .type("We're excited to have you!", {
                speed: 50,
                delay: 300,
                lifeLike: true,
              });
          }}
          options={{
            cursorSpeed: 100,
            speed: 40,
            lifeLike: true,
            afterComplete: (instance: TypeItInstance) => {
              instance.destroy();
            },
          }}
        />
      </div>
    </div>
  );
}
