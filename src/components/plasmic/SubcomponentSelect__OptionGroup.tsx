import * as React from "react";
import {
  DefaultSubcomponentSelect__OptionGroupProps,
  PlasmicSubcomponentSelect__OptionGroup
} from "./autogenerated/senergy_platform/PlasmicSubcomponentSelect__OptionGroup";

export interface SubcomponentSelect__OptionGroupProps
  extends DefaultSubcomponentSelect__OptionGroupProps {
  // Feel free to add any additional props that this component should receive
}
function SubcomponentSelect__OptionGroup(
  props: SubcomponentSelect__OptionGroupProps
) {
  const { plasmicProps } =
    PlasmicSubcomponentSelect__OptionGroup.useBehavior(props);
  return <PlasmicSubcomponentSelect__OptionGroup {...plasmicProps} />;
}

export default Object.assign(
  SubcomponentSelect__OptionGroup,

  {
    __plumeType: "select-option-group"
  }
);
