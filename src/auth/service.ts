import { toast } from 'sonner';
import { authClient } from './client';
import { storeMagicLinkData } from './utils';

export type AuthLoadingType = 'email' | 'passkey' | 'linkedin' | null;

export interface MagicLinkOptions {
  email: string;
  callbackURL?: string;
  onRequest?: () => void;
  onSuccess?: (email: string) => void;
  onError?: (error: Error) => void;
}

export interface SocialAuthOptions {
  provider: 'linkedin';
  callbackURL?: string;
  errorCallbackURL?: string;
  newUserCallbackURL?: string;
  onRequest?: () => void;
  onSuccess?: () => void;
  onError?: (error: Error) => void;
}

export const sendMagicLink = async ({
  email,
  callbackURL = window.location.href,
  onRequest,
  onSuccess,
  onError,
}: MagicLinkOptions) => {
  try {
    if (onRequest) {
      onRequest();
    }

    await authClient.signIn.magicLink(
      {
        email,
        callbackURL,
      },
      {
        onRequest: () => {
          // Internal auth client request handler
        },
        onSuccess: () => {
          storeMagicLinkData(email);
          if (onSuccess) {
            onSuccess(email);
          }
        },
        onError: (ctx) => {
          const errorMessage = ctx.error?.message ?? 'Unknown error';
          toast.error(`Failed to send magic link: ${errorMessage}`);
          if (onError) {
            onError(new Error(errorMessage));
          }
        },
      }
    );
  } catch (error) {
    const errorMessage =
      error instanceof Error ? error.message : 'Unknown error occurred';
    toast.error(`Failed to send magic link: ${errorMessage}`);
    if (onError) {
      onError(new Error(errorMessage));
    }
  }
};

export const socialSignIn = async ({
  provider,
  callbackURL = window.location.href,
  errorCallbackURL = window.location.href,
  newUserCallbackURL = window.location.href,
  onRequest,
  onSuccess,
  onError,
}: SocialAuthOptions) => {
  try {
    if (onRequest) {
      onRequest();
    }

    await authClient.signIn.social(
      {
        provider,
        callbackURL,
        errorCallbackURL,
        newUserCallbackURL,
      },
      {
        onSuccess: () => {
          if (onSuccess) {
            onSuccess();
          }
        },
        onError: (ctx) => {
          const errorMessage = ctx.error?.message ?? 'Unknown error';
          toast.error(`Failed to sign in with ${provider}: ${errorMessage}`);
          if (onError) {
            onError(new Error(errorMessage));
          }
        },
      }
    );
  } catch (error) {
    const errorMessage =
      error instanceof Error ? error.message : 'Unknown error occurred';
    toast.error(`Failed to sign in with ${provider}: ${errorMessage}`);
    if (onError) {
      onError(new Error(errorMessage));
    }
  }
};

export const passkeySignIn = async ({
  onRequest,
  onSuccess,
  onError,
}: {
  onRequest?: () => void;
  onSuccess?: () => void;
  onError?: (error: Error) => void;
}) => {
  try {
    if (onRequest) {
      onRequest();
    }

    await authClient.signIn.passkey();
    if (onSuccess) {
      onSuccess();
    }
  } catch (error) {
    const errorMessage =
      error instanceof Error ? error.message : 'Unknown error occurred';
    toast.error(`Failed to sign in with passkey: ${errorMessage}`);
    if (onError) {
      onError(new Error(errorMessage));
    }
  }
};
