'use client';

import { Button, toast } from '@payloadcms/ui';
import { useRouter } from 'next/navigation';
import { useState } from 'react';
import TypeIt from 'typeit-react';
import { authClient } from '@/auth/client';

export default function SignOut() {
  const [isLoading, setLoading] = useState(false);
  const router = useRouter();

  const handleSignOut = async () => {
    setLoading(true);
    try {
      await authClient.signOut();
      toast.success('Successfully signed out');
      router.push('/');
    } catch (_error) {
      toast.error('Failed to sign out. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  return (
    <Button
      buttonStyle="icon-label"
      disabled={isLoading}
      onClick={handleSignOut}
      size="large"
      type="button"
    >
      {isLoading ? (
        <div>
          Signing out
          <TypeIt
            options={{
              strings: ['...'],
              speed: 200,
              loop: true,
              loopDelay: 0,
              cursor: false,
            }}
          />
        </div>
      ) : (
        'Sign Out'
      )}
    </Button>
  );
}
