.educationSection {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: flex-start;
  width: 100%;
  height: auto;
  max-width: 100%;
  position: relative;
  justify-self: flex-start;
  margin-bottom: 12px;
  grid-column-start: 3 !important;
  grid-column-end: -3 !important;
}
.profileSectionsProfileSectionHeading:global(.__wab_instance) {
  max-width: 100%;
  position: relative;
}
.displayContainer {
  display: flex;
  position: relative;
  flex-direction: column;
  align-items: flex-start;
  justify-content: center;
  width: 100%;
  height: auto;
  max-width: 100%;
  min-width: 0;
}
.educationTile:global(.__wab_instance) {
  max-width: 100%;
  position: relative;
}
