import type { Field } from 'payload';

const WORD_SPLIT_REGEX = /\s+/;

export const certificationFields: Field[] = [
  {
    name: 'certification_name',
    label: 'Certification Name',
    type: 'text',
    required: true,
  },
  {
    type: 'row',
    fields: [
      {
        name: 'certifying_body',
        label: 'Certifying Body',
        type: 'text',
        required: true,
      },
      {
        name: 'instructor',
        label: 'Instructor',
        type: 'text',
        required: false,
      },
    ],
  },
  {
    type: 'row',
    fields: [
      {
        name: 'registration_number',
        label: 'Registration Number',
        type: 'text',
        required: false,
      },
      {
        name: 'location_of_award',
        label: 'Location of Award',
        type: 'text',
      },
    ],
  },
  {
    type: 'row',
    fields: [
      {
        name: 'date_of_completion',
        label: 'Date of Completion',
        type: 'date',
        required: true,
      },
      {
        name: 'expiration_date',
        label: 'Expiration Date',
        type: 'date',
        required: false,
      },
    ],
  },
  {
    name: 'certification_description',
    label: 'Certification Description',
    admin: {
      description: 'Description for the certification',
    },
    type: 'textarea',
    required: false,
    validate: (value: string | null | undefined) => {
      if (value) {
        const wordCount = value.trim().split(WORD_SPLIT_REGEX).length;
        if (wordCount > 300) {
          return `Description must not exceed 300 words. Current word count: ${wordCount}`;
        }
      }
      return true;
    },
  },
  {
    name: 'visible',
    type: 'checkbox',
    required: false,
    defaultValue: true,
  },
];
