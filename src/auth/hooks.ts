import { useRouter } from 'next/navigation';
import { useCallback, useEffect, useState } from 'react';
import { authClient } from './client';
import { clearMagicLinkData, getMagicLinkData } from './utils';

export interface UseMagicLinkResult {
  magicLinkSent: boolean;
  sentEmail: string;
  isExpired: boolean;
  secondsLeft: number;
  clearMagicLink: () => void;
  syncWithStorage: () => void;
}

/**
 * Hook to check authentication status periodically and redirect if authenticated
 * Useful for login pages to handle cross-tab authentication
 */
export function useAuthRedirect() {
  const router = useRouter();

  useEffect(() => {
    // Check immediately on mount
    checkAuthAndRedirect();

    // Check periodically (every 5 seconds)
    const interval = setInterval(checkAuthAndRedirect, 2000);

    // Listen for storage events (for cross-tab communication)
    const handleStorageChange = () => {
      checkAuthAndRedirect();
    };

    window.addEventListener('storage', handleStorageChange);

    return () => {
      clearInterval(interval);
      window.removeEventListener('storage', handleStorageChange);
    };

    async function checkAuthAndRedirect() {
      try {
        const session = await authClient.getSession();
        if (session.data?.user) {
          // User is authenticated, redirect to specified path
          clearMagicLinkData(); // Clear magic link data
          router.refresh();
        }
      } catch (_error) {
        // Ignore errors, we'll try again later
      }
    }
  }, [router]);
}

export function useMagicLink(): UseMagicLinkResult {
  const [magicLinkSent, setMagicLinkSent] = useState(false);
  const [sentEmail, setSentEmail] = useState('');
  const [isExpired, setIsExpired] = useState(false);
  const [secondsLeft, setSecondsLeft] = useState(0);

  const syncWithStorage = useCallback(() => {
    const magicLinkData = getMagicLinkData();

    if (magicLinkData) {
      setMagicLinkSent(true);
      setSentEmail(magicLinkData.email);
      setIsExpired(magicLinkData.isExpired);
      setSecondsLeft(magicLinkData.secondsLeft);
    }
  }, []);

  // Check localStorage on component mount
  useEffect(() => {
    syncWithStorage();
  }, [syncWithStorage]);

  // Update countdown timer
  useEffect(() => {
    if (!magicLinkSent || isExpired) {
      return;
    }

    const interval = setInterval(() => {
      const magicLinkData = getMagicLinkData();

      if (!magicLinkData) {
        setMagicLinkSent(false);
        setSentEmail('');
        setIsExpired(false);
        clearInterval(interval);
        return;
      }

      setSecondsLeft(magicLinkData.secondsLeft);

      if (magicLinkData.isExpired) {
        setIsExpired(true);
        clearMagicLinkData();
        clearInterval(interval);
      }
    }, 1000);

    return () => clearInterval(interval);
  }, [magicLinkSent, isExpired]);

  const clearMagicLink = useCallback(() => {
    clearMagicLinkData();
    setMagicLinkSent(false);
    setSentEmail('');
    setIsExpired(false);
    setSecondsLeft(0);
  }, []);

  return {
    magicLinkSent,
    sentEmail,
    isExpired,
    secondsLeft,
    clearMagicLink,
    syncWithStorage,
  };
}
