'use client';

import {
  PlasmicCanvasHost,
  registerComponent,
} from '@plasmicapp/react-web/lib/host';
import { FloatingImages } from '@/app/(custom)/(site)/components/FloatingImages';
import { TypeItWrapper } from '@/components/plasmic/TypeItWrapper';

export default function PlasmicHost() {
  registerComponent(FloatingImages, {
    name: 'FloatingImages',
    displayName: 'Floating Images',
    importPath: '@/app/(custom)/(site)/components/FloatingImages',
    props: {
      images: {
        type: 'array',
        displayName: 'Images',
        description: 'Array of images to display (max 5)',
        defaultValue: undefined,
        itemType: {
          type: 'object',
          fields: {
            url: {
              type: 'string',
              displayName: 'Image URL',
              description: 'URL of the image',
            },
            title: {
              type: 'string',
              displayName: 'Image Title',
              description: 'Title/alt text for the image',
            },
            author: {
              type: 'string',
              displayName: 'Image Author',
              description: 'Author of the image (optional)',
            },
          },
        },
      },
      sensitivity: {
        type: 'number',
        displayName: 'Floating Sensitivity',
        description: 'Controls how much the images float (-1 to 1)',
        defaultValue: -0.5,
      },
    },
  });

  registerComponent(TypeItWrapper, {
    name: 'TypeIt',
    displayName: 'Type It Effect',
    importPath: '@/components/plasmic/TypeItWrapper',
    props: {
      speed: {
        type: 'number',
        displayName: 'Speed',
        description: 'Typing speed in milliseconds',
        defaultValue: 50,
      },
      cursor: {
        type: 'boolean',
        displayName: 'Show Cursor',
        description: 'Whether to show the typing cursor',
        defaultValue: true,
      },
      loop: {
        type: 'boolean',
        displayName: 'Loop',
        description: 'Whether to loop the typing animation',
        defaultValue: false,
      },
      waitUntilVisible: {
        type: 'boolean',
        displayName: 'Wait Until Visible',
        description: 'Whether to start typing only when the element is visible',
        defaultValue: true,
      },
    },
  });

  return <PlasmicCanvasHost />;
}
