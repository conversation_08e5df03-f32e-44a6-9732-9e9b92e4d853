/* eslint-disable */
/* tslint:disable */
// @ts-nocheck
/* prettier-ignore-start */
import React from "react";
import { classNames } from "@plasmicapp/react-web";

export type InfoGraphicIconProps = React.ComponentProps<"svg"> & {
  title?: string;
};

export function InfoGraphicIcon(props: InfoGraphicIconProps) {
  const { className, style, title, ...restProps } = props;
  return (
    <svg
      xmlns={"http://www.w3.org/2000/svg"}
      className={classNames(
        "plasmic-default__svg",
        className,
        "icon icon-tabler icon-tabler-chart-infographic"
      )}
      viewBox={"0 0 24 24"}
      strokeWidth={"1.5"}
      stroke={"currentColor"}
      fill={"none"}
      strokeLinecap={"round"}
      strokeLinejoin={"round"}
      height={"1em"}
      style={style}
      {...restProps}
    >
      {title && <title>{title}</title>}

      <path d={"M0 0h24v24H0z"} fill={"none"} stroke={"none"}></path>

      <path d={"M3 7a4 4 0 108 0 4 4 0 10-8 0"}></path>

      <path d={"M7 3v4h4M9 17v4m8-7v7m-4-8v8m8-9v9"}></path>
    </svg>
  );
}

export default InfoGraphicIcon;
/* prettier-ignore-end */
