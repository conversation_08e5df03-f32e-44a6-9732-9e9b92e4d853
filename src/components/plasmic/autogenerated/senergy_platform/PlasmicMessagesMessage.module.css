.messageContainer {
  position: relative;
  padding-top: 12px;
  padding-bottom: 8px;
  margin-bottom: 0px;
  display: flex;
  flex-direction: row;
  width: 100%;
  justify-content: flex-start;
  align-items: flex-start;
  margin-left: 0px;
  padding-left: 24px;
  background: none;
  overflow: hidden;
  min-width: 0;
}
.messageContainersentReceived_received {
  background: #00000005;
  padding-top: 8px;
}
.messageContainerchainMessages_chainText {
  padding-top: 4px;
  margin-bottom: 0px;
  padding-bottom: 8px;
  padding-left: 72px;
}
.messageContainerchainMessages_chainAttachment {
  padding-left: 72px;
}
.messageContainerchainMessages_chainTextAndAttachment {
  padding-bottom: 16px;
  padding-left: 72px;
}
.messageContainersentReceived_received_chainMessages_chainText {
  padding-top: 0px;
  padding-bottom: 0px;
}
.messageContainer:hover {
  transform: none;
  background: #d9e5dd2e;
}
.img {
  position: relative;
  object-fit: cover;
  max-width: 100%;
  width: var(--token-xKF2PGG60dHg);
  height: var(--token-VuPwbNNk9FIa);
  margin-top: 4px;
  margin-right: 8px;
  flex-shrink: 0;
  border-radius: 100%;
}
.img > picture > img {
  object-fit: cover;
}
.imgchainMessages_chainText {
  display: none !important;
}
.imgchainMessages_chainAttachment {
  display: none !important;
}
.imgchainMessages_chainTextAndAttachment {
  display: none !important;
}
.messageContent {
  display: flex;
  position: relative;
  flex-direction: column;
  align-items: flex-start;
  justify-content: flex-start;
  width: 100%;
  height: auto;
  max-width: 100%;
  min-width: 0;
}
.messageContentsentReceived_sent {
  padding-bottom: 4px;
}
.messageContentsentReceived_received {
  padding-bottom: 4px;
}
.messageContentsentReceived_received_chainMessages_chainText {
  padding-bottom: 0px;
}
.formattingStack {
  display: flex;
  position: relative;
  flex-direction: column;
  align-items: flex-start;
  justify-content: flex-start;
  width: 100%;
  height: auto;
  max-width: 100%;
  min-width: 0;
  padding: var(--token-sazGmnf7GWAk);
  margin: 0px var(--token-sazGmnf7GWAk) var(--token-sazGmnf7GWAk);
}
.formattingStackchainMessages_chainText {
  margin-bottom: 0px;
  padding-bottom: 8px;
  padding-top: 4px;
}
.timeAndName {
  display: flex;
  width: auto;
  flex-direction: row;
}
.timeAndName > :global(.__wab_flex-container) {
  flex-direction: row;
  justify-content: flex-start;
  align-items: center;
  margin-left: calc(0px - var(--token-4Wrp9mDZCSCQ));
  width: calc(100% + var(--token-4Wrp9mDZCSCQ));
}
.timeAndName > :global(.__wab_flex-container) > *,
.timeAndName > :global(.__wab_flex-container) > :global(.__wab_slot) > *,
.timeAndName > :global(.__wab_flex-container) > picture > img,
.timeAndName
  > :global(.__wab_flex-container)
  > :global(.__wab_slot)
  > picture
  > img {
  margin-left: var(--token-4Wrp9mDZCSCQ);
}
.timeAndNamemessageType_attachment {
  margin-bottom: 8px;
}
.timeAndNamechainMessages_chainText {
  display: none;
}
.timeAndNamechainMessages_chainAttachment {
  display: none;
}
.timeAndNamechainMessages_chainTextAndAttachment {
  display: none;
}
.text__fHFnz {
  position: relative;
  width: auto;
  height: auto;
  max-width: 100%;
  color: var(--token-XQQdLIBrONt7);
  margin-left: calc(0px + var(--token-4Wrp9mDZCSCQ)) !important;
}
.textsentReceived_received__fHFnzGnXii {
  color: var(--token-v4jufhOu3lt9);
  margin-left: calc(0px + var(--token-4Wrp9mDZCSCQ)) !important;
}
.textmessageType_attachment__fHFnzobfOf {
  margin-left: calc(0px + var(--token-4Wrp9mDZCSCQ)) !important;
}
.textchainMessages_chainText__fHFnzGeCe8 {
  margin-left: calc(0px + var(--token-4Wrp9mDZCSCQ)) !important;
}
.textchainMessages_chainAttachment__fHFnziRcWb {
  margin-left: calc(0px + var(--token-4Wrp9mDZCSCQ)) !important;
}
.textchainMessages_chainTextAndAttachment__fHFnzszblr {
  margin-left: calc(0px + var(--token-4Wrp9mDZCSCQ)) !important;
}
.messageContainer:hover .text__fHFnz {
  margin-left: calc(0px + var(--token-4Wrp9mDZCSCQ)) !important;
}
.text__mSb9J {
  position: relative;
  width: auto;
  height: auto;
  max-width: 100%;
  margin-left: calc(0px + var(--token-4Wrp9mDZCSCQ)) !important;
  font-size: var(--token-agfFfkxgxH6d);
  font-weight: 400;
  margin-bottom: -2px;
  padding-bottom: 1px;
  color: var(--token-3OLw18f8JRN3);
}
.textsentReceived_sent__mSb9Jas4Ki {
  margin-left: calc(0px + var(--token-4Wrp9mDZCSCQ)) !important;
}
.textmessageType_attachment__mSb9JobfOf {
  margin-left: calc(0px + var(--token-4Wrp9mDZCSCQ)) !important;
}
.messageBody {
  position: relative;
  width: auto;
  height: auto;
  max-width: 100%;
  margin-left: 0px;
  display: flex;
  flex-direction: row;
}
.messageBodymessageType_attachment {
  display: none;
}
.messageBodymessageType_textAndAttachment {
  margin-bottom: 4px;
}
.messageBodychainMessages_chainText {
  margin-left: 44px;
}
.messageBodychainMessages_chainAttachment {
  margin-left: 44px;
  display: none;
}
.messageBodychainMessages_chainTextAndAttachment {
  margin-left: 49px;
}
.messageBodysentReceived_received_chainMessages_chainText {
  margin-left: 48px;
}
.messageBodysentReceived_sent_chainMessages_chainTextAndAttachment {
  margin-left: 0px;
}
.messageBody2 {
  position: relative;
  width: auto;
  height: auto;
  max-width: 100%;
  margin-left: 0px;
  display: flex;
  flex-direction: row;
}
.messageBody2messageType_attachment {
  display: flex;
}
.messageBody2messageType_textAndAttachment {
  display: flex;
}
.messageBody2chainMessages_chainText {
  margin-left: 44px;
  display: none;
}
.messageBody2chainMessages_chainAttachment {
  margin-left: 48px;
  display: flex;
}
.messageBody2chainMessages_chainTextAndAttachment {
  margin-left: 48px;
  display: flex;
}
.messageBody2sentReceived_sent_chainMessages_chainTextAndAttachment {
  margin-left: 0px;
}
.messageBody2sentReceived_sent_chainMessages_chainAttachment_messageType_attachment {
  margin-left: 0px;
}
.messagesAttachments__ldNrC:global(.__wab_instance) {
  max-width: 100%;
  position: relative;
}
.attachmentSlotFormatting {
  position: relative;
  width: auto;
  margin-left: 0px;
  flex-direction: column;
  display: none;
  padding: var(--token-sazGmnf7GWAk);
}
.text__iMuIi {
  position: relative;
  width: 100%;
  height: auto;
  max-width: 100%;
  font-size: var(--token-agfFfkxgxH6d);
  color: var(--token-3OLw18f8JRN3);
  padding-top: 0px;
  min-width: 0;
}
.messagesAttachments__sEmIx:global(.__wab_instance) {
  max-width: 100%;
  position: relative;
}
