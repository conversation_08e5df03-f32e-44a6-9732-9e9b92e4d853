.aboutMeSection {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: flex-start;
  width: 100%;
  height: auto;
  max-width: 100%;
  background: none;
  position: relative;
  justify-self: flex-start;
  margin-bottom: 0px;
  grid-column-start: 3 !important;
  grid-column-end: -3 !important;
  padding: 0px;
}
.profileSectionsProfileSectionHeading:global(.__wab_instance) {
  max-width: 100%;
  position: relative;
}
.textoverviewGridBlock {
  font-size: var(--token-9KumB6TRpaad);
}
.displayContainer {
  display: flex;
  position: relative;
  width: 100%;
  flex-direction: column;
  min-width: 0;
  padding: var(--token-sazGmnf7GWAk);
}
.profileTileIntroductionTile:global(.__wab_instance) {
  max-width: 100%;
  position: relative;
}
