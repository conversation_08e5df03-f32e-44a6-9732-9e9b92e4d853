/* eslint-disable */
/* tslint:disable */
// @ts-nocheck
/* prettier-ignore-start */

/** @jsxRuntime classic */
/** @jsx createPlasmicElementProxy */
/** @jsxFrag React.Fragment */

// This class is auto-generated by Plasmic; please do not edit!
// Plasmic Project: nZeWKcn21KijvC7eT8B3c7
// Component: iicTcR3JeMJ9

"use client";

import * as React from "react";

import Head from "next/head";
import Link, { LinkProps } from "next/link";
import { useRouter } from "next/navigation";

import {
  Flex as Flex__,
  MultiChoiceArg,
  PlasmicDataSourceContextProvider as PlasmicDataSourceContextProvider__,
  PlasmicIcon as PlasmicIcon__,
  PlasmicImg as PlasmicImg__,
  PlasmicLink as PlasmicLink__,
  PlasmicPageGuard as PlasmicPageGuard__,
  SingleBooleanChoiceArg,
  SingleChoiceArg,
  Stack as Stack__,
  StrictProps,
  Trans as Trans__,
  classNames,
  createPlasmicElementProxy,
  deriveRenderOpts,
  ensureGlobalVariants,
  generateOnMutateForSpec,
  generateStateOnChangeProp,
  generateStateOnChangePropForCodeComponents,
  generateStateValueProp,
  get as $stateGet,
  hasVariant,
  initializeCodeComponentStates,
  initializePlasmicStates,
  makeFragment,
  omit,
  pick,
  renderPlasmicSlot,
  set as $stateSet,
  useCurrentUser,
  useDollarState,
  usePlasmicTranslator,
  useTrigger,
  wrapWithClassName
} from "@plasmicapp/react-web";
import {
  DataCtxReader as DataCtxReader__,
  useDataEnv,
  useGlobalActions
} from "@plasmicapp/host";

import "@plasmicapp/react-web/lib/plasmic.css";

import projectcss from "./plasmic.module.css"; // plasmic-import: nZeWKcn21KijvC7eT8B3c7/projectcss
import sty from "./PlasmicMessagesMessagePreview.module.css"; // plasmic-import: iicTcR3JeMJ9/css

createPlasmicElementProxy;

export type PlasmicMessagesMessagePreview__VariantMembers = {
  readStatus: "read" | "unread";
  modalPreview: "messageModalPreview" | "groupChat";
};
export type PlasmicMessagesMessagePreview__VariantsArgs = {
  readStatus?: SingleChoiceArg<"read" | "unread">;
  modalPreview?: SingleChoiceArg<"messageModalPreview" | "groupChat">;
};
type VariantPropType = keyof PlasmicMessagesMessagePreview__VariantsArgs;
export const PlasmicMessagesMessagePreview__VariantProps =
  new Array<VariantPropType>("readStatus", "modalPreview");

export type PlasmicMessagesMessagePreview__ArgsType = {
  onClick?: (event: any) => void;
};
type ArgPropType = keyof PlasmicMessagesMessagePreview__ArgsType;
export const PlasmicMessagesMessagePreview__ArgProps = new Array<ArgPropType>(
  "onClick"
);

export type PlasmicMessagesMessagePreview__OverridesType = {
  messagePreviewContainer?: Flex__<"div">;
  usersProfileImage?: Flex__<typeof PlasmicImg__>;
  messageInfoContainer?: Flex__<"div">;
  nameAndTime?: Flex__<"div">;
  coloredDivider?: Flex__<"section">;
};

export interface DefaultMessagesMessagePreviewProps {
  onClick?: (event: any) => void;
  readStatus?: SingleChoiceArg<"read" | "unread">;
  modalPreview?: SingleChoiceArg<"messageModalPreview" | "groupChat">;
  className?: string;
}

const $$ = {};

function useNextRouter() {
  try {
    return useRouter();
  } catch {}
  return undefined;
}

function PlasmicMessagesMessagePreview__RenderFunc(props: {
  variants: PlasmicMessagesMessagePreview__VariantsArgs;
  args: PlasmicMessagesMessagePreview__ArgsType;
  overrides: PlasmicMessagesMessagePreview__OverridesType;
  forNode?: string;
}) {
  const { variants, overrides, forNode } = props;

  const args = React.useMemo(
    () =>
      Object.assign(
        {},
        Object.fromEntries(
          Object.entries(props.args).filter(([_, v]) => v !== undefined)
        )
      ),
    [props.args]
  );

  const $props = {
    ...args,
    ...variants
  };

  const __nextRouter = useNextRouter();
  const $ctx = useDataEnv?.() || {};
  const refsRef = React.useRef({});
  const $refs = refsRef.current;

  let [$queries, setDollarQueries] = React.useState<
    Record<string, ReturnType<typeof usePlasmicDataOp>>
  >({});
  const stateSpecs: Parameters<typeof useDollarState>[0] = React.useMemo(
    () => [
      {
        path: "readStatus",
        type: "private",
        variableType: "variant",
        initFunc: ({ $props, $state, $queries, $ctx }) => $props.readStatus
      },
      {
        path: "modalPreview",
        type: "private",
        variableType: "variant",
        initFunc: ({ $props, $state, $queries, $ctx }) => $props.modalPreview
      }
    ],
    [$props, $ctx, $refs]
  );
  const $state = useDollarState(stateSpecs, {
    $props,
    $ctx,
    $queries: $queries,
    $refs
  });

  const new$Queries: Record<string, ReturnType<typeof usePlasmicDataOp>> = {};
  if (Object.keys(new$Queries).some(k => new$Queries[k] !== $queries[k])) {
    setDollarQueries(new$Queries);

    $queries = new$Queries;
  }

  return (
    <div
      data-plasmic-name={"messagePreviewContainer"}
      data-plasmic-override={overrides.messagePreviewContainer}
      data-plasmic-root={true}
      data-plasmic-for-node={forNode}
      className={classNames(
        projectcss.all,
        projectcss.root_reset,
        projectcss.plasmic_default_styles,
        projectcss.plasmic_mixins,
        projectcss.plasmic_tokens,
        sty.messagePreviewContainer,
        {
          [sty.messagePreviewContainermodalPreview_groupChat]: hasVariant(
            $state,
            "modalPreview",
            "groupChat"
          ),
          [sty.messagePreviewContainermodalPreview_messageModalPreview]:
            hasVariant($state, "modalPreview", "messageModalPreview"),
          [sty.messagePreviewContainerreadStatus_unread]: hasVariant(
            $state,
            "readStatus",
            "unread"
          )
        }
      )}
      onClick={args.onClick}
    >
      <PlasmicImg__
        data-plasmic-name={"usersProfileImage"}
        data-plasmic-override={overrides.usersProfileImage}
        alt={""}
        className={classNames(sty.usersProfileImage)}
        displayHeight={"48px"}
        displayMaxHeight={"none"}
        displayMaxWidth={"100%"}
        displayMinHeight={"0"}
        displayMinWidth={"0"}
        displayWidth={"48px"}
        loading={"lazy"}
        src={{
          src: "/plasmic/senergy_platform/images/imagePlaceholderPng.png",
          fullWidth: 800,
          fullHeight: 600,
          aspectRatio: undefined
        }}
      />

      <div
        data-plasmic-name={"messageInfoContainer"}
        data-plasmic-override={overrides.messageInfoContainer}
        className={classNames(projectcss.all, sty.messageInfoContainer)}
      >
        <div
          data-plasmic-name={"nameAndTime"}
          data-plasmic-override={overrides.nameAndTime}
          className={classNames(projectcss.all, sty.nameAndTime, {
            [sty.nameAndTimemodalPreview_groupChat]: hasVariant(
              $state,
              "modalPreview",
              "groupChat"
            ),
            [sty.nameAndTimemodalPreview_messageModalPreview]: hasVariant(
              $state,
              "modalPreview",
              "messageModalPreview"
            )
          })}
        >
          <div
            className={classNames(
              projectcss.all,
              projectcss.__wab_text,
              sty.text__feQum
            )}
          >
            {"Firstname Lastname"}
          </div>
          <div
            className={classNames(
              projectcss.all,
              projectcss.__wab_text,
              sty.text__heXi,
              {
                [sty.textmodalPreview_groupChat__heXIhjjdH]: hasVariant(
                  $state,
                  "modalPreview",
                  "groupChat"
                ),
                [sty.textmodalPreview_messageModalPreview__heXieHxU]:
                  hasVariant($state, "modalPreview", "messageModalPreview"),
                [sty.textreadStatus_unread__heXiOxst5]: hasVariant(
                  $state,
                  "readStatus",
                  "unread"
                )
              }
            )}
          >
            {hasVariant($state, "readStatus", "unread") ? "Unread" : "12:00 PM"}
          </div>
        </div>
        <div
          className={classNames(
            projectcss.all,
            projectcss.__wab_text,
            sty.text__wwlEb,
            {
              [sty.textmodalPreview_groupChat__wwlEbhjjdH]: hasVariant(
                $state,
                "modalPreview",
                "groupChat"
              ),
              [sty.textmodalPreview_messageModalPreview__wwlEbEHxU]: hasVariant(
                $state,
                "modalPreview",
                "messageModalPreview"
              )
            }
          )}
        >
          {hasVariant($state, "modalPreview", "groupChat")
            ? "Commodo pariatur sunt est deserunt..."
            : hasVariant($state, "modalPreview", "messageModalPreview")
            ? "Commodo pariatur sunt est deserunt..."
            : "Commodo pariatur sunt est eserunt elit cupidatat qui reprehenderit nulll"}
        </div>
      </div>
      <section
        data-plasmic-name={"coloredDivider"}
        data-plasmic-override={overrides.coloredDivider}
        className={classNames(projectcss.all, sty.coloredDivider)}
      />
    </div>
  ) as React.ReactElement | null;
}

const PlasmicDescendants = {
  messagePreviewContainer: [
    "messagePreviewContainer",
    "usersProfileImage",
    "messageInfoContainer",
    "nameAndTime",
    "coloredDivider"
  ],
  usersProfileImage: ["usersProfileImage"],
  messageInfoContainer: ["messageInfoContainer", "nameAndTime"],
  nameAndTime: ["nameAndTime"],
  coloredDivider: ["coloredDivider"]
} as const;
type NodeNameType = keyof typeof PlasmicDescendants;
type DescendantsType<T extends NodeNameType> =
  (typeof PlasmicDescendants)[T][number];
type NodeDefaultElementType = {
  messagePreviewContainer: "div";
  usersProfileImage: typeof PlasmicImg__;
  messageInfoContainer: "div";
  nameAndTime: "div";
  coloredDivider: "section";
};

type ReservedPropsType = "variants" | "args" | "overrides";
type NodeOverridesType<T extends NodeNameType> = Pick<
  PlasmicMessagesMessagePreview__OverridesType,
  DescendantsType<T>
>;
type NodeComponentProps<T extends NodeNameType> =
  // Explicitly specify variants, args, and overrides as objects
  {
    variants?: PlasmicMessagesMessagePreview__VariantsArgs;
    args?: PlasmicMessagesMessagePreview__ArgsType;
    overrides?: NodeOverridesType<T>;
  } & Omit<PlasmicMessagesMessagePreview__VariantsArgs, ReservedPropsType> & // Specify variants directly as props
    /* Specify args directly as props*/ Omit<
      PlasmicMessagesMessagePreview__ArgsType,
      ReservedPropsType
    > &
    /* Specify overrides for each element directly as props*/ Omit<
      NodeOverridesType<T>,
      ReservedPropsType | VariantPropType | ArgPropType
    > &
    /* Specify props for the root element*/ Omit<
      Partial<React.ComponentProps<NodeDefaultElementType[T]>>,
      ReservedPropsType | VariantPropType | ArgPropType | DescendantsType<T>
    >;

function makeNodeComponent<NodeName extends NodeNameType>(nodeName: NodeName) {
  type PropsType = NodeComponentProps<NodeName> & { key?: React.Key };
  const func = function <T extends PropsType>(
    props: T & StrictProps<T, PropsType>
  ) {
    const { variants, args, overrides } = React.useMemo(
      () =>
        deriveRenderOpts(props, {
          name: nodeName,
          descendantNames: PlasmicDescendants[nodeName],
          internalArgPropNames: PlasmicMessagesMessagePreview__ArgProps,
          internalVariantPropNames: PlasmicMessagesMessagePreview__VariantProps
        }),
      [props, nodeName]
    );
    return PlasmicMessagesMessagePreview__RenderFunc({
      variants,
      args,
      overrides,
      forNode: nodeName
    });
  };
  if (nodeName === "messagePreviewContainer") {
    func.displayName = "PlasmicMessagesMessagePreview";
  } else {
    func.displayName = `PlasmicMessagesMessagePreview.${nodeName}`;
  }
  return func;
}

export const PlasmicMessagesMessagePreview = Object.assign(
  // Top-level PlasmicMessagesMessagePreview renders the root element
  makeNodeComponent("messagePreviewContainer"),
  {
    // Helper components rendering sub-elements
    usersProfileImage: makeNodeComponent("usersProfileImage"),
    messageInfoContainer: makeNodeComponent("messageInfoContainer"),
    nameAndTime: makeNodeComponent("nameAndTime"),
    coloredDivider: makeNodeComponent("coloredDivider"),

    // Metadata about props expected for PlasmicMessagesMessagePreview
    internalVariantProps: PlasmicMessagesMessagePreview__VariantProps,
    internalArgProps: PlasmicMessagesMessagePreview__ArgProps
  }
);

export default PlasmicMessagesMessagePreview;
/* prettier-ignore-end */
