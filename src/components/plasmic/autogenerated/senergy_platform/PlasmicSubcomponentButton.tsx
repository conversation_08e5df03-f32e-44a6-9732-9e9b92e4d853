/* eslint-disable */
/* tslint:disable */
// @ts-nocheck
/* prettier-ignore-start */

/** @jsxRuntime classic */
/** @jsx createPlasmicElementProxy */
/** @jsxFrag React.Fragment */

// This class is auto-generated by Plasmic; please do not edit!
// Plasmic Project: nZeWKcn21KijvC7eT8B3c7
// Component: ezhuRZvm_fH9

"use client";

import * as React from "react";

import Head from "next/head";
import Link, { LinkProps } from "next/link";
import { useRouter } from "next/navigation";

import {
  Flex as Flex__,
  MultiChoiceArg,
  PlasmicDataSourceContextProvider as PlasmicDataSourceContextProvider__,
  PlasmicIcon as PlasmicIcon__,
  PlasmicImg as PlasmicImg__,
  PlasmicLink as PlasmicLink__,
  PlasmicPageGuard as PlasmicPageGuard__,
  SingleBooleanChoiceArg,
  SingleChoiceArg,
  Stack as Stack__,
  StrictProps,
  Trans as Trans__,
  classNames,
  createPlasmicElementProxy,
  deriveRenderOpts,
  ensureGlobalVariants,
  generateOnMutateForSpec,
  generateStateOnChangeProp,
  generateStateOnChangePropForCodeComponents,
  generateStateValueProp,
  get as $stateGet,
  hasVariant,
  initializeCodeComponentStates,
  initializePlasmicStates,
  makeFragment,
  omit,
  pick,
  renderPlasmicSlot,
  set as $stateSet,
  useCurrentUser,
  useDollarState,
  usePlasmicTranslator,
  useTrigger,
  wrapWithClassName
} from "@plasmicapp/react-web";
import {
  DataCtxReader as DataCtxReader__,
  useDataEnv,
  useGlobalActions
} from "@plasmicapp/host";

import * as pp from "@plasmicapp/react-web";

import "@plasmicapp/react-web/lib/plasmic.css";

import projectcss from "./plasmic.module.css"; // plasmic-import: nZeWKcn21KijvC7eT8B3c7/projectcss
import sty from "./PlasmicSubcomponentButton.module.css"; // plasmic-import: ezhuRZvm_fH9/css

createPlasmicElementProxy;

export type PlasmicSubcomponentButton__VariantMembers = {
  showStartIcon: "showStartIcon";
  showEndIcon: "showEndIcon";
  isDisabled: "isDisabled";
  shape: "rounded" | "sharp";
  size: "compact" | "minimal";
  styling: "square" | "cameo" | "typewriter" | "nittiWColor";
  justification: "left" | "right";
  selected: "selected";
  color: "eggplant" | "forest" | "sage" | "sunflower";
  disabledAnimation: "disabled" | "shakeLeft" | "shakeRight" | "disabledText";
};
export type PlasmicSubcomponentButton__VariantsArgs = {
  showStartIcon?: SingleBooleanChoiceArg<"showStartIcon">;
  showEndIcon?: SingleBooleanChoiceArg<"showEndIcon">;
  isDisabled?: SingleBooleanChoiceArg<"isDisabled">;
  shape?: SingleChoiceArg<"rounded" | "sharp">;
  size?: SingleChoiceArg<"compact" | "minimal">;
  styling?: MultiChoiceArg<"square" | "cameo" | "typewriter" | "nittiWColor">;
  justification?: SingleChoiceArg<"left" | "right">;
  selected?: SingleBooleanChoiceArg<"selected">;
  color?: SingleChoiceArg<"eggplant" | "forest" | "sage" | "sunflower">;
  disabledAnimation?: SingleChoiceArg<
    "disabled" | "shakeLeft" | "shakeRight" | "disabledText"
  >;
};
type VariantPropType = keyof PlasmicSubcomponentButton__VariantsArgs;
export const PlasmicSubcomponentButton__VariantProps =
  new Array<VariantPropType>(
    "showStartIcon",
    "showEndIcon",
    "isDisabled",
    "shape",
    "size",
    "styling",
    "justification",
    "selected",
    "color",
    "disabledAnimation"
  );

export type PlasmicSubcomponentButton__ArgsType = {
  children?: React.ReactNode;
  endIcon?: React.ReactNode;
  link?: string;
  submitsForm?: boolean;
  target?: boolean;
  startIcon?: React.ReactNode;
  disabledShakeText?: string;
};
type ArgPropType = keyof PlasmicSubcomponentButton__ArgsType;
export const PlasmicSubcomponentButton__ArgProps = new Array<ArgPropType>(
  "children",
  "endIcon",
  "link",
  "submitsForm",
  "target",
  "startIcon",
  "disabledShakeText"
);

export type PlasmicSubcomponentButton__OverridesType = {
  root?: Flex__<"button">;
  formattingContainer?: Flex__<"div">;
  contentContainer?: Flex__<"div">;
  startIconContainer?: Flex__<"div">;
  text?: Flex__<"div">;
  endIconContainer?: Flex__<"div">;
  underline?: Flex__<"section">;
  disabledText?: Flex__<"div">;
};

export interface DefaultSubcomponentButtonProps extends pp.BaseButtonProps {
  submitsForm?: boolean;
  target?: boolean;
  disabledShakeText?: string;
  shape?: SingleChoiceArg<"rounded" | "sharp">;
  size?: SingleChoiceArg<"compact" | "minimal">;
  styling?: MultiChoiceArg<"square" | "cameo" | "typewriter" | "nittiWColor">;
  justification?: SingleChoiceArg<"left" | "right">;
  selected?: SingleBooleanChoiceArg<"selected">;
  color?: SingleChoiceArg<"eggplant" | "forest" | "sage" | "sunflower">;
  disabledAnimation?: SingleChoiceArg<
    "disabled" | "shakeLeft" | "shakeRight" | "disabledText"
  >;
}

const $$ = {};

function useNextRouter() {
  try {
    return useRouter();
  } catch {}
  return undefined;
}

function PlasmicSubcomponentButton__RenderFunc(props: {
  variants: PlasmicSubcomponentButton__VariantsArgs;
  args: PlasmicSubcomponentButton__ArgsType;
  overrides: PlasmicSubcomponentButton__OverridesType;
  forNode?: string;
}) {
  const { variants, overrides, forNode } = props;

  const args = React.useMemo(
    () =>
      Object.assign(
        {
          disabledShakeText: "Disabled"
        },
        Object.fromEntries(
          Object.entries(props.args).filter(([_, v]) => v !== undefined)
        )
      ),
    [props.args]
  );

  const $props = {
    ...args,
    ...variants
  };

  const __nextRouter = useNextRouter();
  const $ctx = useDataEnv?.() || {};
  const refsRef = React.useRef({});
  const $refs = refsRef.current;

  let [$queries, setDollarQueries] = React.useState<
    Record<string, ReturnType<typeof usePlasmicDataOp>>
  >({});
  const stateSpecs: Parameters<typeof useDollarState>[0] = React.useMemo(
    () => [
      {
        path: "showStartIcon",
        type: "private",
        variableType: "variant",
        initFunc: ({ $props, $state, $queries, $ctx }) => $props.showStartIcon
      },
      {
        path: "showEndIcon",
        type: "private",
        variableType: "variant",
        initFunc: ({ $props, $state, $queries, $ctx }) => $props.showEndIcon
      },
      {
        path: "isDisabled",
        type: "private",
        variableType: "variant",
        initFunc: ({ $props, $state, $queries, $ctx }) => $props.isDisabled
      },
      {
        path: "shape",
        type: "private",
        variableType: "variant",
        initFunc: ({ $props, $state, $queries, $ctx }) => $props.shape
      },
      {
        path: "size",
        type: "private",
        variableType: "variant",
        initFunc: ({ $props, $state, $queries, $ctx }) => $props.size
      },
      {
        path: "styling",
        type: "private",
        variableType: "variant",
        initFunc: ({ $props, $state, $queries, $ctx }) => $props.styling
      },
      {
        path: "justification",
        type: "private",
        variableType: "variant",
        initFunc: ({ $props, $state, $queries, $ctx }) => $props.justification
      },
      {
        path: "selected",
        type: "private",
        variableType: "variant",
        initFunc: ({ $props, $state, $queries, $ctx }) => $props.selected
      },
      {
        path: "color",
        type: "private",
        variableType: "variant",
        initFunc: ({ $props, $state, $queries, $ctx }) => $props.color
      },
      {
        path: "disabledAnimation",
        type: "private",
        variableType: "variant",
        initFunc: ({ $props, $state, $queries, $ctx }) =>
          $props.disabledAnimation
      }
    ],
    [$props, $ctx, $refs]
  );
  const $state = useDollarState(stateSpecs, {
    $props,
    $ctx,
    $queries: $queries,
    $refs
  });

  const new$Queries: Record<string, ReturnType<typeof usePlasmicDataOp>> = {};
  if (Object.keys(new$Queries).some(k => new$Queries[k] !== $queries[k])) {
    setDollarQueries(new$Queries);

    $queries = new$Queries;
  }

  return (
    <button
      data-plasmic-name={"root"}
      data-plasmic-override={overrides.root}
      data-plasmic-root={true}
      data-plasmic-for-node={forNode}
      className={classNames(
        projectcss.all,
        projectcss.button,
        projectcss.root_reset,
        projectcss.plasmic_default_styles,
        projectcss.plasmic_mixins,
        projectcss.plasmic_tokens,
        sty.root,
        {
          [sty.rootcolor_eggplant]: hasVariant($state, "color", "eggplant"),
          [sty.rootcolor_forest]: hasVariant($state, "color", "forest"),
          [sty.rootcolor_sage]: hasVariant($state, "color", "sage"),
          [sty.rootcolor_sunflower]: hasVariant($state, "color", "sunflower"),
          [sty.rootdisabledAnimation_disabledText]: hasVariant(
            $state,
            "disabledAnimation",
            "disabledText"
          ),
          [sty.rootdisabledAnimation_disabled]: hasVariant(
            $state,
            "disabledAnimation",
            "disabled"
          ),
          [sty.rootdisabledAnimation_shakeLeft]: hasVariant(
            $state,
            "disabledAnimation",
            "shakeLeft"
          ),
          [sty.rootdisabledAnimation_shakeRight]: hasVariant(
            $state,
            "disabledAnimation",
            "shakeRight"
          ),
          [sty.rootisDisabled]: hasVariant($state, "isDisabled", "isDisabled"),
          [sty.rootjustification_left]: hasVariant(
            $state,
            "justification",
            "left"
          ),
          [sty.rootjustification_right]: hasVariant(
            $state,
            "justification",
            "right"
          ),
          [sty.rootselected]: hasVariant($state, "selected", "selected"),
          [sty.rootshape_rounded]: hasVariant($state, "shape", "rounded"),
          [sty.rootshape_rounded_showStartIcon]:
            hasVariant($state, "shape", "rounded") &&
            hasVariant($state, "showStartIcon", "showStartIcon"),
          [sty.rootshape_sharp]: hasVariant($state, "shape", "sharp"),
          [sty.rootshowEndIcon]: hasVariant(
            $state,
            "showEndIcon",
            "showEndIcon"
          ),
          [sty.rootshowEndIcon_shape_rounded]:
            hasVariant($state, "showEndIcon", "showEndIcon") &&
            hasVariant($state, "shape", "rounded"),
          [sty.rootshowEndIcon_size_compact]:
            hasVariant($state, "size", "compact") &&
            hasVariant($state, "showEndIcon", "showEndIcon"),
          [sty.rootshowEndIcon_size_compact_showStartIcon]:
            hasVariant($state, "size", "compact") &&
            hasVariant($state, "showStartIcon", "showStartIcon") &&
            hasVariant($state, "showEndIcon", "showEndIcon"),
          [sty.rootshowStartIcon]: hasVariant(
            $state,
            "showStartIcon",
            "showStartIcon"
          ),
          [sty.rootsize_compact]: hasVariant($state, "size", "compact"),
          [sty.rootsize_compact_shape_rounded]:
            hasVariant($state, "size", "compact") &&
            hasVariant($state, "shape", "rounded"),
          [sty.rootsize_compact_showStartIcon]:
            hasVariant($state, "size", "compact") &&
            hasVariant($state, "showStartIcon", "showStartIcon"),
          [sty.rootsize_minimal]: hasVariant($state, "size", "minimal"),
          [sty.rootstyling_cameo]: hasVariant($state, "styling", "cameo"),
          [sty.rootstyling_nittiWColor]: hasVariant(
            $state,
            "styling",
            "nittiWColor"
          ),
          [sty.rootstyling_square]: hasVariant($state, "styling", "square"),
          [sty.rootstyling_typewriter]: hasVariant(
            $state,
            "styling",
            "typewriter"
          )
        }
      )}
      onClickCapture={async event => {
        const $steps = {};

        $steps["runCode"] =
          $state.isDisabled == true || $state.disabledAnimation != undefined
            ? (() => {
                const actionArgs = {
                  customFunction: async () => {
                    return setTimeout(() => {
                      $state.disabledAnimation = "shakeRight";
                      setTimeout(() => {
                        $state.disabledAnimation = "shakeLeft";
                        setTimeout(() => {
                          $state.disabledAnimation = "shakeRight";
                          setTimeout(() => {
                            $state.disabledAnimation = "shakeLeft";
                            setTimeout(() => {
                              $state.disabledAnimation = "disabledText";
                              setTimeout(() => {
                                $state.disabledAnimation = "disabled";
                              }, 1000);
                            }, 65);
                          }, 65);
                        }, 60);
                      }, 60);
                    }, 60);
                  }
                };
                return (({ customFunction }) => {
                  return customFunction();
                })?.apply(null, [actionArgs]);
              })()
            : undefined;
        if (
          $steps["runCode"] != null &&
          typeof $steps["runCode"] === "object" &&
          typeof $steps["runCode"].then === "function"
        ) {
          $steps["runCode"] = await $steps["runCode"];
        }
      }}
    >
      <Stack__
        as={"div"}
        data-plasmic-name={"formattingContainer"}
        data-plasmic-override={overrides.formattingContainer}
        hasGap={true}
        className={classNames(projectcss.all, sty.formattingContainer, {
          [sty.formattingContainercolor_eggplant]: hasVariant(
            $state,
            "color",
            "eggplant"
          ),
          [sty.formattingContainercolor_forest]: hasVariant(
            $state,
            "color",
            "forest"
          ),
          [sty.formattingContainercolor_sage]: hasVariant(
            $state,
            "color",
            "sage"
          ),
          [sty.formattingContainercolor_sunflower]: hasVariant(
            $state,
            "color",
            "sunflower"
          ),
          [sty.formattingContainerdisabledAnimation_disabledText]: hasVariant(
            $state,
            "disabledAnimation",
            "disabledText"
          ),
          [sty.formattingContainerdisabledAnimation_disabled]: hasVariant(
            $state,
            "disabledAnimation",
            "disabled"
          ),
          [sty.formattingContainerdisabledAnimation_shakeLeft]: hasVariant(
            $state,
            "disabledAnimation",
            "shakeLeft"
          ),
          [sty.formattingContainerdisabledAnimation_shakeRight]: hasVariant(
            $state,
            "disabledAnimation",
            "shakeRight"
          ),
          [sty.formattingContainerisDisabled]: hasVariant(
            $state,
            "isDisabled",
            "isDisabled"
          ),
          [sty.formattingContainerjustification_left]: hasVariant(
            $state,
            "justification",
            "left"
          ),
          [sty.formattingContainerjustification_right]: hasVariant(
            $state,
            "justification",
            "right"
          ),
          [sty.formattingContainerselected]: hasVariant(
            $state,
            "selected",
            "selected"
          ),
          [sty.formattingContainershape_rounded]: hasVariant(
            $state,
            "shape",
            "rounded"
          ),
          [sty.formattingContainershape_rounded_showStartIcon]:
            hasVariant($state, "shape", "rounded") &&
            hasVariant($state, "showStartIcon", "showStartIcon"),
          [sty.formattingContainershape_sharp]: hasVariant(
            $state,
            "shape",
            "sharp"
          ),
          [sty.formattingContainershowEndIcon]: hasVariant(
            $state,
            "showEndIcon",
            "showEndIcon"
          ),
          [sty.formattingContainershowEndIcon_shape_rounded]:
            hasVariant($state, "showEndIcon", "showEndIcon") &&
            hasVariant($state, "shape", "rounded"),
          [sty.formattingContainershowEndIcon_size_compact]:
            hasVariant($state, "size", "compact") &&
            hasVariant($state, "showEndIcon", "showEndIcon"),
          [sty.formattingContainershowEndIcon_size_compact_showStartIcon]:
            hasVariant($state, "size", "compact") &&
            hasVariant($state, "showStartIcon", "showStartIcon") &&
            hasVariant($state, "showEndIcon", "showEndIcon"),
          [sty.formattingContainershowStartIcon]: hasVariant(
            $state,
            "showStartIcon",
            "showStartIcon"
          ),
          [sty.formattingContainersize_compact]: hasVariant(
            $state,
            "size",
            "compact"
          ),
          [sty.formattingContainersize_compact_shape_rounded]:
            hasVariant($state, "size", "compact") &&
            hasVariant($state, "shape", "rounded"),
          [sty.formattingContainersize_compact_showStartIcon]:
            hasVariant($state, "size", "compact") &&
            hasVariant($state, "showStartIcon", "showStartIcon"),
          [sty.formattingContainersize_minimal]: hasVariant(
            $state,
            "size",
            "minimal"
          ),
          [sty.formattingContainerstyling_cameo]: hasVariant(
            $state,
            "styling",
            "cameo"
          ),
          [sty.formattingContainerstyling_nittiWColor]: hasVariant(
            $state,
            "styling",
            "nittiWColor"
          ),
          [sty.formattingContainerstyling_square]: hasVariant(
            $state,
            "styling",
            "square"
          ),
          [sty.formattingContainerstyling_typewriter]: hasVariant(
            $state,
            "styling",
            "typewriter"
          )
        })}
      >
        <Stack__
          as={"div"}
          data-plasmic-name={"contentContainer"}
          data-plasmic-override={overrides.contentContainer}
          hasGap={true}
          className={classNames(projectcss.all, sty.contentContainer, {
            [sty.contentContainerdisabledAnimation_disabledText]: hasVariant(
              $state,
              "disabledAnimation",
              "disabledText"
            ),
            [sty.contentContainerdisabledAnimation_disabled]: hasVariant(
              $state,
              "disabledAnimation",
              "disabled"
            ),
            [sty.contentContainerdisabledAnimation_shakeRight]: hasVariant(
              $state,
              "disabledAnimation",
              "shakeRight"
            ),
            [sty.contentContainerjustification_left]: hasVariant(
              $state,
              "justification",
              "left"
            ),
            [sty.contentContainerselected]: hasVariant(
              $state,
              "selected",
              "selected"
            ),
            [sty.contentContainershowStartIcon]: hasVariant(
              $state,
              "showStartIcon",
              "showStartIcon"
            )
          })}
        >
          {(
            hasVariant($state, "showEndIcon", "showEndIcon")
              ? true
              : hasVariant($state, "showStartIcon", "showStartIcon")
              ? true
              : false
          ) ? (
            <div
              data-plasmic-name={"startIconContainer"}
              data-plasmic-override={overrides.startIconContainer}
              className={classNames(projectcss.all, sty.startIconContainer, {
                [sty.startIconContainerselected]: hasVariant(
                  $state,
                  "selected",
                  "selected"
                ),
                [sty.startIconContainershowEndIcon]: hasVariant(
                  $state,
                  "showEndIcon",
                  "showEndIcon"
                ),
                [sty.startIconContainershowStartIcon]: hasVariant(
                  $state,
                  "showStartIcon",
                  "showStartIcon"
                ),
                [sty.startIconContainersize_minimal]: hasVariant(
                  $state,
                  "size",
                  "minimal"
                )
              })}
            >
              {renderPlasmicSlot({
                defaultContents: (
                  <svg
                    className={classNames(projectcss.all, sty.svg___18VnU, {
                      [sty.svgshowEndIcon___18VnU59Ind]: hasVariant(
                        $state,
                        "showEndIcon",
                        "showEndIcon"
                      )
                    })}
                    role={"img"}
                  />
                ),

                value: args.startIcon,
                className: classNames(sty.slotTargetStartIcon, {
                  [sty.slotTargetStartIconshowEndIcon]: hasVariant(
                    $state,
                    "showEndIcon",
                    "showEndIcon"
                  ),
                  [sty.slotTargetStartIconshowStartIcon]: hasVariant(
                    $state,
                    "showStartIcon",
                    "showStartIcon"
                  ),
                  [sty.slotTargetStartIconsize_minimal]: hasVariant(
                    $state,
                    "size",
                    "minimal"
                  )
                })
              })}
            </div>
          ) : null}
          <div
            data-plasmic-name={"text"}
            data-plasmic-override={overrides.text}
            className={classNames(projectcss.all, sty.text, {
              [sty.textdisabledAnimation_disabledText]: hasVariant(
                $state,
                "disabledAnimation",
                "disabledText"
              ),
              [sty.textdisabledAnimation_disabled]: hasVariant(
                $state,
                "disabledAnimation",
                "disabled"
              ),
              [sty.textdisabledAnimation_shakeLeft]: hasVariant(
                $state,
                "disabledAnimation",
                "shakeLeft"
              ),
              [sty.textdisabledAnimation_shakeRight]: hasVariant(
                $state,
                "disabledAnimation",
                "shakeRight"
              ),
              [sty.textisDisabled]: hasVariant(
                $state,
                "isDisabled",
                "isDisabled"
              ),
              [sty.textselected]: hasVariant($state, "selected", "selected"),
              [sty.textshape_rounded]: hasVariant($state, "shape", "rounded"),
              [sty.textshowEndIcon]: hasVariant(
                $state,
                "showEndIcon",
                "showEndIcon"
              )
            })}
          >
            {renderPlasmicSlot({
              defaultContents: "Button",
              value: args.children,
              className: classNames(sty.slotTargetChildren, {
                [sty.slotTargetChildrendisabledAnimation_disabledText]:
                  hasVariant($state, "disabledAnimation", "disabledText"),
                [sty.slotTargetChildrendisabledAnimation_disabled]: hasVariant(
                  $state,
                  "disabledAnimation",
                  "disabled"
                ),
                [sty.slotTargetChildrendisabledAnimation_shakeLeft]: hasVariant(
                  $state,
                  "disabledAnimation",
                  "shakeLeft"
                ),
                [sty.slotTargetChildrendisabledAnimation_shakeRight]:
                  hasVariant($state, "disabledAnimation", "shakeRight"),
                [sty.slotTargetChildrenisDisabled]: hasVariant(
                  $state,
                  "isDisabled",
                  "isDisabled"
                ),
                [sty.slotTargetChildrenselected]: hasVariant(
                  $state,
                  "selected",
                  "selected"
                ),
                [sty.slotTargetChildrenshape_rounded]: hasVariant(
                  $state,
                  "shape",
                  "rounded"
                ),
                [sty.slotTargetChildrenshowEndIcon]: hasVariant(
                  $state,
                  "showEndIcon",
                  "showEndIcon"
                ),
                [sty.slotTargetChildrenshowStartIcon]: hasVariant(
                  $state,
                  "showStartIcon",
                  "showStartIcon"
                ),
                [sty.slotTargetChildrensize_minimal]: hasVariant(
                  $state,
                  "size",
                  "minimal"
                ),
                [sty.slotTargetChildrenstyling_cameo]: hasVariant(
                  $state,
                  "styling",
                  "cameo"
                ),
                [sty.slotTargetChildrenstyling_nittiWColor]: hasVariant(
                  $state,
                  "styling",
                  "nittiWColor"
                ),
                [sty.slotTargetChildrenstyling_nittiWColor_selected]:
                  hasVariant($state, "styling", "nittiWColor") &&
                  hasVariant($state, "selected", "selected"),
                [sty.slotTargetChildrenstyling_typewriter]: hasVariant(
                  $state,
                  "styling",
                  "typewriter"
                )
              })
            })}
          </div>
          {(hasVariant($state, "showEndIcon", "showEndIcon") ? true : false) ? (
            <div
              data-plasmic-name={"endIconContainer"}
              data-plasmic-override={overrides.endIconContainer}
              className={classNames(projectcss.all, sty.endIconContainer, {
                [sty.endIconContainerselected]: hasVariant(
                  $state,
                  "selected",
                  "selected"
                ),
                [sty.endIconContainershowEndIcon]: hasVariant(
                  $state,
                  "showEndIcon",
                  "showEndIcon"
                )
              })}
            >
              {renderPlasmicSlot({
                defaultContents: (
                  <svg
                    className={classNames(projectcss.all, sty.svg__jMo3E)}
                    role={"img"}
                  />
                ),

                value: args.endIcon,
                className: classNames(sty.slotTargetEndIcon, {
                  [sty.slotTargetEndIconshowEndIcon]: hasVariant(
                    $state,
                    "showEndIcon",
                    "showEndIcon"
                  )
                })
              })}
            </div>
          ) : null}
        </Stack__>
        <section
          data-plasmic-name={"underline"}
          data-plasmic-override={overrides.underline}
          className={classNames(projectcss.all, sty.underline, {
            [sty.underlinedisabledAnimation_shakeLeft]: hasVariant(
              $state,
              "disabledAnimation",
              "shakeLeft"
            ),
            [sty.underlineselected]: hasVariant($state, "selected", "selected"),
            [sty.underlinesize_compact]: hasVariant($state, "size", "compact"),
            [sty.underlinestyling_typewriter_selected]:
              hasVariant($state, "styling", "typewriter") &&
              hasVariant($state, "selected", "selected")
          })}
        />
      </Stack__>
      <div
        data-plasmic-name={"disabledText"}
        data-plasmic-override={overrides.disabledText}
        className={classNames(
          projectcss.all,
          projectcss.__wab_text,
          sty.disabledText,
          {
            [sty.disabledTextdisabledAnimation_disabledText]: hasVariant(
              $state,
              "disabledAnimation",
              "disabledText"
            ),
            [sty.disabledTextdisabledAnimation_disabled]: hasVariant(
              $state,
              "disabledAnimation",
              "disabled"
            ),
            [sty.disabledTextdisabledAnimation_shakeLeft]: hasVariant(
              $state,
              "disabledAnimation",
              "shakeLeft"
            ),
            [sty.disabledTextdisabledAnimation_shakeRight]: hasVariant(
              $state,
              "disabledAnimation",
              "shakeRight"
            ),
            [sty.disabledTextsize_compact]: hasVariant(
              $state,
              "size",
              "compact"
            ),
            [sty.disabledTextsize_minimal]: hasVariant(
              $state,
              "size",
              "minimal"
            )
          }
        )}
      >
        <React.Fragment>{$props.disabledShakeText}</React.Fragment>
      </div>
    </button>
  ) as React.ReactElement | null;
}

function useBehavior<P extends pp.PlumeButtonProps>(
  props: P,
  ref: pp.ButtonRef
) {
  const b = pp.useButton<P, typeof PlasmicSubcomponentButton>(
    PlasmicSubcomponentButton,
    props,
    {
      showStartIconVariant: {
        group: "showStartIcon",
        variant: "showStartIcon"
      },
      showEndIconVariant: { group: "showEndIcon", variant: "showEndIcon" },
      isDisabledVariant: { group: "isDisabled", variant: "isDisabled" },
      contentSlot: "children",
      startIconSlot: "startIcon",
      endIconSlot: "endIcon",
      root: "root"
    },
    ref
  );
  if (b.plasmicProps.overrides.root.as === "a") {
    b.plasmicProps.overrides.root.as = PlasmicLink__;
    b.plasmicProps.overrides.root.props.component = Link;
    b.plasmicProps.overrides.root.props.platform = "nextjs";
  }
  return b;
}

const PlasmicDescendants = {
  root: [
    "root",
    "formattingContainer",
    "contentContainer",
    "startIconContainer",
    "text",
    "endIconContainer",
    "underline",
    "disabledText"
  ],
  formattingContainer: [
    "formattingContainer",
    "contentContainer",
    "startIconContainer",
    "text",
    "endIconContainer",
    "underline"
  ],
  contentContainer: [
    "contentContainer",
    "startIconContainer",
    "text",
    "endIconContainer"
  ],
  startIconContainer: ["startIconContainer"],
  text: ["text"],
  endIconContainer: ["endIconContainer"],
  underline: ["underline"],
  disabledText: ["disabledText"]
} as const;
type NodeNameType = keyof typeof PlasmicDescendants;
type DescendantsType<T extends NodeNameType> =
  (typeof PlasmicDescendants)[T][number];
type NodeDefaultElementType = {
  root: "button";
  formattingContainer: "div";
  contentContainer: "div";
  startIconContainer: "div";
  text: "div";
  endIconContainer: "div";
  underline: "section";
  disabledText: "div";
};

type ReservedPropsType = "variants" | "args" | "overrides";
type NodeOverridesType<T extends NodeNameType> = Pick<
  PlasmicSubcomponentButton__OverridesType,
  DescendantsType<T>
>;
type NodeComponentProps<T extends NodeNameType> =
  // Explicitly specify variants, args, and overrides as objects
  {
    variants?: PlasmicSubcomponentButton__VariantsArgs;
    args?: PlasmicSubcomponentButton__ArgsType;
    overrides?: NodeOverridesType<T>;
  } & Omit<PlasmicSubcomponentButton__VariantsArgs, ReservedPropsType> & // Specify variants directly as props
    /* Specify args directly as props*/ Omit<
      PlasmicSubcomponentButton__ArgsType,
      ReservedPropsType
    > &
    /* Specify overrides for each element directly as props*/ Omit<
      NodeOverridesType<T>,
      ReservedPropsType | VariantPropType | ArgPropType
    > &
    /* Specify props for the root element*/ Omit<
      Partial<React.ComponentProps<NodeDefaultElementType[T]>>,
      ReservedPropsType | VariantPropType | ArgPropType | DescendantsType<T>
    >;

function makeNodeComponent<NodeName extends NodeNameType>(nodeName: NodeName) {
  type PropsType = NodeComponentProps<NodeName> & { key?: React.Key };
  const func = function <T extends PropsType>(
    props: T & StrictProps<T, PropsType>
  ) {
    const { variants, args, overrides } = React.useMemo(
      () =>
        deriveRenderOpts(props, {
          name: nodeName,
          descendantNames: PlasmicDescendants[nodeName],
          internalArgPropNames: PlasmicSubcomponentButton__ArgProps,
          internalVariantPropNames: PlasmicSubcomponentButton__VariantProps
        }),
      [props, nodeName]
    );
    return PlasmicSubcomponentButton__RenderFunc({
      variants,
      args,
      overrides,
      forNode: nodeName
    });
  };
  if (nodeName === "root") {
    func.displayName = "PlasmicSubcomponentButton";
  } else {
    func.displayName = `PlasmicSubcomponentButton.${nodeName}`;
  }
  return func;
}

export const PlasmicSubcomponentButton = Object.assign(
  // Top-level PlasmicSubcomponentButton renders the root element
  makeNodeComponent("root"),
  {
    // Helper components rendering sub-elements
    formattingContainer: makeNodeComponent("formattingContainer"),
    contentContainer: makeNodeComponent("contentContainer"),
    startIconContainer: makeNodeComponent("startIconContainer"),
    text: makeNodeComponent("text"),
    endIconContainer: makeNodeComponent("endIconContainer"),
    underline: makeNodeComponent("underline"),
    disabledText: makeNodeComponent("disabledText"),

    // Metadata about props expected for PlasmicSubcomponentButton
    internalVariantProps: PlasmicSubcomponentButton__VariantProps,
    internalArgProps: PlasmicSubcomponentButton__ArgProps,

    useBehavior
  }
);

export default PlasmicSubcomponentButton;
/* prettier-ignore-end */
