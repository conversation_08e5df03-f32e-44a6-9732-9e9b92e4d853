.root {
  display: flex;
  flex-direction: column;
  position: relative;
  width: 100%;
  height: auto;
  justify-content: flex-start;
  align-items: center;
  background: none;
  min-width: 0;
}
.rootoverviewGrid {
  width: auto;
  justify-self: flex-start;
  align-items: flex-start;
  justify-content: flex-start;
  display: inline-flex;
}
.profileSectionsProfileSectionHeading:global(.__wab_instance) {
  max-width: 100%;
  position: relative;
  height: auto;
  flex-shrink: 1;
}
.langDisplayContainer {
  display: grid;
  position: relative;
  align-content: flex-start;
  justify-items: center;
  width: 100%;
  min-width: 0;
  grid-template-columns:
    var(--plsmc-viewport-gap) 1fr minmax(0, var(--plsmc-wide-chunk))
    min(
      var(--plsmc-standard-width),
      calc(100% - var(--plsmc-viewport-gap) - var(--plsmc-viewport-gap))
    )
    minmax(0, var(--plsmc-wide-chunk)) 1fr var(--plsmc-viewport-gap);
  padding: var(--token-sazGmnf7GWAk);
}
.langDisplayContainer > * {
  grid-column: 4;
}
.langDisplayContaineroverviewGrid {
  width: 100%;
  display: flex;
  flex-direction: column;
  min-width: 0;
}
.langDisplayContaineroverviewGrid > * {
  grid-column: 4;
}
.langDisplayContainereditable > * {
  grid-column: 4;
}
.freeBox {
  display: grid;
  position: relative;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  width: 100%;
  height: auto;
  max-width: 100%;
  grid-row-gap: var(--token-j0qnbpah5w9U);
  grid-column-gap: var(--token-j0qnbpah5w9U);
  grid-column-start: 1 !important;
  grid-column-end: -1 !important;
  padding: var(--token-j0qnbpah5w9U);
}
.freeBoxoverviewGrid {
  display: none;
}
.freeBoxeditable {
  display: grid;
}
.profileTileLanguagesTile__pgImg:global(.__wab_instance) {
  max-width: 100%;
  position: relative;
}
.profileTileLanguagesTileoverviewGrid__pgImg3Upgm:global(.__wab_instance) {
  display: none;
}
.profileTileLanguagesTile__tEeSq:global(.__wab_instance) {
  max-width: 100%;
  position: relative;
  display: none;
}
.profileTileLanguagesTileoverviewGrid__tEeSq3Upgm:global(.__wab_instance) {
  display: flex;
}
.profileTileLanguagesTileeditable__tEeSQbsXvo:global(.__wab_instance) {
  display: none;
}
