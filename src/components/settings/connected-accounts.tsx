import type React from 'react';
import { useCallback, useEffect, useState } from 'react';
import { siGithub, siGoogle } from 'simple-icons';
import { toast } from 'sonner';
import { authClient } from '@/auth/client';
import { Button } from '@/components/ui/button';
import { ProfileSection } from './profile-section';

type SocialProvider = {
  id: 'linkedin' | 'github' | 'google';
  name: string;
  icon: React.ComponentType<{ size?: number }>;
  enabled: boolean;
};

const socialProviders: SocialProvider[] = [
  {
    id: 'linkedin',
    name: 'LinkedIn',
    icon: () => (
      <svg
        className="fill-current"
        height="20"
        role="img"
        viewBox="0 0 24 24"
        width="20"
      />
    ),
    enabled: true,
  },
  {
    id: 'github',
    name: 'GitH<PERSON>',
    icon: () => (
      <svg
        aria-labelledby="githubTitle"
        fill="currentColor"
        height="20"
        role="img"
        viewBox="0 0 24 24"
        width="20"
        xmlns="http://www.w3.org/2000/svg"
      >
        <title id="githubTitle">GitHub</title>
        <path d={siGithub.path} />
      </svg>
    ),
    enabled: false,
  },
  {
    id: 'google',
    name: 'Google',
    icon: () => (
      <svg
        aria-labelledby="googleTitle"
        fill="currentColor"
        height="20"
        role="img"
        viewBox="0 0 24 24"
        width="20"
        xmlns="http://www.w3.org/2000/svg"
      >
        <title id="googleTitle">Google</title>
        <path d={siGoogle.path} />
      </svg>
    ),
    enabled: false,
  },
];

export function ConnectedAccounts() {
  const [connectedAccounts, setConnectedAccounts] = useState<
    Array<{ provider: string; accountId: string }>
  >([]);

  const fetchConnectedAccounts = useCallback(async () => {
    try {
      const accounts = await authClient.listAccounts({});
      if (accounts.data) {
        setConnectedAccounts(
          accounts.data.map((account) => ({
            provider: account.provider,
            accountId: account.id,
          }))
        );
      }
    } catch (_error) {
      toast.error(
        'Failed to load connected accounts. Please refresh to try again.'
      );
    }
  }, []);

  useEffect(() => {
    fetchConnectedAccounts();
  }, [fetchConnectedAccounts]);

  const handleConnectProvider = async (providerId: 'github' | 'google') => {
    const promise = authClient.linkSocial({
      provider: providerId,
      callbackURL: '/settings',
    });

    await toast.promise(promise, {
      loading: `Connecting to ${providerId}...`,
      success: `Successfully connected to ${providerId}`,
      error: `Failed to connect to ${providerId}. Please try again.`,
    });
  };

  const handleDisconnectProvider = async (providerId: 'github' | 'google') => {
    const account = connectedAccounts.find(
      (acc) => acc.provider === providerId
    );
    if (!account) {
      return;
    }

    const promise = authClient
      .unlinkAccount({
        providerId,
        accountId: account.accountId,
      })
      .then(() => fetchConnectedAccounts());

    toast.promise(promise, {
      loading: `Disconnecting from ${providerId}...`,
      success: `Successfully disconnected from ${providerId}`,
      error: `Failed to disconnect from ${providerId}. Please try again.`,
    });
  };

  return (
    <ProfileSection
      description="Manage your connected social accounts"
      title="Connected Accounts"
    >
      <div className="space-y-4">
        {socialProviders.map((provider) => {
          const isConnected = connectedAccounts.some(
            (acc) => acc.provider === provider.id
          );
          const Icon = provider.icon;

          if (!provider.enabled) {
            return (
              <div
                className="flex items-center justify-between rounded-lg border p-4 opacity-50"
                key={provider.id}
              >
                <div className="flex items-center gap-3">
                  <Icon size={20} />
                  <div>
                    <p className="font-medium">{provider.name}</p>
                    <p className="text-muted-foreground text-sm">Coming soon</p>
                  </div>
                </div>
                <Button className="gap-2" disabled variant="outline">
                  Connect
                </Button>
              </div>
            );
          }

          return isConnected ? (
            <div
              className="flex items-center justify-between rounded-lg border p-4"
              key={provider.id}
            >
              <div className="flex items-center gap-3">
                <Icon size={20} />
                <div>
                  <p className="font-medium">{provider.name}</p>
                  <p className="text-muted-foreground text-sm">Connected</p>
                </div>
              </div>
              <Button
                className="gap-2"
                onClick={() =>
                  handleDisconnectProvider(provider.id as 'github' | 'google')
                }
                variant="outline"
              >
                Disconnect
              </Button>
            </div>
          ) : (
            <Button
              className="w-full gap-2"
              key={provider.id}
              onClick={() =>
                handleConnectProvider(provider.id as 'github' | 'google')
              }
              variant="outline"
            >
              <Icon size={16} />
              Connect {provider.name} Account
            </Button>
          );
        })}
      </div>
    </ProfileSection>
  );
}
