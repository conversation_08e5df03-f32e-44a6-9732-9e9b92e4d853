/* eslint-disable */
/* tslint:disable */
// @ts-nocheck
/* prettier-ignore-start */

/** @jsxRuntime classic */
/** @jsx createPlasmicElementProxy */
/** @jsxFrag React.Fragment */

// This class is auto-generated by Plasmic; please do not edit!
// Plasmic Project: nZeWKcn21KijvC7eT8B3c7
// Component: BKP1w5f1sryI

"use client";

import * as React from "react";

import Head from "next/head";
import Link, { LinkProps } from "next/link";
import { useRouter } from "next/navigation";

import {
  Flex as Flex__,
  MultiChoiceArg,
  PlasmicDataSourceContextProvider as PlasmicDataSourceContextProvider__,
  PlasmicIcon as PlasmicIcon__,
  PlasmicImg as PlasmicImg__,
  PlasmicLink as PlasmicLink__,
  PlasmicPageGuard as PlasmicPageGuard__,
  SingleBooleanChoiceArg,
  SingleChoiceArg,
  Stack as Stack__,
  StrictProps,
  Trans as Trans__,
  classNames,
  createPlasmicElementProxy,
  deriveRenderOpts,
  ensureGlobalVariants,
  generateOnMutateForSpec,
  generateStateOnChangeProp,
  generateStateOnChangePropForCodeComponents,
  generateStateValueProp,
  get as $stateGet,
  hasVariant,
  initializeCodeComponentStates,
  initializePlasmicStates,
  makeFragment,
  omit,
  pick,
  renderPlasmicSlot,
  set as $stateSet,
  useCurrentUser,
  useDollarState,
  usePlasmicTranslator,
  useTrigger,
  wrapWithClassName
} from "@plasmicapp/react-web";
import {
  DataCtxReader as DataCtxReader__,
  useDataEnv,
  useGlobalActions
} from "@plasmicapp/host";

import NavigationActiveSlotSlider from "../../NavigationActiveSlotSlider"; // plasmic-import: Jnr7Ch5x507b/component
import NavigationHoverSlotSlider from "../../NavigationHoverSlotSlider"; // plasmic-import: 7r9yyvza21zd/component
import NavigationButton from "../../NavigationButton"; // plasmic-import: Wr1MGCSkm1uC/component

import "@plasmicapp/react-web/lib/plasmic.css";

import projectcss from "./plasmic.module.css"; // plasmic-import: nZeWKcn21KijvC7eT8B3c7/projectcss
import sty from "./PlasmicNavigationBottomButtonAssembly.module.css"; // plasmic-import: BKP1w5f1sryI/css

import LogInIcon from "./icons/PlasmicIcon__LogIn"; // plasmic-import: rp7W-7bfKWRu/icon
import UserIcon from "./icons/PlasmicIcon__User"; // plasmic-import: hcR42vt5qkrz/icon
import SettingsIcon from "./icons/PlasmicIcon__Settings"; // plasmic-import: 3QH7fIS7m04A/icon
import MenuIcon from "./icons/PlasmicIcon__Menu"; // plasmic-import: pjtzwEs6QILW/icon

createPlasmicElementProxy;

export type PlasmicNavigationBottomButtonAssembly__VariantMembers = {
  hoverTab: "slot1" | "slot2";
  activeTab: "slot1" | "slot2";
  collapsed: "collapsed";
  unAuthed: "unAuthed";
};
export type PlasmicNavigationBottomButtonAssembly__VariantsArgs = {
  hoverTab?: SingleChoiceArg<"slot1" | "slot2">;
  activeTab?: SingleChoiceArg<"slot1" | "slot2">;
  collapsed?: SingleBooleanChoiceArg<"collapsed">;
  unAuthed?: SingleBooleanChoiceArg<"unAuthed">;
};
type VariantPropType =
  keyof PlasmicNavigationBottomButtonAssembly__VariantsArgs;
export const PlasmicNavigationBottomButtonAssembly__VariantProps =
  new Array<VariantPropType>("hoverTab", "activeTab", "collapsed", "unAuthed");

export type PlasmicNavigationBottomButtonAssembly__ArgsType = {
  onClickSlot1?: (event: any) => void;
  onClickSlot2?: (event: any) => void;
  onAnimationEnd?: (event: any) => void;
};
type ArgPropType = keyof PlasmicNavigationBottomButtonAssembly__ArgsType;
export const PlasmicNavigationBottomButtonAssembly__ArgProps =
  new Array<ArgPropType>("onClickSlot1", "onClickSlot2", "onAnimationEnd");

export type PlasmicNavigationBottomButtonAssembly__OverridesType = {
  animationContainer?: Flex__<"div">;
  navigationActiveSlotSlider?: Flex__<typeof NavigationActiveSlotSlider>;
  navigationHoverSlotSlider?: Flex__<typeof NavigationHoverSlotSlider>;
  loginButton?: Flex__<typeof NavigationButton>;
  navIcon2?: Flex__<"svg">;
  profileButton?: Flex__<typeof NavigationButton>;
  icon?: Flex__<"svg">;
  settingsButton?: Flex__<typeof NavigationButton>;
  icon2?: Flex__<"svg">;
  moreButton?: Flex__<typeof NavigationButton>;
  navIcon?: Flex__<"svg">;
};

export interface DefaultNavigationBottomButtonAssemblyProps {
  onClickSlot1?: (event: any) => void;
  onClickSlot2?: (event: any) => void;
  onAnimationEnd?: (event: any) => void;
  hoverTab?: SingleChoiceArg<"slot1" | "slot2">;
  activeTab?: SingleChoiceArg<"slot1" | "slot2">;
  collapsed?: SingleBooleanChoiceArg<"collapsed">;
  unAuthed?: SingleBooleanChoiceArg<"unAuthed">;
  className?: string;
}

const $$ = {};

function useNextRouter() {
  try {
    return useRouter();
  } catch {}
  return undefined;
}

function PlasmicNavigationBottomButtonAssembly__RenderFunc(props: {
  variants: PlasmicNavigationBottomButtonAssembly__VariantsArgs;
  args: PlasmicNavigationBottomButtonAssembly__ArgsType;
  overrides: PlasmicNavigationBottomButtonAssembly__OverridesType;
  forNode?: string;
}) {
  const { variants, overrides, forNode } = props;

  const args = React.useMemo(
    () =>
      Object.assign(
        {},
        Object.fromEntries(
          Object.entries(props.args).filter(([_, v]) => v !== undefined)
        )
      ),
    [props.args]
  );

  const $props = {
    ...args,
    ...variants
  };

  const __nextRouter = useNextRouter();
  const $ctx = useDataEnv?.() || {};
  const refsRef = React.useRef({});
  const $refs = refsRef.current;

  let [$queries, setDollarQueries] = React.useState<
    Record<string, ReturnType<typeof usePlasmicDataOp>>
  >({});
  const stateSpecs: Parameters<typeof useDollarState>[0] = React.useMemo(
    () => [
      {
        path: "activeTab",
        type: "private",
        variableType: "variant",
        initFunc: ({ $props, $state, $queries, $ctx }) => $props.activeTab
      },
      {
        path: "collapsed",
        type: "private",
        variableType: "variant",
        initFunc: ({ $props, $state, $queries, $ctx }) => $props.collapsed
      },
      {
        path: "hoverTab",
        type: "private",
        variableType: "variant",
        initFunc: ({ $props, $state, $queries, $ctx }) => $props.hoverTab
      },
      {
        path: "profileButton.modals",
        type: "private",
        variableType: "text",
        initFunc: ({ $props, $state, $queries, $ctx }) => undefined
      },
      {
        path: "settingsButton.modals",
        type: "private",
        variableType: "text",
        initFunc: ({ $props, $state, $queries, $ctx }) => undefined
      },
      {
        path: "moreButton.modals",
        type: "private",
        variableType: "text",
        initFunc: ({ $props, $state, $queries, $ctx }) => undefined
      },
      {
        path: "loginButton.modals",
        type: "private",
        variableType: "text",
        initFunc: ({ $props, $state, $queries, $ctx }) => undefined
      },
      {
        path: "unAuthed",
        type: "private",
        variableType: "variant",
        initFunc: ({ $props, $state, $queries, $ctx }) => $props.unAuthed
      }
    ],
    [$props, $ctx, $refs]
  );
  const $state = useDollarState(stateSpecs, {
    $props,
    $ctx,
    $queries: $queries,
    $refs
  });

  const new$Queries: Record<string, ReturnType<typeof usePlasmicDataOp>> = {};
  if (Object.keys(new$Queries).some(k => new$Queries[k] !== $queries[k])) {
    setDollarQueries(new$Queries);

    $queries = new$Queries;
  }

  return (
    <div
      data-plasmic-name={"animationContainer"}
      data-plasmic-override={overrides.animationContainer}
      data-plasmic-root={true}
      data-plasmic-for-node={forNode}
      className={classNames(
        projectcss.all,
        projectcss.root_reset,
        projectcss.plasmic_default_styles,
        projectcss.plasmic_mixins,
        projectcss.plasmic_tokens,
        sty.animationContainer,
        {
          [sty.animationContaineractiveTab_slot1]: hasVariant(
            $state,
            "activeTab",
            "slot1"
          ),
          [sty.animationContaineractiveTab_slot2_hoverTab_slot2]:
            hasVariant($state, "hoverTab", "slot2") &&
            hasVariant($state, "activeTab", "slot2"),
          [sty.animationContainercollapsed]: hasVariant(
            $state,
            "collapsed",
            "collapsed"
          ),
          [sty.animationContainerhoverTab_slot1]: hasVariant(
            $state,
            "hoverTab",
            "slot1"
          ),
          [sty.animationContainerhoverTab_slot1_activeTab_slot1]:
            hasVariant($state, "hoverTab", "slot1") &&
            hasVariant($state, "activeTab", "slot1")
        }
      )}
    >
      <NavigationActiveSlotSlider
        data-plasmic-name={"navigationActiveSlotSlider"}
        data-plasmic-override={overrides.navigationActiveSlotSlider}
        activeTab={
          hasVariant($state, "unAuthed", "unAuthed") &&
          hasVariant($state, "activeTab", "slot2")
            ? "slot2"
            : hasVariant($state, "unAuthed", "unAuthed") &&
              hasVariant($state, "activeTab", "slot1")
            ? "slot1"
            : hasVariant($state, "hoverTab", "slot2") &&
              hasVariant($state, "activeTab", "slot2")
            ? "slot2"
            : hasVariant($state, "hoverTab", "slot1") &&
              hasVariant($state, "activeTab", "slot1")
            ? "slot1"
            : hasVariant($state, "activeTab", "slot2")
            ? "slot2"
            : hasVariant($state, "activeTab", "slot1")
            ? "slot1"
            : undefined
        }
        className={classNames(
          "__wab_instance",
          sty.navigationActiveSlotSlider,
          {
            [sty.navigationActiveSlotSlideractiveTab_slot1]: hasVariant(
              $state,
              "activeTab",
              "slot1"
            ),
            [sty.navigationActiveSlotSlideractiveTab_slot1_unAuthed]:
              hasVariant($state, "unAuthed", "unAuthed") &&
              hasVariant($state, "activeTab", "slot1"),
            [sty.navigationActiveSlotSlideractiveTab_slot2]: hasVariant(
              $state,
              "activeTab",
              "slot2"
            ),
            [sty.navigationActiveSlotSlideractiveTab_slot2_hoverTab_slot2]:
              hasVariant($state, "hoverTab", "slot2") &&
              hasVariant($state, "activeTab", "slot2"),
            [sty.navigationActiveSlotSlideractiveTab_slot2_unAuthed]:
              hasVariant($state, "unAuthed", "unAuthed") &&
              hasVariant($state, "activeTab", "slot2"),
            [sty.navigationActiveSlotSliderhoverTab_slot1]: hasVariant(
              $state,
              "hoverTab",
              "slot1"
            ),
            [sty.navigationActiveSlotSliderhoverTab_slot1_activeTab_slot1]:
              hasVariant($state, "hoverTab", "slot1") &&
              hasVariant($state, "activeTab", "slot1"),
            [sty.navigationActiveSlotSliderhoverTab_slot2]: hasVariant(
              $state,
              "hoverTab",
              "slot2"
            )
          }
        )}
        hoverTab={
          hasVariant($state, "hoverTab", "slot2") &&
          hasVariant($state, "activeTab", "slot2")
            ? "slot2"
            : hasVariant($state, "hoverTab", "slot1") &&
              hasVariant($state, "activeTab", "slot1")
            ? "slot1"
            : hasVariant($state, "hoverTab", "slot2")
            ? "slot2"
            : hasVariant($state, "hoverTab", "slot1")
            ? "slot1"
            : undefined
        }
      />

      <NavigationHoverSlotSlider
        data-plasmic-name={"navigationHoverSlotSlider"}
        data-plasmic-override={overrides.navigationHoverSlotSlider}
        activeTab={
          hasVariant($state, "activeTab", "slot2") &&
          hasVariant($state, "hoverTab", "slot1")
            ? "slot2"
            : hasVariant($state, "activeTab", "slot1") &&
              hasVariant($state, "hoverTab", "slot2")
            ? "slot1"
            : hasVariant($state, "hoverTab", "slot1") &&
              hasVariant($state, "activeTab", "slot1")
            ? "slot1"
            : hasVariant($state, "activeTab", "slot2")
            ? "slot2"
            : hasVariant($state, "activeTab", "slot1")
            ? "slot1"
            : undefined
        }
        className={classNames("__wab_instance", sty.navigationHoverSlotSlider, {
          [sty.navigationHoverSlotSlideractiveTab_slot1]: hasVariant(
            $state,
            "activeTab",
            "slot1"
          ),
          [sty.navigationHoverSlotSlideractiveTab_slot2]: hasVariant(
            $state,
            "activeTab",
            "slot2"
          ),
          [sty.navigationHoverSlotSlideractiveTab_slot2_hoverTab_slot1]:
            hasVariant($state, "activeTab", "slot2") &&
            hasVariant($state, "hoverTab", "slot1"),
          [sty.navigationHoverSlotSliderhoverTab_slot1]: hasVariant(
            $state,
            "hoverTab",
            "slot1"
          ),
          [sty.navigationHoverSlotSliderhoverTab_slot1_activeTab_slot1]:
            hasVariant($state, "hoverTab", "slot1") &&
            hasVariant($state, "activeTab", "slot1"),
          [sty.navigationHoverSlotSliderhoverTab_slot2]: hasVariant(
            $state,
            "hoverTab",
            "slot2"
          ),
          [sty.navigationHoverSlotSliderhoverTab_slot2_activeTab_slot1]:
            hasVariant($state, "activeTab", "slot1") &&
            hasVariant($state, "hoverTab", "slot2")
        })}
        hoverTab={
          hasVariant($state, "activeTab", "slot2") &&
          hasVariant($state, "hoverTab", "slot1")
            ? "slot1"
            : hasVariant($state, "activeTab", "slot1") &&
              hasVariant($state, "hoverTab", "slot2")
            ? "slot2"
            : hasVariant($state, "hoverTab", "slot1") &&
              hasVariant($state, "activeTab", "slot1")
            ? "slot1"
            : hasVariant($state, "hoverTab", "slot2")
            ? "slot2"
            : hasVariant($state, "hoverTab", "slot1")
            ? "slot1"
            : undefined
        }
      />

      <NavigationButton
        data-plasmic-name={"loginButton"}
        data-plasmic-override={overrides.loginButton}
        buttonText={"Login"}
        className={classNames("__wab_instance", sty.loginButton, {
          [sty.loginButtoncollapsed]: hasVariant(
            $state,
            "collapsed",
            "collapsed"
          ),
          [sty.loginButtonunAuthed]: hasVariant($state, "unAuthed", "unAuthed")
        })}
        collapsed={
          hasVariant($state, "collapsed", "collapsed") ? true : undefined
        }
        modals={generateStateValueProp($state, ["loginButton", "modals"])}
        navIconSlot={
          <LogInIcon
            data-plasmic-name={"navIcon2"}
            data-plasmic-override={overrides.navIcon2}
            className={classNames(projectcss.all, sty.navIcon2)}
            role={"img"}
          />
        }
        onClick={async event => {
          const $steps = {};

          $steps["goToLogin"] = true
            ? (() => {
                const actionArgs = {};
                return (({ destination }) => {
                  if (
                    typeof destination === "string" &&
                    destination.startsWith("#")
                  ) {
                    document
                      .getElementById(destination.substr(1))
                      .scrollIntoView({ behavior: "smooth" });
                  } else {
                    __nextRouter?.push(destination);
                  }
                })?.apply(null, [actionArgs]);
              })()
            : undefined;
          if (
            $steps["goToLogin"] != null &&
            typeof $steps["goToLogin"] === "object" &&
            typeof $steps["goToLogin"].then === "function"
          ) {
            $steps["goToLogin"] = await $steps["goToLogin"];
          }
        }}
        onModalsChange={async (...eventArgs: any) => {
          generateStateOnChangeProp($state, ["loginButton", "modals"]).apply(
            null,
            eventArgs
          );

          if (
            eventArgs.length > 1 &&
            eventArgs[1] &&
            eventArgs[1]._plasmic_state_init_
          ) {
            return;
          }
        }}
      />

      <NavigationButton
        data-plasmic-name={"profileButton"}
        data-plasmic-override={overrides.profileButton}
        buttonText={"Profile"}
        className={classNames("__wab_instance", sty.profileButton, {
          [sty.profileButtonactiveTab_slot1]: hasVariant(
            $state,
            "activeTab",
            "slot1"
          ),
          [sty.profileButtoncollapsed]: hasVariant(
            $state,
            "collapsed",
            "collapsed"
          ),
          [sty.profileButtonhoverTab_slot1]: hasVariant(
            $state,
            "hoverTab",
            "slot1"
          ),
          [sty.profileButtonhoverTab_slot1_activeTab_slot1]:
            hasVariant($state, "hoverTab", "slot1") &&
            hasVariant($state, "activeTab", "slot1"),
          [sty.profileButtonunAuthed]: hasVariant(
            $state,
            "unAuthed",
            "unAuthed"
          )
        })}
        collapsed={
          hasVariant($state, "collapsed", "collapsed") ? true : undefined
        }
        modals={generateStateValueProp($state, ["profileButton", "modals"])}
        navIconSlot={
          <UserIcon
            data-plasmic-name={"icon"}
            data-plasmic-override={overrides.icon}
            className={classNames(projectcss.all, sty.icon, {
              [sty.iconcollapsed]: hasVariant($state, "collapsed", "collapsed")
            })}
            role={"img"}
          />
        }
        onClick={args.onClickSlot1}
        onModalsChange={async (...eventArgs: any) => {
          generateStateOnChangeProp($state, ["profileButton", "modals"]).apply(
            null,
            eventArgs
          );

          if (
            eventArgs.length > 1 &&
            eventArgs[1] &&
            eventArgs[1]._plasmic_state_init_
          ) {
            return;
          }
        }}
        onPointerEnter={async event => {
          const $steps = {};

          $steps["updateHoverTab"] = true
            ? (() => {
                const actionArgs = {
                  vgroup: "hoverTab",
                  operation: 0,
                  value: "slot1"
                };
                return (({ vgroup, value }) => {
                  if (typeof value === "string") {
                    value = [value];
                  }

                  $stateSet($state, vgroup, value);
                  return value;
                })?.apply(null, [actionArgs]);
              })()
            : undefined;
          if (
            $steps["updateHoverTab"] != null &&
            typeof $steps["updateHoverTab"] === "object" &&
            typeof $steps["updateHoverTab"].then === "function"
          ) {
            $steps["updateHoverTab"] = await $steps["updateHoverTab"];
          }
        }}
        onPointerLeave={async event => {
          const $steps = {};

          $steps["updateHoverTab"] = true
            ? (() => {
                const actionArgs = {
                  vgroup: "hoverTab",
                  operation: 1,
                  value: "slot1"
                };
                return (({ vgroup, value }) => {
                  if (typeof value === "string") {
                    value = [value];
                  }

                  $stateSet($state, vgroup, undefined);
                  return undefined;
                })?.apply(null, [actionArgs]);
              })()
            : undefined;
          if (
            $steps["updateHoverTab"] != null &&
            typeof $steps["updateHoverTab"] === "object" &&
            typeof $steps["updateHoverTab"].then === "function"
          ) {
            $steps["updateHoverTab"] = await $steps["updateHoverTab"];
          }
        }}
      />

      <NavigationButton
        data-plasmic-name={"settingsButton"}
        data-plasmic-override={overrides.settingsButton}
        buttonText={"Settings"}
        className={classNames("__wab_instance", sty.settingsButton, {
          [sty.settingsButtonactiveTab_slot1]: hasVariant(
            $state,
            "activeTab",
            "slot1"
          ),
          [sty.settingsButtonactiveTab_slot2]: hasVariant(
            $state,
            "activeTab",
            "slot2"
          ),
          [sty.settingsButtoncollapsed]: hasVariant(
            $state,
            "collapsed",
            "collapsed"
          ),
          [sty.settingsButtonhoverTab_slot1]: hasVariant(
            $state,
            "hoverTab",
            "slot1"
          ),
          [sty.settingsButtonhoverTab_slot2]: hasVariant(
            $state,
            "hoverTab",
            "slot2"
          ),
          [sty.settingsButtonunAuthed]: hasVariant(
            $state,
            "unAuthed",
            "unAuthed"
          )
        })}
        collapsed={
          hasVariant($state, "collapsed", "collapsed") ? true : undefined
        }
        modals={generateStateValueProp($state, ["settingsButton", "modals"])}
        navIconSlot={
          <SettingsIcon
            data-plasmic-name={"icon2"}
            data-plasmic-override={overrides.icon2}
            className={classNames(projectcss.all, sty.icon2)}
            role={"img"}
          />
        }
        onClick={args.onClickSlot2}
        onModalsChange={async (...eventArgs: any) => {
          generateStateOnChangeProp($state, ["settingsButton", "modals"]).apply(
            null,
            eventArgs
          );

          if (
            eventArgs.length > 1 &&
            eventArgs[1] &&
            eventArgs[1]._plasmic_state_init_
          ) {
            return;
          }
        }}
        onPointerEnter={async event => {
          const $steps = {};

          $steps["updateHoverTab"] = true
            ? (() => {
                const actionArgs = {
                  vgroup: "hoverTab",
                  operation: 0,
                  value: "slot2"
                };
                return (({ vgroup, value }) => {
                  if (typeof value === "string") {
                    value = [value];
                  }

                  $stateSet($state, vgroup, value);
                  return value;
                })?.apply(null, [actionArgs]);
              })()
            : undefined;
          if (
            $steps["updateHoverTab"] != null &&
            typeof $steps["updateHoverTab"] === "object" &&
            typeof $steps["updateHoverTab"].then === "function"
          ) {
            $steps["updateHoverTab"] = await $steps["updateHoverTab"];
          }
        }}
        onPointerLeave={async event => {
          const $steps = {};

          $steps["updateHoverTab"] = true
            ? (() => {
                const actionArgs = {
                  vgroup: "hoverTab",
                  operation: 1,
                  value: []
                };
                return (({ vgroup, value }) => {
                  if (typeof value === "string") {
                    value = [value];
                  }

                  $stateSet($state, vgroup, undefined);
                  return undefined;
                })?.apply(null, [actionArgs]);
              })()
            : undefined;
          if (
            $steps["updateHoverTab"] != null &&
            typeof $steps["updateHoverTab"] === "object" &&
            typeof $steps["updateHoverTab"].then === "function"
          ) {
            $steps["updateHoverTab"] = await $steps["updateHoverTab"];
          }
        }}
      />

      <NavigationButton
        data-plasmic-name={"moreButton"}
        data-plasmic-override={overrides.moreButton}
        buttonText={"More"}
        className={classNames("__wab_instance", sty.moreButton, {
          [sty.moreButtoncollapsed]: hasVariant(
            $state,
            "collapsed",
            "collapsed"
          ),
          [sty.moreButtonunAuthed]: hasVariant($state, "unAuthed", "unAuthed")
        })}
        collapsed={
          hasVariant($state, "collapsed", "collapsed") ? true : undefined
        }
        modals={generateStateValueProp($state, ["moreButton", "modals"])}
        navIconSlot={
          <MenuIcon
            data-plasmic-name={"navIcon"}
            data-plasmic-override={overrides.navIcon}
            className={classNames(projectcss.all, sty.navIcon)}
            role={"img"}
          />
        }
        onModalsChange={async (...eventArgs: any) => {
          generateStateOnChangeProp($state, ["moreButton", "modals"]).apply(
            null,
            eventArgs
          );

          if (
            eventArgs.length > 1 &&
            eventArgs[1] &&
            eventArgs[1]._plasmic_state_init_
          ) {
            return;
          }
        }}
        onPointerEnter={async event => {
          const $steps = {};

          $steps["updateNavButtonModals"] = true
            ? (() => {
                const actionArgs = {
                  variable: {
                    objRoot: $state,
                    variablePath: ["moreButton", "modals"]
                  },
                  operation: 0,
                  value: "legal"
                };
                return (({ variable, value, startIndex, deleteCount }) => {
                  if (!variable) {
                    return;
                  }
                  const { objRoot, variablePath } = variable;

                  $stateSet(objRoot, variablePath, value);
                  return value;
                })?.apply(null, [actionArgs]);
              })()
            : undefined;
          if (
            $steps["updateNavButtonModals"] != null &&
            typeof $steps["updateNavButtonModals"] === "object" &&
            typeof $steps["updateNavButtonModals"].then === "function"
          ) {
            $steps["updateNavButtonModals"] = await $steps[
              "updateNavButtonModals"
            ];
          }
        }}
        onPointerLeave={async event => {
          const $steps = {};

          $steps["updateNavButtonModals"] = true
            ? (() => {
                const actionArgs = {
                  variable: {
                    objRoot: $state,
                    variablePath: ["moreButton", "modals"]
                  },
                  operation: 1
                };
                return (({ variable, value, startIndex, deleteCount }) => {
                  if (!variable) {
                    return;
                  }
                  const { objRoot, variablePath } = variable;

                  $stateSet(objRoot, variablePath, undefined);
                  return undefined;
                })?.apply(null, [actionArgs]);
              })()
            : undefined;
          if (
            $steps["updateNavButtonModals"] != null &&
            typeof $steps["updateNavButtonModals"] === "object" &&
            typeof $steps["updateNavButtonModals"].then === "function"
          ) {
            $steps["updateNavButtonModals"] = await $steps[
              "updateNavButtonModals"
            ];
          }
        }}
      />
    </div>
  ) as React.ReactElement | null;
}

const PlasmicDescendants = {
  animationContainer: [
    "animationContainer",
    "navigationActiveSlotSlider",
    "navigationHoverSlotSlider",
    "loginButton",
    "navIcon2",
    "profileButton",
    "icon",
    "settingsButton",
    "icon2",
    "moreButton",
    "navIcon"
  ],
  navigationActiveSlotSlider: ["navigationActiveSlotSlider"],
  navigationHoverSlotSlider: ["navigationHoverSlotSlider"],
  loginButton: ["loginButton", "navIcon2"],
  navIcon2: ["navIcon2"],
  profileButton: ["profileButton", "icon"],
  icon: ["icon"],
  settingsButton: ["settingsButton", "icon2"],
  icon2: ["icon2"],
  moreButton: ["moreButton", "navIcon"],
  navIcon: ["navIcon"]
} as const;
type NodeNameType = keyof typeof PlasmicDescendants;
type DescendantsType<T extends NodeNameType> =
  (typeof PlasmicDescendants)[T][number];
type NodeDefaultElementType = {
  animationContainer: "div";
  navigationActiveSlotSlider: typeof NavigationActiveSlotSlider;
  navigationHoverSlotSlider: typeof NavigationHoverSlotSlider;
  loginButton: typeof NavigationButton;
  navIcon2: "svg";
  profileButton: typeof NavigationButton;
  icon: "svg";
  settingsButton: typeof NavigationButton;
  icon2: "svg";
  moreButton: typeof NavigationButton;
  navIcon: "svg";
};

type ReservedPropsType = "variants" | "args" | "overrides";
type NodeOverridesType<T extends NodeNameType> = Pick<
  PlasmicNavigationBottomButtonAssembly__OverridesType,
  DescendantsType<T>
>;
type NodeComponentProps<T extends NodeNameType> =
  // Explicitly specify variants, args, and overrides as objects
  {
    variants?: PlasmicNavigationBottomButtonAssembly__VariantsArgs;
    args?: PlasmicNavigationBottomButtonAssembly__ArgsType;
    overrides?: NodeOverridesType<T>;
  } & Omit< // Specify variants directly as props
    PlasmicNavigationBottomButtonAssembly__VariantsArgs,
    ReservedPropsType
  > &
    /* Specify args directly as props*/ Omit<
      PlasmicNavigationBottomButtonAssembly__ArgsType,
      ReservedPropsType
    > &
    /* Specify overrides for each element directly as props*/ Omit<
      NodeOverridesType<T>,
      ReservedPropsType | VariantPropType | ArgPropType
    > &
    /* Specify props for the root element*/ Omit<
      Partial<React.ComponentProps<NodeDefaultElementType[T]>>,
      ReservedPropsType | VariantPropType | ArgPropType | DescendantsType<T>
    >;

function makeNodeComponent<NodeName extends NodeNameType>(nodeName: NodeName) {
  type PropsType = NodeComponentProps<NodeName> & { key?: React.Key };
  const func = function <T extends PropsType>(
    props: T & StrictProps<T, PropsType>
  ) {
    const { variants, args, overrides } = React.useMemo(
      () =>
        deriveRenderOpts(props, {
          name: nodeName,
          descendantNames: PlasmicDescendants[nodeName],
          internalArgPropNames: PlasmicNavigationBottomButtonAssembly__ArgProps,
          internalVariantPropNames:
            PlasmicNavigationBottomButtonAssembly__VariantProps
        }),
      [props, nodeName]
    );
    return PlasmicNavigationBottomButtonAssembly__RenderFunc({
      variants,
      args,
      overrides,
      forNode: nodeName
    });
  };
  if (nodeName === "animationContainer") {
    func.displayName = "PlasmicNavigationBottomButtonAssembly";
  } else {
    func.displayName = `PlasmicNavigationBottomButtonAssembly.${nodeName}`;
  }
  return func;
}

export const PlasmicNavigationBottomButtonAssembly = Object.assign(
  // Top-level PlasmicNavigationBottomButtonAssembly renders the root element
  makeNodeComponent("animationContainer"),
  {
    // Helper components rendering sub-elements
    navigationActiveSlotSlider: makeNodeComponent("navigationActiveSlotSlider"),
    navigationHoverSlotSlider: makeNodeComponent("navigationHoverSlotSlider"),
    loginButton: makeNodeComponent("loginButton"),
    navIcon2: makeNodeComponent("navIcon2"),
    profileButton: makeNodeComponent("profileButton"),
    icon: makeNodeComponent("icon"),
    settingsButton: makeNodeComponent("settingsButton"),
    icon2: makeNodeComponent("icon2"),
    moreButton: makeNodeComponent("moreButton"),
    navIcon: makeNodeComponent("navIcon"),

    // Metadata about props expected for PlasmicNavigationBottomButtonAssembly
    internalVariantProps: PlasmicNavigationBottomButtonAssembly__VariantProps,
    internalArgProps: PlasmicNavigationBottomButtonAssembly__ArgProps
  }
);

export default PlasmicNavigationBottomButtonAssembly;
/* prettier-ignore-end */
