/* eslint-disable */
/* tslint:disable */
// @ts-nocheck
/* prettier-ignore-start */
import React from "react";
import { classNames } from "@plasmicapp/react-web";

export type CertificateIconProps = React.ComponentProps<"svg"> & {
  title?: string;
};

export function CertificateIcon(props: CertificateIconProps) {
  const { className, style, title, ...restProps } = props;
  return (
    <svg
      xmlns={"http://www.w3.org/2000/svg"}
      className={classNames(
        "plasmic-default__svg",
        className,
        "icon icon-tabler icon-tabler-certificate"
      )}
      viewBox={"0 0 24 24"}
      strokeWidth={"1.5"}
      stroke={"currentColor"}
      fill={"none"}
      strokeLinecap={"round"}
      strokeLinejoin={"round"}
      height={"1em"}
      style={style}
      {...restProps}
    >
      {title && <title>{title}</title>}

      <path d={"M0 0h24v24H0z"} fill={"none"} stroke={"none"}></path>

      <path d={"M12 15a3 3 0 106 0 3 3 0 10-6 0"}></path>

      <path d={"M13 17.5V22l2-1.5 2 1.5v-4.5"}></path>

      <path
        d={
          "M10 19H5a2 2 0 01-2-2V7c0-1.1.9-2 2-2h14a2 2 0 012 2v10a2 2 0 01-1 1.73M6 9h12M6 12h3m-3 3h2"
        }
      ></path>
    </svg>
  );
}

export default CertificateIcon;
/* prettier-ignore-end */
