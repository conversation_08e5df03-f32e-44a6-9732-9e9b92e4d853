.caseStudyFormatting {
  box-shadow: 0px 2px 20px -2px #8b8b8b33;
  display: flex;
  flex-direction: column;
  position: relative;
  width: 425px;
  height: 325px;
  justify-content: flex-start;
  align-items: center;
  transition-property: all;
  transition-duration: 0.05s;
  background: var(--token-1AMvw6c2eIK7);
  -webkit-transition-property: all;
  -webkit-transition-duration: 0.05s;
  border: 0.01cm none #e4e4e7;
}
.caseStudyFormattingcontent_text {
  background: var(--token-K5FbAPSIIrXM);
}
.caseStudyFormattingmodals_popped {
  max-width: 80%;
  height: 100%;
  max-height: 80%;
  width: 100%;
  min-width: 0;
  min-height: 0;
}
.caseStudyFormattinghoverAndClick_hover {
  transform: scaleX(1.02) scaleY(1.015) scaleZ(1);
}
.caseStudyFormattinghoverAndClick_click {
  transform: scaleX(1.015) scaleY(1.01) scaleZ(1);
}
.glassOverlay {
  position: relative;
  align-content: flex-start;
  justify-items: center;
  width: 100%;
  min-width: 0;
  display: none;
  grid-template-columns:
    var(--plsmc-viewport-gap) 1fr minmax(0, var(--plsmc-wide-chunk))
    min(
      var(--plsmc-standard-width),
      calc(100% - var(--plsmc-viewport-gap) - var(--plsmc-viewport-gap))
    )
    minmax(0, var(--plsmc-wide-chunk)) 1fr var(--plsmc-viewport-gap);
  padding: var(--token-sazGmnf7GWAk);
}
.glassOverlay > * {
  grid-column: 4;
}
.glassOverlaycontent_photo > * {
  grid-column: 4;
}
.glassOverlaycontent_text > * {
  grid-column: 4;
}
.glassOverlaycontent_photoWText > * {
  grid-column: 4;
}
.glassOverlaycomingSoon {
  display: flex;
  flex-direction: column;
  background: linear-gradient(0deg, #ededed8c 0%, #ededed0d 100%);
  width: auto;
  height: auto;
  backdrop-filter: blur(3px);
  position: absolute;
  top: 0;
  z-index: 100;
  align-items: center;
  justify-content: center;
  bottom: 0;
  right: 0;
  left: 0;
  -webkit-backdrop-filter: blur(3px);
  border: 1px solid var(--token-1AMvw6c2eIK7);
}
.glassOverlaycomingSoon > * {
  grid-column: 4;
}
.h3 {
  position: relative;
  width: 100%;
  height: auto;
  max-width: 100%;
  font-family: var(--token-z1yrQVi72Nj1);
  text-align: center;
  color: var(--token-K5FbAPSIIrXM);
  font-weight: 700;
  transform: rotateX(0deg) rotateY(0deg) rotateZ(-2deg);
  left: auto;
  top: auto;
  margin-top: 0px;
  z-index: 101;
  grid-column-start: 1 !important;
  grid-column-end: -1 !important;
  padding: var(--token-4Wrp9mDZCSCQ);
}
.headerSection {
  position: relative;
  align-content: center;
  justify-items: flex-start;
  width: 100%;
  height: 42px;
  background: var(--token-K5FbAPSIIrXM);
  flex-direction: column;
  justify-content: center;
  align-items: flex-start;
  margin-bottom: 0px;
  min-width: 0;
  flex-shrink: 0;
  display: none;
  grid-template-columns:
    var(--plsmc-viewport-gap) 1fr minmax(0, var(--plsmc-wide-chunk))
    min(
      var(--plsmc-standard-width),
      calc(100% - var(--plsmc-viewport-gap) - var(--plsmc-viewport-gap))
    )
    minmax(0, var(--plsmc-wide-chunk)) 1fr var(--plsmc-viewport-gap);
  padding: var(--token-sazGmnf7GWAk) var(--token-sazGmnf7GWAk)
    var(--token-sazGmnf7GWAk) 12px;
}
.headerSection > * {
  grid-column: 4;
}
.headerSectioncontent_photo {
  background: var(--token-hx9QrD7iOwnt);
  display: grid;
}
.headerSectioncontent_photo > * {
  grid-column: 4;
}
.headerSectioncontent_text {
  display: grid;
}
.headerSectioncontent_text > * {
  grid-column: 4;
}
.headerSectioncontent_photoWText {
  display: grid;
}
.headerSectioncontent_photoWText > * {
  grid-column: 4;
}
.headerSectionmodals_popped {
  display: grid;
}
.headerSectionmodals_popped > * {
  grid-column: 4;
}
.headerSectioncomingSoon {
  display: none;
}
.headerSectioncomingSoon > * {
  grid-column: 4;
}
.heading2 {
  position: relative;
  width: 100%;
  height: auto;
  max-width: 100%;
  font-size: var(--token-_-i82ElPHE7I);
  padding-left: 0px;
  color: var(--token-1AMvw6c2eIK7);
  min-width: 0;
}
.backgroundBaseColor {
  position: relative;
  width: 100%;
  flex-direction: column;
  height: 100%;
  background: var(--token-1AMvw6c2eIK7);
  min-width: 0;
  min-height: 0;
  display: none;
}
.backgroundBaseColorcontent_photo {
  display: flex;
}
.backgroundBaseColorcomingSoon {
  display: flex;
}
.displayImage {
  max-width: 100%;
  object-fit: cover;
  height: 100%;
  position: relative;
  min-height: 0;
  display: none !important;
}
.displayImage > picture > img {
  object-fit: cover;
}
.displayImagecontent_photo {
  display: block !important;
}
.displayImagecomingSoon {
  display: block !important;
}
.freeBox {
  display: flex;
  position: relative;
  flex-direction: row;
  width: 100%;
  height: auto;
  max-width: 100%;
  min-width: 0;
  padding: var(--token-sazGmnf7GWAk);
  margin: var(--token-sazGmnf7GWAk);
}
.freeBox > :global(.__wab_flex-container) {
  flex-direction: row;
  align-items: stretch;
  justify-content: flex-start;
  min-width: 0;
  margin-left: calc(0px - var(--token-4Wrp9mDZCSCQ));
  width: calc(100% + var(--token-4Wrp9mDZCSCQ));
}
.freeBox > :global(.__wab_flex-container) > *,
.freeBox > :global(.__wab_flex-container) > :global(.__wab_slot) > *,
.freeBox > :global(.__wab_flex-container) > picture > img,
.freeBox
  > :global(.__wab_flex-container)
  > :global(.__wab_slot)
  > picture
  > img {
  margin-left: var(--token-4Wrp9mDZCSCQ);
}
.freeBoxcontent_photoWText > :global(.__wab_flex-container) {
  align-items: center;
}
.summaryText {
  width: 100%;
  height: auto;
  max-width: 100%;
  min-width: 0;
  display: none;
}
.summaryTextcontent_text {
  color: var(--token-1AMvw6c2eIK7);
  display: block;
  padding: var(--token-4Wrp9mDZCSCQ) 20px;
}
.summaryTextcontent_photoWText {
  display: block;
  padding: var(--token-4Wrp9mDZCSCQ) 20px;
}
.img {
  position: relative;
  max-width: 100%;
  width: 185px;
  height: 210px;
  margin-right: 14px;
  margin-top: 0px;
  flex-shrink: 0;
  display: none !important;
}
.imgcontent_photoWText {
  display: block !important;
}
.skillsBadgeBar {
  position: relative;
  width: 100%;
  flex-direction: column;
  left: auto;
  top: auto;
  z-index: 1;
  height: auto;
  bottom: auto;
  min-width: 0;
  display: none;
  padding: var(--token-sazGmnf7GWAk);
}
.skillsBadgeBar > :global(.__wab_flex-container) {
  flex-direction: column;
  align-items: flex-end;
  justify-content: flex-start;
  min-width: 0;
  margin-top: calc(0px - var(--token-4Wrp9mDZCSCQ));
  height: calc(100% + var(--token-4Wrp9mDZCSCQ));
}
.skillsBadgeBar > :global(.__wab_flex-container) > *,
.skillsBadgeBar > :global(.__wab_flex-container) > :global(.__wab_slot) > *,
.skillsBadgeBar > :global(.__wab_flex-container) > picture > img,
.skillsBadgeBar
  > :global(.__wab_flex-container)
  > :global(.__wab_slot)
  > picture
  > img {
  margin-top: var(--token-4Wrp9mDZCSCQ);
}
.skillsBadgeBarcontent_text {
  display: flex;
  flex-direction: row;
  padding-left: 16px;
  overflow: auto;
  padding-right: 16px;
}
.skillsBadgeBarcontent_text > :global(.__wab_flex-container) {
  flex-direction: row;
  justify-content: flex-start;
  align-items: flex-end;
  margin-left: calc(0px - var(--token-4Wrp9mDZCSCQ));
  width: calc(100% + var(--token-4Wrp9mDZCSCQ));
  margin-top: calc(0px - 0px);
  height: calc(100% + 0px);
}
.skillsBadgeBarcontent_text > :global(.__wab_flex-container) > *,
.skillsBadgeBarcontent_text
  > :global(.__wab_flex-container)
  > :global(.__wab_slot)
  > *,
.skillsBadgeBarcontent_text > :global(.__wab_flex-container) > picture > img,
.skillsBadgeBarcontent_text
  > :global(.__wab_flex-container)
  > :global(.__wab_slot)
  > picture
  > img {
  margin-left: var(--token-4Wrp9mDZCSCQ);
  margin-top: 0px;
}
.skillsBadgeBarcomingSoon {
  position: absolute;
  left: 0px;
  bottom: 16px;
  display: flex;
}
.subcomponentSkillsBadgeForCaseStudyTiles:global(.__wab_instance) {
  max-width: 100%;
  position: relative;
}
