import { getSessionCookie } from 'better-auth/cookies';
import { type NextRequest, NextResponse } from 'next/server';

// Define protected and public routes
const protectedPaths = ['/onboarding', '/settings', '/waitlisted'];
const protectedPatterns = [/^\/profile\/.+$/];
const authenticatedRedirects: Record<string, string> = {
  '/login': '/settings',
  '/admin/login': '/admin/',
};
const redirectRules: Record<string, string> = {
  '/admin/forgot': '/admin/login',
};

// Validates user session and handles waitlist-related redirects
async function validateSession(req: NextRequest, path: string) {
  try {
    // Use the correct better-auth session endpoint
    const apiUrl = new URL(
      '/api/auth/session',
      req.nextUrl.origin
    ).toString();
    const response = await fetch(apiUrl, {
      headers: {
        cookie: req.headers.get('cookie') || '',
      },
      cache: 'no-store', // Prevent caching issues in production
    });

    // If the response is not ok, return null instead of redirecting
    if (!response.ok) {
      console.log('Session fetch failed:', response.status, response.statusText);
      return null;
    }

    const session = await response.json();
    console.log('Session data:', { user: session?.user, role: session?.user?.role, path });

    // If user is not waitlisted and trying to access waitlist page, redirect to login
    if (session?.user?.role !== 'waitlisted' && path === '/waitlisted') {
      console.log('Non-waitlisted user trying to access waitlist page, redirecting to login');
      return NextResponse.redirect(new URL('/login', req.url));
    }

    // If user is waitlisted and trying to access a protected route (except waitlist page), redirect to waitlist page
    if (
      session?.user?.role === 'waitlisted' &&
      isProtectedRoute(path) &&
      path !== '/waitlisted'
    ) {
      console.log('Waitlisted user trying to access protected route, redirecting to waitlist page');
      return NextResponse.redirect(new URL('/waitlisted', req.url));
    }
  } catch (error) {
    console.log('Session validation error:', error);
    return null;
  }
  return null;
}

// Checks if a path is protected by exact match or pattern
function isProtectedRoute(path: string) {
  return (
    protectedPaths.includes(path) ||
    protectedPatterns.some((pattern) => pattern.test(path))
  );
}

export default async function middleware(req: NextRequest) {
  const path = req.nextUrl.pathname;
  console.log('Middleware processing path:', path);

  // Get the session cookie
  const sessionCookie = getSessionCookie(req, {
    cookiePrefix: 'spherical-auth',
  });
  console.log('Session cookie exists:', !!sessionCookie);

  // If the route is protected and there is no session, redirect to the login page
  const isProtected = isProtectedRoute(path);
  console.log('Is protected route:', isProtected);

  if (isProtected && !sessionCookie) {
    console.log('Protected route without session, redirecting to login');
    return NextResponse.redirect(new URL('/login', req.url));
  }

  // If there's a session, check for waitlisted status
  if (sessionCookie) {
    console.log('Session cookie found, validating session');
    const validationResult = await validateSession(req, path);
    if (validationResult) {
      console.log('Validation result returned, redirecting');
      return validationResult;
    }
    console.log('No validation result, continuing');
  }

  // If the route is authenticated, redirect as explicitly specified
  if (authenticatedRedirects[path] && sessionCookie) {
    console.log('Authenticated redirect for path:', path);
    return NextResponse.redirect(
      new URL(authenticatedRedirects[path], req.url)
    );
  }

  // Apply explicit redirect rules
  if (redirectRules[path]) {
    console.log('Applying redirect rule for path:', path);
    return NextResponse.redirect(new URL(redirectRules[path], req.url));
  }

  console.log('Allowing access to path:', path);
  return NextResponse.next();
}

export const config = {
  // More specific matcher to avoid running middleware unnecessarily
  matcher: [
    '/login',
    '/admin/login',
    '/admin/forgot',
    '/onboarding/:path*',
    '/settings/:path*',
    '/profile/:path*',
    '/waitlisted',
  ],
};
