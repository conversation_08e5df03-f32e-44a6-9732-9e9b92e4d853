import path from 'node:path';
import { fileURLToPath } from 'node:url';
// storage-adapter-import-placeholder
import { mongooseAdapter } from '@payloadcms/db-mongodb';
import { resendAdapter } from '@payloadcms/email-resend';
import { sentryPlugin } from '@payloadcms/plugin-sentry';
import { lexicalEditor } from '@payloadcms/richtext-lexical';
import * as Sentry from '@sentry/nextjs';
import { buildConfig } from 'payload';
import sharp from 'sharp';
import { Media } from '@/collections/media';
import { Profile } from '@/collections/profile';
import { CaseStudies } from '@/collections/profile/case-studies';
import { Users } from '@/collections/users';

const filename = fileURLToPath(import.meta.url);
const dirname = path.dirname(filename);

export default buildConfig({
  admin: {
    user: Users.slug,
    autoLogin:
      process.env.NODE_ENV === 'development'
        ? {
            email: '<EMAIL>',
            password: 'spherical',
            prefillOnly: true,
          }
        : false,
    importMap: {
      baseDir: path.resolve(dirname),
    },
    components: {
      graphics: {
        Icon: { path: '@/components/spcms/spherical-icon' },
        Logo: { path: '@/components/spcms/spherical-logo' },
      },
      afterLogin: [
        {
          path: '@/components/spcms/auth-form',
        },
      ],
      logout: {
        Button: {
          path: '@/components/spcms/sign-out',
        },
      },
    },
    meta: {
      titleSuffix: '- Spherical CMS',
      description: 'A CMS for Spherical',
    },
    suppressHydrationWarning: true,
  },
  collections: [Media, Profile, CaseStudies, Users],
  editor: lexicalEditor(),
  secret: process.env.PAYLOAD_SECRET || '',
  typescript: {
    outputFile: path.resolve(dirname, 'payload-types.ts'),
  },
  db: mongooseAdapter({
    url: process.env.DATABASE_URI || '',
    allowAdditionalKeys: true,
  }),
  email: resendAdapter({
    defaultFromName: process.env.APP_NAME || 'Spherical CMS',
    defaultFromAddress: process.env.APP_EMAIL || '<EMAIL>',
    apiKey: process.env.RESEND_API_KEY || '',
  }),
  sharp,
  plugins: [
    sentryPlugin({
      Sentry,
      options: {
        captureErrors: [
          400, 401, 403, 404, 405, 406, 407, 408, 409, 410, 411, 412, 413, 414,
          415, 416, 417, 418, 421, 422, 423, 424, 426, 428, 429, 431, 451, 500,
          501, 502, 503, 504, 505, 506, 507, 508, 510, 511,
        ],
      },
    }),
  ],
});
