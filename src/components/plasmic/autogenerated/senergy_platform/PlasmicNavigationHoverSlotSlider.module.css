.hoverSlotSlider {
  cursor: pointer;
  transition-property: transform;
  transition-duration: 0.15s;
  position: relative;
  background: #f7f5f399;
  justify-content: center;
  flex-direction: column;
  height: 36px;
  width: 100%;
  max-width: 93%;
  min-width: 0;
  display: none;
  -webkit-transition-property: transform;
  -webkit-transition-duration: 0.15s;
  padding: var(--token-sazGmnf7GWAk);
}
.hoverSlotSlideractiveTab_slot1 {
  display: none;
}
.hoverSlotSlideractiveTab_slot2 {
  display: none;
}
.hoverSlotSlideractiveTab_slot3 {
  display: none;
}
.hoverSlotSliderhoverTab_slot1 {
  transform: translateX(0px) translateY(0px) translateZ(0px);
  display: flex;
}
.hoverSlotSliderhoverTab_slot2 {
  transform: translateX(0px) translateY(36px) translateZ(0px);
  display: flex;
}
.hoverSlotSliderhoverTab_slot3 {
  transform: translateX(0px) translateY(72px) translateZ(0px);
  display: flex;
}
.hoverSlotSlideractiveTab_slot1_hoverTab_slot2 {
  display: flex;
}
.hoverSlotSlideractiveTab_slot2_hoverTab_slot1 {
  display: flex;
}
.hoverSlotSlideractiveTab_slot1_hoverTab_slot3 {
  display: flex;
}
.hoverSlotSlideractiveTab_slot3_hoverTab_slot1 {
  display: flex;
}
.hoverSlotSlideractiveTab_slot2_hoverTab_slot3 {
  display: flex;
}
.hoverSlotSliderhoverTab_slot2_activeTab_slot3 {
  display: flex;
}
