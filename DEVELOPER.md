# Developer Guide

## Getting Started

This project uses Docker Compose for local development to ensure a consistent development environment across all machines.

### Prerequisites

- [Docker](https://docs.docker.com/get-docker/)
- [Docker Compose](https://docs.docker.com/compose/install/) (usually included with Docker)
- [Node.js](https://nodejs.org/) (for running commands outside containers if needed)
- [pnpm](https://pnpm.io/installation) (package manager)

### Development Workflow

#### Starting the Development Environment
