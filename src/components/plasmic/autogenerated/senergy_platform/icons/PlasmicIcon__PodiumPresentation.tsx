/* eslint-disable */
/* tslint:disable */
// @ts-nocheck
/* prettier-ignore-start */
import React from "react";
import { classNames } from "@plasmicapp/react-web";

export type PodiumPresentationIconProps = React.ComponentProps<"svg"> & {
  title?: string;
};

export function PodiumPresentationIcon(props: PodiumPresentationIconProps) {
  const { className, style, title, ...restProps } = props;
  return (
    <svg
      xmlns={"http://www.w3.org/2000/svg"}
      className={classNames(
        "plasmic-default__svg",
        className,
        "icon icon-tabler icon-tabler-podium"
      )}
      viewBox={"0 0 24 24"}
      strokeWidth={"1.5"}
      stroke={"currentColor"}
      fill={"none"}
      strokeLinecap={"round"}
      strokeLinejoin={"round"}
      height={"1em"}
      style={style}
      {...restProps}
    >
      {title && <title>{title}</title>}

      <path d={"M0 0h24v24H0z"} fill={"none"} stroke={"none"}></path>

      <path
        d={
          "M5 8h14l-.621 2.485A2 2 0 0116.439 12H7.561a2 2 0 01-1.94-1.515L5 8zm2 0V6a3 3 0 013-3m-2 9l1 9m7-9l-1 9m-8 0h10"
        }
      ></path>
    </svg>
  );
}

export default PodiumPresentationIcon;
/* prettier-ignore-end */
