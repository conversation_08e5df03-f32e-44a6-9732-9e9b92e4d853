/* eslint-disable */
/* tslint:disable */
// @ts-nocheck
/* prettier-ignore-start */
import React from "react";
import { classNames } from "@plasmicapp/react-web";

export type CurrentLocationIconProps = React.ComponentProps<"svg"> & {
  title?: string;
};

export function CurrentLocationIcon(props: CurrentLocationIconProps) {
  const { className, style, title, ...restProps } = props;
  return (
    <svg
      xmlns={"http://www.w3.org/2000/svg"}
      className={classNames(
        "plasmic-default__svg",
        className,
        "icon icon-tabler icon-tabler-current-location"
      )}
      viewBox={"0 0 24 24"}
      strokeWidth={"1.5"}
      stroke={"currentColor"}
      fill={"none"}
      strokeLinecap={"round"}
      strokeLinejoin={"round"}
      height={"1em"}
      style={style}
      {...restProps}
    >
      {title && <title>{title}</title>}

      <path d={"M0 0h24v24H0z"} fill={"none"} stroke={"none"}></path>

      <path d={"M9 12a3 3 0 106 0 3 3 0 10-6 0"}></path>

      <path
        d={"M4 12a8 8 0 1016 0 8 8 0 10-16 0m8-10v2m0 16v2m8-10h2M2 12h2"}
      ></path>
    </svg>
  );
}

export default CurrentLocationIcon;
/* prettier-ignore-end */
