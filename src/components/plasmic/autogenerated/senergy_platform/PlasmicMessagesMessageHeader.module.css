.messagesBanner {
  display: flex;
  flex-direction: row;
  position: relative;
  width: 100%;
  height: auto;
  justify-content: space-between;
  align-items: center;
  background: var(--token-1AMvw6c2eIK7);
  min-width: 0;
  padding: 16px 24px;
}
.messageBannerProfilePhoto {
  position: relative;
  object-fit: cover;
  max-width: 100%;
  width: var(--token-M1cOQzpe0467);
  height: var(--token-M1cOQzpe0467);
  flex-shrink: 0;
  border-radius: 100%;
}
.messageBannerProfilePhoto > picture > img {
  object-fit: cover;
}
.freeBox {
  display: flex;
  position: relative;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start;
  width: 100%;
  height: auto;
  max-width: 100%;
  min-width: 0;
  padding: var(--token-sazGmnf7GWAk);
  margin: var(--token-sazGmnf7GWAk) var(--token-sazGmnf7GWAk)
    var(--token-sazGmnf7GWAk) 16px;
}
.text {
  width: 100%;
  height: auto;
  max-width: 100%;
  font-size: var(--token-9KumB6TRpaad);
  padding-left: 0px;
  white-space: pre;
  text-overflow: ellipsis;
  overflow: hidden;
  min-width: 0;
}
.viewProfileButton:global(.__wab_instance) {
  max-width: 100%;
  position: relative;
}
.svg__flJa0 {
  position: relative;
  object-fit: cover;
  width: auto;
  height: 1em;
}
.svg__smfum {
  position: relative;
  object-fit: cover;
  width: auto;
  height: 1em;
}
