{"platform": "nextjs", "code": {"lang": "ts", "scheme": "blackbox", "reactRuntime": "classic"}, "style": {"scheme": "css-modules", "defaultStyleCssFilePath": "autogenerated/plasmic__default_style.module.css"}, "images": {"scheme": "public-files", "publicDir": "../../../public", "publicUrlPrefix": "/"}, "tokens": {"scheme": "theo", "tokensFilePath": "plasmic-tokens.theo.json"}, "srcDir": "./src/components/plasmic", "defaultPlasmicDir": "./autogenerated", "projects": [{"projectId": "nZeWKcn21KijvC7eT8B3c7", "projectApiToken": "nJ4ZwV2WlDFZAFlwzoaFilAIL8DKOEVFIyxjICpOzuMb9nqh4Fn89Vj5cMMwBVKXfYByvjpenP6pgXQ", "projectName": "Senergy Platform", "version": "latest", "cssFilePath": "autogenerated/senergy_platform/plasmic.module.css", "components": [{"id": "50ENdwdc_-7i", "name": "PageComingSoon", "type": "managed", "projectId": "nZeWKcn21KijvC7eT8B3c7", "renderModuleFilePath": "autogenerated/senergy_platform/PlasmicPageComingSoon.tsx", "importSpec": {"modulePath": "PageComingSoon.tsx"}, "cssFilePath": "autogenerated/senergy_platform/PlasmicPageComingSoon.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "ezhuRZvm_fH9", "name": "SubcomponentButton", "type": "managed", "projectId": "nZeWKcn21KijvC7eT8B3c7", "renderModuleFilePath": "autogenerated/senergy_platform/PlasmicSubcomponentButton.tsx", "importSpec": {"modulePath": "SubcomponentButton.tsx"}, "cssFilePath": "autogenerated/senergy_platform/PlasmicSubcomponentButton.module.css", "scheme": "blackbox", "componentType": "component", "plumeType": "button"}, {"id": "cW7vJ_ZjQaBV", "name": "PageOnboarding", "type": "managed", "projectId": "nZeWKcn21KijvC7eT8B3c7", "renderModuleFilePath": "autogenerated/senergy_platform/PlasmicPageOnboarding.tsx", "importSpec": {"modulePath": "PageOnboarding.tsx"}, "cssFilePath": "autogenerated/senergy_platform/PlasmicPageOnboarding.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "Uz656rZzIxzC", "name": "ProfileSectionsProfileSectionHeading", "type": "managed", "projectId": "nZeWKcn21KijvC7eT8B3c7", "renderModuleFilePath": "autogenerated/senergy_platform/PlasmicProfileSectionsProfileSectionHeading.tsx", "importSpec": {"modulePath": "ProfileSectionsProfileSectionHeading.tsx"}, "cssFilePath": "autogenerated/senergy_platform/PlasmicProfileSectionsProfileSectionHeading.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "oCayNd_9ZCYX", "name": "SearchTile", "type": "managed", "projectId": "nZeWKcn21KijvC7eT8B3c7", "renderModuleFilePath": "autogenerated/senergy_platform/PlasmicSearchTile.tsx", "importSpec": {"modulePath": "SearchTile.tsx"}, "cssFilePath": "autogenerated/senergy_platform/PlasmicSearchTile.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "Vq_8v2qcFNSB", "name": "ProfileOverviewHighlightedWorksLayout", "type": "managed", "projectId": "nZeWKcn21KijvC7eT8B3c7", "renderModuleFilePath": "autogenerated/senergy_platform/PlasmicProfileOverviewHighlightedWorksLayout.tsx", "importSpec": {"modulePath": "ProfileOverviewHighlightedWorksLayout.tsx"}, "cssFilePath": "autogenerated/senergy_platform/PlasmicProfileOverviewHighlightedWorksLayout.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "qfVGJMrc3gkW", "name": "ProfileSectionsIntroductionBlock", "type": "managed", "projectId": "nZeWKcn21KijvC7eT8B3c7", "renderModuleFilePath": "autogenerated/senergy_platform/PlasmicProfileSectionsIntroductionBlock.tsx", "importSpec": {"modulePath": "ProfileSectionsIntroductionBlock.tsx"}, "cssFilePath": "autogenerated/senergy_platform/PlasmicProfileSectionsIntroductionBlock.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "Z1o0_V1Y-Oht", "name": "ProfileSectionsEducationBlock", "type": "managed", "projectId": "nZeWKcn21KijvC7eT8B3c7", "renderModuleFilePath": "autogenerated/senergy_platform/PlasmicProfileSectionsEducationBlock.tsx", "importSpec": {"modulePath": "ProfileSectionsEducationBlock.tsx"}, "cssFilePath": "autogenerated/senergy_platform/PlasmicProfileSectionsEducationBlock.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "zdegYaKxvR34", "name": "ProfileSectionsPublicationsBlock", "type": "managed", "projectId": "nZeWKcn21KijvC7eT8B3c7", "renderModuleFilePath": "autogenerated/senergy_platform/PlasmicProfileSectionsPublicationsBlock.tsx", "importSpec": {"modulePath": "ProfileSectionsPublicationsBlock.tsx"}, "cssFilePath": "autogenerated/senergy_platform/PlasmicProfileSectionsPublicationsBlock.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "f3EcElmDdrge", "name": "ProfileSectionsLicensesAndCertificationsComboBlock", "type": "managed", "projectId": "nZeWKcn21KijvC7eT8B3c7", "renderModuleFilePath": "autogenerated/senergy_platform/PlasmicProfileSectionsLicensesAndCertificationsComboBlock.tsx", "importSpec": {"modulePath": "ProfileSectionsLicensesAndCertificationsComboBlock.tsx"}, "cssFilePath": "autogenerated/senergy_platform/PlasmicProfileSectionsLicensesAndCertificationsComboBlock.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "9Evwwahu_Iga", "name": "ProfileSectionsExperienceBlock", "type": "managed", "projectId": "nZeWKcn21KijvC7eT8B3c7", "renderModuleFilePath": "autogenerated/senergy_platform/PlasmicProfileSectionsExperienceBlock.tsx", "importSpec": {"modulePath": "ProfileSectionsExperienceBlock.tsx"}, "cssFilePath": "autogenerated/senergy_platform/PlasmicProfileSectionsExperienceBlock.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "NkPXGNNENGDZ", "name": "ProfileSectionsPatentsAndTrademarksComboBlock", "type": "managed", "projectId": "nZeWKcn21KijvC7eT8B3c7", "renderModuleFilePath": "autogenerated/senergy_platform/PlasmicProfileSectionsPatentsAndTrademarksComboBlock.tsx", "importSpec": {"modulePath": "ProfileSectionsPatentsAndTrademarksComboBlock.tsx"}, "cssFilePath": "autogenerated/senergy_platform/PlasmicProfileSectionsPatentsAndTrademarksComboBlock.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "6qIgV9tA-TG7", "name": "ProfileCoreContentWrapper", "type": "managed", "projectId": "nZeWKcn21KijvC7eT8B3c7", "renderModuleFilePath": "autogenerated/senergy_platform/PlasmicProfileCoreContentWrapper.tsx", "importSpec": {"modulePath": "ProfileCoreContentWrapper.tsx"}, "cssFilePath": "autogenerated/senergy_platform/PlasmicProfileCoreContentWrapper.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "8Z-KG0onltAY", "name": "ProfileOverviewOverviewContentBlock", "type": "managed", "projectId": "nZeWKcn21KijvC7eT8B3c7", "renderModuleFilePath": "autogenerated/senergy_platform/PlasmicProfileOverviewOverviewContentBlock.tsx", "importSpec": {"modulePath": "ProfileOverviewOverviewContentBlock.tsx"}, "cssFilePath": "autogenerated/senergy_platform/PlasmicProfileOverviewOverviewContentBlock.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "pRxYe0rDyQBY", "name": "SubcomponentSlotHeading", "type": "managed", "projectId": "nZeWKcn21KijvC7eT8B3c7", "renderModuleFilePath": "autogenerated/senergy_platform/PlasmicSubcomponentSlotHeading.tsx", "importSpec": {"modulePath": "SubcomponentSlotHeading.tsx"}, "cssFilePath": "autogenerated/senergy_platform/PlasmicSubcomponentSlotHeading.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "HVy6-eBfWfOH", "name": "ProfileOverviewSectionWrapperSm", "type": "managed", "projectId": "nZeWKcn21KijvC7eT8B3c7", "renderModuleFilePath": "autogenerated/senergy_platform/PlasmicProfileOverviewSectionWrapperSm.tsx", "importSpec": {"modulePath": "ProfileOverviewSectionWrapperSm.tsx"}, "cssFilePath": "autogenerated/senergy_platform/PlasmicProfileOverviewSectionWrapperSm.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "tlsYnN8cn_ki", "name": "ProfileTileLanguagesTile", "type": "managed", "projectId": "nZeWKcn21KijvC7eT8B3c7", "renderModuleFilePath": "autogenerated/senergy_platform/PlasmicProfileTileLanguagesTile.tsx", "importSpec": {"modulePath": "ProfileTileLanguagesTile.tsx"}, "cssFilePath": "autogenerated/senergy_platform/PlasmicProfileTileLanguagesTile.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "ptWaGH-zd0tJ", "name": "OnboardingOnboardingModal", "type": "managed", "projectId": "nZeWKcn21KijvC7eT8B3c7", "renderModuleFilePath": "autogenerated/senergy_platform/PlasmicOnboardingOnboardingModal.tsx", "importSpec": {"modulePath": "OnboardingOnboardingModal.tsx"}, "cssFilePath": "autogenerated/senergy_platform/PlasmicOnboardingOnboardingModal.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "Jugl-x2pKrbY", "name": "WipStatusBar", "type": "managed", "projectId": "nZeWKcn21KijvC7eT8B3c7", "renderModuleFilePath": "autogenerated/senergy_platform/PlasmicWipStatusBar.tsx", "importSpec": {"modulePath": "WipStatusBar.tsx"}, "cssFilePath": "autogenerated/senergy_platform/PlasmicWipStatusBar.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "iicTcR3JeMJ9", "name": "MessagesMessagePreview", "type": "managed", "projectId": "nZeWKcn21KijvC7eT8B3c7", "renderModuleFilePath": "autogenerated/senergy_platform/PlasmicMessagesMessagePreview.tsx", "importSpec": {"modulePath": "MessagesMessagePreview.tsx"}, "cssFilePath": "autogenerated/senergy_platform/PlasmicMessagesMessagePreview.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "J-ppsQZTajkA", "name": "MessagesMessage", "type": "managed", "projectId": "nZeWKcn21KijvC7eT8B3c7", "renderModuleFilePath": "autogenerated/senergy_platform/PlasmicMessagesMessage.tsx", "importSpec": {"modulePath": "MessagesMessage.tsx"}, "cssFilePath": "autogenerated/senergy_platform/PlasmicMessagesMessage.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "U2k0KPt8XoY6", "name": "MessagesDateDivider", "type": "managed", "projectId": "nZeWKcn21KijvC7eT8B3c7", "renderModuleFilePath": "autogenerated/senergy_platform/PlasmicMessagesDateDivider.tsx", "importSpec": {"modulePath": "MessagesDateDivider.tsx"}, "cssFilePath": "autogenerated/senergy_platform/PlasmicMessagesDateDivider.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "l8L6cX9yUnMy", "name": "MessagesAssembledMessagesEx", "type": "managed", "projectId": "nZeWKcn21KijvC7eT8B3c7", "renderModuleFilePath": "autogenerated/senergy_platform/PlasmicMessagesAssembledMessagesEx.tsx", "importSpec": {"modulePath": "MessagesAssembledMessagesEx.tsx"}, "cssFilePath": "autogenerated/senergy_platform/PlasmicMessagesAssembledMessagesEx.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "C1Ttec365eGb", "name": "MessagesAttachments", "type": "managed", "projectId": "nZeWKcn21KijvC7eT8B3c7", "renderModuleFilePath": "autogenerated/senergy_platform/PlasmicMessagesAttachments.tsx", "importSpec": {"modulePath": "MessagesAttachments.tsx"}, "cssFilePath": "autogenerated/senergy_platform/PlasmicMessagesAttachments.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "gmYVz9YWamHt", "name": "PageSettings", "type": "managed", "projectId": "nZeWKcn21KijvC7eT8B3c7", "renderModuleFilePath": "autogenerated/senergy_platform/PlasmicPageSettings.tsx", "importSpec": {"modulePath": "PageSettings.tsx"}, "cssFilePath": "autogenerated/senergy_platform/PlasmicPageSettings.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "A7wamfGEqfKZ", "name": "SubcomponentToggleSwitch", "type": "managed", "projectId": "nZeWKcn21KijvC7eT8B3c7", "renderModuleFilePath": "autogenerated/senergy_platform/PlasmicSubcomponentToggleSwitch.tsx", "importSpec": {"modulePath": "SubcomponentToggleSwitch.tsx"}, "cssFilePath": "autogenerated/senergy_platform/PlasmicSubcomponentToggleSwitch.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "gy59mJRQDt26", "name": "SettingsEmails", "type": "managed", "projectId": "nZeWKcn21KijvC7eT8B3c7", "renderModuleFilePath": "autogenerated/senergy_platform/PlasmicSettingsEmails.tsx", "importSpec": {"modulePath": "SettingsEmails.tsx"}, "cssFilePath": "autogenerated/senergy_platform/PlasmicSettingsEmails.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "OeFTSKgMult8", "name": "SubcomponentCheckbox", "type": "managed", "projectId": "nZeWKcn21KijvC7eT8B3c7", "renderModuleFilePath": "autogenerated/senergy_platform/PlasmicSubcomponentCheckbox.tsx", "importSpec": {"modulePath": "SubcomponentCheckbox.tsx"}, "cssFilePath": "autogenerated/senergy_platform/PlasmicSubcomponentCheckbox.module.css", "scheme": "blackbox", "componentType": "component", "plumeType": "checkbox"}, {"id": "_xepyTVjeZkI", "name": "MessagesInputBox", "type": "managed", "projectId": "nZeWKcn21KijvC7eT8B3c7", "renderModuleFilePath": "autogenerated/senergy_platform/PlasmicMessagesInputBox.tsx", "importSpec": {"modulePath": "MessagesInputBox.tsx"}, "cssFilePath": "autogenerated/senergy_platform/PlasmicMessagesInputBox.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "UWTH8LMACb3s", "name": "ProfileOverviewSeeMoreButton", "type": "managed", "projectId": "nZeWKcn21KijvC7eT8B3c7", "renderModuleFilePath": "autogenerated/senergy_platform/PlasmicProfileOverviewSeeMoreButton.tsx", "importSpec": {"modulePath": "ProfileOverviewSeeMoreButton.tsx"}, "cssFilePath": "autogenerated/senergy_platform/PlasmicProfileOverviewSeeMoreButton.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "po-xZOF5f8ym", "name": "ProfileSectionsLanguageBlock", "type": "managed", "projectId": "nZeWKcn21KijvC7eT8B3c7", "renderModuleFilePath": "autogenerated/senergy_platform/PlasmicProfileSectionsLanguageBlock.tsx", "importSpec": {"modulePath": "ProfileSectionsLanguageBlock.tsx"}, "cssFilePath": "autogenerated/senergy_platform/PlasmicProfileSectionsLanguageBlock.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "lypEqrndDg8A", "name": "OnboardingSelectionOptions", "type": "managed", "projectId": "nZeWKcn21KijvC7eT8B3c7", "renderModuleFilePath": "autogenerated/senergy_platform/PlasmicOnboardingSelectionOptions.tsx", "importSpec": {"modulePath": "OnboardingSelectionOptions.tsx"}, "cssFilePath": "autogenerated/senergy_platform/PlasmicOnboardingSelectionOptions.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "5U1t8gggh1Oz", "name": "ProfileBanner", "type": "managed", "projectId": "nZeWKcn21KijvC7eT8B3c7", "renderModuleFilePath": "autogenerated/senergy_platform/PlasmicProfileBanner.tsx", "importSpec": {"modulePath": "ProfileBanner.tsx"}, "cssFilePath": "autogenerated/senergy_platform/PlasmicProfileBanner.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "NQLRskEW8iAu", "name": "LoginLoginModal", "type": "managed", "projectId": "nZeWKcn21KijvC7eT8B3c7", "renderModuleFilePath": "autogenerated/senergy_platform/PlasmicLoginLoginModal.tsx", "importSpec": {"modulePath": "LoginLoginModal.tsx"}, "cssFilePath": "autogenerated/senergy_platform/PlasmicLoginLoginModal.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "FSzKF-1XfOQy", "name": "SearchCoreBookmarkButton", "type": "managed", "projectId": "nZeWKcn21KijvC7eT8B3c7", "renderModuleFilePath": "autogenerated/senergy_platform/PlasmicSearchCoreBookmarkButton.tsx", "importSpec": {"modulePath": "SearchCoreBookmarkButton.tsx"}, "cssFilePath": "autogenerated/senergy_platform/PlasmicSearchCoreBookmarkButton.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "o8dphkrS007N", "name": "SearchFiltersSection", "type": "managed", "projectId": "nZeWKcn21KijvC7eT8B3c7", "renderModuleFilePath": "autogenerated/senergy_platform/PlasmicSearchFiltersSection.tsx", "importSpec": {"modulePath": "SearchFiltersSection.tsx"}, "cssFilePath": "autogenerated/senergy_platform/PlasmicSearchFiltersSection.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "cEGGrNaGJB_3", "name": "SearchCoreFilterIcon", "type": "managed", "projectId": "nZeWKcn21KijvC7eT8B3c7", "renderModuleFilePath": "autogenerated/senergy_platform/PlasmicSearchCoreFilterIcon.tsx", "importSpec": {"modulePath": "SearchCoreFilterIcon.tsx"}, "cssFilePath": "autogenerated/senergy_platform/PlasmicSearchCoreFilterIcon.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "CvlbcsmPEnlZ", "name": "SubcomponentSelect", "type": "managed", "projectId": "nZeWKcn21KijvC7eT8B3c7", "renderModuleFilePath": "autogenerated/senergy_platform/PlasmicSubcomponentSelect.tsx", "importSpec": {"modulePath": "SubcomponentSelect.tsx"}, "cssFilePath": "autogenerated/senergy_platform/PlasmicSubcomponentSelect.module.css", "scheme": "blackbox", "componentType": "component", "plumeType": "select"}, {"id": "l0JdCpH74Lv2", "name": "SubcomponentSelect__Option", "type": "managed", "projectId": "nZeWKcn21KijvC7eT8B3c7", "renderModuleFilePath": "autogenerated/senergy_platform/PlasmicSubcomponentSelect__Option.tsx", "importSpec": {"modulePath": "SubcomponentSelect__Option.tsx"}, "cssFilePath": "autogenerated/senergy_platform/PlasmicSubcomponentSelect__Option.module.css", "scheme": "blackbox", "componentType": "component", "plumeType": "select-option"}, {"id": "w8TyM0MxRxnr", "name": "SubcomponentSelect__OptionGroup", "type": "managed", "projectId": "nZeWKcn21KijvC7eT8B3c7", "renderModuleFilePath": "autogenerated/senergy_platform/PlasmicSubcomponentSelect__OptionGroup.tsx", "importSpec": {"modulePath": "SubcomponentSelect__OptionGroup.tsx"}, "cssFilePath": "autogenerated/senergy_platform/PlasmicSubcomponentSelect__OptionGroup.module.css", "scheme": "blackbox", "componentType": "component", "plumeType": "select-option-group"}, {"id": "6o8B97wIM4e2", "name": "SubcomponentSelect__Overlay", "type": "managed", "projectId": "nZeWKcn21KijvC7eT8B3c7", "renderModuleFilePath": "autogenerated/senergy_platform/PlasmicSubcomponentSelect__Overlay.tsx", "importSpec": {"modulePath": "SubcomponentSelect__Overlay.tsx"}, "cssFilePath": "autogenerated/senergy_platform/PlasmicSubcomponentSelect__Overlay.module.css", "scheme": "blackbox", "componentType": "component", "plumeType": "triggered-overlay"}, {"id": "3wep4sj90mYo", "name": "MessagesMessageHeader", "type": "managed", "projectId": "nZeWKcn21KijvC7eT8B3c7", "renderModuleFilePath": "autogenerated/senergy_platform/PlasmicMessagesMessageHeader.tsx", "importSpec": {"modulePath": "MessagesMessageHeader.tsx"}, "cssFilePath": "autogenerated/senergy_platform/PlasmicMessagesMessageHeader.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "b0Ccy1-sNNdM", "name": "SearchDisplayResults", "type": "managed", "projectId": "nZeWKcn21KijvC7eT8B3c7", "renderModuleFilePath": "autogenerated/senergy_platform/PlasmicSearchDisplayResults.tsx", "importSpec": {"modulePath": "SearchDisplayResults.tsx"}, "cssFilePath": "autogenerated/senergy_platform/PlasmicSearchDisplayResults.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "9Tp3HjHzEzcX", "name": "ProfileSectionsSkillsBlock", "type": "managed", "projectId": "nZeWKcn21KijvC7eT8B3c7", "renderModuleFilePath": "autogenerated/senergy_platform/PlasmicProfileSectionsSkillsBlock.tsx", "importSpec": {"modulePath": "ProfileSectionsSkillsBlock.tsx"}, "cssFilePath": "autogenerated/senergy_platform/PlasmicProfileSectionsSkillsBlock.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "lS3Tush4KDKz", "name": "ProfileSectionsToolsBlock", "type": "managed", "projectId": "nZeWKcn21KijvC7eT8B3c7", "renderModuleFilePath": "autogenerated/senergy_platform/PlasmicProfileSectionsToolsBlock.tsx", "importSpec": {"modulePath": "ProfileSectionsToolsBlock.tsx"}, "cssFilePath": "autogenerated/senergy_platform/PlasmicProfileSectionsToolsBlock.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "V4zwOqV9lT52", "name": "ProfileTileSkillsTile", "type": "managed", "projectId": "nZeWKcn21KijvC7eT8B3c7", "renderModuleFilePath": "autogenerated/senergy_platform/PlasmicProfileTileSkillsTile.tsx", "importSpec": {"modulePath": "ProfileTileSkillsTile.tsx"}, "cssFilePath": "autogenerated/senergy_platform/PlasmicProfileTileSkillsTile.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "9IgGeZ9Jjxx9", "name": "ProfileTileToolsTile", "type": "managed", "projectId": "nZeWKcn21KijvC7eT8B3c7", "renderModuleFilePath": "autogenerated/senergy_platform/PlasmicProfileTileToolsTile.tsx", "importSpec": {"modulePath": "ProfileTileToolsTile.tsx"}, "cssFilePath": "autogenerated/senergy_platform/PlasmicProfileTileToolsTile.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "6bj154JTcwB9", "name": "ProfileOverviewSectionWrapperLrg", "type": "managed", "projectId": "nZeWKcn21KijvC7eT8B3c7", "renderModuleFilePath": "autogenerated/senergy_platform/PlasmicProfileOverviewSectionWrapperLrg.tsx", "importSpec": {"modulePath": "ProfileOverviewSectionWrapperLrg.tsx"}, "cssFilePath": "autogenerated/senergy_platform/PlasmicProfileOverviewSectionWrapperLrg.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "WMfLdq1qoZyo", "name": "SubcomponentDeleteButton", "type": "managed", "projectId": "nZeWKcn21KijvC7eT8B3c7", "renderModuleFilePath": "autogenerated/senergy_platform/PlasmicSubcomponentDeleteButton.tsx", "importSpec": {"modulePath": "SubcomponentDeleteButton.tsx"}, "cssFilePath": "autogenerated/senergy_platform/PlasmicSubcomponentDeleteButton.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "_UuKQc4xy7Sd", "name": "BookmarksBookmarkTiles", "type": "managed", "projectId": "nZeWKcn21KijvC7eT8B3c7", "renderModuleFilePath": "autogenerated/senergy_platform/PlasmicBookmarksBookmarkTiles.tsx", "importSpec": {"modulePath": "BookmarksBookmarkTiles.tsx"}, "cssFilePath": "autogenerated/senergy_platform/PlasmicBookmarksBookmarkTiles.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "xRgfrIgY1urL", "name": "<PERSON><PERSON><PERSON><PERSON>", "type": "managed", "projectId": "nZeWKcn21KijvC7eT8B3c7", "renderModuleFilePath": "autogenerated/senergy_platform/PlasmicPageLogin.tsx", "importSpec": {"modulePath": "PageLogin.tsx"}, "cssFilePath": "autogenerated/senergy_platform/PlasmicPageLogin.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "vjtGF3glVSRj", "name": "SubcomponentSkillsBadgeForCaseStudyTiles", "type": "managed", "projectId": "nZeWKcn21KijvC7eT8B3c7", "renderModuleFilePath": "autogenerated/senergy_platform/PlasmicSubcomponentSkillsBadgeForCaseStudyTiles.tsx", "importSpec": {"modulePath": "SubcomponentSkillsBadgeForCaseStudyTiles.tsx"}, "cssFilePath": "autogenerated/senergy_platform/PlasmicSubcomponentSkillsBadgeForCaseStudyTiles.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "oBy3j97i5seo", "name": "SettingsEmailEntry", "type": "managed", "projectId": "nZeWKcn21KijvC7eT8B3c7", "renderModuleFilePath": "autogenerated/senergy_platform/PlasmicSettingsEmailEntry.tsx", "importSpec": {"modulePath": "SettingsEmailEntry.tsx"}, "cssFilePath": "autogenerated/senergy_platform/PlasmicSettingsEmailEntry.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "msZSdug4VrvS", "name": "ProfileTileCaseStudyTile", "type": "managed", "projectId": "nZeWKcn21KijvC7eT8B3c7", "renderModuleFilePath": "autogenerated/senergy_platform/PlasmicProfileTileCaseStudyTile.tsx", "importSpec": {"modulePath": "ProfileTileCaseStudyTile.tsx"}, "cssFilePath": "autogenerated/senergy_platform/PlasmicProfileTileCaseStudyTile.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "YSAU2xF9PL58", "name": "ProfileCaseStudiesWorksGrid", "type": "managed", "projectId": "nZeWKcn21KijvC7eT8B3c7", "renderModuleFilePath": "autogenerated/senergy_platform/PlasmicProfileCaseStudiesWorksGrid.tsx", "importSpec": {"modulePath": "ProfileCaseStudiesWorksGrid.tsx"}, "cssFilePath": "autogenerated/senergy_platform/PlasmicProfileCaseStudiesWorksGrid.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "FmLLnhtvs10f", "name": "ProfileCoreProfileImage", "type": "managed", "projectId": "nZeWKcn21KijvC7eT8B3c7", "renderModuleFilePath": "autogenerated/senergy_platform/PlasmicProfileCoreProfileImage.tsx", "importSpec": {"modulePath": "ProfileCoreProfileImage.tsx"}, "cssFilePath": "autogenerated/senergy_platform/PlasmicProfileCoreProfileImage.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "vFlsxZd2-jZM", "name": "SubcomponentAddSectionButton", "type": "managed", "projectId": "nZeWKcn21KijvC7eT8B3c7", "renderModuleFilePath": "autogenerated/senergy_platform/PlasmicSubcomponentAddSectionButton.tsx", "importSpec": {"modulePath": "SubcomponentAddSectionButton.tsx"}, "cssFilePath": "autogenerated/senergy_platform/PlasmicSubcomponentAddSectionButton.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "ZpGWQx6DTU7R", "name": "ProfileTileIntroductionTile", "type": "managed", "projectId": "nZeWKcn21KijvC7eT8B3c7", "renderModuleFilePath": "autogenerated/senergy_platform/PlasmicProfileTileIntroductionTile.tsx", "importSpec": {"modulePath": "ProfileTileIntroductionTile.tsx"}, "cssFilePath": "autogenerated/senergy_platform/PlasmicProfileTileIntroductionTile.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "6P769Hd_cTVa", "name": "ProfileTileEducationTile", "type": "managed", "projectId": "nZeWKcn21KijvC7eT8B3c7", "renderModuleFilePath": "autogenerated/senergy_platform/PlasmicProfileTileEducationTile.tsx", "importSpec": {"modulePath": "ProfileTileEducationTile.tsx"}, "cssFilePath": "autogenerated/senergy_platform/PlasmicProfileTileEducationTile.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "Wr1MGCSkm1uC", "name": "NavigationButton", "type": "managed", "projectId": "nZeWKcn21KijvC7eT8B3c7", "renderModuleFilePath": "autogenerated/senergy_platform/PlasmicNavigationButton.tsx", "importSpec": {"modulePath": "NavigationButton.tsx"}, "cssFilePath": "autogenerated/senergy_platform/PlasmicNavigationButton.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "BKP1w5f1sryI", "name": "NavigationBottomButtonAssembly", "type": "managed", "projectId": "nZeWKcn21KijvC7eT8B3c7", "renderModuleFilePath": "autogenerated/senergy_platform/PlasmicNavigationBottomButtonAssembly.tsx", "importSpec": {"modulePath": "NavigationBottomButtonAssembly.tsx"}, "cssFilePath": "autogenerated/senergy_platform/PlasmicNavigationBottomButtonAssembly.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "EbN_LUazXitj", "name": "NavigationTopButtonAssembly", "type": "managed", "projectId": "nZeWKcn21KijvC7eT8B3c7", "renderModuleFilePath": "autogenerated/senergy_platform/PlasmicNavigationTopButtonAssembly.tsx", "importSpec": {"modulePath": "NavigationTopButtonAssembly.tsx"}, "cssFilePath": "autogenerated/senergy_platform/PlasmicNavigationTopButtonAssembly.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "4AlhyCYF3WHm", "name": "NavigationLegalButtons", "type": "managed", "projectId": "nZeWKcn21KijvC7eT8B3c7", "renderModuleFilePath": "autogenerated/senergy_platform/PlasmicNavigationLegalButtons.tsx", "importSpec": {"modulePath": "NavigationLegalButtons.tsx"}, "cssFilePath": "autogenerated/senergy_platform/PlasmicNavigationLegalButtons.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "t4FaTc0WFZv3", "name": "NavigationSidebar", "type": "managed", "projectId": "nZeWKcn21KijvC7eT8B3c7", "renderModuleFilePath": "autogenerated/senergy_platform/PlasmicNavigationSidebar.tsx", "importSpec": {"modulePath": "NavigationSidebar.tsx"}, "cssFilePath": "autogenerated/senergy_platform/PlasmicNavigationSidebar.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "19fXsiHSxAKf", "name": "NavigationSubBarMenus", "type": "managed", "projectId": "nZeWKcn21KijvC7eT8B3c7", "renderModuleFilePath": "autogenerated/senergy_platform/PlasmicNavigationSubBarMenus.tsx", "importSpec": {"modulePath": "NavigationSubBarMenus.tsx"}, "cssFilePath": "autogenerated/senergy_platform/PlasmicNavigationSubBarMenus.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "5qBtmyZf-TyR", "name": "ProfileCoreBannerNavigationBar", "type": "managed", "projectId": "nZeWKcn21KijvC7eT8B3c7", "renderModuleFilePath": "autogenerated/senergy_platform/PlasmicProfileCoreBannerNavigationBar.tsx", "importSpec": {"modulePath": "ProfileCoreBannerNavigationBar.tsx"}, "cssFilePath": "autogenerated/senergy_platform/PlasmicProfileCoreBannerNavigationBar.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "LXUgsJuF4wtn", "name": "NavigationPopupModals", "type": "managed", "projectId": "nZeWKcn21KijvC7eT8B3c7", "renderModuleFilePath": "autogenerated/senergy_platform/PlasmicNavigationPopupModals.tsx", "importSpec": {"modulePath": "NavigationPopupModals.tsx"}, "cssFilePath": "autogenerated/senergy_platform/PlasmicNavigationPopupModals.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "0PT5smaq5Bm6", "name": "MainLayout", "type": "managed", "projectId": "nZeWKcn21KijvC7eT8B3c7", "renderModuleFilePath": "autogenerated/senergy_platform/PlasmicMainLayout.tsx", "importSpec": {"modulePath": "MainLayout.tsx"}, "cssFilePath": "autogenerated/senergy_platform/PlasmicMainLayout.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "Pt5FPuEzirSe", "name": "SubcomponentTextInput", "type": "managed", "projectId": "nZeWKcn21KijvC7eT8B3c7", "renderModuleFilePath": "autogenerated/senergy_platform/PlasmicSubcomponentTextInput.tsx", "importSpec": {"modulePath": "SubcomponentTextInput.tsx"}, "cssFilePath": "autogenerated/senergy_platform/PlasmicSubcomponentTextInput.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "_eFlbiSm6hZU", "name": "SubcomponentIconWithText", "type": "managed", "projectId": "nZeWKcn21KijvC7eT8B3c7", "renderModuleFilePath": "autogenerated/senergy_platform/PlasmicSubcomponentIconWithText.tsx", "importSpec": {"modulePath": "SubcomponentIconWithText.tsx"}, "cssFilePath": "autogenerated/senergy_platform/PlasmicSubcomponentIconWithText.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "9HLEjheuNhYo", "name": "ProfileCoreButtonForProfileNavigation", "type": "managed", "projectId": "nZeWKcn21KijvC7eT8B3c7", "renderModuleFilePath": "autogenerated/senergy_platform/PlasmicProfileCoreButtonForProfileNavigation.tsx", "importSpec": {"modulePath": "ProfileCoreButtonForProfileNavigation.tsx"}, "cssFilePath": "autogenerated/senergy_platform/PlasmicProfileCoreButtonForProfileNavigation.module.css", "scheme": "blackbox", "componentType": "component", "plumeType": "button"}, {"id": "ld24XSt22oQ7", "name": "ProfileTileCertificationTile", "type": "managed", "projectId": "nZeWKcn21KijvC7eT8B3c7", "renderModuleFilePath": "autogenerated/senergy_platform/PlasmicProfileTileCertificationTile.tsx", "importSpec": {"modulePath": "ProfileTileCertificationTile.tsx"}, "cssFilePath": "autogenerated/senergy_platform/PlasmicProfileTileCertificationTile.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "NlwHt2k50uIY", "name": "ProfileTileLicenseTile", "type": "managed", "projectId": "nZeWKcn21KijvC7eT8B3c7", "renderModuleFilePath": "autogenerated/senergy_platform/PlasmicProfileTileLicenseTile.tsx", "importSpec": {"modulePath": "ProfileTileLicenseTile.tsx"}, "cssFilePath": "autogenerated/senergy_platform/PlasmicProfileTileLicenseTile.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "R0Gfd7-8fcvf", "name": "ProfileTilePatentTile", "type": "managed", "projectId": "nZeWKcn21KijvC7eT8B3c7", "renderModuleFilePath": "autogenerated/senergy_platform/PlasmicProfileTilePatentTile.tsx", "importSpec": {"modulePath": "ProfileTilePatentTile.tsx"}, "cssFilePath": "autogenerated/senergy_platform/PlasmicProfileTilePatentTile.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "G5tbmo_PlO9M", "name": "ProfileTileTrademarkTile", "type": "managed", "projectId": "nZeWKcn21KijvC7eT8B3c7", "renderModuleFilePath": "autogenerated/senergy_platform/PlasmicProfileTileTrademarkTile.tsx", "importSpec": {"modulePath": "ProfileTileTrademarkTile.tsx"}, "cssFilePath": "autogenerated/senergy_platform/PlasmicProfileTileTrademarkTile.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "kKAHWOIWX-5u", "name": "ProfileTilePublicationTile", "type": "managed", "projectId": "nZeWKcn21KijvC7eT8B3c7", "renderModuleFilePath": "autogenerated/senergy_platform/PlasmicProfileTilePublicationTile.tsx", "importSpec": {"modulePath": "ProfileTilePublicationTile.tsx"}, "cssFilePath": "autogenerated/senergy_platform/PlasmicProfileTilePublicationTile.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "HKIN54Ptec2v", "name": "ProfileTileExperiencesTile", "type": "managed", "projectId": "nZeWKcn21KijvC7eT8B3c7", "renderModuleFilePath": "autogenerated/senergy_platform/PlasmicProfileTileExperiencesTile.tsx", "importSpec": {"modulePath": "ProfileTileExperiencesTile.tsx"}, "cssFilePath": "autogenerated/senergy_platform/PlasmicProfileTileExperiencesTile.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "FPZd4ZDDgKTk", "name": "SubcomponentSelectorButtonsWSlot", "type": "managed", "projectId": "nZeWKcn21KijvC7eT8B3c7", "renderModuleFilePath": "autogenerated/senergy_platform/PlasmicSubcomponentSelectorButtonsWSlot.tsx", "importSpec": {"modulePath": "SubcomponentSelectorButtonsWSlot.tsx"}, "cssFilePath": "autogenerated/senergy_platform/PlasmicSubcomponentSelectorButtonsWSlot.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "JPWHSAS340Sf", "name": "SubcomponentIconButton", "type": "managed", "projectId": "nZeWKcn21KijvC7eT8B3c7", "renderModuleFilePath": "autogenerated/senergy_platform/PlasmicSubcomponentIconButton.tsx", "importSpec": {"modulePath": "SubcomponentIconButton.tsx"}, "cssFilePath": "autogenerated/senergy_platform/PlasmicSubcomponentIconButton.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "hyPjg5023lCQ", "name": "SubcomponentLogOutButton", "type": "managed", "projectId": "nZeWKcn21KijvC7eT8B3c7", "renderModuleFilePath": "autogenerated/senergy_platform/PlasmicSubcomponentLogOutButton.tsx", "importSpec": {"modulePath": "SubcomponentLogOutButton.tsx"}, "cssFilePath": "autogenerated/senergy_platform/PlasmicSubcomponentLogOutButton.module.css", "scheme": "blackbox", "componentType": "component", "plumeType": "button"}, {"id": "LmgcGPBzVGFy", "name": "SubcomponentLinkPreview", "type": "managed", "projectId": "nZeWKcn21KijvC7eT8B3c7", "renderModuleFilePath": "autogenerated/senergy_platform/PlasmicSubcomponentLinkPreview.tsx", "importSpec": {"modulePath": "SubcomponentLinkPreview.tsx"}, "cssFilePath": "autogenerated/senergy_platform/PlasmicSubcomponentLinkPreview.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "XngpIn4SLbDb", "name": "ProfileSectionsUnderConstructionDisplay", "type": "managed", "projectId": "nZeWKcn21KijvC7eT8B3c7", "renderModuleFilePath": "autogenerated/senergy_platform/PlasmicProfileSectionsUnderConstructionDisplay.tsx", "importSpec": {"modulePath": "ProfileSectionsUnderConstructionDisplay.tsx"}, "cssFilePath": "autogenerated/senergy_platform/PlasmicProfileSectionsUnderConstructionDisplay.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "8ZiaPQ4apvBM", "name": "NavigationLogoStack", "type": "managed", "projectId": "nZeWKcn21KijvC7eT8B3c7", "renderModuleFilePath": "autogenerated/senergy_platform/PlasmicNavigationLogoStack.tsx", "importSpec": {"modulePath": "NavigationLogoStack.tsx"}, "cssFilePath": "autogenerated/senergy_platform/PlasmicNavigationLogoStack.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "MDX1x1PMdyNM", "name": "SubcomponentChevrons", "type": "managed", "projectId": "nZeWKcn21KijvC7eT8B3c7", "renderModuleFilePath": "autogenerated/senergy_platform/PlasmicSubcomponentChevrons.tsx", "importSpec": {"modulePath": "SubcomponentChevrons.tsx"}, "cssFilePath": "autogenerated/senergy_platform/PlasmicSubcomponentChevrons.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "LXE5jCWhvLgf", "name": "ProfileAboutSectionSelector", "type": "managed", "projectId": "nZeWKcn21KijvC7eT8B3c7", "renderModuleFilePath": "autogenerated/senergy_platform/PlasmicProfileAboutSectionSelector.tsx", "importSpec": {"modulePath": "ProfileAboutSectionSelector.tsx"}, "cssFilePath": "autogenerated/senergy_platform/PlasmicProfileAboutSectionSelector.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "xb6rsawcP4mG", "name": "OnboardingProfileSectionModule", "type": "managed", "projectId": "nZeWKcn21KijvC7eT8B3c7", "renderModuleFilePath": "autogenerated/senergy_platform/PlasmicOnboardingProfileSectionModule.tsx", "importSpec": {"modulePath": "OnboardingProfileSectionModule.tsx"}, "cssFilePath": "autogenerated/senergy_platform/PlasmicOnboardingProfileSectionModule.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "q4dIf6ELFdYO", "name": "SubcomponentUploadButton", "type": "managed", "projectId": "nZeWKcn21KijvC7eT8B3c7", "renderModuleFilePath": "autogenerated/senergy_platform/PlasmicSubcomponentUploadButton.tsx", "importSpec": {"modulePath": "SubcomponentUploadButton.tsx"}, "cssFilePath": "autogenerated/senergy_platform/PlasmicSubcomponentUploadButton.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "Jnr7Ch5x507b", "name": "NavigationActiveSlotSlider", "type": "managed", "projectId": "nZeWKcn21KijvC7eT8B3c7", "renderModuleFilePath": "autogenerated/senergy_platform/PlasmicNavigationActiveSlotSlider.tsx", "importSpec": {"modulePath": "NavigationActiveSlotSlider.tsx"}, "cssFilePath": "autogenerated/senergy_platform/PlasmicNavigationActiveSlotSlider.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "7r9yyvza21zd", "name": "NavigationHoverSlotSlider", "type": "managed", "projectId": "nZeWKcn21KijvC7eT8B3c7", "renderModuleFilePath": "autogenerated/senergy_platform/PlasmicNavigationHoverSlotSlider.tsx", "importSpec": {"modulePath": "NavigationHoverSlotSlider.tsx"}, "cssFilePath": "autogenerated/senergy_platform/PlasmicNavigationHoverSlotSlider.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "oWulBe4jXB2e", "name": "FeedbackFeedbackModal", "type": "managed", "projectId": "nZeWKcn21KijvC7eT8B3c7", "renderModuleFilePath": "autogenerated/senergy_platform/PlasmicFeedbackFeedbackModal.tsx", "importSpec": {"modulePath": "FeedbackFeedbackModal.tsx"}, "cssFilePath": "autogenerated/senergy_platform/PlasmicFeedbackFeedbackModal.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "9xrOS05wbunZ", "name": "OnboardingCompletionPrompt", "type": "managed", "projectId": "nZeWKcn21KijvC7eT8B3c7", "renderModuleFilePath": "autogenerated/senergy_platform/PlasmicOnboardingCompletionPrompt.tsx", "importSpec": {"modulePath": "OnboardingCompletionPrompt.tsx"}, "cssFilePath": "autogenerated/senergy_platform/PlasmicOnboardingCompletionPrompt.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "wf5-zG-i1Jzo", "name": "ProfileOverviewOverviewBlocks", "type": "managed", "projectId": "nZeWKcn21KijvC7eT8B3c7", "renderModuleFilePath": "autogenerated/senergy_platform/PlasmicProfileOverviewOverviewBlocks.tsx", "importSpec": {"modulePath": "ProfileOverviewOverviewBlocks.tsx"}, "cssFilePath": "autogenerated/senergy_platform/PlasmicProfileOverviewOverviewBlocks.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "n9af0JVJyMFI", "name": "ProfileSectionsPatentsBlock", "type": "managed", "projectId": "nZeWKcn21KijvC7eT8B3c7", "renderModuleFilePath": "autogenerated/senergy_platform/PlasmicProfileSectionsPatentsBlock.tsx", "importSpec": {"modulePath": "ProfileSectionsPatentsBlock.tsx"}, "cssFilePath": "autogenerated/senergy_platform/PlasmicProfileSectionsPatentsBlock.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "VoKRKQ4gT9bR", "name": "ProfileSectionsTrademarksBlock", "type": "managed", "projectId": "nZeWKcn21KijvC7eT8B3c7", "renderModuleFilePath": "autogenerated/senergy_platform/PlasmicProfileSectionsTrademarksBlock.tsx", "importSpec": {"modulePath": "ProfileSectionsTrademarksBlock.tsx"}, "cssFilePath": "autogenerated/senergy_platform/PlasmicProfileSectionsTrademarksBlock.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "5i3SEiGXS3YW", "name": "ProfileSectionsLicensesBlock", "type": "managed", "projectId": "nZeWKcn21KijvC7eT8B3c7", "renderModuleFilePath": "autogenerated/senergy_platform/PlasmicProfileSectionsLicensesBlock.tsx", "importSpec": {"modulePath": "ProfileSectionsLicensesBlock.tsx"}, "cssFilePath": "autogenerated/senergy_platform/PlasmicProfileSectionsLicensesBlock.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "iCin3UKpQ4Hq", "name": "ProfileSectionsCertificationsBlock", "type": "managed", "projectId": "nZeWKcn21KijvC7eT8B3c7", "renderModuleFilePath": "autogenerated/senergy_platform/PlasmicProfileSectionsCertificationsBlock.tsx", "importSpec": {"modulePath": "ProfileSectionsCertificationsBlock.tsx"}, "cssFilePath": "autogenerated/senergy_platform/PlasmicProfileSectionsCertificationsBlock.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "X8jYPMQOFST0", "name": "ProfileOverviewOverviewContentBlock2", "type": "managed", "projectId": "nZeWKcn21KijvC7eT8B3c7", "renderModuleFilePath": "autogenerated/senergy_platform/PlasmicProfileOverviewOverviewContentBlock2.tsx", "importSpec": {"modulePath": "ProfileOverviewOverviewContentBlock2.tsx"}, "cssFilePath": "autogenerated/senergy_platform/PlasmicProfileOverviewOverviewContentBlock2.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "eb9GZKRV2I9e", "name": "SubcomponentDropdownSelector", "type": "managed", "projectId": "nZeWKcn21KijvC7eT8B3c7", "renderModuleFilePath": "autogenerated/senergy_platform/PlasmicSubcomponentDropdownSelector.tsx", "importSpec": {"modulePath": "SubcomponentDropdownSelector.tsx"}, "cssFilePath": "autogenerated/senergy_platform/PlasmicSubcomponentDropdownSelector.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "p6_6lEP1C3cJ", "name": "ProfileOverview", "type": "managed", "projectId": "nZeWKcn21KijvC7eT8B3c7", "renderModuleFilePath": "autogenerated/senergy_platform/PlasmicProfileOverview.tsx", "importSpec": {"modulePath": "ProfileOverview.tsx"}, "cssFilePath": "autogenerated/senergy_platform/PlasmicProfileOverview.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "GFYS_XLCIH0W", "name": "ProfileAbout", "type": "managed", "projectId": "nZeWKcn21KijvC7eT8B3c7", "renderModuleFilePath": "autogenerated/senergy_platform/PlasmicProfileAbout.tsx", "importSpec": {"modulePath": "ProfileAbout.tsx"}, "cssFilePath": "autogenerated/senergy_platform/PlasmicProfileAbout.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "LK4I3Kl7_-vt", "name": "ProfileMyWorks", "type": "managed", "projectId": "nZeWKcn21KijvC7eT8B3c7", "renderModuleFilePath": "autogenerated/senergy_platform/PlasmicProfileMyWorks.tsx", "importSpec": {"modulePath": "ProfileMyWorks.tsx"}, "cssFilePath": "autogenerated/senergy_platform/PlasmicProfileMyWorks.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "oCnNzqTJcCfl", "name": "ProfilePageLayout", "type": "managed", "projectId": "nZeWKcn21KijvC7eT8B3c7", "renderModuleFilePath": "autogenerated/senergy_platform/PlasmicProfilePageLayout.tsx", "importSpec": {"modulePath": "ProfilePageLayout.tsx"}, "cssFilePath": "autogenerated/senergy_platform/PlasmicProfilePageLayout.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "N6mlKPJGFxDf", "name": "WipLanguagesEditorModule", "type": "managed", "projectId": "nZeWKcn21KijvC7eT8B3c7", "renderModuleFilePath": "autogenerated/senergy_platform/PlasmicWipLanguagesEditorModule.tsx", "importSpec": {"modulePath": "WipLanguagesEditorModule.tsx"}, "cssFilePath": "autogenerated/senergy_platform/PlasmicWipLanguagesEditorModule.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "x5ySv6UMingi", "name": "SubcomponentTextInput2", "type": "managed", "projectId": "nZeWKcn21KijvC7eT8B3c7", "renderModuleFilePath": "autogenerated/senergy_platform/PlasmicSubcomponentTextInput2.tsx", "importSpec": {"modulePath": "SubcomponentTextInput2.tsx"}, "cssFilePath": "autogenerated/senergy_platform/PlasmicSubcomponentTextInput2.module.css", "scheme": "blackbox", "componentType": "component", "plumeType": "text-input"}, {"id": "QCVznhFR7DuX", "name": "NavigationButtonGroup", "type": "managed", "projectId": "nZeWKcn21KijvC7eT8B3c7", "renderModuleFilePath": "autogenerated/senergy_platform/PlasmicNavigationButtonGroup.tsx", "importSpec": {"modulePath": "NavigationButtonGroup.tsx"}, "cssFilePath": "autogenerated/senergy_platform/PlasmicNavigationButtonGroup.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "MY4cHaJaVAVx", "name": "LogoStack", "type": "managed", "projectId": "nZeWKcn21KijvC7eT8B3c7", "renderModuleFilePath": "autogenerated/senergy_platform/PlasmicLogoStack.tsx", "importSpec": {"modulePath": "LogoStack.tsx"}, "cssFilePath": "autogenerated/senergy_platform/PlasmicLogoStack.module.css", "scheme": "blackbox", "componentType": "component"}], "icons": [{"id": "IWHvfvi8cOL9", "name": "VerificationIcon", "moduleFilePath": "autogenerated/senergy_platform/icons/PlasmicIcon__Verification.tsx"}, {"id": "4U-HfnBQfBTq", "name": "GlobeIcon", "moduleFilePath": "autogenerated/senergy_platform/icons/PlasmicIcon__Globe.tsx"}, {"id": "kJJX9PdsKpcg", "name": "CoffeeMugIcon", "moduleFilePath": "autogenerated/senergy_platform/icons/PlasmicIcon__CoffeeMug.tsx"}, {"id": "01ij0lND9yQw", "name": "LanguagIcon", "moduleFilePath": "autogenerated/senergy_platform/icons/PlasmicIcon__Languag.tsx"}, {"id": "hosM0D-aZ2A1", "name": "SearchIcon", "moduleFilePath": "autogenerated/senergy_platform/icons/PlasmicIcon__Search.tsx"}, {"id": "CaAupFMSbhPP", "name": "BookmarkEmptyIcon", "moduleFilePath": "autogenerated/senergy_platform/icons/PlasmicIcon__BookmarkEmpty.tsx"}, {"id": "mYm5L1LPvJxL", "name": "BookmarkFilledIcon", "moduleFilePath": "autogenerated/senergy_platform/icons/PlasmicIcon__BookmarkFilled.tsx"}, {"id": "PjKgIKB6-HKl", "name": "MessagesIcon", "moduleFilePath": "autogenerated/senergy_platform/icons/PlasmicIcon__Messages.tsx"}, {"id": "hcR42vt5qkrz", "name": "UserIcon", "moduleFilePath": "autogenerated/senergy_platform/icons/PlasmicIcon__User.tsx"}, {"id": "3QH7fIS7m04A", "name": "SettingsIcon", "moduleFilePath": "autogenerated/senergy_platform/icons/PlasmicIcon__Settings.tsx"}, {"id": "3DUdjU3Or2Rw", "name": "InfoIcon", "moduleFilePath": "autogenerated/senergy_platform/icons/PlasmicIcon__Info.tsx"}, {"id": "gCJGgC3_7lN-", "name": "InfoGraphicIcon", "moduleFilePath": "autogenerated/senergy_platform/icons/PlasmicIcon__InfoGraphic.tsx"}, {"id": "UeR0cNpxaAkf", "name": "FileOptionsIcon", "moduleFilePath": "autogenerated/senergy_platform/icons/PlasmicIcon__FileOptions.tsx"}, {"id": "8rjY-nsmb3pJ", "name": "FileInfoIcon", "moduleFilePath": "autogenerated/senergy_platform/icons/PlasmicIcon__FileInfo.tsx"}, {"id": "0HEABec35TzS", "name": "PhotoCheckIcon", "moduleFilePath": "autogenerated/senergy_platform/icons/PlasmicIcon__PhotoCheck.tsx"}, {"id": "Tzwp7spjytX-", "name": "PhotoEditIcon", "moduleFilePath": "autogenerated/senergy_platform/icons/PlasmicIcon__PhotoEdit.tsx"}, {"id": "9oJCPjtStkkM", "name": "PhotoDeleteIcon", "moduleFilePath": "autogenerated/senergy_platform/icons/PlasmicIcon__PhotoDelete.tsx"}, {"id": "84-ui_oh3KvJ", "name": "PhotoUploadIcon", "moduleFilePath": "autogenerated/senergy_platform/icons/PlasmicIcon__PhotoUpload.tsx"}, {"id": "mMHpRaI0f1ug", "name": "PhotoIcon", "moduleFilePath": "autogenerated/senergy_platform/icons/PlasmicIcon__Photo.tsx"}, {"id": "O8tWPRH1GIxU", "name": "EditIcon", "moduleFilePath": "autogenerated/senergy_platform/icons/PlasmicIcon__Edit.tsx"}, {"id": "63gk7-p6lhAv", "name": "ClockIcon", "moduleFilePath": "autogenerated/senergy_platform/icons/PlasmicIcon__Clock.tsx"}, {"id": "Ra-yugMEa7AY", "name": "CalendarExpirationIcon", "moduleFilePath": "autogenerated/senergy_platform/icons/PlasmicIcon__CalendarExpiration.tsx"}, {"id": "-xP51745fXLc", "name": "CalendarIcon", "moduleFilePath": "autogenerated/senergy_platform/icons/PlasmicIcon__Calendar.tsx"}, {"id": "AA2-KUsid802", "name": "RegisteredIcon", "moduleFilePath": "autogenerated/senergy_platform/icons/PlasmicIcon__Registered.tsx"}, {"id": "7fTFQT6QY_o8", "name": "HashtagIcon", "moduleFilePath": "autogenerated/senergy_platform/icons/PlasmicIcon__Hashtag.tsx"}, {"id": "sSdobEjCBpaW", "name": "CurrentLocationIcon", "moduleFilePath": "autogenerated/senergy_platform/icons/PlasmicIcon__CurrentLocation.tsx"}, {"id": "2X2dwcZjjUKR", "name": "MapIcon", "moduleFilePath": "autogenerated/senergy_platform/icons/PlasmicIcon__Map.tsx"}, {"id": "Q_wDLzMm5l2Y", "name": "MapPinIcon", "moduleFilePath": "autogenerated/senergy_platform/icons/PlasmicIcon__MapPin.tsx"}, {"id": "i9bbaFOlpQVC", "name": "GradCapIcon", "moduleFilePath": "autogenerated/senergy_platform/icons/PlasmicIcon__GradCap.tsx"}, {"id": "V2fj2y1HdsrP", "name": "SchoolBellIcon", "moduleFilePath": "autogenerated/senergy_platform/icons/PlasmicIcon__SchoolBell.tsx"}, {"id": "RuQrxOj-BhnB", "name": "BuildingIcon", "moduleFilePath": "autogenerated/senergy_platform/icons/PlasmicIcon__Building.tsx"}, {"id": "oMeblj4iBOSs", "name": "CertificateIcon", "moduleFilePath": "autogenerated/senergy_platform/icons/PlasmicIcon__Certificate.tsx"}, {"id": "TJR3b86TU-Do", "name": "TrademarkIcon", "moduleFilePath": "autogenerated/senergy_platform/icons/PlasmicIcon__Trademark.tsx"}, {"id": "W353ZMHbwyfZ", "name": "ExperienceOfficeIcon", "moduleFilePath": "autogenerated/senergy_platform/icons/PlasmicIcon__ExperienceOffice.tsx"}, {"id": "8BfErdfvqP2t", "name": "ContractTypeIcon", "moduleFilePath": "autogenerated/senergy_platform/icons/PlasmicIcon__ContractType.tsx"}, {"id": "GsLUL8ZP9xQL", "name": "DownloadIcon", "moduleFilePath": "autogenerated/senergy_platform/icons/PlasmicIcon__Download.tsx"}, {"id": "rFA5-L_-tbal", "name": "PlaceholderIcon", "moduleFilePath": "autogenerated/senergy_platform/icons/PlasmicIcon__Placeholder.tsx"}, {"id": "TfTuYHhdLsP9", "name": "PlaceholderCircleIcon", "moduleFilePath": "autogenerated/senergy_platform/icons/PlasmicIcon__PlaceholderCircle.tsx"}, {"id": "Pj3AHjkjxrhS", "name": "PodiumPresentationIcon", "moduleFilePath": "autogenerated/senergy_platform/icons/PlasmicIcon__PodiumPresentation.tsx"}, {"id": "5Mfu2p9hEVrd", "name": "CallIcon", "moduleFilePath": "autogenerated/senergy_platform/icons/PlasmicIcon__Call.tsx"}, {"id": "UrjtEH_cdeeJ", "name": "DeskIcon", "moduleFilePath": "autogenerated/senergy_platform/icons/PlasmicIcon__Desk.tsx"}, {"id": "E4KjqrsC_pyT", "name": "BackpackIcon", "moduleFilePath": "autogenerated/senergy_platform/icons/PlasmicIcon__Backpack.tsx"}, {"id": "NifquClTOvsN", "name": "PendingIcon", "moduleFilePath": "autogenerated/senergy_platform/icons/PlasmicIcon__Pending.tsx"}, {"id": "ikewbQ1hZ9qn", "name": "AbandonedIcon", "moduleFilePath": "autogenerated/senergy_platform/icons/PlasmicIcon__Abandoned.tsx"}, {"id": "0zRPSen3c0Ql", "name": "PatentedIcon", "moduleFilePath": "autogenerated/senergy_platform/icons/PlasmicIcon__Patented.tsx"}, {"id": "7cetWB1_lXa5", "name": "PatentPendingIcon", "moduleFilePath": "autogenerated/senergy_platform/icons/PlasmicIcon__PatentPending.tsx"}, {"id": "cxogJbe-XADD", "name": "NoteIcon", "moduleFilePath": "autogenerated/senergy_platform/icons/PlasmicIcon__Note.tsx"}, {"id": "e4d22TnM8LDb", "name": "DesignArtBoardIcon", "moduleFilePath": "autogenerated/senergy_platform/icons/PlasmicIcon__DesignArtBoard.tsx"}, {"id": "3SU3Z2EeP247", "name": "UtilitiesIcon", "moduleFilePath": "autogenerated/senergy_platform/icons/PlasmicIcon__Utilities.tsx"}, {"id": "5N-JONxXLEz9", "name": "PlantIcon", "moduleFilePath": "autogenerated/senergy_platform/icons/PlasmicIcon__Plant.tsx"}, {"id": "j-ct0zISt5hl", "name": "LicenseIcon", "moduleFilePath": "autogenerated/senergy_platform/icons/PlasmicIcon__License.tsx"}, {"id": "oMu8DbYA988n", "name": "IdIcon", "moduleFilePath": "autogenerated/senergy_platform/icons/PlasmicIcon__Id.tsx"}, {"id": "BXxYsiTdXJr_", "name": "AwardIcon", "moduleFilePath": "autogenerated/senergy_platform/icons/PlasmicIcon__Award.tsx"}, {"id": "q_kchLaCTXkl", "name": "WorldIcon", "moduleFilePath": "autogenerated/senergy_platform/icons/PlasmicIcon__World.tsx"}, {"id": "3yiBdPB4DXzP", "name": "SkillsIcon", "moduleFilePath": "autogenerated/senergy_platform/icons/PlasmicIcon__Skills.tsx"}, {"id": "AHRVoCS9vdiJ", "name": "ToolsIcon", "moduleFilePath": "autogenerated/senergy_platform/icons/PlasmicIcon__Tools.tsx"}, {"id": "vkhXQ8JxL2wu", "name": "SupportHelpIcon", "moduleFilePath": "autogenerated/senergy_platform/icons/PlasmicIcon__SupportHelp.tsx"}, {"id": "1vlHySSOKWkP", "name": "PublicationBookIcon", "moduleFilePath": "autogenerated/senergy_platform/icons/PlasmicIcon__PublicationBook.tsx"}, {"id": "OR6AbF6nYdAn", "name": "PublicationTypeIcon", "moduleFilePath": "autogenerated/senergy_platform/icons/PlasmicIcon__PublicationType.tsx"}, {"id": "Y7CGwGvldN-s", "name": "GenreIcon", "moduleFilePath": "autogenerated/senergy_platform/icons/PlasmicIcon__Genre.tsx"}, {"id": "P_52Tj-4F1hZ", "name": "IsbnIssnIcon", "moduleFilePath": "autogenerated/senergy_platform/icons/PlasmicIcon__IsbnIssn.tsx"}, {"id": "o8F8SDzVakKs", "name": "PersonPlusAdditionalAuthorsIcon", "moduleFilePath": "autogenerated/senergy_platform/icons/PlasmicIcon__PersonPlusAdditionalAuthors.tsx"}, {"id": "9aghGjj-Yn0f", "name": "PlusIcon", "moduleFilePath": "autogenerated/senergy_platform/icons/PlasmicIcon__Plus.tsx"}, {"id": "-YxnRXLJIlYW", "name": "MinusIcon", "moduleFilePath": "autogenerated/senergy_platform/icons/PlasmicIcon__Minus.tsx"}, {"id": "deLi5PaFbuAg", "name": "XIcon", "moduleFilePath": "autogenerated/senergy_platform/icons/PlasmicIcon__X.tsx"}, {"id": "e6s2KgA0Bmlu", "name": "ChevronUpIcon", "moduleFilePath": "autogenerated/senergy_platform/icons/PlasmicIcon__ChevronUp.tsx"}, {"id": "UW2ddwYm4xaW", "name": "ChevronDownIcon", "moduleFilePath": "autogenerated/senergy_platform/icons/PlasmicIcon__ChevronDown.tsx"}, {"id": "IE1Jvgi--DTA", "name": "ChevronRightIcon", "moduleFilePath": "autogenerated/senergy_platform/icons/PlasmicIcon__ChevronRight.tsx"}, {"id": "Xs7YR_mpBuGG", "name": "ChevronLeftIcon", "moduleFilePath": "autogenerated/senergy_platform/icons/PlasmicIcon__ChevronLeft.tsx"}, {"id": "wT9fUmmwCdGN", "name": "ArrowsSortIcon", "moduleFilePath": "autogenerated/senergy_platform/icons/PlasmicIcon__ArrowsSort.tsx"}, {"id": "fiAoih5NT_8w", "name": "ArrowDownIcon", "moduleFilePath": "autogenerated/senergy_platform/icons/PlasmicIcon__ArrowDown.tsx"}, {"id": "iTBXxnWeIvpt", "name": "IconIcon", "moduleFilePath": "autogenerated/senergy_platform/icons/PlasmicIcon__Icon.tsx"}, {"id": "ag12qSR0XmCJ", "name": "ArrowUpIcon", "moduleFilePath": "autogenerated/senergy_platform/icons/PlasmicIcon__ArrowUp.tsx"}, {"id": "0GbuOQuYcSoq", "name": "ArrowRightIcon", "moduleFilePath": "autogenerated/senergy_platform/icons/PlasmicIcon__ArrowRight.tsx"}, {"id": "-dA0VFzNmMvB", "name": "ArrowLeftIcon", "moduleFilePath": "autogenerated/senergy_platform/icons/PlasmicIcon__ArrowLeft.tsx"}, {"id": "Wky401I3Udms", "name": "CautionIcon", "moduleFilePath": "autogenerated/senergy_platform/icons/PlasmicIcon__Caution.tsx"}, {"id": "fUSEBkvP3I3E", "name": "AlertIcon", "moduleFilePath": "autogenerated/senergy_platform/icons/PlasmicIcon__Alert.tsx"}, {"id": "fGdp1-n78qAE", "name": "AlertFilledIcon", "moduleFilePath": "autogenerated/senergy_platform/icons/PlasmicIcon__AlertFilled.tsx"}, {"id": "yQrSS5Om6kat", "name": "LoadingIcon", "moduleFilePath": "autogenerated/senergy_platform/icons/PlasmicIcon__Loading.tsx"}, {"id": "jV9p1xFAB-68", "name": "DashboardIcon", "moduleFilePath": "autogenerated/senergy_platform/icons/PlasmicIcon__Dashboard.tsx"}, {"id": "Y5Klmp_AFjSn", "name": "HeadphonesIcon", "moduleFilePath": "autogenerated/senergy_platform/icons/PlasmicIcon__Headphones.tsx"}, {"id": "dx0WIzPw9kwy", "name": "PlayButtonIcon", "moduleFilePath": "autogenerated/senergy_platform/icons/PlasmicIcon__PlayButton.tsx"}, {"id": "uXqFzwXwnb0C", "name": "CameraIcon", "moduleFilePath": "autogenerated/senergy_platform/icons/PlasmicIcon__Camera.tsx"}, {"id": "SmNxaoWtdFLr", "name": "VideoIcon", "moduleFilePath": "autogenerated/senergy_platform/icons/PlasmicIcon__Video.tsx"}, {"id": "g-LJdPral85e", "name": "LinkIcon", "moduleFilePath": "autogenerated/senergy_platform/icons/PlasmicIcon__Link.tsx"}, {"id": "VeGBlXIH6Sji", "name": "ToggleBoxIcon", "moduleFilePath": "autogenerated/senergy_platform/icons/PlasmicIcon__ToggleBox.tsx"}, {"id": "oRrgirT7F-o0", "name": "Icon2Icon", "moduleFilePath": "autogenerated/senergy_platform/icons/PlasmicIcon__Icon2.tsx"}, {"id": "HysHdcf64O3p", "name": "Icon3Icon", "moduleFilePath": "autogenerated/senergy_platform/icons/PlasmicIcon__Icon3.tsx"}, {"id": "L0D3mLblRaX0", "name": "Icon4Icon", "moduleFilePath": "autogenerated/senergy_platform/icons/PlasmicIcon__Icon4.tsx"}, {"id": "_Z1nsWZI7LH8", "name": "LoadingDotsIcon", "moduleFilePath": "autogenerated/senergy_platform/icons/PlasmicIcon__LoadingDots.tsx"}, {"id": "rj9bnAg3Gp9P", "name": "SunIcon", "moduleFilePath": "autogenerated/senergy_platform/icons/PlasmicIcon__Sun.tsx"}, {"id": "pZCm_WpNUCMd", "name": "MoonIcon", "moduleFilePath": "autogenerated/senergy_platform/icons/PlasmicIcon__Moon.tsx"}, {"id": "6Ni9R0Lyd72t", "name": "Icon5Icon", "moduleFilePath": "autogenerated/senergy_platform/icons/PlasmicIcon__Icon5.tsx"}, {"id": "jnSZ1lN8rKAR", "name": "Icon6Icon", "moduleFilePath": "autogenerated/senergy_platform/icons/PlasmicIcon__Icon6.tsx"}, {"id": "OPT4MICMQydN", "name": "ConstructionIcon", "moduleFilePath": "autogenerated/senergy_platform/icons/PlasmicIcon__Construction.tsx"}, {"id": "4brFoQLUD5pm", "name": "ConstructionCraneIcon", "moduleFilePath": "autogenerated/senergy_platform/icons/PlasmicIcon__ConstructionCrane.tsx"}, {"id": "rp7W-7bfKWRu", "name": "LogInIcon", "moduleFilePath": "autogenerated/senergy_platform/icons/PlasmicIcon__LogIn.tsx"}, {"id": "_hLK4Q26vGiT", "name": "LogOutIcon", "moduleFilePath": "autogenerated/senergy_platform/icons/PlasmicIcon__LogOut.tsx"}, {"id": "ZnRes5AmblwA", "name": "ExclamationIcon", "moduleFilePath": "autogenerated/senergy_platform/icons/PlasmicIcon__Exclamation.tsx"}, {"id": "rV0UWZ0cXJFP", "name": "MoreIcon", "moduleFilePath": "autogenerated/senergy_platform/icons/PlasmicIcon__More.tsx"}, {"id": "pjtzwEs6QILW", "name": "MenuIcon", "moduleFilePath": "autogenerated/senergy_platform/icons/PlasmicIcon__Menu.tsx"}, {"id": "4K0CYpsh7UAX", "name": "MenuDeepIcon", "moduleFilePath": "autogenerated/senergy_platform/icons/PlasmicIcon__MenuDeep.tsx"}, {"id": "2m49ojpkQhGO", "name": "AtIcon", "moduleFilePath": "autogenerated/senergy_platform/icons/PlasmicIcon__At.tsx"}, {"id": "hVrGYzCddhms", "name": "PaperClipFileIcon", "moduleFilePath": "autogenerated/senergy_platform/icons/PlasmicIcon__PaperClipFile.tsx"}, {"id": "osM7S8sYo-QG", "name": "SendPaperPlaneIcon", "moduleFilePath": "autogenerated/senergy_platform/icons/PlasmicIcon__SendPaperPlane.tsx"}, {"id": "TBmN1RDMqt46", "name": "BarrierIcon", "moduleFilePath": "autogenerated/senergy_platform/icons/PlasmicIcon__Barrier.tsx"}, {"id": "3fHP2U7UmHxq", "name": "CheckInBoxIcon", "moduleFilePath": "autogenerated/senergy_platform/icons/PlasmicIcon__CheckInBox.tsx"}, {"id": "uoBC83hY783T", "name": "CheckmarkIcon", "moduleFilePath": "autogenerated/senergy_platform/icons/PlasmicIcon__Checkmark.tsx"}, {"id": "7WkmBwOD3bCe", "name": "CompassIcon", "moduleFilePath": "autogenerated/senergy_platform/icons/PlasmicIcon__Compass.tsx"}, {"id": "udZhM66I4W3u", "name": "KeyboardIcon", "moduleFilePath": "autogenerated/senergy_platform/icons/PlasmicIcon__Keyboard.tsx"}, {"id": "Oz8WQKhDZZk4", "name": "ComputerIcon", "moduleFilePath": "autogenerated/senergy_platform/icons/PlasmicIcon__Computer.tsx"}, {"id": "GMFDqlfUwNBt", "name": "PenPlusIcon", "moduleFilePath": "autogenerated/senergy_platform/icons/PlasmicIcon__PenPlus.tsx"}, {"id": "o4-xGtH6_4AB", "name": "SearchSvgIcon", "moduleFilePath": "autogenerated/senergy_platform/icons/PlasmicIcon__SearchSvg.tsx"}, {"id": "OUHDvNzmrJwc", "name": "CircleIcon", "moduleFilePath": "autogenerated/senergy_platform/icons/PlasmicIcon__Circle.tsx"}, {"id": "HSNqfAMdYLo9", "name": "ChevronDown2Icon", "moduleFilePath": "autogenerated/senergy_platform/icons/PlasmicIcon__ChevronDown2.tsx"}], "images": [{"id": "aoncvqE4_NJ4", "name": "Image placeholder.png", "filePath": "../../../public/plasmic/senergy_platform/images/imagePlaceholderPng.png"}, {"id": "0DnHdDwZyBM0", "name": "Turtle Senergy.png", "filePath": "../../../public/plasmic/senergy_platform/images/turtleSenergyPng.png"}, {"id": "firHTIBJZIMh", "name": "Placeholder Logo SM.svg", "filePath": "../../../public/plasmic/senergy_platform/images/placeholderLogoSmSvg.svg"}, {"id": "RT3ySu2RMmxM", "name": "image", "filePath": "../../../public/plasmic/senergy_platform/images/image.gif"}, {"id": "5CNRCImZF3Yw", "name": "IMG_0745.jpeg", "filePath": "../../../public/plasmic/senergy_platform/images/img0745Jpeg.jpg"}, {"id": "SHEv1eL6hh2j", "name": "IMG_0500.jpeg", "filePath": "../../../public/plasmic/senergy_platform/images/img0500Jpeg.jpg"}, {"id": "LiamRvo3Fktk", "name": "IMG_5129.jpeg", "filePath": "../../../public/plasmic/senergy_platform/images/img5129Jpeg.jpg"}], "indirect": false, "globalContextsFilePath": "", "splitsProviderFilePath": "", "codeComponents": [{"id": "1ePdHAX8mDaS", "name": "PlasmicHead", "displayName": "hostless-plasmic-head", "componentImportPath": "@plasmicapp/react-web"}, {"id": "nTWyO8zPqetR", "name": "<PERSON><PERSON><PERSON>", "displayName": "plasmic-data-source-fetcher", "componentImportPath": "@plasmicapp/react-web/lib/data-sources"}, {"id": "PDxJ4QTeafge", "name": "FloatingImages", "displayName": "FloatingImages", "componentImportPath": "@/app/(custom)/(site)/components/FloatingImages"}, {"id": "bvxDDKE_lDZB", "name": "TypeIt", "displayName": "TypeIt", "componentImportPath": "@/components/plasmic/TypeItWrapper"}], "customFunctions": []}], "globalVariants": {"variantGroups": [{"id": "4Hrhi5G5aNwQ", "name": "FormattingBreakPoint", "projectId": "nZeWKcn21KijvC7eT8B3c7", "contextFilePath": "autogenerated/senergy_platform/PlasmicGlobalVariant__FormattingBreakPoint.tsx"}]}, "wrapPagesWithGlobalContexts": true, "nextjsConfig": {"pagesDir": "../../app"}, "cliVersion": "0.1.335", "$schema": "https://unpkg.com/@plasmicapp/cli@0.1.335/dist/plasmic.schema.json"}