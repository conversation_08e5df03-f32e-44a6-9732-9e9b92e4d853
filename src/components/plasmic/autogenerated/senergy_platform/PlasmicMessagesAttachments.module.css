.attachmentsContainer {
  display: inline-flex;
  flex-direction: row;
  position: relative;
  width: auto;
  height: auto;
  justify-content: flex-start;
  align-items: center;
  justify-self: flex-start;
  cursor: pointer;
  border-radius: 1px;
  padding: 12px 42px 12px 11px;
  border: 1px solid var(--token-p09LDPmbF81_);
}
.attachmentsContainerimage {
  display: flex;
  flex-direction: column;
  padding-right: 0px;
  padding-top: 0px;
  padding-left: 0px;
  max-width: 300px;
  border-style: none;
}
.attachmentsContainer:hover {
  padding-right: 0px;
}
.filetypeIcon {
  position: relative;
  object-fit: cover;
  max-width: 100%;
  width: 40px;
  height: 40px;
  flex-shrink: 0;
}
.filetypeIconimage {
  display: none;
}
.img {
  box-shadow: 0px 4px 4px 0px var(--token-p09LDPmbF81_);
  object-fit: contain;
  max-width: 350px;
  width: auto;
  height: auto;
  position: relative;
  max-height: 350px;
  min-width: 50px;
  min-height: 50px;
  flex-grow: 1;
  flex-shrink: 1;
  display: none !important;
}
.img > picture > img {
  object-fit: contain;
}
.imgimage {
  display: block !important;
}
.fileInformation {
  display: flex;
  position: relative;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start;
  width: 100%;
  height: auto;
  max-width: 100%;
  min-width: 0;
  padding: var(--token-sazGmnf7GWAk);
  margin: var(--token-sazGmnf7GWAk) 12px;
}
.text__ytpWw {
  width: 100%;
  height: auto;
  max-width: 100%;
  padding-left: 0px;
  min-width: 0;
}
.textimage__ytpWwqH9T6 {
  display: none;
}
.text___944W5 {
  width: 100%;
  height: auto;
  max-width: 100%;
  padding-left: 0px;
  color: var(--token-yPq8Z3hhDPZH);
  font-size: var(--token-agfFfkxgxH6d);
  padding-top: 4px;
  transition-property: all;
  transition-duration: 1s;
  transition-timing-function: ease-in-out;
  min-width: 0;
  -webkit-transition-property: all;
  -webkit-transition-timing-function: ease-in-out;
  -webkit-transition-duration: 1s;
}
.textimage___944W5QH9T6 {
  padding-left: 0px;
  padding-top: 4px;
}
.attachmentsContainer:hover .text___944W5 {
  padding-left: 0px;
  padding-top: 4px;
}
.downloadButtonPopUp {
  position: relative;
  width: auto;
  height: auto;
  flex-direction: row;
  margin-right: 12px;
  display: none;
  border-radius: 1px;
  padding: 4px;
  border: 1px solid var(--token-p09LDPmbF81_);
}
.downloadButtonPopUpimage {
  left: auto;
  top: 12px;
  z-index: 1;
  position: absolute;
  width: auto;
  right: 12px;
  margin-right: 0px;
  display: none;
}
.attachmentsContainer:hover .downloadButtonPopUp {
  display: flex;
}
.svg {
  position: relative;
  object-fit: cover;
  width: var(--token-j0qnbpah5w9U);
  height: var(--token-j0qnbpah5w9U);
  flex-shrink: 0;
}
