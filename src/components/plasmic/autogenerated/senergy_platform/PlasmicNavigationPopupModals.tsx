/* eslint-disable */
/* tslint:disable */
// @ts-nocheck
/* prettier-ignore-start */

/** @jsxRuntime classic */
/** @jsx createPlasmicElementProxy */
/** @jsxFrag React.Fragment */

// This class is auto-generated by Plasmic; please do not edit!
// Plasmic Project: nZeWKcn21KijvC7eT8B3c7
// Component: LXUgsJuF4wtn

"use client";

import * as React from "react";

import Head from "next/head";
import Link, { LinkProps } from "next/link";
import { useRouter } from "next/navigation";

import {
  Flex as Flex__,
  MultiChoiceArg,
  PlasmicDataSourceContextProvider as PlasmicDataSourceContextProvider__,
  PlasmicIcon as PlasmicIcon__,
  PlasmicImg as PlasmicImg__,
  PlasmicLink as PlasmicLink__,
  PlasmicPageGuard as PlasmicPageGuard__,
  SingleBooleanChoiceArg,
  SingleChoiceArg,
  Stack as Stack__,
  StrictProps,
  Trans as Trans__,
  classNames,
  createPlasmicElementProxy,
  deriveRenderOpts,
  ensureGlobalVariants,
  generateOnMutateForSpec,
  generateStateOnChangeProp,
  generateStateOnChangePropForCodeComponents,
  generateStateValueProp,
  get as $stateGet,
  hasVariant,
  initializeCodeComponentStates,
  initializePlasmicStates,
  makeFragment,
  omit,
  pick,
  renderPlasmicSlot,
  set as $stateSet,
  useCurrentUser,
  useDollarState,
  usePlasmicTranslator,
  useTrigger,
  wrapWithClassName
} from "@plasmicapp/react-web";
import {
  DataCtxReader as DataCtxReader__,
  useDataEnv,
  useGlobalActions
} from "@plasmicapp/host";

import MessagesMessagePreview from "../../MessagesMessagePreview"; // plasmic-import: iicTcR3JeMJ9/component
import BookmarksBookmarkTiles from "../../BookmarksBookmarkTiles"; // plasmic-import: _UuKQc4xy7Sd/component
import NavigationLegalButtons from "../../NavigationLegalButtons"; // plasmic-import: 4AlhyCYF3WHm/component

import "@plasmicapp/react-web/lib/plasmic.css";

import projectcss from "./plasmic.module.css"; // plasmic-import: nZeWKcn21KijvC7eT8B3c7/projectcss
import sty from "./PlasmicNavigationPopupModals.module.css"; // plasmic-import: LXUgsJuF4wtn/css

createPlasmicElementProxy;

export type PlasmicNavigationPopupModals__VariantMembers = {
  navBarModals: "messages" | "bookmarks" | "legal";
};
export type PlasmicNavigationPopupModals__VariantsArgs = {
  navBarModals?: SingleChoiceArg<"messages" | "bookmarks" | "legal">;
};
type VariantPropType = keyof PlasmicNavigationPopupModals__VariantsArgs;
export const PlasmicNavigationPopupModals__VariantProps =
  new Array<VariantPropType>("navBarModals");

export type PlasmicNavigationPopupModals__ArgsType = {
  messagesSlot2?: React.ReactNode;
  children?: React.ReactNode;
};
type ArgPropType = keyof PlasmicNavigationPopupModals__ArgsType;
export const PlasmicNavigationPopupModals__ArgProps = new Array<ArgPropType>(
  "messagesSlot2",
  "children"
);

export type PlasmicNavigationPopupModals__OverridesType = {
  formattingContainer?: Flex__<"div">;
  header?: Flex__<"section">;
  text?: Flex__<"div">;
  section?: Flex__<"section">;
  dynamicContent?: Flex__<"section">;
  messagesSlot?: Flex__<"section">;
  bookmarkSlot?: Flex__<"section">;
  legalStack?: Flex__<"div">;
  termsOfUse?: Flex__<typeof NavigationLegalButtons>;
  privacyPolicy?: Flex__<typeof NavigationLegalButtons>;
  careers?: Flex__<typeof NavigationLegalButtons>;
  contactUs?: Flex__<typeof NavigationLegalButtons>;
  logoutButton?: Flex__<typeof NavigationLegalButtons>;
};

export interface DefaultNavigationPopupModalsProps {
  messagesSlot2?: React.ReactNode;
  children?: React.ReactNode;
  navBarModals?: SingleChoiceArg<"messages" | "bookmarks" | "legal">;
  className?: string;
}

const $$ = {};

function useNextRouter() {
  try {
    return useRouter();
  } catch {}
  return undefined;
}

function PlasmicNavigationPopupModals__RenderFunc(props: {
  variants: PlasmicNavigationPopupModals__VariantsArgs;
  args: PlasmicNavigationPopupModals__ArgsType;
  overrides: PlasmicNavigationPopupModals__OverridesType;
  forNode?: string;
}) {
  const { variants, overrides, forNode } = props;

  const args = React.useMemo(
    () =>
      Object.assign(
        {},
        Object.fromEntries(
          Object.entries(props.args).filter(([_, v]) => v !== undefined)
        )
      ),
    [props.args]
  );

  const $props = {
    ...args,
    ...variants
  };

  const __nextRouter = useNextRouter();
  const $ctx = useDataEnv?.() || {};
  const refsRef = React.useRef({});
  const $refs = refsRef.current;

  let [$queries, setDollarQueries] = React.useState<
    Record<string, ReturnType<typeof usePlasmicDataOp>>
  >({});
  const stateSpecs: Parameters<typeof useDollarState>[0] = React.useMemo(
    () => [
      {
        path: "navBarModals",
        type: "private",
        variableType: "variant",
        initFunc: ({ $props, $state, $queries, $ctx }) => $props.navBarModals
      }
    ],
    [$props, $ctx, $refs]
  );
  const $state = useDollarState(stateSpecs, {
    $props,
    $ctx,
    $queries: $queries,
    $refs
  });

  const new$Queries: Record<string, ReturnType<typeof usePlasmicDataOp>> = {};
  if (Object.keys(new$Queries).some(k => new$Queries[k] !== $queries[k])) {
    setDollarQueries(new$Queries);

    $queries = new$Queries;
  }

  return (
    <div
      data-plasmic-name={"formattingContainer"}
      data-plasmic-override={overrides.formattingContainer}
      data-plasmic-root={true}
      data-plasmic-for-node={forNode}
      className={classNames(
        projectcss.all,
        projectcss.root_reset,
        projectcss.plasmic_default_styles,
        projectcss.plasmic_mixins,
        projectcss.plasmic_tokens,
        sty.formattingContainer,
        {
          [sty.formattingContainernavBarModals_legal]: hasVariant(
            $state,
            "navBarModals",
            "legal"
          )
        }
      )}
    >
      <section
        data-plasmic-name={"header"}
        data-plasmic-override={overrides.header}
        className={classNames(projectcss.all, sty.header)}
      >
        <div
          data-plasmic-name={"text"}
          data-plasmic-override={overrides.text}
          className={classNames(
            projectcss.all,
            projectcss.__wab_text,
            sty.text,
            {
              [sty.textnavBarModals_bookmarks]: hasVariant(
                $state,
                "navBarModals",
                "bookmarks"
              ),
              [sty.textnavBarModals_legal]: hasVariant(
                $state,
                "navBarModals",
                "legal"
              ),
              [sty.textnavBarModals_messages]: hasVariant(
                $state,
                "navBarModals",
                "messages"
              )
            }
          )}
        >
          {hasVariant($state, "navBarModals", "legal")
            ? "Platform Info"
            : hasVariant($state, "navBarModals", "bookmarks")
            ? "Bookmarks"
            : hasVariant($state, "navBarModals", "messages")
            ? "Messages"
            : "Header"}
        </div>
        <section
          data-plasmic-name={"section"}
          data-plasmic-override={overrides.section}
          className={classNames(projectcss.all, sty.section)}
        />
      </section>
      <section
        data-plasmic-name={"dynamicContent"}
        data-plasmic-override={overrides.dynamicContent}
        className={classNames(projectcss.all, sty.dynamicContent, {
          [sty.dynamicContentnavBarModals_bookmarks]: hasVariant(
            $state,
            "navBarModals",
            "bookmarks"
          ),
          [sty.dynamicContentnavBarModals_legal]: hasVariant(
            $state,
            "navBarModals",
            "legal"
          ),
          [sty.dynamicContentnavBarModals_messages]: hasVariant(
            $state,
            "navBarModals",
            "messages"
          )
        })}
      >
        <section
          data-plasmic-name={"messagesSlot"}
          data-plasmic-override={overrides.messagesSlot}
          className={classNames(projectcss.all, sty.messagesSlot, {
            [sty.messagesSlotnavBarModals_messages]: hasVariant(
              $state,
              "navBarModals",
              "messages"
            )
          })}
        >
          {renderPlasmicSlot({
            defaultContents: (_par =>
              !_par ? [] : Array.isArray(_par) ? _par : [_par])([2, 3, 4]).map(
              (__plasmic_item_0, __plasmic_idx_0) => {
                const currentItem = __plasmic_item_0;
                const currentIndex = __plasmic_idx_0;
                return (
                  <MessagesMessagePreview
                    className={classNames(
                      "__wab_instance",
                      sty.messagesMessagePreview___5E1Kk
                    )}
                    key={currentIndex}
                    modalPreview={"messageModalPreview"}
                  />
                );
              }
            ),
            value: args.messagesSlot2
          })}
        </section>
        <section
          data-plasmic-name={"bookmarkSlot"}
          data-plasmic-override={overrides.bookmarkSlot}
          className={classNames(projectcss.all, sty.bookmarkSlot, {
            [sty.bookmarkSlotnavBarModals_bookmarks]: hasVariant(
              $state,
              "navBarModals",
              "bookmarks"
            ),
            [sty.bookmarkSlotnavBarModals_legal]: hasVariant(
              $state,
              "navBarModals",
              "legal"
            ),
            [sty.bookmarkSlotnavBarModals_messages]: hasVariant(
              $state,
              "navBarModals",
              "messages"
            )
          })}
        >
          {renderPlasmicSlot({
            defaultContents: (_par =>
              !_par ? [] : Array.isArray(_par) ? _par : [_par])([2, 3, 4]).map(
              (__plasmic_item_0, __plasmic_idx_0) => {
                const currentItem = __plasmic_item_0;
                const currentIndex = __plasmic_idx_0;
                return (
                  <BookmarksBookmarkTiles
                    className={classNames(
                      "__wab_instance",
                      sty.bookmarksBookmarkTiles__vujno
                    )}
                    key={currentIndex}
                  />
                );
              }
            ),
            value: args.children
          })}
        </section>
        <div
          data-plasmic-name={"legalStack"}
          data-plasmic-override={overrides.legalStack}
          className={classNames(projectcss.all, sty.legalStack, {
            [sty.legalStacknavBarModals_bookmarks]: hasVariant(
              $state,
              "navBarModals",
              "bookmarks"
            ),
            [sty.legalStacknavBarModals_legal]: hasVariant(
              $state,
              "navBarModals",
              "legal"
            ),
            [sty.legalStacknavBarModals_messages]: hasVariant(
              $state,
              "navBarModals",
              "messages"
            )
          })}
        >
          <NavigationLegalButtons
            data-plasmic-name={"termsOfUse"}
            data-plasmic-override={overrides.termsOfUse}
            className={classNames("__wab_instance", sty.termsOfUse)}
          >
            {"Terms of Use"}
          </NavigationLegalButtons>
          <NavigationLegalButtons
            data-plasmic-name={"privacyPolicy"}
            data-plasmic-override={overrides.privacyPolicy}
            className={classNames("__wab_instance", sty.privacyPolicy)}
          >
            {"Privacy Policy"}
          </NavigationLegalButtons>
          <NavigationLegalButtons
            data-plasmic-name={"careers"}
            data-plasmic-override={overrides.careers}
            className={classNames("__wab_instance", sty.careers, {
              [sty.careersnavBarModals_legal]: hasVariant(
                $state,
                "navBarModals",
                "legal"
              )
            })}
          >
            {"Careers"}
          </NavigationLegalButtons>
          <NavigationLegalButtons
            data-plasmic-name={"contactUs"}
            data-plasmic-override={overrides.contactUs}
            className={classNames("__wab_instance", sty.contactUs)}
          >
            {"Contact Us"}
          </NavigationLegalButtons>
          <NavigationLegalButtons
            data-plasmic-name={"logoutButton"}
            data-plasmic-override={overrides.logoutButton}
            className={classNames("__wab_instance", sty.logoutButton, {
              [sty.logoutButtonnavBarModals_legal]: hasVariant(
                $state,
                "navBarModals",
                "legal"
              )
            })}
          >
            {"Log Out"}
          </NavigationLegalButtons>
        </div>
      </section>
    </div>
  ) as React.ReactElement | null;
}

const PlasmicDescendants = {
  formattingContainer: [
    "formattingContainer",
    "header",
    "text",
    "section",
    "dynamicContent",
    "messagesSlot",
    "bookmarkSlot",
    "legalStack",
    "termsOfUse",
    "privacyPolicy",
    "careers",
    "contactUs",
    "logoutButton"
  ],
  header: ["header", "text", "section"],
  text: ["text"],
  section: ["section"],
  dynamicContent: [
    "dynamicContent",
    "messagesSlot",
    "bookmarkSlot",
    "legalStack",
    "termsOfUse",
    "privacyPolicy",
    "careers",
    "contactUs",
    "logoutButton"
  ],
  messagesSlot: ["messagesSlot"],
  bookmarkSlot: ["bookmarkSlot"],
  legalStack: [
    "legalStack",
    "termsOfUse",
    "privacyPolicy",
    "careers",
    "contactUs",
    "logoutButton"
  ],
  termsOfUse: ["termsOfUse"],
  privacyPolicy: ["privacyPolicy"],
  careers: ["careers"],
  contactUs: ["contactUs"],
  logoutButton: ["logoutButton"]
} as const;
type NodeNameType = keyof typeof PlasmicDescendants;
type DescendantsType<T extends NodeNameType> =
  (typeof PlasmicDescendants)[T][number];
type NodeDefaultElementType = {
  formattingContainer: "div";
  header: "section";
  text: "div";
  section: "section";
  dynamicContent: "section";
  messagesSlot: "section";
  bookmarkSlot: "section";
  legalStack: "div";
  termsOfUse: typeof NavigationLegalButtons;
  privacyPolicy: typeof NavigationLegalButtons;
  careers: typeof NavigationLegalButtons;
  contactUs: typeof NavigationLegalButtons;
  logoutButton: typeof NavigationLegalButtons;
};

type ReservedPropsType = "variants" | "args" | "overrides";
type NodeOverridesType<T extends NodeNameType> = Pick<
  PlasmicNavigationPopupModals__OverridesType,
  DescendantsType<T>
>;
type NodeComponentProps<T extends NodeNameType> =
  // Explicitly specify variants, args, and overrides as objects
  {
    variants?: PlasmicNavigationPopupModals__VariantsArgs;
    args?: PlasmicNavigationPopupModals__ArgsType;
    overrides?: NodeOverridesType<T>;
  } & Omit<PlasmicNavigationPopupModals__VariantsArgs, ReservedPropsType> & // Specify variants directly as props
    /* Specify args directly as props*/ Omit<
      PlasmicNavigationPopupModals__ArgsType,
      ReservedPropsType
    > &
    /* Specify overrides for each element directly as props*/ Omit<
      NodeOverridesType<T>,
      ReservedPropsType | VariantPropType | ArgPropType
    > &
    /* Specify props for the root element*/ Omit<
      Partial<React.ComponentProps<NodeDefaultElementType[T]>>,
      ReservedPropsType | VariantPropType | ArgPropType | DescendantsType<T>
    >;

function makeNodeComponent<NodeName extends NodeNameType>(nodeName: NodeName) {
  type PropsType = NodeComponentProps<NodeName> & { key?: React.Key };
  const func = function <T extends PropsType>(
    props: T & StrictProps<T, PropsType>
  ) {
    const { variants, args, overrides } = React.useMemo(
      () =>
        deriveRenderOpts(props, {
          name: nodeName,
          descendantNames: PlasmicDescendants[nodeName],
          internalArgPropNames: PlasmicNavigationPopupModals__ArgProps,
          internalVariantPropNames: PlasmicNavigationPopupModals__VariantProps
        }),
      [props, nodeName]
    );
    return PlasmicNavigationPopupModals__RenderFunc({
      variants,
      args,
      overrides,
      forNode: nodeName
    });
  };
  if (nodeName === "formattingContainer") {
    func.displayName = "PlasmicNavigationPopupModals";
  } else {
    func.displayName = `PlasmicNavigationPopupModals.${nodeName}`;
  }
  return func;
}

export const PlasmicNavigationPopupModals = Object.assign(
  // Top-level PlasmicNavigationPopupModals renders the root element
  makeNodeComponent("formattingContainer"),
  {
    // Helper components rendering sub-elements
    header: makeNodeComponent("header"),
    text: makeNodeComponent("text"),
    section: makeNodeComponent("section"),
    dynamicContent: makeNodeComponent("dynamicContent"),
    messagesSlot: makeNodeComponent("messagesSlot"),
    bookmarkSlot: makeNodeComponent("bookmarkSlot"),
    legalStack: makeNodeComponent("legalStack"),
    termsOfUse: makeNodeComponent("termsOfUse"),
    privacyPolicy: makeNodeComponent("privacyPolicy"),
    careers: makeNodeComponent("careers"),
    contactUs: makeNodeComponent("contactUs"),
    logoutButton: makeNodeComponent("logoutButton"),

    // Metadata about props expected for PlasmicNavigationPopupModals
    internalVariantProps: PlasmicNavigationPopupModals__VariantProps,
    internalArgProps: PlasmicNavigationPopupModals__ArgProps
  }
);

export default PlasmicNavigationPopupModals;
/* prettier-ignore-end */
