/* eslint-disable */
/* tslint:disable */
// @ts-nocheck
/* prettier-ignore-start */
import React from "react";
import { classNames } from "@plasmicapp/react-web";

export type MoreIconProps = React.ComponentProps<"svg"> & {
  title?: string;
};

export function MoreIcon(props: MoreIconProps) {
  const { className, style, title, ...restProps } = props;
  return (
    <svg
      xmlns={"http://www.w3.org/2000/svg"}
      className={classNames(
        "plasmic-default__svg",
        className,
        "icon icon-tabler icon-tabler-dots-circle-horizontal"
      )}
      viewBox={"0 0 24 24"}
      strokeWidth={"1.5"}
      stroke={"currentColor"}
      fill={"none"}
      strokeLinecap={"round"}
      strokeLinejoin={"round"}
      height={"1em"}
      style={style}
      {...restProps}
    >
      {title && <title>{title}</title>}

      <path stroke={"none"} d={"M0 0h24v24H0z"} fill={"none"}></path>

      <path
        d={"M3 12a9 9 0 1018 0 9 9 0 10-18 0m5 0v.01m4-.01v.01m4-.01v.01"}
      ></path>
    </svg>
  );
}

export default MoreIcon;
/* prettier-ignore-end */
