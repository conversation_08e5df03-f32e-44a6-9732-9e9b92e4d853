.dot {
  display: flex;
  flex-direction: column;
  position: relative;
  width: 40px;
  height: 40px;
  justify-content: center;
  align-items: flex-end;
  background: var(--token-xo_r2w5pebq-);
  opacity: 0.5;
  border-radius: 100%;
}
.dotdirection_left {
  align-items: flex-start;
  justify-content: center;
}
.dotdirection_up {
  align-items: center;
  justify-content: center;
}
.dotdirection_down {
  align-items: center;
  justify-content: center;
}
.svg {
  position: relative;
  object-fit: cover;
  max-width: 100%;
  width: 36px;
  height: 40px;
  color: var(--token-K5FbAPSIIrXM);
  flex-shrink: 0;
}
.svgdirection_up {
  height: 36px;
  margin-right: 0px;
  width: 40px;
  flex-shrink: 0;
}
