/* eslint-disable */
/* tslint:disable */
// @ts-nocheck
/* prettier-ignore-start */

/** @jsxRuntime classic */
/** @jsx createPlasmicElementProxy */
/** @jsxFrag React.Fragment */

// This class is auto-generated by Plasmic; please do not edit!
// Plasmic Project: nZeWKcn21KijvC7eT8B3c7
// Component: VoKRKQ4gT9bR

"use client";

import * as React from "react";

import Head from "next/head";
import Link, { LinkProps } from "next/link";
import { useRouter } from "next/navigation";

import {
  Flex as Flex__,
  MultiChoiceArg,
  PlasmicDataSourceContextProvider as PlasmicDataSourceContextProvider__,
  PlasmicIcon as PlasmicIcon__,
  PlasmicImg as PlasmicImg__,
  PlasmicLink as PlasmicLink__,
  PlasmicPageGuard as PlasmicPageGuard__,
  SingleBooleanChoiceArg,
  SingleC<PERSON>iceArg,
  Stack as Stack__,
  StrictProps,
  Trans as Trans__,
  classNames,
  createPlasmicElementProxy,
  deriveRenderOpts,
  ensureGlobalVariants,
  generateOnMutateForSpec,
  generateStateOnChangeProp,
  generateStateOnChangePropForCodeComponents,
  generateStateValueProp,
  get as $stateGet,
  hasVariant,
  initializeCodeComponentStates,
  initializePlasmicStates,
  makeFragment,
  omit,
  pick,
  renderPlasmicSlot,
  set as $stateSet,
  useCurrentUser,
  useDollarState,
  usePlasmicTranslator,
  useTrigger,
  wrapWithClassName
} from "@plasmicapp/react-web";
import {
  DataCtxReader as DataCtxReader__,
  useDataEnv,
  useGlobalActions
} from "@plasmicapp/host";

import ProfileSectionsProfileSectionHeading from "../../ProfileSectionsProfileSectionHeading"; // plasmic-import: Uz656rZzIxzC/component
import ProfileTileTrademarkTile from "../../ProfileTileTrademarkTile"; // plasmic-import: G5tbmo_PlO9M/component

import "@plasmicapp/react-web/lib/plasmic.css";

import projectcss from "./plasmic.module.css"; // plasmic-import: nZeWKcn21KijvC7eT8B3c7/projectcss
import sty from "./PlasmicProfileSectionsTrademarksBlock.module.css"; // plasmic-import: VoKRKQ4gT9bR/css

createPlasmicElementProxy;

export type PlasmicProfileSectionsTrademarksBlock__VariantMembers = {
  overviewGrid: "overviewGridBlock";
  editable: "editable";
};
export type PlasmicProfileSectionsTrademarksBlock__VariantsArgs = {
  overviewGrid?: SingleChoiceArg<"overviewGridBlock">;
  editable?: SingleBooleanChoiceArg<"editable">;
};
type VariantPropType =
  keyof PlasmicProfileSectionsTrademarksBlock__VariantsArgs;
export const PlasmicProfileSectionsTrademarksBlock__VariantProps =
  new Array<VariantPropType>("overviewGrid", "editable");

export type PlasmicProfileSectionsTrademarksBlock__ArgsType = {
  allTrademarks?: any;
  onAllTrademarksChange?: (val: string) => void;
  addButtonBaseOnClick?: (event: any) => void;
  addButtonPatentOnClick?: (event: any) => void;
  addButtonTrademarkOnClick?: (event: any) => void;
  addButtonLicenseOnClick?: (event: any) => void;
  addButtonCertificationOnClick?: (event: any) => void;
};
type ArgPropType = keyof PlasmicProfileSectionsTrademarksBlock__ArgsType;
export const PlasmicProfileSectionsTrademarksBlock__ArgProps =
  new Array<ArgPropType>(
    "allTrademarks",
    "onAllTrademarksChange",
    "addButtonBaseOnClick",
    "addButtonPatentOnClick",
    "addButtonTrademarkOnClick",
    "addButtonLicenseOnClick",
    "addButtonCertificationOnClick"
  );

export type PlasmicProfileSectionsTrademarksBlock__OverridesType = {
  patentsAndTrademarksSection?: Flex__<"div">;
  profileSectionsProfileSectionHeading?: Flex__<
    typeof ProfileSectionsProfileSectionHeading
  >;
  displayContainer?: Flex__<"section">;
  tTrademarkTile?: Flex__<typeof ProfileTileTrademarkTile>;
};

export interface DefaultProfileSectionsTrademarksBlockProps {
  allTrademarks?: any;
  onAllTrademarksChange?: (val: string) => void;
  addButtonBaseOnClick?: (event: any) => void;
  addButtonPatentOnClick?: (event: any) => void;
  addButtonTrademarkOnClick?: (event: any) => void;
  addButtonLicenseOnClick?: (event: any) => void;
  addButtonCertificationOnClick?: (event: any) => void;
  overviewGrid?: SingleChoiceArg<"overviewGridBlock">;
  editable?: SingleBooleanChoiceArg<"editable">;
  className?: string;
}

const $$ = {};

function useNextRouter() {
  try {
    return useRouter();
  } catch {}
  return undefined;
}

function PlasmicProfileSectionsTrademarksBlock__RenderFunc(props: {
  variants: PlasmicProfileSectionsTrademarksBlock__VariantsArgs;
  args: PlasmicProfileSectionsTrademarksBlock__ArgsType;
  overrides: PlasmicProfileSectionsTrademarksBlock__OverridesType;
  forNode?: string;
}) {
  const { variants, overrides, forNode } = props;

  const args = React.useMemo(
    () =>
      Object.assign(
        {},
        Object.fromEntries(
          Object.entries(props.args).filter(([_, v]) => v !== undefined)
        )
      ),
    [props.args]
  );

  const $props = {
    ...args,
    ...variants
  };

  const __nextRouter = useNextRouter();
  const $ctx = useDataEnv?.() || {};
  const refsRef = React.useRef({});
  const $refs = refsRef.current;

  let [$queries, setDollarQueries] = React.useState<
    Record<string, ReturnType<typeof usePlasmicDataOp>>
  >({});
  const stateSpecs: Parameters<typeof useDollarState>[0] = React.useMemo(
    () => [
      {
        path: "overviewGrid",
        type: "private",
        variableType: "variant",
        initFunc: ({ $props, $state, $queries, $ctx }) => $props.overviewGrid
      },
      {
        path: "editable",
        type: "private",
        variableType: "variant",
        initFunc: ({ $props, $state, $queries, $ctx }) => $props.editable
      },
      {
        path: "tTrademarkTile[].titleInputValue",
        type: "private",
        variableType: "text"
      },
      {
        path: "tTrademarkTile[].countryOfRegistrationInputValue",
        type: "private",
        variableType: "text"
      },
      {
        path: "tTrademarkTile[].registrationDateInputValue",
        type: "private",
        variableType: "text"
      },
      {
        path: "tTrademarkTile[].registrationNumberInputValue",
        type: "private",
        variableType: "text"
      },
      {
        path: "tTrademarkTile[].awardDateInputValue",
        type: "private",
        variableType: "text"
      },
      {
        path: "tTrademarkTile[].deleteButtonClickStage",
        type: "private",
        variableType: "number"
      },
      {
        path: "tTrademarkTile[].deleteButtonDisabled",
        type: "private",
        variableType: "text"
      },
      {
        path: "tTrademarkTile[].descriptionInputValue",
        type: "private",
        variableType: "text"
      },
      {
        path: "allTrademarks",
        type: "writable",
        variableType: "object",

        valueProp: "allTrademarks",
        onChangeProp: "onAllTrademarksChange"
      }
    ],
    [$props, $ctx, $refs]
  );
  const $state = useDollarState(stateSpecs, {
    $props,
    $ctx,
    $queries: $queries,
    $refs
  });

  const new$Queries: Record<string, ReturnType<typeof usePlasmicDataOp>> = {};
  if (Object.keys(new$Queries).some(k => new$Queries[k] !== $queries[k])) {
    setDollarQueries(new$Queries);

    $queries = new$Queries;
  }

  return (
    <div
      data-plasmic-name={"patentsAndTrademarksSection"}
      data-plasmic-override={overrides.patentsAndTrademarksSection}
      data-plasmic-root={true}
      data-plasmic-for-node={forNode}
      className={classNames(
        projectcss.all,
        projectcss.root_reset,
        projectcss.plasmic_default_styles,
        projectcss.plasmic_mixins,
        projectcss.plasmic_tokens,
        sty.patentsAndTrademarksSection,
        {
          [sty.patentsAndTrademarksSectioneditable]: hasVariant(
            $state,
            "editable",
            "editable"
          ),
          [sty.patentsAndTrademarksSectionoverviewGrid_overviewGridBlock]:
            hasVariant($state, "overviewGrid", "overviewGridBlock")
        }
      )}
    >
      <ProfileSectionsProfileSectionHeading
        data-plasmic-name={"profileSectionsProfileSectionHeading"}
        data-plasmic-override={overrides.profileSectionsProfileSectionHeading}
        addButtonBaseOnClick={args.addButtonBaseOnClick}
        addButtonCertificationOnClick={args.addButtonCertificationOnClick}
        addButtonLicenseOnClick={args.addButtonLicenseOnClick}
        addButtonPatentOnClick={args.addButtonPatentOnClick}
        addButtonTrademarkOnClick={args.addButtonTrademarkOnClick}
        className={classNames(
          "__wab_instance",
          sty.profileSectionsProfileSectionHeading,
          {
            [sty.profileSectionsProfileSectionHeadingeditable]: hasVariant(
              $state,
              "editable",
              "editable"
            ),
            [sty.profileSectionsProfileSectionHeadingoverviewGrid_overviewGridBlock]:
              hasVariant($state, "overviewGrid", "overviewGridBlock")
          }
        )}
        editable={
          hasVariant($state, "editable", "editable")
            ? true
            : (() => {
                try {
                  return $state.editable;
                } catch (e) {
                  if (
                    e instanceof TypeError ||
                    e?.plasmicType === "PlasmicUndefinedDataError"
                  ) {
                    return [];
                  }
                  throw e;
                }
              })()
        }
        overviewGrid={
          hasVariant($state, "overviewGrid", "overviewGridBlock")
            ? true
            : undefined
        }
      >
        {"Trademarks"}
      </ProfileSectionsProfileSectionHeading>
      <section
        data-plasmic-name={"displayContainer"}
        data-plasmic-override={overrides.displayContainer}
        className={classNames(projectcss.all, sty.displayContainer)}
      >
        {(_par => (!_par ? [] : Array.isArray(_par) ? _par : [_par]))(
          (() => {
            try {
              return $state.allTrademarks.data;
            } catch (e) {
              if (
                e instanceof TypeError ||
                e?.plasmicType === "PlasmicUndefinedDataError"
              ) {
                return [];
              }
              throw e;
            }
          })()
        ).map((__plasmic_item_0, __plasmic_idx_0) => {
          const currentItem = __plasmic_item_0;
          const currentIndex = __plasmic_idx_0;
          return (() => {
            const child$Props = {
              awardDateInputValue: generateStateValueProp($state, [
                "tTrademarkTile",
                __plasmic_idx_0,
                "awardDateInputValue"
              ]),
              className: classNames("__wab_instance", sty.tTrademarkTile, {
                [sty.tTrademarkTileeditable]: hasVariant(
                  $state,
                  "editable",
                  "editable"
                ),
                [sty.tTrademarkTileoverviewGrid_overviewGridBlock]: hasVariant(
                  $state,
                  "overviewGrid",
                  "overviewGridBlock"
                )
              }),
              countryOfRegistrationInputValue: generateStateValueProp($state, [
                "tTrademarkTile",
                __plasmic_idx_0,
                "countryOfRegistrationInputValue"
              ]),
              deleteButtonClickStage: generateStateValueProp($state, [
                "tTrademarkTile",
                __plasmic_idx_0,
                "deleteButtonClickStage"
              ]),
              deleteButtonDisabled: generateStateValueProp($state, [
                "tTrademarkTile",
                __plasmic_idx_0,
                "deleteButtonDisabled"
              ]),
              descriptionInputValue: generateStateValueProp($state, [
                "tTrademarkTile",
                __plasmic_idx_0,
                "descriptionInputValue"
              ]),
              editable: hasVariant($state, "editable", "editable")
                ? true
                : (() => {
                    try {
                      return $state.editable;
                    } catch (e) {
                      if (
                        e instanceof TypeError ||
                        e?.plasmicType === "PlasmicUndefinedDataError"
                      ) {
                        return [];
                      }
                      throw e;
                    }
                  })(),
              key: currentIndex,
              onAwardDateInputValueChange: async (...eventArgs: any) => {
                generateStateOnChangeProp($state, [
                  "tTrademarkTile",
                  __plasmic_idx_0,
                  "awardDateInputValue"
                ]).apply(null, eventArgs);

                if (
                  eventArgs.length > 1 &&
                  eventArgs[1] &&
                  eventArgs[1]._plasmic_state_init_
                ) {
                  return;
                }
              },
              onCountryOfRegistrationInputValueChange: async (
                ...eventArgs: any
              ) => {
                generateStateOnChangeProp($state, [
                  "tTrademarkTile",
                  __plasmic_idx_0,
                  "countryOfRegistrationInputValue"
                ]).apply(null, eventArgs);

                if (
                  eventArgs.length > 1 &&
                  eventArgs[1] &&
                  eventArgs[1]._plasmic_state_init_
                ) {
                  return;
                }
              },
              onDeleteButtonClickStageChange: async (...eventArgs: any) => {
                generateStateOnChangeProp($state, [
                  "tTrademarkTile",
                  __plasmic_idx_0,
                  "deleteButtonClickStage"
                ]).apply(null, eventArgs);

                if (
                  eventArgs.length > 1 &&
                  eventArgs[1] &&
                  eventArgs[1]._plasmic_state_init_
                ) {
                  return;
                }
              },
              onDeleteButtonDisabledChange: async (...eventArgs: any) => {
                generateStateOnChangeProp($state, [
                  "tTrademarkTile",
                  __plasmic_idx_0,
                  "deleteButtonDisabled"
                ]).apply(null, eventArgs);

                if (
                  eventArgs.length > 1 &&
                  eventArgs[1] &&
                  eventArgs[1]._plasmic_state_init_
                ) {
                  return;
                }
              },
              onDescriptionInputValueChange: async (...eventArgs: any) => {
                generateStateOnChangeProp($state, [
                  "tTrademarkTile",
                  __plasmic_idx_0,
                  "descriptionInputValue"
                ]).apply(null, eventArgs);

                if (
                  eventArgs.length > 1 &&
                  eventArgs[1] &&
                  eventArgs[1]._plasmic_state_init_
                ) {
                  return;
                }
              },
              onRegistrationDateInputValueChange: async (...eventArgs: any) => {
                generateStateOnChangeProp($state, [
                  "tTrademarkTile",
                  __plasmic_idx_0,
                  "registrationDateInputValue"
                ]).apply(null, eventArgs);

                if (
                  eventArgs.length > 1 &&
                  eventArgs[1] &&
                  eventArgs[1]._plasmic_state_init_
                ) {
                  return;
                }
              },
              onRegistrationNumberInputValueChange: async (
                ...eventArgs: any
              ) => {
                generateStateOnChangeProp($state, [
                  "tTrademarkTile",
                  __plasmic_idx_0,
                  "registrationNumberInputValue"
                ]).apply(null, eventArgs);

                if (
                  eventArgs.length > 1 &&
                  eventArgs[1] &&
                  eventArgs[1]._plasmic_state_init_
                ) {
                  return;
                }
              },
              onTitleInputValueChange: async (...eventArgs: any) => {
                generateStateOnChangeProp($state, [
                  "tTrademarkTile",
                  __plasmic_idx_0,
                  "titleInputValue"
                ]).apply(null, eventArgs);

                if (
                  eventArgs.length > 1 &&
                  eventArgs[1] &&
                  eventArgs[1]._plasmic_state_init_
                ) {
                  return;
                }
              },
              overview: hasVariant($state, "overviewGrid", "overviewGridBlock")
                ? true
                : undefined,
              registrationDateInputValue: generateStateValueProp($state, [
                "tTrademarkTile",
                __plasmic_idx_0,
                "registrationDateInputValue"
              ]),
              registrationNumberInputValue: generateStateValueProp($state, [
                "tTrademarkTile",
                __plasmic_idx_0,
                "registrationNumberInputValue"
              ]),
              titleInputValue: generateStateValueProp($state, [
                "tTrademarkTile",
                __plasmic_idx_0,
                "titleInputValue"
              ]),
              trademarkId: (() => {
                try {
                  return currentItem.id;
                } catch (e) {
                  if (
                    e instanceof TypeError ||
                    e?.plasmicType === "PlasmicUndefinedDataError"
                  ) {
                    return undefined;
                  }
                  throw e;
                }
              })()
            };

            initializePlasmicStates(
              $state,
              [
                {
                  name: "tTrademarkTile[].titleInputValue",
                  initFunc: ({ $props, $state, $queries }) =>
                    (() => {
                      try {
                        return currentItem.title;
                      } catch (e) {
                        if (
                          e instanceof TypeError ||
                          e?.plasmicType === "PlasmicUndefinedDataError"
                        ) {
                          return undefined;
                        }
                        throw e;
                      }
                    })()
                },
                {
                  name: "tTrademarkTile[].countryOfRegistrationInputValue",
                  initFunc: ({ $props, $state, $queries }) =>
                    (() => {
                      try {
                        return currentItem.registration_location;
                      } catch (e) {
                        if (
                          e instanceof TypeError ||
                          e?.plasmicType === "PlasmicUndefinedDataError"
                        ) {
                          return undefined;
                        }
                        throw e;
                      }
                    })()
                },
                {
                  name: "tTrademarkTile[].registrationDateInputValue",
                  initFunc: ({ $props, $state, $queries }) =>
                    (() => {
                      try {
                        return currentItem.registration_date
                          ? currentItem.registration_date
                              .split("T")[0]
                              .split("-")
                              .join("-")
                          : null;
                      } catch (e) {
                        if (
                          e instanceof TypeError ||
                          e?.plasmicType === "PlasmicUndefinedDataError"
                        ) {
                          return undefined;
                        }
                        throw e;
                      }
                    })()
                },
                {
                  name: "tTrademarkTile[].registrationNumberInputValue",
                  initFunc: ({ $props, $state, $queries }) =>
                    (() => {
                      try {
                        return currentItem.registration_number;
                      } catch (e) {
                        if (
                          e instanceof TypeError ||
                          e?.plasmicType === "PlasmicUndefinedDataError"
                        ) {
                          return undefined;
                        }
                        throw e;
                      }
                    })()
                },
                {
                  name: "tTrademarkTile[].awardDateInputValue",
                  initFunc: ({ $props, $state, $queries }) =>
                    (() => {
                      try {
                        return currentItem.award_date
                          ? currentItem.award_date
                              .split("T")[0]
                              .split("-")
                              .join("-")
                          : null;
                      } catch (e) {
                        if (
                          e instanceof TypeError ||
                          e?.plasmicType === "PlasmicUndefinedDataError"
                        ) {
                          return undefined;
                        }
                        throw e;
                      }
                    })()
                },
                {
                  name: "tTrademarkTile[].deleteButtonClickStage",
                  initFunc: ({ $props, $state, $queries }) => 0
                },
                {
                  name: "tTrademarkTile[].deleteButtonDisabled",
                  initFunc: ({ $props, $state, $queries }) => undefined
                },
                {
                  name: "tTrademarkTile[].descriptionInputValue",
                  initFunc: ({ $props, $state, $queries }) =>
                    (() => {
                      try {
                        return currentItem.summary;
                      } catch (e) {
                        if (
                          e instanceof TypeError ||
                          e?.plasmicType === "PlasmicUndefinedDataError"
                        ) {
                          return undefined;
                        }
                        throw e;
                      }
                    })()
                }
              ],
              [__plasmic_idx_0]
            );
            return (
              <ProfileTileTrademarkTile
                data-plasmic-name={"tTrademarkTile"}
                data-plasmic-override={overrides.tTrademarkTile}
                {...child$Props}
              />
            );
          })();
        })}
      </section>
    </div>
  ) as React.ReactElement | null;
}

const PlasmicDescendants = {
  patentsAndTrademarksSection: [
    "patentsAndTrademarksSection",
    "profileSectionsProfileSectionHeading",
    "displayContainer",
    "tTrademarkTile"
  ],
  profileSectionsProfileSectionHeading: [
    "profileSectionsProfileSectionHeading"
  ],
  displayContainer: ["displayContainer", "tTrademarkTile"],
  tTrademarkTile: ["tTrademarkTile"]
} as const;
type NodeNameType = keyof typeof PlasmicDescendants;
type DescendantsType<T extends NodeNameType> =
  (typeof PlasmicDescendants)[T][number];
type NodeDefaultElementType = {
  patentsAndTrademarksSection: "div";
  profileSectionsProfileSectionHeading: typeof ProfileSectionsProfileSectionHeading;
  displayContainer: "section";
  tTrademarkTile: typeof ProfileTileTrademarkTile;
};

type ReservedPropsType = "variants" | "args" | "overrides";
type NodeOverridesType<T extends NodeNameType> = Pick<
  PlasmicProfileSectionsTrademarksBlock__OverridesType,
  DescendantsType<T>
>;
type NodeComponentProps<T extends NodeNameType> =
  // Explicitly specify variants, args, and overrides as objects
  {
    variants?: PlasmicProfileSectionsTrademarksBlock__VariantsArgs;
    args?: PlasmicProfileSectionsTrademarksBlock__ArgsType;
    overrides?: NodeOverridesType<T>;
  } & Omit< // Specify variants directly as props
    PlasmicProfileSectionsTrademarksBlock__VariantsArgs,
    ReservedPropsType
  > &
    /* Specify args directly as props*/ Omit<
      PlasmicProfileSectionsTrademarksBlock__ArgsType,
      ReservedPropsType
    > &
    /* Specify overrides for each element directly as props*/ Omit<
      NodeOverridesType<T>,
      ReservedPropsType | VariantPropType | ArgPropType
    > &
    /* Specify props for the root element*/ Omit<
      Partial<React.ComponentProps<NodeDefaultElementType[T]>>,
      ReservedPropsType | VariantPropType | ArgPropType | DescendantsType<T>
    >;

function makeNodeComponent<NodeName extends NodeNameType>(nodeName: NodeName) {
  type PropsType = NodeComponentProps<NodeName> & { key?: React.Key };
  const func = function <T extends PropsType>(
    props: T & StrictProps<T, PropsType>
  ) {
    const { variants, args, overrides } = React.useMemo(
      () =>
        deriveRenderOpts(props, {
          name: nodeName,
          descendantNames: PlasmicDescendants[nodeName],
          internalArgPropNames: PlasmicProfileSectionsTrademarksBlock__ArgProps,
          internalVariantPropNames:
            PlasmicProfileSectionsTrademarksBlock__VariantProps
        }),
      [props, nodeName]
    );
    return PlasmicProfileSectionsTrademarksBlock__RenderFunc({
      variants,
      args,
      overrides,
      forNode: nodeName
    });
  };
  if (nodeName === "patentsAndTrademarksSection") {
    func.displayName = "PlasmicProfileSectionsTrademarksBlock";
  } else {
    func.displayName = `PlasmicProfileSectionsTrademarksBlock.${nodeName}`;
  }
  return func;
}

export const PlasmicProfileSectionsTrademarksBlock = Object.assign(
  // Top-level PlasmicProfileSectionsTrademarksBlock renders the root element
  makeNodeComponent("patentsAndTrademarksSection"),
  {
    // Helper components rendering sub-elements
    profileSectionsProfileSectionHeading: makeNodeComponent(
      "profileSectionsProfileSectionHeading"
    ),
    displayContainer: makeNodeComponent("displayContainer"),
    tTrademarkTile: makeNodeComponent("tTrademarkTile"),

    // Metadata about props expected for PlasmicProfileSectionsTrademarksBlock
    internalVariantProps: PlasmicProfileSectionsTrademarksBlock__VariantProps,
    internalArgProps: PlasmicProfileSectionsTrademarksBlock__ArgProps
  }
);

export default PlasmicProfileSectionsTrademarksBlock;
/* prettier-ignore-end */
