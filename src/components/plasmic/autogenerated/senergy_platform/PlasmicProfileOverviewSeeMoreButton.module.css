.root {
  display: inline-flex;
  flex-direction: row;
  width: auto;
  height: 32px;
  justify-content: center;
  align-items: center;
  padding-right: 8px;
  padding-left: 8px;
  justify-self: flex-start;
  position: relative;
  cursor: pointer;
  border-width: 1px;
  border-style: none;
}
.formattingContainer {
  display: flex;
  position: relative;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start;
  width: 100%;
  height: auto;
  max-width: 100%;
  min-width: 0;
  padding: var(--token-sazGmnf7GWAk);
  margin: var(--token-sazGmnf7GWAk);
}
.root:hover .formattingContainer {
  align-items: center;
  justify-content: flex-end;
  width: auto;
}
.iconAndTextStack {
  display: flex;
  position: relative;
  flex-direction: row;
  align-items: center;
  justify-content: flex-start;
  width: 100%;
  height: auto;
  max-width: 100%;
  min-width: 0;
  padding: var(--token-sazGmnf7GWAk);
  margin: var(--token-sazGmnf7GWAk);
}
.root:hover .iconAndTextStack {
  width: auto;
}
.displayText {
  width: 100%;
  height: auto;
  max-width: 100%;
  font-family: var(--token-z1yrQVi72Nj1);
  font-size: var(--token-2UEfYzPsoOY0);
  min-width: 0;
}
.svg {
  position: relative;
  object-fit: cover;
  max-width: 100%;
  margin-left: 8px;
  transition-property: all;
  transition-duration: 0.045s;
  transition-timing-function: linear;
  height: 1em;
  -webkit-transition-property: all;
  -webkit-transition-timing-function: linear;
  -webkit-transition-duration: 0.045s;
}
.root:active .svg {
  transform: translateX(4px) translateY(0px) translateZ(0px);
}
.underline {
  position: relative;
  align-content: flex-start;
  justify-items: center;
  width: 100px;
  left: auto;
  bottom: auto;
  right: auto;
  top: auto;
  z-index: 1;
  height: 1px;
  background: var(--token-K5FbAPSIIrXM);
  flex-shrink: 0;
  display: none;
  grid-template-columns:
    var(--plsmc-viewport-gap) 1fr minmax(0, var(--plsmc-wide-chunk))
    min(
      var(--plsmc-standard-width),
      calc(100% - var(--plsmc-viewport-gap) - var(--plsmc-viewport-gap))
    )
    minmax(0, var(--plsmc-wide-chunk)) 1fr var(--plsmc-viewport-gap);
  padding: var(--token-sazGmnf7GWAk);
}
.underline > * {
  grid-column: 4;
}
.root:hover .underline {
  justify-items: center;
  align-content: center;
  left: 0px;
  top: 24px;
  position: absolute;
  margin-bottom: -1px;
  width: 100%;
  min-width: 0;
  display: grid;
}
.root:hover .underline > * {
  grid-column: 4;
}
.root:active .underline {
  display: grid;
}
.root:active .underline > * {
  grid-column: 4;
}
