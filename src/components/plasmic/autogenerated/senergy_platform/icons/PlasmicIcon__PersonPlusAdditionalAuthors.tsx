/* eslint-disable */
/* tslint:disable */
// @ts-nocheck
/* prettier-ignore-start */
import React from "react";
import { classNames } from "@plasmicapp/react-web";

export type PersonPlusAdditionalAuthorsIconProps =
  React.ComponentProps<"svg"> & {
    title?: string;
  };

export function PersonPlusAdditionalAuthorsIcon(
  props: PersonPlusAdditionalAuthorsIconProps
) {
  const { className, style, title, ...restProps } = props;
  return (
    <svg
      xmlns={"http://www.w3.org/2000/svg"}
      className={classNames(
        "plasmic-default__svg",
        className,
        "icon icon-tabler icon-tabler-user-plus"
      )}
      viewBox={"0 0 24 24"}
      strokeWidth={"1.5"}
      stroke={"currentColor"}
      fill={"none"}
      strokeLinecap={"round"}
      strokeLinejoin={"round"}
      height={"1em"}
      style={style}
      {...restProps}
    >
      {title && <title>{title}</title>}

      <path d={"M0 0h24v24H0z"} fill={"none"} stroke={"none"}></path>

      <path
        d={"M8 7a4 4 0 108 0 4 4 0 00-8 0m8 12h6m-3-3v6M6 21v-2a4 4 0 014-4h4"}
      ></path>
    </svg>
  );
}

export default PersonPlusAdditionalAuthorsIcon;
/* prettier-ignore-end */
