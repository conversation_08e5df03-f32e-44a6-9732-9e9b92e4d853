/* eslint-disable */
/* tslint:disable */
// @ts-nocheck
/* prettier-ignore-start */

/** @jsxRuntime classic */
/** @jsx createPlasmicElementProxy */
/** @jsxFrag React.Fragment */

// This class is auto-generated by Plasmic; please do not edit!
// Plasmic Project: nZeWKcn21KijvC7eT8B3c7
// Component: xb6rsawcP4mG

"use client";

import * as React from "react";

import Head from "next/head";
import Link, { LinkProps } from "next/link";
import { useRouter } from "next/navigation";

import {
  Flex as Flex__,
  MultiChoiceArg,
  PlasmicDataSourceContextProvider as PlasmicDataSourceContextProvider__,
  PlasmicIcon as PlasmicIcon__,
  PlasmicImg as PlasmicImg__,
  PlasmicLink as PlasmicLink__,
  PlasmicPageGuard as PlasmicPageGuard__,
  SingleBooleanChoiceArg,
  SingleChoiceArg,
  Stack as Stack__,
  StrictProps,
  Trans as Trans__,
  classNames,
  createPlasmicElementProxy,
  deriveRenderOpts,
  ensureGlobalVariants,
  generateOnMutateForSpec,
  generateStateOnChangeProp,
  generateStateOnChangePropForCodeComponents,
  generateStateValueProp,
  get as $stateGet,
  hasVariant,
  initializeCodeComponentStates,
  initializePlasmicStates,
  makeFragment,
  omit,
  pick,
  renderPlasmicSlot,
  set as $stateSet,
  useCurrentUser,
  useDollarState,
  usePlasmicTranslator,
  useTrigger,
  wrapWithClassName
} from "@plasmicapp/react-web";
import {
  DataCtxReader as DataCtxReader__,
  useDataEnv,
  useGlobalActions
} from "@plasmicapp/host";

import SubcomponentSelectorButtonsWSlot from "../../SubcomponentSelectorButtonsWSlot"; // plasmic-import: FPZd4ZDDgKTk/component

import "@plasmicapp/react-web/lib/plasmic.css";

import projectcss from "./plasmic.module.css"; // plasmic-import: nZeWKcn21KijvC7eT8B3c7/projectcss
import sty from "./PlasmicOnboardingProfileSectionModule.module.css"; // plasmic-import: xb6rsawcP4mG/css

import UserIcon from "./icons/PlasmicIcon__User"; // plasmic-import: hcR42vt5qkrz/icon
import PhotoCheckIcon from "./icons/PlasmicIcon__PhotoCheck"; // plasmic-import: 0HEABec35TzS/icon
import GradCapIcon from "./icons/PlasmicIcon__GradCap"; // plasmic-import: i9bbaFOlpQVC/icon
import BuildingIcon from "./icons/PlasmicIcon__Building"; // plasmic-import: RuQrxOj-BhnB/icon
import ToolsIcon from "./icons/PlasmicIcon__Tools"; // plasmic-import: AHRVoCS9vdiJ/icon
import SkillsIcon from "./icons/PlasmicIcon__Skills"; // plasmic-import: 3yiBdPB4DXzP/icon
import LanguagIcon from "./icons/PlasmicIcon__Languag"; // plasmic-import: 01ij0lND9yQw/icon
import PatentedIcon from "./icons/PlasmicIcon__Patented"; // plasmic-import: 0zRPSen3c0Ql/icon
import PublicationBookIcon from "./icons/PlasmicIcon__PublicationBook"; // plasmic-import: 1vlHySSOKWkP/icon
import CertificateIcon from "./icons/PlasmicIcon__Certificate"; // plasmic-import: oMeblj4iBOSs/icon
import LicenseIcon from "./icons/PlasmicIcon__License"; // plasmic-import: j-ct0zISt5hl/icon
import TrademarkIcon from "./icons/PlasmicIcon__Trademark"; // plasmic-import: TJR3b86TU-Do/icon
import PlusIcon from "./icons/PlasmicIcon__Plus"; // plasmic-import: 9aghGjj-Yn0f/icon
import MinusIcon from "./icons/PlasmicIcon__Minus"; // plasmic-import: -YxnRXLJIlYW/icon

createPlasmicElementProxy;

export type PlasmicOnboardingProfileSectionModule__VariantMembers = {
  sectionOnboarding: "optionSelected" | "unnamedVariant2";
  selection: "seeMore" | "seeLess";
};
export type PlasmicOnboardingProfileSectionModule__VariantsArgs = {
  sectionOnboarding?: SingleChoiceArg<"optionSelected" | "unnamedVariant2">;
  selection?: SingleChoiceArg<"seeMore" | "seeLess">;
};
type VariantPropType =
  keyof PlasmicOnboardingProfileSectionModule__VariantsArgs;
export const PlasmicOnboardingProfileSectionModule__VariantProps =
  new Array<VariantPropType>("sectionOnboarding", "selection");

export type PlasmicOnboardingProfileSectionModule__ArgsType = {};
type ArgPropType = keyof PlasmicOnboardingProfileSectionModule__ArgsType;
export const PlasmicOnboardingProfileSectionModule__ArgProps =
  new Array<ArgPropType>();

export type PlasmicOnboardingProfileSectionModule__OverridesType = {
  formattingContainer?: Flex__<"div">;
  mainSectionButtons?: Flex__<"div">;
  profileBanner?: Flex__<typeof SubcomponentSelectorButtonsWSlot>;
  introduction?: Flex__<typeof SubcomponentSelectorButtonsWSlot>;
  educationButton?: Flex__<typeof SubcomponentSelectorButtonsWSlot>;
  experienceButton?: Flex__<typeof SubcomponentSelectorButtonsWSlot>;
  toolsButton?: Flex__<typeof SubcomponentSelectorButtonsWSlot>;
  skillsButton?: Flex__<typeof SubcomponentSelectorButtonsWSlot>;
  languagesButton?: Flex__<typeof SubcomponentSelectorButtonsWSlot>;
  patentsButton?: Flex__<typeof SubcomponentSelectorButtonsWSlot>;
  publicationsButton?: Flex__<typeof SubcomponentSelectorButtonsWSlot>;
  certificationsButton?: Flex__<typeof SubcomponentSelectorButtonsWSlot>;
  licensesButton?: Flex__<typeof SubcomponentSelectorButtonsWSlot>;
  trademarksButton?: Flex__<typeof SubcomponentSelectorButtonsWSlot>;
  seeLessButton2?: Flex__<typeof SubcomponentSelectorButtonsWSlot>;
  seeLessButton?: Flex__<typeof SubcomponentSelectorButtonsWSlot>;
  completedSections?: Flex__<"div">;
  sectionsWithContent?: Flex__<"div">;
  introductionButtonEntry?: Flex__<typeof SubcomponentSelectorButtonsWSlot>;
  educationButtonEntry?: Flex__<typeof SubcomponentSelectorButtonsWSlot>;
  experienceButtonEntry?: Flex__<typeof SubcomponentSelectorButtonsWSlot>;
  skillsButtonEntry?: Flex__<typeof SubcomponentSelectorButtonsWSlot>;
  toolsButtonEntry?: Flex__<typeof SubcomponentSelectorButtonsWSlot>;
  languagesButtonEntry?: Flex__<typeof SubcomponentSelectorButtonsWSlot>;
  publicationsButtonEntry?: Flex__<typeof SubcomponentSelectorButtonsWSlot>;
  licensesButtonEntry?: Flex__<typeof SubcomponentSelectorButtonsWSlot>;
  certificationsButtonEntry?: Flex__<typeof SubcomponentSelectorButtonsWSlot>;
  patentsButtonEntry?: Flex__<typeof SubcomponentSelectorButtonsWSlot>;
  trademarksButtonEntry?: Flex__<typeof SubcomponentSelectorButtonsWSlot>;
};

export interface DefaultOnboardingProfileSectionModuleProps {
  sectionOnboarding?: SingleChoiceArg<"optionSelected" | "unnamedVariant2">;
  selection?: SingleChoiceArg<"seeMore" | "seeLess">;
  className?: string;
}

const $$ = {};

function useNextRouter() {
  try {
    return useRouter();
  } catch {}
  return undefined;
}

function PlasmicOnboardingProfileSectionModule__RenderFunc(props: {
  variants: PlasmicOnboardingProfileSectionModule__VariantsArgs;
  args: PlasmicOnboardingProfileSectionModule__ArgsType;
  overrides: PlasmicOnboardingProfileSectionModule__OverridesType;
  forNode?: string;
}) {
  const { variants, overrides, forNode } = props;

  const args = React.useMemo(
    () =>
      Object.assign(
        {},
        Object.fromEntries(
          Object.entries(props.args).filter(([_, v]) => v !== undefined)
        )
      ),
    [props.args]
  );

  const $props = {
    ...args,
    ...variants
  };

  const __nextRouter = useNextRouter();
  const $ctx = useDataEnv?.() || {};
  const refsRef = React.useRef({});
  const $refs = refsRef.current;

  let [$queries, setDollarQueries] = React.useState<
    Record<string, ReturnType<typeof usePlasmicDataOp>>
  >({});
  const stateSpecs: Parameters<typeof useDollarState>[0] = React.useMemo(
    () => [
      {
        path: "sectionOnboarding",
        type: "private",
        variableType: "variant",
        initFunc: ({ $props, $state, $queries, $ctx }) =>
          $props.sectionOnboarding
      },
      {
        path: "selection",
        type: "private",
        variableType: "variant",
        initFunc: ({ $props, $state, $queries, $ctx }) => $props.selection
      }
    ],
    [$props, $ctx, $refs]
  );
  const $state = useDollarState(stateSpecs, {
    $props,
    $ctx,
    $queries: $queries,
    $refs
  });

  const new$Queries: Record<string, ReturnType<typeof usePlasmicDataOp>> = {};
  if (Object.keys(new$Queries).some(k => new$Queries[k] !== $queries[k])) {
    setDollarQueries(new$Queries);

    $queries = new$Queries;
  }

  return (
    <div
      data-plasmic-name={"formattingContainer"}
      data-plasmic-override={overrides.formattingContainer}
      data-plasmic-root={true}
      data-plasmic-for-node={forNode}
      className={classNames(
        projectcss.all,
        projectcss.root_reset,
        projectcss.plasmic_default_styles,
        projectcss.plasmic_mixins,
        projectcss.plasmic_tokens,
        sty.formattingContainer,
        {
          [sty.formattingContainerselection_seeLess]: hasVariant(
            $state,
            "selection",
            "seeLess"
          )
        }
      )}
    >
      <div
        className={classNames(
          projectcss.all,
          projectcss.__wab_text,
          sty.text___3ZZpP,
          {
            [sty.textselection_seeLess___3ZZpPfYe9Z]: hasVariant(
              $state,
              "selection",
              "seeLess"
            )
          }
        )}
      >
        {
          "Let's start building your profile.\nSelect a section that you want to start filling out."
        }
      </div>
      <Stack__
        as={"div"}
        data-plasmic-name={"mainSectionButtons"}
        data-plasmic-override={overrides.mainSectionButtons}
        hasGap={true}
        className={classNames(projectcss.all, sty.mainSectionButtons, {
          [sty.mainSectionButtonsselection_seeMore]: hasVariant(
            $state,
            "selection",
            "seeMore"
          )
        })}
      >
        <SubcomponentSelectorButtonsWSlot
          data-plasmic-name={"profileBanner"}
          data-plasmic-override={overrides.profileBanner}
          className={classNames("__wab_instance", sty.profileBanner)}
          color={"green"}
          size={"medium"}
        >
          <UserIcon
            className={classNames(projectcss.all, sty.svg__ypD08)}
            role={"img"}
          />

          <div
            className={classNames(
              projectcss.all,
              projectcss.__wab_text,
              sty.text___5OBl5
            )}
          >
            {"Profile Banner"}
          </div>
        </SubcomponentSelectorButtonsWSlot>
        <SubcomponentSelectorButtonsWSlot
          data-plasmic-name={"introduction"}
          data-plasmic-override={overrides.introduction}
          className={classNames("__wab_instance", sty.introduction, {
            [sty.introductionsectionOnboarding_optionSelected]: hasVariant(
              $state,
              "sectionOnboarding",
              "optionSelected"
            )
          })}
          color={"green"}
          size={"medium"}
        >
          <PhotoCheckIcon
            className={classNames(projectcss.all, sty.svg__xLvtw)}
            role={"img"}
          />

          <div
            className={classNames(
              projectcss.all,
              projectcss.__wab_text,
              sty.text__cJrv1
            )}
          >
            {"Introduction"}
          </div>
        </SubcomponentSelectorButtonsWSlot>
        <SubcomponentSelectorButtonsWSlot
          data-plasmic-name={"educationButton"}
          data-plasmic-override={overrides.educationButton}
          className={classNames("__wab_instance", sty.educationButton, {
            [sty.educationButtonselection_seeMore]: hasVariant(
              $state,
              "selection",
              "seeMore"
            )
          })}
          size={"medium"}
        >
          <GradCapIcon
            className={classNames(projectcss.all, sty.svg__yBdoc)}
            role={"img"}
          />

          <div
            className={classNames(
              projectcss.all,
              projectcss.__wab_text,
              sty.text__gJqri
            )}
          >
            {"Education"}
          </div>
        </SubcomponentSelectorButtonsWSlot>
        <SubcomponentSelectorButtonsWSlot
          data-plasmic-name={"experienceButton"}
          data-plasmic-override={overrides.experienceButton}
          className={classNames("__wab_instance", sty.experienceButton)}
          size={"medium"}
        >
          <BuildingIcon
            className={classNames(projectcss.all, sty.svg___90Lqx)}
            role={"img"}
          />

          <div
            className={classNames(
              projectcss.all,
              projectcss.__wab_text,
              sty.text___6Gq1
            )}
          >
            {"Experiences"}
          </div>
        </SubcomponentSelectorButtonsWSlot>
        <SubcomponentSelectorButtonsWSlot
          data-plasmic-name={"toolsButton"}
          data-plasmic-override={overrides.toolsButton}
          className={classNames("__wab_instance", sty.toolsButton)}
          size={"medium"}
        >
          <ToolsIcon
            className={classNames(projectcss.all, sty.svg__d7J9S)}
            role={"img"}
          />

          <div
            className={classNames(
              projectcss.all,
              projectcss.__wab_text,
              sty.text__d8F25
            )}
          >
            {"Tools"}
          </div>
        </SubcomponentSelectorButtonsWSlot>
        <SubcomponentSelectorButtonsWSlot
          data-plasmic-name={"skillsButton"}
          data-plasmic-override={overrides.skillsButton}
          className={classNames("__wab_instance", sty.skillsButton)}
          size={"medium"}
        >
          <SkillsIcon
            className={classNames(projectcss.all, sty.svg__pAu)}
            role={"img"}
          />

          <div
            className={classNames(
              projectcss.all,
              projectcss.__wab_text,
              sty.text__pIr4V
            )}
          >
            {"Skills"}
          </div>
        </SubcomponentSelectorButtonsWSlot>
        <SubcomponentSelectorButtonsWSlot
          data-plasmic-name={"languagesButton"}
          data-plasmic-override={overrides.languagesButton}
          className={classNames("__wab_instance", sty.languagesButton)}
          size={"medium"}
        >
          <LanguagIcon
            className={classNames(projectcss.all, sty.svg__uGuEl)}
            role={"img"}
          />

          <div
            className={classNames(
              projectcss.all,
              projectcss.__wab_text,
              sty.text__gdbbJ
            )}
          >
            {"Languages"}
          </div>
        </SubcomponentSelectorButtonsWSlot>
        <SubcomponentSelectorButtonsWSlot
          data-plasmic-name={"patentsButton"}
          data-plasmic-override={overrides.patentsButton}
          className={classNames("__wab_instance", sty.patentsButton, {
            [sty.patentsButtonselection_seeMore]: hasVariant(
              $state,
              "selection",
              "seeMore"
            )
          })}
          size={"medium"}
        >
          <PatentedIcon
            className={classNames(projectcss.all, sty.svg__h9PL5)}
            role={"img"}
          />

          <div
            className={classNames(
              projectcss.all,
              projectcss.__wab_text,
              sty.text__nUmJn
            )}
          >
            {"Patents"}
          </div>
        </SubcomponentSelectorButtonsWSlot>
        <SubcomponentSelectorButtonsWSlot
          data-plasmic-name={"publicationsButton"}
          data-plasmic-override={overrides.publicationsButton}
          className={classNames("__wab_instance", sty.publicationsButton, {
            [sty.publicationsButtonselection_seeMore]: hasVariant(
              $state,
              "selection",
              "seeMore"
            )
          })}
          size={"medium"}
        >
          <PublicationBookIcon
            className={classNames(projectcss.all, sty.svg__vRpy0)}
            role={"img"}
          />

          <div
            className={classNames(
              projectcss.all,
              projectcss.__wab_text,
              sty.text__lTqE7
            )}
          >
            {"Publications"}
          </div>
        </SubcomponentSelectorButtonsWSlot>
        <SubcomponentSelectorButtonsWSlot
          data-plasmic-name={"certificationsButton"}
          data-plasmic-override={overrides.certificationsButton}
          className={classNames("__wab_instance", sty.certificationsButton, {
            [sty.certificationsButtonselection_seeMore]: hasVariant(
              $state,
              "selection",
              "seeMore"
            )
          })}
          size={"medium"}
        >
          <CertificateIcon
            className={classNames(projectcss.all, sty.svg___34E0Y)}
            role={"img"}
          />

          <div
            className={classNames(
              projectcss.all,
              projectcss.__wab_text,
              sty.text__zln2R
            )}
          >
            {"Certificates"}
          </div>
        </SubcomponentSelectorButtonsWSlot>
        <SubcomponentSelectorButtonsWSlot
          data-plasmic-name={"licensesButton"}
          data-plasmic-override={overrides.licensesButton}
          className={classNames("__wab_instance", sty.licensesButton, {
            [sty.licensesButtonselection_seeMore]: hasVariant(
              $state,
              "selection",
              "seeMore"
            )
          })}
          size={"medium"}
        >
          <LicenseIcon
            className={classNames(projectcss.all, sty.svg__gBu6)}
            role={"img"}
          />

          <div
            className={classNames(
              projectcss.all,
              projectcss.__wab_text,
              sty.text__gphE
            )}
          >
            {"Licenses"}
          </div>
        </SubcomponentSelectorButtonsWSlot>
        <SubcomponentSelectorButtonsWSlot
          data-plasmic-name={"trademarksButton"}
          data-plasmic-override={overrides.trademarksButton}
          className={classNames("__wab_instance", sty.trademarksButton, {
            [sty.trademarksButtonselection_seeMore]: hasVariant(
              $state,
              "selection",
              "seeMore"
            )
          })}
          size={"medium"}
        >
          <TrademarkIcon
            className={classNames(projectcss.all, sty.svg___8QP93)}
            role={"img"}
          />

          <div
            className={classNames(
              projectcss.all,
              projectcss.__wab_text,
              sty.text__irLzN
            )}
          >
            {"Trademarks"}
          </div>
        </SubcomponentSelectorButtonsWSlot>
        <SubcomponentSelectorButtonsWSlot
          data-plasmic-name={"seeLessButton2"}
          data-plasmic-override={overrides.seeLessButton2}
          className={classNames("__wab_instance", sty.seeLessButton2, {
            [sty.seeLessButton2selection_seeMore]: hasVariant(
              $state,
              "selection",
              "seeMore"
            )
          })}
          size={"medium"}
        >
          <PlusIcon
            className={classNames(projectcss.all, sty.svg__yPhTl)}
            role={"img"}
          />

          <div
            className={classNames(
              projectcss.all,
              projectcss.__wab_text,
              sty.text__xBcP1
            )}
          >
            {"More Sections"}
          </div>
        </SubcomponentSelectorButtonsWSlot>
        <SubcomponentSelectorButtonsWSlot
          data-plasmic-name={"seeLessButton"}
          data-plasmic-override={overrides.seeLessButton}
          className={classNames("__wab_instance", sty.seeLessButton, {
            [sty.seeLessButtonselection_seeMore]: hasVariant(
              $state,
              "selection",
              "seeMore"
            )
          })}
          size={"medium"}
        >
          <MinusIcon
            className={classNames(projectcss.all, sty.svg__p9Vn)}
            role={"img"}
          />

          <div
            className={classNames(
              projectcss.all,
              projectcss.__wab_text,
              sty.text___2YvFp
            )}
          >
            {"Collapse Sections"}
          </div>
        </SubcomponentSelectorButtonsWSlot>
      </Stack__>
      <div
        data-plasmic-name={"completedSections"}
        data-plasmic-override={overrides.completedSections}
        className={classNames(projectcss.all, sty.completedSections)}
      >
        <div
          className={classNames(
            projectcss.all,
            projectcss.__wab_text,
            sty.text__rUbev
          )}
        >
          {"Sections with Entries"}
        </div>
        <Stack__
          as={"div"}
          data-plasmic-name={"sectionsWithContent"}
          data-plasmic-override={overrides.sectionsWithContent}
          hasGap={true}
          className={classNames(projectcss.all, sty.sectionsWithContent)}
        >
          <SubcomponentSelectorButtonsWSlot
            data-plasmic-name={"introductionButtonEntry"}
            data-plasmic-override={overrides.introductionButtonEntry}
            className={classNames(
              "__wab_instance",
              sty.introductionButtonEntry
            )}
            color={"green"}
            size={"small"}
          >
            <UserIcon
              className={classNames(projectcss.all, sty.svg__uuHdw)}
              role={"img"}
            />

            <div
              className={classNames(
                projectcss.all,
                projectcss.__wab_text,
                sty.text__zwaI3
              )}
            >
              {"Introduction"}
            </div>
          </SubcomponentSelectorButtonsWSlot>
          <SubcomponentSelectorButtonsWSlot
            data-plasmic-name={"educationButtonEntry"}
            data-plasmic-override={overrides.educationButtonEntry}
            className={classNames("__wab_instance", sty.educationButtonEntry)}
            color={"green"}
            size={"small"}
          >
            <GradCapIcon
              className={classNames(projectcss.all, sty.svg___5SvUy)}
              role={"img"}
            />

            <div
              className={classNames(
                projectcss.all,
                projectcss.__wab_text,
                sty.text__paO5F
              )}
            >
              {"Education"}
            </div>
          </SubcomponentSelectorButtonsWSlot>
          <SubcomponentSelectorButtonsWSlot
            data-plasmic-name={"experienceButtonEntry"}
            data-plasmic-override={overrides.experienceButtonEntry}
            className={classNames("__wab_instance", sty.experienceButtonEntry)}
            color={"green"}
            size={"small"}
          >
            <BuildingIcon
              className={classNames(projectcss.all, sty.svg__jbaX)}
              role={"img"}
            />

            <div
              className={classNames(
                projectcss.all,
                projectcss.__wab_text,
                sty.text__kCqa
              )}
            >
              {"Experiences"}
            </div>
          </SubcomponentSelectorButtonsWSlot>
          <SubcomponentSelectorButtonsWSlot
            data-plasmic-name={"skillsButtonEntry"}
            data-plasmic-override={overrides.skillsButtonEntry}
            className={classNames("__wab_instance", sty.skillsButtonEntry)}
            color={"green"}
            size={"small"}
          >
            <SkillsIcon
              className={classNames(projectcss.all, sty.svg__m7GHp)}
              role={"img"}
            />

            <div
              className={classNames(
                projectcss.all,
                projectcss.__wab_text,
                sty.text__fbElQ
              )}
            >
              {"Skills"}
            </div>
          </SubcomponentSelectorButtonsWSlot>
          <SubcomponentSelectorButtonsWSlot
            data-plasmic-name={"toolsButtonEntry"}
            data-plasmic-override={overrides.toolsButtonEntry}
            className={classNames("__wab_instance", sty.toolsButtonEntry)}
            color={"green"}
            size={"small"}
          >
            <ToolsIcon
              className={classNames(projectcss.all, sty.svg___7FP6P)}
              role={"img"}
            />

            <div
              className={classNames(
                projectcss.all,
                projectcss.__wab_text,
                sty.text__zi6Gz
              )}
            >
              {"Tools"}
            </div>
          </SubcomponentSelectorButtonsWSlot>
          <SubcomponentSelectorButtonsWSlot
            data-plasmic-name={"languagesButtonEntry"}
            data-plasmic-override={overrides.languagesButtonEntry}
            className={classNames("__wab_instance", sty.languagesButtonEntry)}
            color={"green"}
            size={"small"}
          >
            <LanguagIcon
              className={classNames(projectcss.all, sty.svg__vByQw)}
              role={"img"}
            />

            <div
              className={classNames(
                projectcss.all,
                projectcss.__wab_text,
                sty.text__jjUn7
              )}
            >
              {"Languages"}
            </div>
          </SubcomponentSelectorButtonsWSlot>
          <SubcomponentSelectorButtonsWSlot
            data-plasmic-name={"publicationsButtonEntry"}
            data-plasmic-override={overrides.publicationsButtonEntry}
            className={classNames(
              "__wab_instance",
              sty.publicationsButtonEntry
            )}
            color={"green"}
            size={"small"}
          >
            <PublicationBookIcon
              className={classNames(projectcss.all, sty.svg__ovHd5)}
              role={"img"}
            />

            <div
              className={classNames(
                projectcss.all,
                projectcss.__wab_text,
                sty.text__bAeQn
              )}
            >
              {"Publications"}
            </div>
          </SubcomponentSelectorButtonsWSlot>
          <SubcomponentSelectorButtonsWSlot
            data-plasmic-name={"licensesButtonEntry"}
            data-plasmic-override={overrides.licensesButtonEntry}
            className={classNames("__wab_instance", sty.licensesButtonEntry)}
            color={"green"}
            size={"small"}
          >
            <LicenseIcon
              className={classNames(projectcss.all, sty.svg___9Ztn7)}
              role={"img"}
            />

            <div
              className={classNames(
                projectcss.all,
                projectcss.__wab_text,
                sty.text__g1HhZ
              )}
            >
              {"Licenses"}
            </div>
          </SubcomponentSelectorButtonsWSlot>
          <SubcomponentSelectorButtonsWSlot
            data-plasmic-name={"certificationsButtonEntry"}
            data-plasmic-override={overrides.certificationsButtonEntry}
            className={classNames(
              "__wab_instance",
              sty.certificationsButtonEntry
            )}
            color={"green"}
            size={"small"}
          >
            <CertificateIcon
              className={classNames(projectcss.all, sty.svg__ocBOm)}
              role={"img"}
            />

            <div
              className={classNames(
                projectcss.all,
                projectcss.__wab_text,
                sty.text__o7JVn
              )}
            >
              {"Certificates"}
            </div>
          </SubcomponentSelectorButtonsWSlot>
          <SubcomponentSelectorButtonsWSlot
            data-plasmic-name={"patentsButtonEntry"}
            data-plasmic-override={overrides.patentsButtonEntry}
            className={classNames("__wab_instance", sty.patentsButtonEntry)}
            color={"green"}
            size={"small"}
          >
            <PatentedIcon
              className={classNames(projectcss.all, sty.svg__gfrBd)}
              role={"img"}
            />

            <div
              className={classNames(
                projectcss.all,
                projectcss.__wab_text,
                sty.text__zix1X
              )}
            >
              {"Patents"}
            </div>
          </SubcomponentSelectorButtonsWSlot>
          <SubcomponentSelectorButtonsWSlot
            data-plasmic-name={"trademarksButtonEntry"}
            data-plasmic-override={overrides.trademarksButtonEntry}
            className={classNames("__wab_instance", sty.trademarksButtonEntry)}
            color={"green"}
            size={"small"}
          >
            <TrademarkIcon
              className={classNames(projectcss.all, sty.svg__juyro)}
              role={"img"}
            />

            <div
              className={classNames(
                projectcss.all,
                projectcss.__wab_text,
                sty.text__gQDoE
              )}
            >
              {"Trademarks"}
            </div>
          </SubcomponentSelectorButtonsWSlot>
        </Stack__>
      </div>
    </div>
  ) as React.ReactElement | null;
}

const PlasmicDescendants = {
  formattingContainer: [
    "formattingContainer",
    "mainSectionButtons",
    "profileBanner",
    "introduction",
    "educationButton",
    "experienceButton",
    "toolsButton",
    "skillsButton",
    "languagesButton",
    "patentsButton",
    "publicationsButton",
    "certificationsButton",
    "licensesButton",
    "trademarksButton",
    "seeLessButton2",
    "seeLessButton",
    "completedSections",
    "sectionsWithContent",
    "introductionButtonEntry",
    "educationButtonEntry",
    "experienceButtonEntry",
    "skillsButtonEntry",
    "toolsButtonEntry",
    "languagesButtonEntry",
    "publicationsButtonEntry",
    "licensesButtonEntry",
    "certificationsButtonEntry",
    "patentsButtonEntry",
    "trademarksButtonEntry"
  ],
  mainSectionButtons: [
    "mainSectionButtons",
    "profileBanner",
    "introduction",
    "educationButton",
    "experienceButton",
    "toolsButton",
    "skillsButton",
    "languagesButton",
    "patentsButton",
    "publicationsButton",
    "certificationsButton",
    "licensesButton",
    "trademarksButton",
    "seeLessButton2",
    "seeLessButton"
  ],
  profileBanner: ["profileBanner"],
  introduction: ["introduction"],
  educationButton: ["educationButton"],
  experienceButton: ["experienceButton"],
  toolsButton: ["toolsButton"],
  skillsButton: ["skillsButton"],
  languagesButton: ["languagesButton"],
  patentsButton: ["patentsButton"],
  publicationsButton: ["publicationsButton"],
  certificationsButton: ["certificationsButton"],
  licensesButton: ["licensesButton"],
  trademarksButton: ["trademarksButton"],
  seeLessButton2: ["seeLessButton2"],
  seeLessButton: ["seeLessButton"],
  completedSections: [
    "completedSections",
    "sectionsWithContent",
    "introductionButtonEntry",
    "educationButtonEntry",
    "experienceButtonEntry",
    "skillsButtonEntry",
    "toolsButtonEntry",
    "languagesButtonEntry",
    "publicationsButtonEntry",
    "licensesButtonEntry",
    "certificationsButtonEntry",
    "patentsButtonEntry",
    "trademarksButtonEntry"
  ],
  sectionsWithContent: [
    "sectionsWithContent",
    "introductionButtonEntry",
    "educationButtonEntry",
    "experienceButtonEntry",
    "skillsButtonEntry",
    "toolsButtonEntry",
    "languagesButtonEntry",
    "publicationsButtonEntry",
    "licensesButtonEntry",
    "certificationsButtonEntry",
    "patentsButtonEntry",
    "trademarksButtonEntry"
  ],
  introductionButtonEntry: ["introductionButtonEntry"],
  educationButtonEntry: ["educationButtonEntry"],
  experienceButtonEntry: ["experienceButtonEntry"],
  skillsButtonEntry: ["skillsButtonEntry"],
  toolsButtonEntry: ["toolsButtonEntry"],
  languagesButtonEntry: ["languagesButtonEntry"],
  publicationsButtonEntry: ["publicationsButtonEntry"],
  licensesButtonEntry: ["licensesButtonEntry"],
  certificationsButtonEntry: ["certificationsButtonEntry"],
  patentsButtonEntry: ["patentsButtonEntry"],
  trademarksButtonEntry: ["trademarksButtonEntry"]
} as const;
type NodeNameType = keyof typeof PlasmicDescendants;
type DescendantsType<T extends NodeNameType> =
  (typeof PlasmicDescendants)[T][number];
type NodeDefaultElementType = {
  formattingContainer: "div";
  mainSectionButtons: "div";
  profileBanner: typeof SubcomponentSelectorButtonsWSlot;
  introduction: typeof SubcomponentSelectorButtonsWSlot;
  educationButton: typeof SubcomponentSelectorButtonsWSlot;
  experienceButton: typeof SubcomponentSelectorButtonsWSlot;
  toolsButton: typeof SubcomponentSelectorButtonsWSlot;
  skillsButton: typeof SubcomponentSelectorButtonsWSlot;
  languagesButton: typeof SubcomponentSelectorButtonsWSlot;
  patentsButton: typeof SubcomponentSelectorButtonsWSlot;
  publicationsButton: typeof SubcomponentSelectorButtonsWSlot;
  certificationsButton: typeof SubcomponentSelectorButtonsWSlot;
  licensesButton: typeof SubcomponentSelectorButtonsWSlot;
  trademarksButton: typeof SubcomponentSelectorButtonsWSlot;
  seeLessButton2: typeof SubcomponentSelectorButtonsWSlot;
  seeLessButton: typeof SubcomponentSelectorButtonsWSlot;
  completedSections: "div";
  sectionsWithContent: "div";
  introductionButtonEntry: typeof SubcomponentSelectorButtonsWSlot;
  educationButtonEntry: typeof SubcomponentSelectorButtonsWSlot;
  experienceButtonEntry: typeof SubcomponentSelectorButtonsWSlot;
  skillsButtonEntry: typeof SubcomponentSelectorButtonsWSlot;
  toolsButtonEntry: typeof SubcomponentSelectorButtonsWSlot;
  languagesButtonEntry: typeof SubcomponentSelectorButtonsWSlot;
  publicationsButtonEntry: typeof SubcomponentSelectorButtonsWSlot;
  licensesButtonEntry: typeof SubcomponentSelectorButtonsWSlot;
  certificationsButtonEntry: typeof SubcomponentSelectorButtonsWSlot;
  patentsButtonEntry: typeof SubcomponentSelectorButtonsWSlot;
  trademarksButtonEntry: typeof SubcomponentSelectorButtonsWSlot;
};

type ReservedPropsType = "variants" | "args" | "overrides";
type NodeOverridesType<T extends NodeNameType> = Pick<
  PlasmicOnboardingProfileSectionModule__OverridesType,
  DescendantsType<T>
>;
type NodeComponentProps<T extends NodeNameType> =
  // Explicitly specify variants, args, and overrides as objects
  {
    variants?: PlasmicOnboardingProfileSectionModule__VariantsArgs;
    args?: PlasmicOnboardingProfileSectionModule__ArgsType;
    overrides?: NodeOverridesType<T>;
  } & Omit< // Specify variants directly as props
    PlasmicOnboardingProfileSectionModule__VariantsArgs,
    ReservedPropsType
  > &
    /* Specify args directly as props*/ Omit<
      PlasmicOnboardingProfileSectionModule__ArgsType,
      ReservedPropsType
    > &
    /* Specify overrides for each element directly as props*/ Omit<
      NodeOverridesType<T>,
      ReservedPropsType | VariantPropType | ArgPropType
    > &
    /* Specify props for the root element*/ Omit<
      Partial<React.ComponentProps<NodeDefaultElementType[T]>>,
      ReservedPropsType | VariantPropType | ArgPropType | DescendantsType<T>
    >;

function makeNodeComponent<NodeName extends NodeNameType>(nodeName: NodeName) {
  type PropsType = NodeComponentProps<NodeName> & { key?: React.Key };
  const func = function <T extends PropsType>(
    props: T & StrictProps<T, PropsType>
  ) {
    const { variants, args, overrides } = React.useMemo(
      () =>
        deriveRenderOpts(props, {
          name: nodeName,
          descendantNames: PlasmicDescendants[nodeName],
          internalArgPropNames: PlasmicOnboardingProfileSectionModule__ArgProps,
          internalVariantPropNames:
            PlasmicOnboardingProfileSectionModule__VariantProps
        }),
      [props, nodeName]
    );
    return PlasmicOnboardingProfileSectionModule__RenderFunc({
      variants,
      args,
      overrides,
      forNode: nodeName
    });
  };
  if (nodeName === "formattingContainer") {
    func.displayName = "PlasmicOnboardingProfileSectionModule";
  } else {
    func.displayName = `PlasmicOnboardingProfileSectionModule.${nodeName}`;
  }
  return func;
}

export const PlasmicOnboardingProfileSectionModule = Object.assign(
  // Top-level PlasmicOnboardingProfileSectionModule renders the root element
  makeNodeComponent("formattingContainer"),
  {
    // Helper components rendering sub-elements
    mainSectionButtons: makeNodeComponent("mainSectionButtons"),
    profileBanner: makeNodeComponent("profileBanner"),
    introduction: makeNodeComponent("introduction"),
    educationButton: makeNodeComponent("educationButton"),
    experienceButton: makeNodeComponent("experienceButton"),
    toolsButton: makeNodeComponent("toolsButton"),
    skillsButton: makeNodeComponent("skillsButton"),
    languagesButton: makeNodeComponent("languagesButton"),
    patentsButton: makeNodeComponent("patentsButton"),
    publicationsButton: makeNodeComponent("publicationsButton"),
    certificationsButton: makeNodeComponent("certificationsButton"),
    licensesButton: makeNodeComponent("licensesButton"),
    trademarksButton: makeNodeComponent("trademarksButton"),
    seeLessButton2: makeNodeComponent("seeLessButton2"),
    seeLessButton: makeNodeComponent("seeLessButton"),
    completedSections: makeNodeComponent("completedSections"),
    sectionsWithContent: makeNodeComponent("sectionsWithContent"),
    introductionButtonEntry: makeNodeComponent("introductionButtonEntry"),
    educationButtonEntry: makeNodeComponent("educationButtonEntry"),
    experienceButtonEntry: makeNodeComponent("experienceButtonEntry"),
    skillsButtonEntry: makeNodeComponent("skillsButtonEntry"),
    toolsButtonEntry: makeNodeComponent("toolsButtonEntry"),
    languagesButtonEntry: makeNodeComponent("languagesButtonEntry"),
    publicationsButtonEntry: makeNodeComponent("publicationsButtonEntry"),
    licensesButtonEntry: makeNodeComponent("licensesButtonEntry"),
    certificationsButtonEntry: makeNodeComponent("certificationsButtonEntry"),
    patentsButtonEntry: makeNodeComponent("patentsButtonEntry"),
    trademarksButtonEntry: makeNodeComponent("trademarksButtonEntry"),

    // Metadata about props expected for PlasmicOnboardingProfileSectionModule
    internalVariantProps: PlasmicOnboardingProfileSectionModule__VariantProps,
    internalArgProps: PlasmicOnboardingProfileSectionModule__ArgProps
  }
);

export default PlasmicOnboardingProfileSectionModule;
/* prettier-ignore-end */
