/* eslint-disable */
/* tslint:disable */
// @ts-nocheck
/* prettier-ignore-start */
import React from "react";
import { classNames } from "@plasmicapp/react-web";

export type CompassIconProps = React.ComponentProps<"svg"> & {
  title?: string;
};

export function CompassIcon(props: CompassIconProps) {
  const { className, style, title, ...restProps } = props;
  return (
    <svg
      xmlns={"http://www.w3.org/2000/svg"}
      className={classNames(
        "plasmic-default__svg",
        className,
        "icon icon-tabler icon-tabler-compass"
      )}
      viewBox={"0 0 24 24"}
      strokeWidth={"1.5"}
      stroke={"currentColor"}
      fill={"none"}
      strokeLinecap={"round"}
      strokeLinejoin={"round"}
      height={"1em"}
      style={style}
      {...restProps}
    >
      {title && <title>{title}</title>}

      <path d={"M0 0h24v24H0z"} fill={"none"} stroke={"none"}></path>

      <path d={"M8 16l2-6 6-2-2 6-6 2"}></path>

      <path
        d={"M3 12a9 9 0 1018 0 9 9 0 10-18 0m9-9v2m0 14v2m-9-9h2m14 0h2"}
      ></path>
    </svg>
  );
}

export default CompassIcon;
/* prettier-ignore-end */
