import { HTMLElementRefOf } from "@plasmicapp/react-web";
// This is a skeleton starter React component generated by Plasmic.
// This file is owned by you, feel free to edit as you see fit.
import * as React from "react";
import {
  DefaultProfileAboutSectionSelectorProps,
  PlasmicProfileAboutSectionSelector
} from "./autogenerated/senergy_platform/PlasmicProfileAboutSectionSelector";

// Your component props start with props for variants and slots you defined
// in Plasmic, but you can add more here, like event handlers that you can
// attach to named nodes in your component.
//
// If you don't want to expose certain variants or slots as a prop, you can use
// Omit to hide them:
//
// interface ProfileAboutSectionSelectorProps extends Omit<DefaultProfileAboutSectionSelectorProps, "hideProps1"|"hideProp2"> {
//   // etc.
// }
//
// You can also stop extending from DefaultProfileAboutSectionSelectorProps altogether and have
// total control over the props for your component.
export interface ProfileAboutSectionSelectorProps
  extends DefaultProfileAboutSectionSelectorProps {}

function ProfileAboutSectionSelector_(
  props: ProfileAboutSectionSelectorProps,
  ref: HTMLElementRefOf<"div">
) {
  // Use PlasmicProfileAboutSectionSelector to render this component as it was
  // designed in Plasmic, by activating the appropriate variants,
  // attaching the appropriate event handlers, etc.  You
  // can also install whatever React hooks you need here to manage state or
  // fetch data.
  //
  // Props you can pass into PlasmicProfileAboutSectionSelector are:
  // 1. Variants you want to activate,
  // 2. Contents for slots you want to fill,
  // 3. Overrides for any named node in the component to attach behavior and data,
  // 4. Props to set on the root node.
  //
  // By default, we are just piping all ProfileAboutSectionSelectorProps here, but feel free
  // to do whatever works for you.

  return <PlasmicProfileAboutSectionSelector root={{ ref }} {...props} />;
}

const ProfileAboutSectionSelector = React.forwardRef(
  ProfileAboutSectionSelector_
);
export default ProfileAboutSectionSelector;
