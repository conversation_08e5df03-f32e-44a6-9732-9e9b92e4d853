/** @type {import('next-sitemap').IConfig} */
module.exports = {
  siteUrl: process.env.NEXT_PUBLIC_SITE_URL || 'https://example.com',
  generateRobotsTxt: true,
  robotsTxtOptions: {
    policies: [
      {
        userAgent: '*',
        allow: '/',
      },
    ],
  },
  exclude: [
    '/server-sitemap.xml', // Exclude server-side generated sitemap
    '/api/*', // Exclude API routes
    '/admin/*', // Exclude admin pages
    '/404', // Exclude error pages
    '/500',
  ],
  generateIndexSitemap: false, // Set to true if your site has more than 50,000 URLs
  outDir: 'public',
  changefreq: 'daily',
  priority: 0.7,
};
