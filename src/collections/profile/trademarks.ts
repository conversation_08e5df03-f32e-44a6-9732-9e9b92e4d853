import type { Field } from 'payload';

const WORD_SPLIT_REGEX = /\s+/;

export const trademarksFields: Field[] = [
  {
    name: 'trademark_name',
    label: 'Trademark Name',
    type: 'text',
    required: true,
  },
  {
    name: 'registration_number',
    label: 'Registration Number',
    type: 'text',
    required: false,
  },
  {
    name: 'country_of_registration',
    label: 'Country of Registration',
    type: 'text',
    required: false,
  },
  {
    type: 'row',
    fields: [
      {
        name: 'registration_date',
        label: 'Registration Date',
        type: 'date',
        required: false,
      },
      {
        name: 'award_date',
        label: 'Award Date',
        type: 'date',
        required: false,
      },
      {
        name: 'registration_status',
        label: 'Registration Status',
        type: 'select',
        options: [
          { label: 'Registered', value: 'registered' },
          { label: 'Pending', value: 'pending' },
          { label: 'Cancelled', value: 'cancelled' },
          { label: 'Abandoned', value: 'abandoned' },
        ],
        required: false,
      },
    ],
  },
  {
    name: 'trademark_summary',
    label: 'Trademark Summary',
    type: 'textarea',
    admin: {
      description: 'Description of the trademark',
    },
    required: false,
    validate: (value: string | null | undefined) => {
      if (value && value.split(WORD_SPLIT_REGEX).length > 500) {
        return 'Summary must not exceed 500 words';
      }
      return true;
    },
  },
  {
    name: 'visible',
    type: 'checkbox',
    required: false,
    defaultValue: true,
  },
];
