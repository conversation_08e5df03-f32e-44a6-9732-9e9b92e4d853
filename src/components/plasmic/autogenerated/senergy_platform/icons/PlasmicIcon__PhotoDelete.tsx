/* eslint-disable */
/* tslint:disable */
// @ts-nocheck
/* prettier-ignore-start */
import React from "react";
import { classNames } from "@plasmicapp/react-web";

export type PhotoDeleteIconProps = React.ComponentProps<"svg"> & {
  title?: string;
};

export function PhotoDeleteIcon(props: PhotoDeleteIconProps) {
  const { className, style, title, ...restProps } = props;
  return (
    <svg
      xmlns={"http://www.w3.org/2000/svg"}
      className={classNames(
        "plasmic-default__svg",
        className,
        "icon icon-tabler icon-tabler-photo-minus"
      )}
      viewBox={"0 0 24 24"}
      strokeWidth={"1.5"}
      stroke={"currentColor"}
      fill={"none"}
      strokeLinecap={"round"}
      strokeLinejoin={"round"}
      height={"1em"}
      style={style}
      {...restProps}
    >
      {title && <title>{title}</title>}

      <path d={"M0 0h24v24H0z"} fill={"none"} stroke={"none"}></path>

      <path
        d={"M15 8h.01M12.5 21H6a3 3 0 01-3-3V6a3 3 0 013-3h12a3 3 0 013 3v9"}
      ></path>

      <path d={"M3 16l5-5c.928-.893 2.072-.893 3 0l4 4"}></path>

      <path d={"M14 14l1-1c.928-.893 2.072-.893 3 0l2 2m-4 4h6"}></path>
    </svg>
  );
}

export default PhotoDeleteIcon;
/* prettier-ignore-end */
