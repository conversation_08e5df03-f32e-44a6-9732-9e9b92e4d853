.root {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start;
  width: 100%;
  height: auto;
  max-width: 100%;
  background: var(--token-F0-tInDly4PE);
  position: relative;
  min-width: 0;
  padding: 0px var(--token-4Wrp9mDZCSCQ);
}
.freeBox___5BRu {
  display: flex;
  position: relative;
  flex-direction: row;
  width: 100%;
  height: auto;
  max-width: 100%;
  min-width: 0;
  padding: var(--token-sazGmnf7GWAk);
  margin: var(--token-sazGmnf7GWAk);
}
.freeBox___5BRu > :global(.__wab_flex-container) {
  flex-direction: row;
  align-items: center;
  justify-content: center;
  min-width: 0;
  margin-left: calc(0px - var(--token-4Wrp9mDZCSCQ));
  width: calc(100% + var(--token-4Wrp9mDZCSCQ));
}
.freeBox___5BRu > :global(.__wab_flex-container) > *,
.freeBox___5BRu > :global(.__wab_flex-container) > :global(.__wab_slot) > *,
.freeBox___5BRu > :global(.__wab_flex-container) > picture > img,
.freeBox___5BRu
  > :global(.__wab_flex-container)
  > :global(.__wab_slot)
  > picture
  > img {
  margin-left: var(--token-4Wrp9mDZCSCQ);
}
.freeBox__gqmMu {
  display: flex;
  position: relative;
  flex-direction: row;
  width: 100%;
  height: auto;
  max-width: 100%;
  margin-top: 8px;
  margin-right: 8px;
  margin-bottom: 8px;
  margin-left: calc(8px + var(--token-4Wrp9mDZCSCQ)) !important;
  min-width: 0;
}
.freeBox__gqmMu > :global(.__wab_flex-container) {
  flex-direction: row;
  align-items: center;
  justify-content: flex-start;
  min-width: 0;
  margin-left: calc(0px - var(--token-4Wrp9mDZCSCQ));
  width: calc(100% + var(--token-4Wrp9mDZCSCQ));
}
.freeBox__gqmMu > :global(.__wab_flex-container) > *,
.freeBox__gqmMu > :global(.__wab_flex-container) > :global(.__wab_slot) > *,
.freeBox__gqmMu > :global(.__wab_flex-container) > picture > img,
.freeBox__gqmMu
  > :global(.__wab_flex-container)
  > :global(.__wab_slot)
  > picture
  > img {
  margin-left: var(--token-4Wrp9mDZCSCQ);
}
.freeBoxprimary__gqmMudOdY {
  margin-left: calc(8px + var(--token-4Wrp9mDZCSCQ)) !important;
}
.freeBoxverified__gqmMugAa1K {
  margin-left: calc(8px + var(--token-4Wrp9mDZCSCQ)) !important;
}
.text {
  width: auto;
  height: auto;
  max-width: 100%;
}
.primaryTag:global(.__wab_instance) {
  max-width: 100%;
  position: relative;
  display: none;
}
.primaryTagprimary:global(.__wab_instance) {
  display: flex;
}
.iconSpot {
  position: relative;
  object-fit: cover;
  max-width: 100%;
  margin-right: 0;
  width: 100%;
  height: 100%;
  min-width: 0;
  min-height: 0;
}
.verifiedTag:global(.__wab_instance) {
  max-width: 100%;
  position: relative;
  display: none;
}
.verifiedTagverified:global(.__wab_instance) {
  display: flex;
}
.iconSpot2 {
  position: relative;
  object-fit: cover;
  max-width: 100%;
  margin-right: 0;
  width: 100%;
  height: 100%;
  min-width: 0;
  min-height: 0;
}
