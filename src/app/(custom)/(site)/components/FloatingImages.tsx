import { motion } from 'framer-motion';
import Image from 'next/image';
import Floating, { FloatingElement } from '@/components/ui/parallax-floating';
import { exampleImages } from '../data/example-images';

export interface FloatingImage {
  url: string;
  title: string;
  author?: string;
}

export interface FloatingImagesProps {
  images?: FloatingImage[];
  sensitivity?: number;
}

export function FloatingImages({
  images = [...exampleImages],
  sensitivity = -0.5,
}: FloatingImagesProps) {
  const imageConfigs = [
    {
      depth: 0.5,
      className: 'top-[15%] left-[2%] md:top-[25%] md:left-[5%]',
      imageClass:
        '-rotate-[3deg] h-12 w-16 sm:h-16 sm:w-24 md:h-20 md:w-28 lg:h-24 lg:w-32',
      delay: 0.5,
    },
    {
      depth: 1,
      className: 'top-[0%] left-[8%] md:top-[6%] md:left-[11%]',
      imageClass:
        '-rotate-12 h-28 w-40 sm:h-36 sm:w-48 md:h-44 md:w-56 lg:h-48 lg:w-60',
      delay: 0.7,
    },
    {
      depth: 4,
      className: 'top-[90%] left-[6%] md:top-[80%] md:left-[8%]',
      imageClass:
        '-rotate-[4deg] h-40 w-40 sm:h-48 sm:w-48 md:h-60 md:w-60 lg:h-64 lg:w-64',
      delay: 0.9,
    },
    {
      depth: 2,
      className: 'top-[0%] left-[87%] md:top-[2%] md:left-[83%]',
      imageClass:
        'h-36 w-40 rotate-[6deg] sm:h-44 sm:w-48 md:h-52 md:w-60 lg:h-56 lg:w-64',
      delay: 1.1,
    },
    {
      depth: 1,
      className: 'top-[78%] left-[83%] md:top-[68%] md:left-[83%]',
      imageClass:
        'h-44 w-44 rotate-[19deg] sm:h-64 sm:w-64 md:h-72 md:w-72 lg:h-80 lg:w-80',
      delay: 1.3,
    },
  ];

  return (
    <Floating className="h-full" sensitivity={sensitivity}>
      {images.slice(0, 5).map((image, index) => (
        <FloatingElement
          className={imageConfigs[index].className}
          depth={imageConfigs[index].depth}
          key={image.url}
        >
          <FloatingImage
            alt={image.title}
            className={imageConfigs[index].imageClass}
            delay={imageConfigs[index].delay}
            src={image.url}
          />
        </FloatingElement>
      ))}
    </Floating>
  );
}

interface FloatingImageProps {
  src: string;
  alt: string;
  className?: string;
  delay?: number;
}

function FloatingImage({
  src,
  alt,
  className = '',
  delay = 0,
}: FloatingImageProps) {
  return (
    <motion.div
      animate={{ opacity: 1 }}
      className={`relative cursor-pointer overflow-hidden rounded-xl shadow-2xl transition-transform duration-200 hover:scale-105 ${className}`}
      initial={{ opacity: 0 }}
      transition={{ delay }}
    >
      <Image
        alt={alt}
        className="object-cover"
        fill
        sizes="(max-width: 640px) 10rem, (max-width: 768px) 12rem, (max-width: 1024px) 15rem, 16rem"
        src={src}
      />
    </motion.div>
  );
}
