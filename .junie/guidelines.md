# Senergy Project Guidelines

## Project Overview
Senergy is a transformative platform for the emerging freelance economy, facilitating the assembly and management of highly effective teams. It connects business service providers with available projects, enhancing collaboration and productivity in a digital workspace that feels as personal and engaging as in-person interactions.

### Core Values
- **Transparency**: Open and clear communication is central to the platform's operation.
- **Collaboration**: Senergy emphasizes teamwork and the collective achievement of common objectives.
- **Innovation**: The platform encourages new ideas and creative problem-solving.
- **Nurturing**: Fostering growth and development among users and within the broader economy.

### Brand Identity
- Senergy's design blends earthy, natural elements with minimalistic, modern, and sometimes industrial touches.
- Aesthetic choices prioritize clean, modern designs with earthy, natural tones.
- The brand style is inspired by industrial design and typewriter fonts.

## Technology Stack

### NextJS
- We are using NextJS 15. Always use the latest NextJS features and best practices.
- Use modern JavaScript features like nullish coalescing (`??`) over logical OR (`||`).
- Use the `app` folder for API routes.
- Use pnpm instead of npm for package management.
- Always provide explicit type props for button elements.

### PayloadCMS
- We are using PayloadCMS 3.0.
- A fully initialized payload instance is available to be imported from `src/lib/payload.ts`.
- When importing from payload, always use `from 'payload'` and never from sub-modules like `payload/fields` or `payload/types`.
- Use types from `payload-types.ts` whenever possible. Do not create duplicate types.
- Be aware that nested relationships in Payload types (e.g., `User: String | User`) may be strings if not loaded.
- Use `zodToPayload` to validate fields.
- After modifying any collection (files in `src/collection`), run `pnpm generate:types` to update the types.
- Use PayloadCMS auto-generated APIs instead of creating custom routes.

### Plasmic
- We are using Plasmic codegen.
- Plasmic autogenerated components are located in `src/components/plasmic/.autogenerated`.
- Wrappers for autogenerated components are in `src/components/plasmic/`.
- You can modify the wrappers, but not the autogenerated components.
- Do not modify any Plasmic owned files or files in `src/components/plasmic/.autogenerated`.
- All properties for Plasmic wrapper components are in the root component file referenced in the wrapper.

## Coding Standards

### General Guidelines
- Always specify an explicit type for buttons.
- Always specify titles for SVGs.
- Break down new code into multiple reusable components for future use, better readability, and long-term maintenance.
- Always check for existing components before creating new ones. If appropriate, extend existing components to meet new use cases.

### Project Structure
- The project follows the NextJS app directory structure.
- API routes are in the `app` folder.
- Plasmic components are in `src/components/plasmic/`.
- PayloadCMS collections are in `src/collection`.

### Testing and Building
- Before submitting changes, ensure all components work as expected.
- Use the appropriate testing methods for the components you modify.
- Run `pnpm generate:types` after modifying PayloadCMS collections.
