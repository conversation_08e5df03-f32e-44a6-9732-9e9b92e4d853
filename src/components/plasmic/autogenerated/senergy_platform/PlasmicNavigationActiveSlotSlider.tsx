/* eslint-disable */
/* tslint:disable */
// @ts-nocheck
/* prettier-ignore-start */

/** @jsxRuntime classic */
/** @jsx createPlasmicElementProxy */
/** @jsxFrag React.Fragment */

// This class is auto-generated by Plasmic; please do not edit!
// Plasmic Project: nZeWKcn21KijvC7eT8B3c7
// Component: Jnr7Ch5x507b

"use client";

import * as React from "react";

import Head from "next/head";
import Link, { LinkProps } from "next/link";
import { useRouter } from "next/navigation";

import {
  Flex as Flex__,
  MultiChoiceArg,
  PlasmicDataSourceContextProvider as PlasmicDataSourceContextProvider__,
  PlasmicIcon as PlasmicIcon__,
  PlasmicImg as PlasmicImg__,
  PlasmicLink as PlasmicLink__,
  PlasmicPageGuard as PlasmicPageGuard__,
  SingleBooleanChoiceArg,
  SingleChoiceArg,
  Stack as Stack__,
  StrictProps,
  Trans as Trans__,
  classNames,
  createPlasmicElementProxy,
  deriveRenderOpts,
  ensureGlobalVariants,
  generateOnMutateForSpec,
  generateStateOnChangeProp,
  generateStateOnChangePropForCodeComponents,
  generateStateValueProp,
  get as $stateGet,
  hasVariant,
  initializeCodeComponentStates,
  initializePlasmicStates,
  makeFragment,
  omit,
  pick,
  renderPlasmicSlot,
  set as $stateSet,
  useCurrentUser,
  useDollarState,
  usePlasmicTranslator,
  useTrigger,
  wrapWithClassName
} from "@plasmicapp/react-web";
import {
  DataCtxReader as DataCtxReader__,
  useDataEnv,
  useGlobalActions
} from "@plasmicapp/host";

import "@plasmicapp/react-web/lib/plasmic.css";

import projectcss from "./plasmic.module.css"; // plasmic-import: nZeWKcn21KijvC7eT8B3c7/projectcss
import sty from "./PlasmicNavigationActiveSlotSlider.module.css"; // plasmic-import: Jnr7Ch5x507b/css

createPlasmicElementProxy;

export type PlasmicNavigationActiveSlotSlider__VariantMembers = {
  activeTab: "slot1" | "slot2" | "slot3";
  hoverTab: "slot1" | "slot2" | "slot3";
};
export type PlasmicNavigationActiveSlotSlider__VariantsArgs = {
  activeTab?: SingleChoiceArg<"slot1" | "slot2" | "slot3">;
  hoverTab?: SingleChoiceArg<"slot1" | "slot2" | "slot3">;
};
type VariantPropType = keyof PlasmicNavigationActiveSlotSlider__VariantsArgs;
export const PlasmicNavigationActiveSlotSlider__VariantProps =
  new Array<VariantPropType>("activeTab", "hoverTab");

export type PlasmicNavigationActiveSlotSlider__ArgsType = {};
type ArgPropType = keyof PlasmicNavigationActiveSlotSlider__ArgsType;
export const PlasmicNavigationActiveSlotSlider__ArgProps =
  new Array<ArgPropType>();

export type PlasmicNavigationActiveSlotSlider__OverridesType = {
  activeSlotSlider?: Flex__<"section">;
};

export interface DefaultNavigationActiveSlotSliderProps {
  activeTab?: SingleChoiceArg<"slot1" | "slot2" | "slot3">;
  hoverTab?: SingleChoiceArg<"slot1" | "slot2" | "slot3">;
  className?: string;
}

const $$ = {};

function useNextRouter() {
  try {
    return useRouter();
  } catch {}
  return undefined;
}

function PlasmicNavigationActiveSlotSlider__RenderFunc(props: {
  variants: PlasmicNavigationActiveSlotSlider__VariantsArgs;
  args: PlasmicNavigationActiveSlotSlider__ArgsType;
  overrides: PlasmicNavigationActiveSlotSlider__OverridesType;
  forNode?: string;
}) {
  const { variants, overrides, forNode } = props;

  const args = React.useMemo(
    () =>
      Object.assign(
        {},
        Object.fromEntries(
          Object.entries(props.args).filter(([_, v]) => v !== undefined)
        )
      ),
    [props.args]
  );

  const $props = {
    ...args,
    ...variants
  };

  const __nextRouter = useNextRouter();
  const $ctx = useDataEnv?.() || {};
  const refsRef = React.useRef({});
  const $refs = refsRef.current;

  let [$queries, setDollarQueries] = React.useState<
    Record<string, ReturnType<typeof usePlasmicDataOp>>
  >({});
  const stateSpecs: Parameters<typeof useDollarState>[0] = React.useMemo(
    () => [
      {
        path: "activeTab",
        type: "private",
        variableType: "variant",
        initFunc: ({ $props, $state, $queries, $ctx }) => $props.activeTab
      },
      {
        path: "hoverTab",
        type: "private",
        variableType: "variant",
        initFunc: ({ $props, $state, $queries, $ctx }) => $props.hoverTab
      }
    ],
    [$props, $ctx, $refs]
  );
  const $state = useDollarState(stateSpecs, {
    $props,
    $ctx,
    $queries: $queries,
    $refs
  });

  const new$Queries: Record<string, ReturnType<typeof usePlasmicDataOp>> = {};
  if (Object.keys(new$Queries).some(k => new$Queries[k] !== $queries[k])) {
    setDollarQueries(new$Queries);

    $queries = new$Queries;
  }

  return (
    <section
      data-plasmic-name={"activeSlotSlider"}
      data-plasmic-override={overrides.activeSlotSlider}
      data-plasmic-root={true}
      data-plasmic-for-node={forNode}
      className={classNames(
        projectcss.all,
        projectcss.root_reset,
        projectcss.plasmic_default_styles,
        projectcss.plasmic_mixins,
        projectcss.plasmic_tokens,
        sty.activeSlotSlider,
        {
          [sty.activeSlotSlideractiveTab_slot1]: hasVariant(
            $state,
            "activeTab",
            "slot1"
          ),
          [sty.activeSlotSlideractiveTab_slot2]: hasVariant(
            $state,
            "activeTab",
            "slot2"
          ),
          [sty.activeSlotSlideractiveTab_slot3]: hasVariant(
            $state,
            "activeTab",
            "slot3"
          ),
          [sty.activeSlotSliderhoverTab_slot1]: hasVariant(
            $state,
            "hoverTab",
            "slot1"
          ),
          [sty.activeSlotSliderhoverTab_slot2]: hasVariant(
            $state,
            "hoverTab",
            "slot2"
          ),
          [sty.activeSlotSliderhoverTab_slot3]: hasVariant(
            $state,
            "hoverTab",
            "slot3"
          )
        }
      )}
    />
  ) as React.ReactElement | null;
}

const PlasmicDescendants = {
  activeSlotSlider: ["activeSlotSlider"]
} as const;
type NodeNameType = keyof typeof PlasmicDescendants;
type DescendantsType<T extends NodeNameType> =
  (typeof PlasmicDescendants)[T][number];
type NodeDefaultElementType = {
  activeSlotSlider: "section";
};

type ReservedPropsType = "variants" | "args" | "overrides";
type NodeOverridesType<T extends NodeNameType> = Pick<
  PlasmicNavigationActiveSlotSlider__OverridesType,
  DescendantsType<T>
>;
type NodeComponentProps<T extends NodeNameType> =
  // Explicitly specify variants, args, and overrides as objects
  {
    variants?: PlasmicNavigationActiveSlotSlider__VariantsArgs;
    args?: PlasmicNavigationActiveSlotSlider__ArgsType;
    overrides?: NodeOverridesType<T>;
  } & Omit<PlasmicNavigationActiveSlotSlider__VariantsArgs, ReservedPropsType> & // Specify variants directly as props
    /* Specify args directly as props*/ Omit<
      PlasmicNavigationActiveSlotSlider__ArgsType,
      ReservedPropsType
    > &
    /* Specify overrides for each element directly as props*/ Omit<
      NodeOverridesType<T>,
      ReservedPropsType | VariantPropType | ArgPropType
    > &
    /* Specify props for the root element*/ Omit<
      Partial<React.ComponentProps<NodeDefaultElementType[T]>>,
      ReservedPropsType | VariantPropType | ArgPropType | DescendantsType<T>
    >;

function makeNodeComponent<NodeName extends NodeNameType>(nodeName: NodeName) {
  type PropsType = NodeComponentProps<NodeName> & { key?: React.Key };
  const func = function <T extends PropsType>(
    props: T & StrictProps<T, PropsType>
  ) {
    const { variants, args, overrides } = React.useMemo(
      () =>
        deriveRenderOpts(props, {
          name: nodeName,
          descendantNames: PlasmicDescendants[nodeName],
          internalArgPropNames: PlasmicNavigationActiveSlotSlider__ArgProps,
          internalVariantPropNames:
            PlasmicNavigationActiveSlotSlider__VariantProps
        }),
      [props, nodeName]
    );
    return PlasmicNavigationActiveSlotSlider__RenderFunc({
      variants,
      args,
      overrides,
      forNode: nodeName
    });
  };
  if (nodeName === "activeSlotSlider") {
    func.displayName = "PlasmicNavigationActiveSlotSlider";
  } else {
    func.displayName = `PlasmicNavigationActiveSlotSlider.${nodeName}`;
  }
  return func;
}

export const PlasmicNavigationActiveSlotSlider = Object.assign(
  // Top-level PlasmicNavigationActiveSlotSlider renders the root element
  makeNodeComponent("activeSlotSlider"),
  {
    // Helper components rendering sub-elements

    // Metadata about props expected for PlasmicNavigationActiveSlotSlider
    internalVariantProps: PlasmicNavigationActiveSlotSlider__VariantProps,
    internalArgProps: PlasmicNavigationActiveSlotSlider__ArgProps
  }
);

export default PlasmicNavigationActiveSlotSlider;
/* prettier-ignore-end */
