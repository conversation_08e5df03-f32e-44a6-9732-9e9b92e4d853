/* eslint-disable */
/* tslint:disable */
// @ts-nocheck
/* prettier-ignore-start */

/** @jsxRuntime classic */
/** @jsx createPlasmicElementProxy */
/** @jsxFrag React.Fragment */

// This class is auto-generated by Plasmic; please do not edit!
// Plasmic Project: nZeWKcn21KijvC7eT8B3c7
// Component: vFlsxZd2-jZM

"use client";

import * as React from "react";

import Head from "next/head";
import Link, { LinkProps } from "next/link";
import { useRouter } from "next/navigation";

import {
  Flex as Flex__,
  MultiChoiceArg,
  PlasmicDataSourceContextProvider as PlasmicDataSourceContextProvider__,
  PlasmicIcon as PlasmicIcon__,
  PlasmicImg as PlasmicImg__,
  PlasmicLink as PlasmicLink__,
  PlasmicPageGuard as PlasmicPageGuard__,
  SingleBooleanChoiceArg,
  SingleChoiceArg,
  Stack as Stack__,
  StrictProps,
  Trans as Trans__,
  classNames,
  createPlasmicElementProxy,
  deriveRenderOpts,
  ensureGlobalVariants,
  generateOnMutateForSpec,
  generateStateOnChangeProp,
  generateStateOnChangePropForCodeComponents,
  generateStateValueProp,
  get as $stateGet,
  hasVariant,
  initializeCodeComponentStates,
  initializePlasmicStates,
  makeFragment,
  omit,
  pick,
  renderPlasmicSlot,
  set as $stateSet,
  useCurrentUser,
  useDollarState,
  usePlasmicTranslator,
  useTrigger,
  wrapWithClassName
} from "@plasmicapp/react-web";
import {
  DataCtxReader as DataCtxReader__,
  useDataEnv,
  useGlobalActions
} from "@plasmicapp/host";

import SubcomponentButton from "../../SubcomponentButton"; // plasmic-import: ezhuRZvm_fH9/component
import { Fetcher } from "@plasmicapp/react-web/lib/data-sources";

import "@plasmicapp/react-web/lib/plasmic.css";

import projectcss from "./plasmic.module.css"; // plasmic-import: nZeWKcn21KijvC7eT8B3c7/projectcss
import sty from "./PlasmicSubcomponentAddSectionButton.module.css"; // plasmic-import: vFlsxZd2-jZM/css

import ChevronDownIcon from "./icons/PlasmicIcon__ChevronDown"; // plasmic-import: UW2ddwYm4xaW/icon
import ChevronUpIcon from "./icons/PlasmicIcon__ChevronUp"; // plasmic-import: e6s2KgA0Bmlu/icon

createPlasmicElementProxy;

export type PlasmicSubcomponentAddSectionButton__VariantMembers = {
  patentOrTrademark: "collapsed" | "expanded";
  licenseOrCertification: "collapsed" | "expanded";
};
export type PlasmicSubcomponentAddSectionButton__VariantsArgs = {
  patentOrTrademark?: SingleChoiceArg<"collapsed" | "expanded">;
  licenseOrCertification?: SingleChoiceArg<"collapsed" | "expanded">;
};
type VariantPropType = keyof PlasmicSubcomponentAddSectionButton__VariantsArgs;
export const PlasmicSubcomponentAddSectionButton__VariantProps =
  new Array<VariantPropType>("patentOrTrademark", "licenseOrCertification");

export type PlasmicSubcomponentAddSectionButton__ArgsType = {
  onClickBase?: (event: any) => void;
  onClickPatent?: (event: any) => void;
  onClickTrademark?: (event: any) => void;
  onClickLicense?: (event: any) => void;
  onClickCertification?: (event: any) => void;
};
type ArgPropType = keyof PlasmicSubcomponentAddSectionButton__ArgsType;
export const PlasmicSubcomponentAddSectionButton__ArgProps =
  new Array<ArgPropType>(
    "onClickBase",
    "onClickPatent",
    "onClickTrademark",
    "onClickLicense",
    "onClickCertification"
  );

export type PlasmicSubcomponentAddSectionButton__OverridesType = {
  root?: Flex__<"div">;
  addSectionButton?: Flex__<typeof SubcomponentButton>;
  addSectionButtonPatentOrTrademark?: Flex__<typeof SubcomponentButton>;
  addSectionButtonLicenseOrCertification?: Flex__<typeof SubcomponentButton>;
  freeBoxForDropDown?: Flex__<"section">;
  addPatent?: Flex__<typeof SubcomponentButton>;
  addTrademark?: Flex__<typeof SubcomponentButton>;
  addLicense?: Flex__<typeof SubcomponentButton>;
  addCertification?: Flex__<typeof SubcomponentButton>;
};

export interface DefaultSubcomponentAddSectionButtonProps {
  onClickBase?: (event: any) => void;
  onClickPatent?: (event: any) => void;
  onClickTrademark?: (event: any) => void;
  onClickLicense?: (event: any) => void;
  onClickCertification?: (event: any) => void;
  patentOrTrademark?: SingleChoiceArg<"collapsed" | "expanded">;
  licenseOrCertification?: SingleChoiceArg<"collapsed" | "expanded">;
  className?: string;
}

const $$ = {};

function useNextRouter() {
  try {
    return useRouter();
  } catch {}
  return undefined;
}

function PlasmicSubcomponentAddSectionButton__RenderFunc(props: {
  variants: PlasmicSubcomponentAddSectionButton__VariantsArgs;
  args: PlasmicSubcomponentAddSectionButton__ArgsType;
  overrides: PlasmicSubcomponentAddSectionButton__OverridesType;
  forNode?: string;
}) {
  const { variants, overrides, forNode } = props;

  const args = React.useMemo(
    () =>
      Object.assign(
        {},
        Object.fromEntries(
          Object.entries(props.args).filter(([_, v]) => v !== undefined)
        )
      ),
    [props.args]
  );

  const $props = {
    ...args,
    ...variants
  };

  const __nextRouter = useNextRouter();
  const $ctx = useDataEnv?.() || {};
  const refsRef = React.useRef({});
  const $refs = refsRef.current;

  let [$queries, setDollarQueries] = React.useState<
    Record<string, ReturnType<typeof usePlasmicDataOp>>
  >({});
  const stateSpecs: Parameters<typeof useDollarState>[0] = React.useMemo(
    () => [
      {
        path: "patentOrTrademark",
        type: "private",
        variableType: "variant",
        initFunc: ({ $props, $state, $queries, $ctx }) =>
          $props.patentOrTrademark
      },
      {
        path: "licenseOrCertification",
        type: "private",
        variableType: "variant",
        initFunc: ({ $props, $state, $queries, $ctx }) =>
          $props.licenseOrCertification
      }
    ],
    [$props, $ctx, $refs]
  );
  const $state = useDollarState(stateSpecs, {
    $props,
    $ctx,
    $queries: $queries,
    $refs
  });

  const new$Queries: Record<string, ReturnType<typeof usePlasmicDataOp>> = {};
  if (Object.keys(new$Queries).some(k => new$Queries[k] !== $queries[k])) {
    setDollarQueries(new$Queries);

    $queries = new$Queries;
  }

  return (
    <div
      data-plasmic-name={"root"}
      data-plasmic-override={overrides.root}
      data-plasmic-root={true}
      data-plasmic-for-node={forNode}
      className={classNames(
        projectcss.all,
        projectcss.root_reset,
        projectcss.plasmic_default_styles,
        projectcss.plasmic_mixins,
        projectcss.plasmic_tokens,
        sty.root,
        {
          [sty.rootpatentOrTrademark_collapsed]: hasVariant(
            $state,
            "patentOrTrademark",
            "collapsed"
          ),
          [sty.rootpatentOrTrademark_expanded]: hasVariant(
            $state,
            "patentOrTrademark",
            "expanded"
          )
        }
      )}
    >
      <SubcomponentButton
        data-plasmic-name={"addSectionButton"}
        data-plasmic-override={overrides.addSectionButton}
        className={classNames("__wab_instance", sty.addSectionButton, {
          [sty.addSectionButtonlicenseOrCertification_collapsed]: hasVariant(
            $state,
            "licenseOrCertification",
            "collapsed"
          ),
          [sty.addSectionButtonlicenseOrCertification_expanded]: hasVariant(
            $state,
            "licenseOrCertification",
            "expanded"
          ),
          [sty.addSectionButtonpatentOrTrademark_collapsed]: hasVariant(
            $state,
            "patentOrTrademark",
            "collapsed"
          ),
          [sty.addSectionButtonpatentOrTrademark_expanded]: hasVariant(
            $state,
            "patentOrTrademark",
            "expanded"
          )
        })}
        endIcon={
          <svg
            className={classNames(projectcss.all, sty.svg__qvIpd)}
            role={"img"}
          />
        }
        onClick={args.onClickBase}
        startIcon={
          <svg
            className={classNames(projectcss.all, sty.svg__bcDkS)}
            role={"img"}
          />
        }
        styling={["nittiWColor"]}
      >
        <div
          className={classNames(
            projectcss.all,
            projectcss.__wab_text,
            sty.text__iatjy
          )}
        >
          {"+ Add Entry"}
        </div>
      </SubcomponentButton>
      <SubcomponentButton
        data-plasmic-name={"addSectionButtonPatentOrTrademark"}
        data-plasmic-override={overrides.addSectionButtonPatentOrTrademark}
        className={classNames(
          "__wab_instance",
          sty.addSectionButtonPatentOrTrademark,
          {
            [sty.addSectionButtonPatentOrTrademarklicenseOrCertification_collapsed]:
              hasVariant($state, "licenseOrCertification", "collapsed"),
            [sty.addSectionButtonPatentOrTrademarklicenseOrCertification_expanded]:
              hasVariant($state, "licenseOrCertification", "expanded"),
            [sty.addSectionButtonPatentOrTrademarkpatentOrTrademark_collapsed]:
              hasVariant($state, "patentOrTrademark", "collapsed"),
            [sty.addSectionButtonPatentOrTrademarkpatentOrTrademark_expanded]:
              hasVariant($state, "patentOrTrademark", "expanded")
          }
        )}
        endIcon={
          <svg
            className={classNames(projectcss.all, sty.svg__xTvqM)}
            role={"img"}
          />
        }
        onClick={async event => {
          const $steps = {};

          $steps["runCode"] = true
            ? (() => {
                const actionArgs = {
                  customFunction: async () => {
                    return (() => {
                      return ($state.patentOrTrademark =
                        $state.patentOrTrademark == "collapsed"
                          ? "expanded"
                          : "collapsed");
                    })();
                  }
                };
                return (({ customFunction }) => {
                  return customFunction();
                })?.apply(null, [actionArgs]);
              })()
            : undefined;
          if (
            $steps["runCode"] != null &&
            typeof $steps["runCode"] === "object" &&
            typeof $steps["runCode"].then === "function"
          ) {
            $steps["runCode"] = await $steps["runCode"];
          }
        }}
        showStartIcon={true}
        startIcon={
          <PlasmicIcon__
            PlasmicIconType={
              hasVariant($state, "patentOrTrademark", "expanded")
                ? ChevronUpIcon
                : ChevronDownIcon
            }
            className={classNames(projectcss.all, sty.svg__v0ODt, {
              [sty.svgpatentOrTrademark_expanded__v0ODtWKgTp]: hasVariant(
                $state,
                "patentOrTrademark",
                "expanded"
              )
            })}
            role={"img"}
          />
        }
        styling={["nittiWColor"]}
      >
        <div
          className={classNames(
            projectcss.all,
            projectcss.__wab_text,
            sty.text__xogXi
          )}
        >
          {"Add Section"}
        </div>
      </SubcomponentButton>
      <SubcomponentButton
        data-plasmic-name={"addSectionButtonLicenseOrCertification"}
        data-plasmic-override={overrides.addSectionButtonLicenseOrCertification}
        className={classNames(
          "__wab_instance",
          sty.addSectionButtonLicenseOrCertification,
          {
            [sty.addSectionButtonLicenseOrCertificationlicenseOrCertification_collapsed]:
              hasVariant($state, "licenseOrCertification", "collapsed"),
            [sty.addSectionButtonLicenseOrCertificationlicenseOrCertification_expanded]:
              hasVariant($state, "licenseOrCertification", "expanded"),
            [sty.addSectionButtonLicenseOrCertificationpatentOrTrademark_collapsed]:
              hasVariant($state, "patentOrTrademark", "collapsed"),
            [sty.addSectionButtonLicenseOrCertificationpatentOrTrademark_expanded]:
              hasVariant($state, "patentOrTrademark", "expanded")
          }
        )}
        endIcon={
          <svg
            className={classNames(projectcss.all, sty.svg__baRvG)}
            role={"img"}
          />
        }
        onClick={async event => {
          const $steps = {};

          $steps["runCode"] = true
            ? (() => {
                const actionArgs = {
                  customFunction: async () => {
                    return ($state.licenseOrCertification =
                      $state.licenseOrCertification == "collapsed"
                        ? "expanded"
                        : "collapsed");
                  }
                };
                return (({ customFunction }) => {
                  return customFunction();
                })?.apply(null, [actionArgs]);
              })()
            : undefined;
          if (
            $steps["runCode"] != null &&
            typeof $steps["runCode"] === "object" &&
            typeof $steps["runCode"].then === "function"
          ) {
            $steps["runCode"] = await $steps["runCode"];
          }
        }}
        showStartIcon={true}
        startIcon={
          <PlasmicIcon__
            PlasmicIconType={
              hasVariant($state, "licenseOrCertification", "expanded")
                ? ChevronUpIcon
                : hasVariant($state, "licenseOrCertification", "collapsed")
                ? ChevronDownIcon
                : "div"
            }
            className={classNames(projectcss.all, sty.svg__pzKyD, {
              [sty.svglicenseOrCertification_collapsed__pzKyDfgW8O]: hasVariant(
                $state,
                "licenseOrCertification",
                "collapsed"
              ),
              [sty.svglicenseOrCertification_expanded__pzKyDp68Or]: hasVariant(
                $state,
                "licenseOrCertification",
                "expanded"
              )
            })}
            role={"img"}
          />
        }
        styling={["nittiWColor"]}
      >
        <div
          className={classNames(
            projectcss.all,
            projectcss.__wab_text,
            sty.text__rmqA
          )}
        >
          {"Add Section"}
        </div>
      </SubcomponentButton>
      <section
        data-plasmic-name={"freeBoxForDropDown"}
        data-plasmic-override={overrides.freeBoxForDropDown}
        className={classNames(projectcss.all, sty.freeBoxForDropDown, {
          [sty.freeBoxForDropDownlicenseOrCertification_collapsed]: hasVariant(
            $state,
            "licenseOrCertification",
            "collapsed"
          ),
          [sty.freeBoxForDropDownlicenseOrCertification_expanded]: hasVariant(
            $state,
            "licenseOrCertification",
            "expanded"
          ),
          [sty.freeBoxForDropDownpatentOrTrademark_collapsed]: hasVariant(
            $state,
            "patentOrTrademark",
            "collapsed"
          ),
          [sty.freeBoxForDropDownpatentOrTrademark_expanded]: hasVariant(
            $state,
            "patentOrTrademark",
            "expanded"
          )
        })}
      >
        <SubcomponentButton
          data-plasmic-name={"addPatent"}
          data-plasmic-override={overrides.addPatent}
          className={classNames("__wab_instance", sty.addPatent, {
            [sty.addPatentlicenseOrCertification_expanded]: hasVariant(
              $state,
              "licenseOrCertification",
              "expanded"
            ),
            [sty.addPatentpatentOrTrademark_collapsed]: hasVariant(
              $state,
              "patentOrTrademark",
              "collapsed"
            )
          })}
          endIcon={
            <svg
              className={classNames(projectcss.all, sty.svg__r31FS)}
              role={"img"}
            />
          }
          justification={"left"}
          onClick={args.onClickPatent}
          onClickCapture={async event => {
            const $steps = {};

            $steps["updatePatentOrTrademark"] = true
              ? (() => {
                  const actionArgs = {
                    vgroup: "patentOrTrademark",
                    operation: 0,
                    value: "collapsed"
                  };
                  return (({ vgroup, value }) => {
                    if (typeof value === "string") {
                      value = [value];
                    }

                    $stateSet($state, vgroup, value);
                    return value;
                  })?.apply(null, [actionArgs]);
                })()
              : undefined;
            if (
              $steps["updatePatentOrTrademark"] != null &&
              typeof $steps["updatePatentOrTrademark"] === "object" &&
              typeof $steps["updatePatentOrTrademark"].then === "function"
            ) {
              $steps["updatePatentOrTrademark"] = await $steps[
                "updatePatentOrTrademark"
              ];
            }
          }}
          startIcon={
            <svg
              className={classNames(projectcss.all, sty.svg__uUmnv)}
              role={"img"}
            />
          }
          styling={["nittiWColor"]}
        >
          {"+ Patent"}
        </SubcomponentButton>
        <SubcomponentButton
          data-plasmic-name={"addTrademark"}
          data-plasmic-override={overrides.addTrademark}
          className={classNames("__wab_instance", sty.addTrademark, {
            [sty.addTrademarklicenseOrCertification_expanded]: hasVariant(
              $state,
              "licenseOrCertification",
              "expanded"
            ),
            [sty.addTrademarkpatentOrTrademark_collapsed]: hasVariant(
              $state,
              "patentOrTrademark",
              "collapsed"
            )
          })}
          endIcon={
            <svg
              className={classNames(projectcss.all, sty.svg__vyUdg)}
              role={"img"}
            />
          }
          justification={"left"}
          onClick={args.onClickTrademark}
          onClickCapture={async event => {
            const $steps = {};

            $steps["updatePatentOrTrademark"] = true
              ? (() => {
                  const actionArgs = {
                    vgroup: "patentOrTrademark",
                    operation: 0,
                    value: "collapsed"
                  };
                  return (({ vgroup, value }) => {
                    if (typeof value === "string") {
                      value = [value];
                    }

                    $stateSet($state, vgroup, value);
                    return value;
                  })?.apply(null, [actionArgs]);
                })()
              : undefined;
            if (
              $steps["updatePatentOrTrademark"] != null &&
              typeof $steps["updatePatentOrTrademark"] === "object" &&
              typeof $steps["updatePatentOrTrademark"].then === "function"
            ) {
              $steps["updatePatentOrTrademark"] = await $steps[
                "updatePatentOrTrademark"
              ];
            }
          }}
          startIcon={
            <svg
              className={classNames(projectcss.all, sty.svg__dcn1K)}
              role={"img"}
            />
          }
          styling={["nittiWColor"]}
        >
          {"+ Trademark"}
        </SubcomponentButton>
        <SubcomponentButton
          data-plasmic-name={"addLicense"}
          data-plasmic-override={overrides.addLicense}
          className={classNames("__wab_instance", sty.addLicense, {
            [sty.addLicensepatentOrTrademark_collapsed]: hasVariant(
              $state,
              "patentOrTrademark",
              "collapsed"
            ),
            [sty.addLicensepatentOrTrademark_expanded]: hasVariant(
              $state,
              "patentOrTrademark",
              "expanded"
            )
          })}
          endIcon={
            <svg
              className={classNames(projectcss.all, sty.svg__wkxKc)}
              role={"img"}
            />
          }
          justification={"left"}
          onClick={args.onClickLicense}
          onClickCapture={async event => {
            const $steps = {};

            $steps["updateLicenseOrCertification"] = true
              ? (() => {
                  const actionArgs = {
                    vgroup: "licenseOrCertification",
                    operation: 0,
                    value: "collapsed"
                  };
                  return (({ vgroup, value }) => {
                    if (typeof value === "string") {
                      value = [value];
                    }

                    $stateSet($state, vgroup, value);
                    return value;
                  })?.apply(null, [actionArgs]);
                })()
              : undefined;
            if (
              $steps["updateLicenseOrCertification"] != null &&
              typeof $steps["updateLicenseOrCertification"] === "object" &&
              typeof $steps["updateLicenseOrCertification"].then === "function"
            ) {
              $steps["updateLicenseOrCertification"] = await $steps[
                "updateLicenseOrCertification"
              ];
            }
          }}
          startIcon={
            <svg
              className={classNames(projectcss.all, sty.svg__eoCd6)}
              role={"img"}
            />
          }
          styling={["nittiWColor"]}
        >
          {"+ License"}
        </SubcomponentButton>
        <SubcomponentButton
          data-plasmic-name={"addCertification"}
          data-plasmic-override={overrides.addCertification}
          className={classNames("__wab_instance", sty.addCertification, {
            [sty.addCertificationpatentOrTrademark_collapsed]: hasVariant(
              $state,
              "patentOrTrademark",
              "collapsed"
            ),
            [sty.addCertificationpatentOrTrademark_expanded]: hasVariant(
              $state,
              "patentOrTrademark",
              "expanded"
            )
          })}
          endIcon={
            <svg
              className={classNames(projectcss.all, sty.svg__cdaRd)}
              role={"img"}
            />
          }
          justification={"left"}
          onClick={args.onClickCertification}
          onClickCapture={async event => {
            const $steps = {};

            $steps["updateLicenseOrCertification"] = true
              ? (() => {
                  const actionArgs = {
                    vgroup: "licenseOrCertification",
                    operation: 0,
                    value: "collapsed"
                  };
                  return (({ vgroup, value }) => {
                    if (typeof value === "string") {
                      value = [value];
                    }

                    $stateSet($state, vgroup, value);
                    return value;
                  })?.apply(null, [actionArgs]);
                })()
              : undefined;
            if (
              $steps["updateLicenseOrCertification"] != null &&
              typeof $steps["updateLicenseOrCertification"] === "object" &&
              typeof $steps["updateLicenseOrCertification"].then === "function"
            ) {
              $steps["updateLicenseOrCertification"] = await $steps[
                "updateLicenseOrCertification"
              ];
            }
          }}
          startIcon={
            <svg
              className={classNames(projectcss.all, sty.svg___6E21M)}
              role={"img"}
            />
          }
          styling={["nittiWColor"]}
        >
          {"+ Certification"}
        </SubcomponentButton>
      </section>
    </div>
  ) as React.ReactElement | null;
}

const PlasmicDescendants = {
  root: [
    "root",
    "addSectionButton",
    "addSectionButtonPatentOrTrademark",
    "addSectionButtonLicenseOrCertification",
    "freeBoxForDropDown",
    "addPatent",
    "addTrademark",
    "addLicense",
    "addCertification"
  ],
  addSectionButton: ["addSectionButton"],
  addSectionButtonPatentOrTrademark: ["addSectionButtonPatentOrTrademark"],
  addSectionButtonLicenseOrCertification: [
    "addSectionButtonLicenseOrCertification"
  ],
  freeBoxForDropDown: [
    "freeBoxForDropDown",
    "addPatent",
    "addTrademark",
    "addLicense",
    "addCertification"
  ],
  addPatent: ["addPatent"],
  addTrademark: ["addTrademark"],
  addLicense: ["addLicense"],
  addCertification: ["addCertification"]
} as const;
type NodeNameType = keyof typeof PlasmicDescendants;
type DescendantsType<T extends NodeNameType> =
  (typeof PlasmicDescendants)[T][number];
type NodeDefaultElementType = {
  root: "div";
  addSectionButton: typeof SubcomponentButton;
  addSectionButtonPatentOrTrademark: typeof SubcomponentButton;
  addSectionButtonLicenseOrCertification: typeof SubcomponentButton;
  freeBoxForDropDown: "section";
  addPatent: typeof SubcomponentButton;
  addTrademark: typeof SubcomponentButton;
  addLicense: typeof SubcomponentButton;
  addCertification: typeof SubcomponentButton;
};

type ReservedPropsType = "variants" | "args" | "overrides";
type NodeOverridesType<T extends NodeNameType> = Pick<
  PlasmicSubcomponentAddSectionButton__OverridesType,
  DescendantsType<T>
>;
type NodeComponentProps<T extends NodeNameType> =
  // Explicitly specify variants, args, and overrides as objects
  {
    variants?: PlasmicSubcomponentAddSectionButton__VariantsArgs;
    args?: PlasmicSubcomponentAddSectionButton__ArgsType;
    overrides?: NodeOverridesType<T>;
  } & Omit< // Specify variants directly as props
    PlasmicSubcomponentAddSectionButton__VariantsArgs,
    ReservedPropsType
  > &
    /* Specify args directly as props*/ Omit<
      PlasmicSubcomponentAddSectionButton__ArgsType,
      ReservedPropsType
    > &
    /* Specify overrides for each element directly as props*/ Omit<
      NodeOverridesType<T>,
      ReservedPropsType | VariantPropType | ArgPropType
    > &
    /* Specify props for the root element*/ Omit<
      Partial<React.ComponentProps<NodeDefaultElementType[T]>>,
      ReservedPropsType | VariantPropType | ArgPropType | DescendantsType<T>
    >;

function makeNodeComponent<NodeName extends NodeNameType>(nodeName: NodeName) {
  type PropsType = NodeComponentProps<NodeName> & { key?: React.Key };
  const func = function <T extends PropsType>(
    props: T & StrictProps<T, PropsType>
  ) {
    const { variants, args, overrides } = React.useMemo(
      () =>
        deriveRenderOpts(props, {
          name: nodeName,
          descendantNames: PlasmicDescendants[nodeName],
          internalArgPropNames: PlasmicSubcomponentAddSectionButton__ArgProps,
          internalVariantPropNames:
            PlasmicSubcomponentAddSectionButton__VariantProps
        }),
      [props, nodeName]
    );
    return PlasmicSubcomponentAddSectionButton__RenderFunc({
      variants,
      args,
      overrides,
      forNode: nodeName
    });
  };
  if (nodeName === "root") {
    func.displayName = "PlasmicSubcomponentAddSectionButton";
  } else {
    func.displayName = `PlasmicSubcomponentAddSectionButton.${nodeName}`;
  }
  return func;
}

export const PlasmicSubcomponentAddSectionButton = Object.assign(
  // Top-level PlasmicSubcomponentAddSectionButton renders the root element
  makeNodeComponent("root"),
  {
    // Helper components rendering sub-elements
    addSectionButton: makeNodeComponent("addSectionButton"),
    addSectionButtonPatentOrTrademark: makeNodeComponent(
      "addSectionButtonPatentOrTrademark"
    ),
    addSectionButtonLicenseOrCertification: makeNodeComponent(
      "addSectionButtonLicenseOrCertification"
    ),
    freeBoxForDropDown: makeNodeComponent("freeBoxForDropDown"),
    addPatent: makeNodeComponent("addPatent"),
    addTrademark: makeNodeComponent("addTrademark"),
    addLicense: makeNodeComponent("addLicense"),
    addCertification: makeNodeComponent("addCertification"),

    // Metadata about props expected for PlasmicSubcomponentAddSectionButton
    internalVariantProps: PlasmicSubcomponentAddSectionButton__VariantProps,
    internalArgProps: PlasmicSubcomponentAddSectionButton__ArgProps
  }
);

export default PlasmicSubcomponentAddSectionButton;
/* prettier-ignore-end */
