.trademarkSpacingContainer {
  display: flex;
  flex-direction: row;
  align-items: flex-start;
  justify-content: flex-start;
  width: 100%;
  flex-shrink: 0;
  height: auto;
  margin-bottom: 12px;
  transform: translateX(0px) translateY(0px) translateZ(0px);
  margin-top: 4px;
  margin-left: 4px;
  position: relative;
  min-width: 0;
  padding: 16px 16px 8px 0px;
}
.trademarkSpacingContaineroverview {
  width: auto;
  justify-self: flex-start;
  display: inline-flex;
}
.trademarkIcon {
  margin-top: 6px;
  margin-right: 8px;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: flex-start;
  width: var(--token-ey2y7HI9U2zx);
  height: var(--token-ey2y7HI9U2zx);
  position: relative;
  object-fit: cover;
  max-width: 100%;
  flex-shrink: 0;
}
.trademarkIconoverview {
  display: none;
}
.informationStack {
  display: flex;
  position: relative;
  flex-direction: column;
  align-items: flex-start;
  justify-content: flex-start;
  width: 100%;
  height: auto;
  max-width: 100%;
  min-width: 0;
  padding: var(--token-sazGmnf7GWAk);
  margin: var(--token-sazGmnf7GWAk);
}
.titleInput:global(.__wab_instance) {
  max-width: 100%;
}
.infoBar {
  display: flex;
  position: relative;
  width: 100%;
  flex-direction: row;
  margin-bottom: 2px;
  min-width: 0;
}
.infoBar > :global(.__wab_flex-container) {
  flex-direction: row;
  flex-wrap: wrap;
  align-items: stretch;
  min-width: 0;
  margin-left: calc(0px - var(--token-rB3BKCgczoWa));
  width: calc(100% + var(--token-rB3BKCgczoWa));
  margin-top: calc(0px - var(--token-ptnlAHOp9Vq0));
  height: calc(100% + var(--token-ptnlAHOp9Vq0));
}
.infoBar > :global(.__wab_flex-container) > *,
.infoBar > :global(.__wab_flex-container) > :global(.__wab_slot) > *,
.infoBar > :global(.__wab_flex-container) > picture > img,
.infoBar
  > :global(.__wab_flex-container)
  > :global(.__wab_slot)
  > picture
  > img {
  margin-left: var(--token-rB3BKCgczoWa);
  margin-top: var(--token-ptnlAHOp9Vq0);
}
.infoBareditable {
  margin-top: 12px;
  margin-bottom: 8px;
}
.infoBareditable > :global(.__wab_flex-container) {
  justify-content: flex-start;
  align-content: center;
}
.infoBaroverview {
  display: flex;
  flex-direction: row;
}
.infoBaroverview > :global(.__wab_flex-container) {
  flex-direction: row;
  flex-wrap: wrap;
  align-items: stretch;
  align-content: flex-start;
  justify-content: flex-start;
  margin-left: calc(0px - var(--token-4Wrp9mDZCSCQ));
  width: calc(100% + var(--token-4Wrp9mDZCSCQ));
  margin-top: calc(0px - var(--token-ptnlAHOp9Vq0));
  height: calc(100% + var(--token-ptnlAHOp9Vq0));
}
.infoBaroverview > :global(.__wab_flex-container) > *,
.infoBaroverview > :global(.__wab_flex-container) > :global(.__wab_slot) > *,
.infoBaroverview > :global(.__wab_flex-container) > picture > img,
.infoBaroverview
  > :global(.__wab_flex-container)
  > :global(.__wab_slot)
  > picture
  > img {
  margin-left: var(--token-4Wrp9mDZCSCQ);
  margin-top: var(--token-ptnlAHOp9Vq0);
}
.registrationNumberInput:global(.__wab_instance) {
  max-width: 100%;
  position: relative;
  align-self: center;
}
.registrationNumberInputeditable:global(.__wab_instance) {
  align-self: center;
  display: flex;
}
.registrationNumberInputoverview:global(.__wab_instance) {
  display: none;
}
.iconSpot3 {
  position: relative;
  object-fit: cover;
  max-width: 100%;
  margin-right: 0;
  width: 100%;
  height: 100%;
  min-width: 0;
  min-height: 0;
}
.countryOfRegistrationInput:global(.__wab_instance) {
  max-width: 100%;
  position: relative;
  align-self: center;
}
.countryOfRegistrationInputeditable:global(.__wab_instance) {
  align-self: center;
  display: flex;
}
.iconSpot {
  position: relative;
  object-fit: cover;
  max-width: 100%;
  margin-right: 0;
  width: 100%;
  height: 100%;
  min-width: 0;
  min-height: 0;
}
.registrationDateInput:global(.__wab_instance) {
  max-width: 100%;
  position: relative;
  align-self: center;
}
.registrationDateInputeditable:global(.__wab_instance) {
  align-self: center;
  display: flex;
}
.registrationDateInputoverview:global(.__wab_instance) {
  display: none;
}
.iconSpot2 {
  position: relative;
  object-fit: cover;
  max-width: 100%;
  margin-right: 0;
  width: 100%;
  height: 100%;
  min-width: 0;
  min-height: 0;
}
.awardDateInput:global(.__wab_instance) {
  max-width: 100%;
  position: relative;
  align-self: center;
}
.awardDateInputeditable:global(.__wab_instance) {
  align-self: center;
  display: flex;
}
.awardDateInputoverview:global(.__wab_instance) {
  display: flex;
}
.iconSpot6 {
  position: relative;
  object-fit: cover;
  max-width: 100%;
  margin-right: 0;
  width: 100%;
  height: 100%;
  min-width: 0;
  min-height: 0;
}
.registrationStatusSelector:global(.__wab_instance) {
  max-width: 100%;
  position: relative;
}
.registrationStatusSelectoreditable:global(.__wab_instance) {
  margin-bottom: 12px;
  display: flex;
}
.registrationStatusSelectoroverview:global(.__wab_instance) {
  display: flex;
}
.iconSpot4 {
  position: relative;
  object-fit: cover;
  max-width: 100%;
  margin-right: 0;
  width: 100%;
  height: 100%;
  min-width: 0;
  min-height: 0;
}
.description:global(.__wab_instance) {
  max-width: 100%;
  position: relative;
}
.descriptioneditable:global(.__wab_instance) {
  margin-top: 4px;
}
.subDeleteButton:global(.__wab_instance) {
  max-width: 100%;
  position: absolute;
  top: 16px;
  right: 16px;
  flex-shrink: 0;
  display: none;
}
.subDeleteButtoneditable:global(.__wab_instance) {
  flex-shrink: 0;
  display: flex;
}
