/* eslint-disable */
/* tslint:disable */
// @ts-nocheck
/* prettier-ignore-start */

/** @jsxRuntime classic */
/** @jsx createPlasmicElementProxy */
/** @jsxFrag React.Fragment */

// This class is auto-generated by Plasmic; please do not edit!
// Plasmic Project: nZeWKcn21KijvC7eT8B3c7
// Component: FPZd4ZDDgKTk

"use client";

import * as React from "react";

import Head from "next/head";
import Link, { LinkProps } from "next/link";
import { useRouter } from "next/navigation";

import {
  Flex as Flex__,
  MultiChoiceArg,
  PlasmicDataSourceContextProvider as PlasmicDataSourceContextProvider__,
  PlasmicIcon as PlasmicIcon__,
  PlasmicImg as PlasmicImg__,
  PlasmicLink as PlasmicLink__,
  PlasmicPageGuard as PlasmicPageGuard__,
  SingleBooleanChoiceArg,
  SingleC<PERSON>iceArg,
  Stack as Stack__,
  StrictProps,
  Trans as Trans__,
  classNames,
  createPlasmicElementProxy,
  deriveRenderOpts,
  ensureGlobalVariants,
  generateOnMutateForSpec,
  generateStateOnChangeProp,
  generateStateOnChangePropForCodeComponents,
  generateStateValueProp,
  get as $stateGet,
  hasVariant,
  initializeCodeComponentStates,
  initializePlasmicStates,
  makeFragment,
  omit,
  pick,
  renderPlasmicSlot,
  set as $stateSet,
  useCurrentUser,
  useDollarState,
  usePlasmicTranslator,
  useTrigger,
  wrapWithClassName
} from "@plasmicapp/react-web";
import {
  DataCtxReader as DataCtxReader__,
  useDataEnv,
  useGlobalActions
} from "@plasmicapp/host";

import "@plasmicapp/react-web/lib/plasmic.css";

import projectcss from "./plasmic.module.css"; // plasmic-import: nZeWKcn21KijvC7eT8B3c7/projectcss
import sty from "./PlasmicSubcomponentSelectorButtonsWSlot.module.css"; // plasmic-import: FPZd4ZDDgKTk/css

import CheckInBoxIcon from "./icons/PlasmicIcon__CheckInBox"; // plasmic-import: 3fHP2U7UmHxq/icon
import XIcon from "./icons/PlasmicIcon__X"; // plasmic-import: deLi5PaFbuAg/icon

createPlasmicElementProxy;

export type PlasmicSubcomponentSelectorButtonsWSlot__VariantMembers = {
  hoverAndClick: "hover" | "click";
  selected: "selected";
  size: "small" | "medium" | "large";
  color: "normal" | "gray" | "green";
  completionStatus: "complete" | "incomplete";
};
export type PlasmicSubcomponentSelectorButtonsWSlot__VariantsArgs = {
  hoverAndClick?: SingleChoiceArg<"hover" | "click">;
  selected?: SingleBooleanChoiceArg<"selected">;
  size?: SingleChoiceArg<"small" | "medium" | "large">;
  color?: SingleChoiceArg<"normal" | "gray" | "green">;
  completionStatus?: SingleChoiceArg<"complete" | "incomplete">;
};
type VariantPropType =
  keyof PlasmicSubcomponentSelectorButtonsWSlot__VariantsArgs;
export const PlasmicSubcomponentSelectorButtonsWSlot__VariantProps =
  new Array<VariantPropType>(
    "hoverAndClick",
    "selected",
    "size",
    "color",
    "completionStatus"
  );

export type PlasmicSubcomponentSelectorButtonsWSlot__ArgsType = {
  children?: React.ReactNode;
  onClick?: (event: any) => void;
};
type ArgPropType = keyof PlasmicSubcomponentSelectorButtonsWSlot__ArgsType;
export const PlasmicSubcomponentSelectorButtonsWSlot__ArgProps =
  new Array<ArgPropType>("children", "onClick");

export type PlasmicSubcomponentSelectorButtonsWSlot__OverridesType = {
  caseStudyFormatting?: Flex__<"div">;
  checkbox?: Flex__<"div">;
  text?: Flex__<"div">;
  checkmark2?: Flex__<"svg">;
  freeBox?: Flex__<"div">;
};

export interface DefaultSubcomponentSelectorButtonsWSlotProps {
  children?: React.ReactNode;
  onClick?: (event: any) => void;
  hoverAndClick?: SingleChoiceArg<"hover" | "click">;
  selected?: SingleBooleanChoiceArg<"selected">;
  size?: SingleChoiceArg<"small" | "medium" | "large">;
  color?: SingleChoiceArg<"normal" | "gray" | "green">;
  completionStatus?: SingleChoiceArg<"complete" | "incomplete">;
  className?: string;
}

const $$ = {};

function useNextRouter() {
  try {
    return useRouter();
  } catch {}
  return undefined;
}

function PlasmicSubcomponentSelectorButtonsWSlot__RenderFunc(props: {
  variants: PlasmicSubcomponentSelectorButtonsWSlot__VariantsArgs;
  args: PlasmicSubcomponentSelectorButtonsWSlot__ArgsType;
  overrides: PlasmicSubcomponentSelectorButtonsWSlot__OverridesType;
  forNode?: string;
}) {
  const { variants, overrides, forNode } = props;

  const args = React.useMemo(
    () =>
      Object.assign(
        {},
        Object.fromEntries(
          Object.entries(props.args).filter(([_, v]) => v !== undefined)
        )
      ),
    [props.args]
  );

  const $props = {
    ...args,
    ...variants
  };

  const __nextRouter = useNextRouter();
  const $ctx = useDataEnv?.() || {};
  const refsRef = React.useRef({});
  const $refs = refsRef.current;

  let [$queries, setDollarQueries] = React.useState<
    Record<string, ReturnType<typeof usePlasmicDataOp>>
  >({});
  const stateSpecs: Parameters<typeof useDollarState>[0] = React.useMemo(
    () => [
      {
        path: "hoverAndClick",
        type: "private",
        variableType: "variant",
        initFunc: ({ $props, $state, $queries, $ctx }) => $props.hoverAndClick
      },
      {
        path: "selected",
        type: "private",
        variableType: "variant",
        initFunc: ({ $props, $state, $queries, $ctx }) => $props.selected
      },
      {
        path: "size",
        type: "private",
        variableType: "variant",
        initFunc: ({ $props, $state, $queries, $ctx }) => $props.size
      },
      {
        path: "color",
        type: "private",
        variableType: "variant",
        initFunc: ({ $props, $state, $queries, $ctx }) => $props.color
      },
      {
        path: "completionStatus",
        type: "private",
        variableType: "variant",
        initFunc: ({ $props, $state, $queries, $ctx }) =>
          $props.completionStatus
      }
    ],
    [$props, $ctx, $refs]
  );
  const $state = useDollarState(stateSpecs, {
    $props,
    $ctx,
    $queries: $queries,
    $refs
  });

  const new$Queries: Record<string, ReturnType<typeof usePlasmicDataOp>> = {};
  if (Object.keys(new$Queries).some(k => new$Queries[k] !== $queries[k])) {
    setDollarQueries(new$Queries);

    $queries = new$Queries;
  }

  return (
    <div
      data-plasmic-name={"caseStudyFormatting"}
      data-plasmic-override={overrides.caseStudyFormatting}
      data-plasmic-root={true}
      data-plasmic-for-node={forNode}
      className={classNames(
        projectcss.all,
        projectcss.root_reset,
        projectcss.plasmic_default_styles,
        projectcss.plasmic_mixins,
        projectcss.plasmic_tokens,
        sty.caseStudyFormatting,
        {
          [sty.caseStudyFormattingcolor_gray]: hasVariant(
            $state,
            "color",
            "gray"
          ),
          [sty.caseStudyFormattingcolor_green]: hasVariant(
            $state,
            "color",
            "green"
          ),
          [sty.caseStudyFormattingcolor_green_size_small]:
            hasVariant($state, "size", "small") &&
            hasVariant($state, "color", "green"),
          [sty.caseStudyFormattingcompletionStatus_complete]: hasVariant(
            $state,
            "completionStatus",
            "complete"
          ),
          [sty.caseStudyFormattingcompletionStatus_incomplete]: hasVariant(
            $state,
            "completionStatus",
            "incomplete"
          ),
          [sty.caseStudyFormattingcompletionStatus_incomplete_size_medium]:
            hasVariant($state, "size", "medium") &&
            hasVariant($state, "completionStatus", "incomplete"),
          [sty.caseStudyFormattinghoverAndClick_click]: hasVariant(
            $state,
            "hoverAndClick",
            "click"
          ),
          [sty.caseStudyFormattinghoverAndClick_hover]: hasVariant(
            $state,
            "hoverAndClick",
            "hover"
          ),
          [sty.caseStudyFormattingselected]: hasVariant(
            $state,
            "selected",
            "selected"
          ),
          [sty.caseStudyFormattingsize_large]: hasVariant(
            $state,
            "size",
            "large"
          ),
          [sty.caseStudyFormattingsize_medium]: hasVariant(
            $state,
            "size",
            "medium"
          ),
          [sty.caseStudyFormattingsize_small]: hasVariant(
            $state,
            "size",
            "small"
          )
        }
      )}
      onClick={args.onClick}
      onMouseDown={async event => {
        const $steps = {};

        $steps["updateHoverAndClick"] = true
          ? (() => {
              const actionArgs = {
                vgroup: "hoverAndClick",
                operation: 0,
                value: "click"
              };
              return (({ vgroup, value }) => {
                if (typeof value === "string") {
                  value = [value];
                }

                $stateSet($state, vgroup, value);
                return value;
              })?.apply(null, [actionArgs]);
            })()
          : undefined;
        if (
          $steps["updateHoverAndClick"] != null &&
          typeof $steps["updateHoverAndClick"] === "object" &&
          typeof $steps["updateHoverAndClick"].then === "function"
        ) {
          $steps["updateHoverAndClick"] = await $steps["updateHoverAndClick"];
        }
      }}
      onMouseEnter={async event => {
        const $steps = {};

        $steps["updateHoverAndClick"] = true
          ? (() => {
              const actionArgs = {
                vgroup: "hoverAndClick",
                operation: 0,
                value: "hover"
              };
              return (({ vgroup, value }) => {
                if (typeof value === "string") {
                  value = [value];
                }

                $stateSet($state, vgroup, value);
                return value;
              })?.apply(null, [actionArgs]);
            })()
          : undefined;
        if (
          $steps["updateHoverAndClick"] != null &&
          typeof $steps["updateHoverAndClick"] === "object" &&
          typeof $steps["updateHoverAndClick"].then === "function"
        ) {
          $steps["updateHoverAndClick"] = await $steps["updateHoverAndClick"];
        }
      }}
      onMouseLeave={async event => {
        const $steps = {};

        $steps["updateHoverAndClick"] = true
          ? (() => {
              const actionArgs = {
                vgroup: "hoverAndClick",
                operation: 0,
                value: []
              };
              return (({ vgroup, value }) => {
                if (typeof value === "string") {
                  value = [value];
                }

                $stateSet($state, vgroup, value);
                return value;
              })?.apply(null, [actionArgs]);
            })()
          : undefined;
        if (
          $steps["updateHoverAndClick"] != null &&
          typeof $steps["updateHoverAndClick"] === "object" &&
          typeof $steps["updateHoverAndClick"].then === "function"
        ) {
          $steps["updateHoverAndClick"] = await $steps["updateHoverAndClick"];
        }
      }}
      onMouseUp={async event => {
        const $steps = {};

        $steps["updateHoverAndClick"] = true
          ? (() => {
              const actionArgs = {
                vgroup: "hoverAndClick",
                operation: 0,
                value: "hover"
              };
              return (({ vgroup, value }) => {
                if (typeof value === "string") {
                  value = [value];
                }

                $stateSet($state, vgroup, value);
                return value;
              })?.apply(null, [actionArgs]);
            })()
          : undefined;
        if (
          $steps["updateHoverAndClick"] != null &&
          typeof $steps["updateHoverAndClick"] === "object" &&
          typeof $steps["updateHoverAndClick"].then === "function"
        ) {
          $steps["updateHoverAndClick"] = await $steps["updateHoverAndClick"];
        }
      }}
    >
      <Stack__
        as={"div"}
        data-plasmic-name={"checkbox"}
        data-plasmic-override={overrides.checkbox}
        hasGap={true}
        className={classNames(projectcss.all, sty.checkbox, {
          [sty.checkboxcompletionStatus_complete]: hasVariant(
            $state,
            "completionStatus",
            "complete"
          ),
          [sty.checkboxcompletionStatus_incomplete]: hasVariant(
            $state,
            "completionStatus",
            "incomplete"
          )
        })}
      >
        <div
          data-plasmic-name={"text"}
          data-plasmic-override={overrides.text}
          className={classNames(
            projectcss.all,
            projectcss.__wab_text,
            sty.text,
            {
              [sty.textcompletionStatus_incomplete]: hasVariant(
                $state,
                "completionStatus",
                "incomplete"
              )
            }
          )}
        >
          {hasVariant($state, "completionStatus", "incomplete")
            ? "Incomplete"
            : "Complete"}
        </div>
        <PlasmicIcon__
          data-plasmic-name={"checkmark2"}
          data-plasmic-override={overrides.checkmark2}
          PlasmicIconType={
            hasVariant($state, "completionStatus", "incomplete")
              ? XIcon
              : CheckInBoxIcon
          }
          className={classNames(projectcss.all, sty.checkmark2, {
            [sty.checkmark2completionStatus_incomplete]: hasVariant(
              $state,
              "completionStatus",
              "incomplete"
            )
          })}
          role={"img"}
        />
      </Stack__>
      <Stack__
        as={"div"}
        data-plasmic-name={"freeBox"}
        data-plasmic-override={overrides.freeBox}
        hasGap={true}
        className={classNames(projectcss.all, sty.freeBox, {
          [sty.freeBoxcompletionStatus_complete]: hasVariant(
            $state,
            "completionStatus",
            "complete"
          ),
          [sty.freeBoxcompletionStatus_incomplete]: hasVariant(
            $state,
            "completionStatus",
            "incomplete"
          ),
          [sty.freeBoxsize_large]: hasVariant($state, "size", "large"),
          [sty.freeBoxsize_small]: hasVariant($state, "size", "small")
        })}
      >
        {renderPlasmicSlot({
          defaultContents: null,
          value: args.children
        })}
      </Stack__>
    </div>
  ) as React.ReactElement | null;
}

const PlasmicDescendants = {
  caseStudyFormatting: [
    "caseStudyFormatting",
    "checkbox",
    "text",
    "checkmark2",
    "freeBox"
  ],
  checkbox: ["checkbox", "text", "checkmark2"],
  text: ["text"],
  checkmark2: ["checkmark2"],
  freeBox: ["freeBox"]
} as const;
type NodeNameType = keyof typeof PlasmicDescendants;
type DescendantsType<T extends NodeNameType> =
  (typeof PlasmicDescendants)[T][number];
type NodeDefaultElementType = {
  caseStudyFormatting: "div";
  checkbox: "div";
  text: "div";
  checkmark2: "svg";
  freeBox: "div";
};

type ReservedPropsType = "variants" | "args" | "overrides";
type NodeOverridesType<T extends NodeNameType> = Pick<
  PlasmicSubcomponentSelectorButtonsWSlot__OverridesType,
  DescendantsType<T>
>;
type NodeComponentProps<T extends NodeNameType> =
  // Explicitly specify variants, args, and overrides as objects
  {
    variants?: PlasmicSubcomponentSelectorButtonsWSlot__VariantsArgs;
    args?: PlasmicSubcomponentSelectorButtonsWSlot__ArgsType;
    overrides?: NodeOverridesType<T>;
  } & Omit< // Specify variants directly as props
    PlasmicSubcomponentSelectorButtonsWSlot__VariantsArgs,
    ReservedPropsType
  > &
    /* Specify args directly as props*/ Omit<
      PlasmicSubcomponentSelectorButtonsWSlot__ArgsType,
      ReservedPropsType
    > &
    /* Specify overrides for each element directly as props*/ Omit<
      NodeOverridesType<T>,
      ReservedPropsType | VariantPropType | ArgPropType
    > &
    /* Specify props for the root element*/ Omit<
      Partial<React.ComponentProps<NodeDefaultElementType[T]>>,
      ReservedPropsType | VariantPropType | ArgPropType | DescendantsType<T>
    >;

function makeNodeComponent<NodeName extends NodeNameType>(nodeName: NodeName) {
  type PropsType = NodeComponentProps<NodeName> & { key?: React.Key };
  const func = function <T extends PropsType>(
    props: T & StrictProps<T, PropsType>
  ) {
    const { variants, args, overrides } = React.useMemo(
      () =>
        deriveRenderOpts(props, {
          name: nodeName,
          descendantNames: PlasmicDescendants[nodeName],
          internalArgPropNames:
            PlasmicSubcomponentSelectorButtonsWSlot__ArgProps,
          internalVariantPropNames:
            PlasmicSubcomponentSelectorButtonsWSlot__VariantProps
        }),
      [props, nodeName]
    );
    return PlasmicSubcomponentSelectorButtonsWSlot__RenderFunc({
      variants,
      args,
      overrides,
      forNode: nodeName
    });
  };
  if (nodeName === "caseStudyFormatting") {
    func.displayName = "PlasmicSubcomponentSelectorButtonsWSlot";
  } else {
    func.displayName = `PlasmicSubcomponentSelectorButtonsWSlot.${nodeName}`;
  }
  return func;
}

export const PlasmicSubcomponentSelectorButtonsWSlot = Object.assign(
  // Top-level PlasmicSubcomponentSelectorButtonsWSlot renders the root element
  makeNodeComponent("caseStudyFormatting"),
  {
    // Helper components rendering sub-elements
    checkbox: makeNodeComponent("checkbox"),
    text: makeNodeComponent("text"),
    checkmark2: makeNodeComponent("checkmark2"),
    freeBox: makeNodeComponent("freeBox"),

    // Metadata about props expected for PlasmicSubcomponentSelectorButtonsWSlot
    internalVariantProps: PlasmicSubcomponentSelectorButtonsWSlot__VariantProps,
    internalArgProps: PlasmicSubcomponentSelectorButtonsWSlot__ArgProps
  }
);

export default PlasmicSubcomponentSelectorButtonsWSlot;
/* prettier-ignore-end */
