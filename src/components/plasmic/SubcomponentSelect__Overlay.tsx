import * as React from "react";
import {
  DefaultSubcomponentSelect__OverlayProps,
  PlasmicSubcomponentSelect__Overlay
} from "./autogenerated/senergy_platform/PlasmicSubcomponentSelect__Overlay";

import { TriggeredOverlayRef } from "@plasmicapp/react-web";

export interface SubcomponentSelect__OverlayProps
  extends DefaultSubcomponentSelect__OverlayProps {
  // Feel free to add any additional props that this component should receive
}
function SubcomponentSelect__Overlay_(
  props: SubcomponentSelect__OverlayProps,
  ref: TriggeredOverlayRef
) {
  const { plasmicProps } = PlasmicSubcomponentSelect__Overlay.useBehavior(
    props,
    ref
  );
  return <PlasmicSubcomponentSelect__Overlay {...plasmicProps} />;
}

const SubcomponentSelect__Overlay = React.forwardRef(
  SubcomponentSelect__Overlay_
);

export default Object.assign(SubcomponentSelect__Overlay, {
  __plumeType: "triggered-overlay"
});
