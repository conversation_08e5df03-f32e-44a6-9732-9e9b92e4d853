/* eslint-disable */
/* tslint:disable */
// @ts-nocheck
/* prettier-ignore-start */
import React from "react";
import { classNames } from "@plasmicapp/react-web";

export type AbandonedIconProps = React.ComponentProps<"svg"> & {
  title?: string;
};

export function AbandonedIcon(props: AbandonedIconProps) {
  const { className, style, title, ...restProps } = props;
  return (
    <svg
      xmlns={"http://www.w3.org/2000/svg"}
      className={classNames(
        "plasmic-default__svg",
        className,
        "icon icon-tabler icon-tabler-clock-off"
      )}
      viewBox={"0 0 24 24"}
      strokeWidth={"1.5"}
      stroke={"currentColor"}
      fill={"none"}
      strokeLinecap={"round"}
      strokeLinejoin={"round"}
      height={"1em"}
      style={style}
      {...restProps}
    >
      {title && <title>{title}</title>}

      <path d={"M0 0h24v24H0z"} fill={"none"} stroke={"none"}></path>

      <path
        d={
          "M5.633 5.64a9 9 0 1012.735 12.72m1.674-2.32A9 9 0 007.96 3.958M12 7v1M3 3l18 18"
        }
      ></path>
    </svg>
  );
}

export default AbandonedIcon;
/* prettier-ignore-end */
