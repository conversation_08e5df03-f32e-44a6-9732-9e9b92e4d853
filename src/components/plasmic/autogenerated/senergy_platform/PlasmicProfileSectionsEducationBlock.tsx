/* eslint-disable */
/* tslint:disable */
// @ts-nocheck
/* prettier-ignore-start */

/** @jsxRuntime classic */
/** @jsx createPlasmicElementProxy */
/** @jsxFrag React.Fragment */

// This class is auto-generated by Plasmic; please do not edit!
// Plasmic Project: nZeWKcn21KijvC7eT8B3c7
// Component: Z1o0_V1Y-Oht

"use client";

import * as React from "react";

import Head from "next/head";
import Link, { LinkProps } from "next/link";
import { useRouter } from "next/navigation";

import {
  Flex as Flex__,
  MultiChoiceArg,
  PlasmicDataSourceContextProvider as PlasmicDataSourceContextProvider__,
  PlasmicIcon as PlasmicIcon__,
  PlasmicImg as PlasmicImg__,
  PlasmicLink as PlasmicLink__,
  PlasmicPageGuard as PlasmicPageGuard__,
  SingleBooleanChoiceArg,
  SingleChoiceArg,
  Stack as Stack__,
  StrictProps,
  Trans as Trans__,
  classNames,
  createPlasmicElementProxy,
  deriveRenderOpts,
  ensureGlobalVariants,
  generateOnMutateForSpec,
  generateStateOnChangeProp,
  generateStateOnChangePropForCodeComponents,
  generateStateValueProp,
  get as $stateGet,
  hasVariant,
  initializeCodeComponentStates,
  initializePlasmicStates,
  makeFragment,
  omit,
  pick,
  renderPlasmicSlot,
  set as $stateSet,
  useCurrentUser,
  useDollarState,
  usePlasmicTranslator,
  useTrigger,
  wrapWithClassName
} from "@plasmicapp/react-web";
import {
  DataCtxReader as DataCtxReader__,
  useDataEnv,
  useGlobalActions
} from "@plasmicapp/host";

import ProfileSectionsProfileSectionHeading from "../../ProfileSectionsProfileSectionHeading"; // plasmic-import: Uz656rZzIxzC/component
import ProfileTileEducationTile from "../../ProfileTileEducationTile"; // plasmic-import: 6P769Hd_cTVa/component

import "@plasmicapp/react-web/lib/plasmic.css";

import projectcss from "./plasmic.module.css"; // plasmic-import: nZeWKcn21KijvC7eT8B3c7/projectcss
import sty from "./PlasmicProfileSectionsEducationBlock.module.css"; // plasmic-import: Z1o0_V1Y-Oht/css

createPlasmicElementProxy;

export type PlasmicProfileSectionsEducationBlock__VariantMembers = {
  overviewGrid: "overviewBlock";
  editable: "editable";
};
export type PlasmicProfileSectionsEducationBlock__VariantsArgs = {
  overviewGrid?: SingleChoiceArg<"overviewBlock">;
  editable?: SingleBooleanChoiceArg<"editable">;
};
type VariantPropType = keyof PlasmicProfileSectionsEducationBlock__VariantsArgs;
export const PlasmicProfileSectionsEducationBlock__VariantProps =
  new Array<VariantPropType>("overviewGrid", "editable");

export type PlasmicProfileSectionsEducationBlock__ArgsType = {
  allEducation?: any;
  onAllEducationChange?: (val: string) => void;
  addButtonOnClick?: (event: any) => void;
};
type ArgPropType = keyof PlasmicProfileSectionsEducationBlock__ArgsType;
export const PlasmicProfileSectionsEducationBlock__ArgProps =
  new Array<ArgPropType>(
    "allEducation",
    "onAllEducationChange",
    "addButtonOnClick"
  );

export type PlasmicProfileSectionsEducationBlock__OverridesType = {
  educationSection?: Flex__<"div">;
  profileSectionsProfileSectionHeading?: Flex__<
    typeof ProfileSectionsProfileSectionHeading
  >;
  displayContainer?: Flex__<"div">;
  educationTile?: Flex__<typeof ProfileTileEducationTile>;
};

export interface DefaultProfileSectionsEducationBlockProps {
  allEducation?: any;
  onAllEducationChange?: (val: string) => void;
  addButtonOnClick?: (event: any) => void;
  overviewGrid?: SingleChoiceArg<"overviewBlock">;
  editable?: SingleBooleanChoiceArg<"editable">;
  className?: string;
}

const $$ = {};

function useNextRouter() {
  try {
    return useRouter();
  } catch {}
  return undefined;
}

function PlasmicProfileSectionsEducationBlock__RenderFunc(props: {
  variants: PlasmicProfileSectionsEducationBlock__VariantsArgs;
  args: PlasmicProfileSectionsEducationBlock__ArgsType;
  overrides: PlasmicProfileSectionsEducationBlock__OverridesType;
  forNode?: string;
}) {
  const { variants, overrides, forNode } = props;

  const args = React.useMemo(
    () =>
      Object.assign(
        {},
        Object.fromEntries(
          Object.entries(props.args).filter(([_, v]) => v !== undefined)
        )
      ),
    [props.args]
  );

  const $props = {
    ...args,
    ...variants
  };

  const __nextRouter = useNextRouter();
  const $ctx = useDataEnv?.() || {};
  const refsRef = React.useRef({});
  const $refs = refsRef.current;

  let [$queries, setDollarQueries] = React.useState<
    Record<string, ReturnType<typeof usePlasmicDataOp>>
  >({});
  const stateSpecs: Parameters<typeof useDollarState>[0] = React.useMemo(
    () => [
      {
        path: "overviewGrid",
        type: "private",
        variableType: "variant",
        initFunc: ({ $props, $state, $queries, $ctx }) => $props.overviewGrid
      },
      {
        path: "editable",
        type: "private",
        variableType: "variant",
        initFunc: ({ $props, $state, $queries, $ctx }) => $props.editable
      },
      {
        path: "educationTile[].degreeNameInputValue",
        type: "private",
        variableType: "text"
      },
      {
        path: "educationTile[].locationInputValue",
        type: "private",
        variableType: "text"
      },
      {
        path: "educationTile[].startDateInputValue",
        type: "private",
        variableType: "text"
      },
      {
        path: "educationTile[].endDateInputValue",
        type: "private",
        variableType: "text"
      },
      {
        path: "educationTile[].graduatedCheckboxValue",
        type: "private",
        variableType: "boolean"
      },
      {
        path: "educationTile[].deleteButtonDisabled",
        type: "private",
        variableType: "text"
      },
      {
        path: "educationTile[].deleteButtonClickStage",
        type: "private",
        variableType: "number"
      },
      {
        path: "educationTile[].educationalInstitutionInputValue",
        type: "private",
        variableType: "text"
      },
      {
        path: "allEducation",
        type: "writable",
        variableType: "object",

        valueProp: "allEducation",
        onChangeProp: "onAllEducationChange"
      }
    ],
    [$props, $ctx, $refs]
  );
  const $state = useDollarState(stateSpecs, {
    $props,
    $ctx,
    $queries: $queries,
    $refs
  });

  const new$Queries: Record<string, ReturnType<typeof usePlasmicDataOp>> = {};
  if (Object.keys(new$Queries).some(k => new$Queries[k] !== $queries[k])) {
    setDollarQueries(new$Queries);

    $queries = new$Queries;
  }

  return (
    <div
      data-plasmic-name={"educationSection"}
      data-plasmic-override={overrides.educationSection}
      data-plasmic-root={true}
      data-plasmic-for-node={forNode}
      className={classNames(
        projectcss.all,
        projectcss.root_reset,
        projectcss.plasmic_default_styles,
        projectcss.plasmic_mixins,
        projectcss.plasmic_tokens,
        sty.educationSection,
        {
          [sty.educationSectioneditable]: hasVariant(
            $state,
            "editable",
            "editable"
          ),
          [sty.educationSectionoverviewGrid_overviewBlock]: hasVariant(
            $state,
            "overviewGrid",
            "overviewBlock"
          )
        }
      )}
    >
      <ProfileSectionsProfileSectionHeading
        data-plasmic-name={"profileSectionsProfileSectionHeading"}
        data-plasmic-override={overrides.profileSectionsProfileSectionHeading}
        addButtonBaseOnClick={args.addButtonOnClick}
        className={classNames(
          "__wab_instance",
          sty.profileSectionsProfileSectionHeading,
          {
            [sty.profileSectionsProfileSectionHeadingeditable]: hasVariant(
              $state,
              "editable",
              "editable"
            ),
            [sty.profileSectionsProfileSectionHeadingoverviewGrid_overviewBlock]:
              hasVariant($state, "overviewGrid", "overviewBlock")
          }
        )}
        editable={hasVariant($state, "editable", "editable") ? true : undefined}
        overviewGrid={
          hasVariant($state, "overviewGrid", "overviewBlock") ? true : undefined
        }
      >
        {"Education"}
      </ProfileSectionsProfileSectionHeading>
      <div
        data-plasmic-name={"displayContainer"}
        data-plasmic-override={overrides.displayContainer}
        className={classNames(projectcss.all, sty.displayContainer, {
          [sty.displayContainereditable]: hasVariant(
            $state,
            "editable",
            "editable"
          ),
          [sty.displayContaineroverviewGrid_overviewBlock]: hasVariant(
            $state,
            "overviewGrid",
            "overviewBlock"
          )
        })}
      >
        {(_par => (!_par ? [] : Array.isArray(_par) ? _par : [_par]))(
          (() => {
            try {
              return $state.allEducation.data;
            } catch (e) {
              if (
                e instanceof TypeError ||
                e?.plasmicType === "PlasmicUndefinedDataError"
              ) {
                return [];
              }
              throw e;
            }
          })()
        ).map((__plasmic_item_0, __plasmic_idx_0) => {
          const currentItem = __plasmic_item_0;
          const currentIndex = __plasmic_idx_0;
          return (() => {
            const child$Props = {
              className: classNames("__wab_instance", sty.educationTile, {
                [sty.educationTileeditable]: hasVariant(
                  $state,
                  "editable",
                  "editable"
                ),
                [sty.educationTileoverviewGrid_overviewBlock]: hasVariant(
                  $state,
                  "overviewGrid",
                  "overviewBlock"
                )
              }),
              degreeNameInputValue: generateStateValueProp($state, [
                "educationTile",
                __plasmic_idx_0,
                "degreeNameInputValue"
              ]),
              deleteButtonClickStage: generateStateValueProp($state, [
                "educationTile",
                __plasmic_idx_0,
                "deleteButtonClickStage"
              ]),
              deleteButtonDisabled: generateStateValueProp($state, [
                "educationTile",
                __plasmic_idx_0,
                "deleteButtonDisabled"
              ]),
              editable: (() => {
                try {
                  return $state.editable;
                } catch (e) {
                  if (
                    e instanceof TypeError ||
                    e?.plasmicType === "PlasmicUndefinedDataError"
                  ) {
                    return [];
                  }
                  throw e;
                }
              })(),
              educationId: (() => {
                try {
                  return currentItem.id;
                } catch (e) {
                  if (
                    e instanceof TypeError ||
                    e?.plasmicType === "PlasmicUndefinedDataError"
                  ) {
                    return undefined;
                  }
                  throw e;
                }
              })(),
              educationalInstitutionInputValue: generateStateValueProp($state, [
                "educationTile",
                __plasmic_idx_0,
                "educationalInstitutionInputValue"
              ]),
              endDateInputValue: generateStateValueProp($state, [
                "educationTile",
                __plasmic_idx_0,
                "endDateInputValue"
              ]),
              graduatedCheckboxValue: generateStateValueProp($state, [
                "educationTile",
                __plasmic_idx_0,
                "graduatedCheckboxValue"
              ]),
              key: currentIndex,
              locationInputValue: generateStateValueProp($state, [
                "educationTile",
                __plasmic_idx_0,
                "locationInputValue"
              ]),
              onDegreeNameInputValueChange: async (...eventArgs: any) => {
                generateStateOnChangeProp($state, [
                  "educationTile",
                  __plasmic_idx_0,
                  "degreeNameInputValue"
                ]).apply(null, eventArgs);

                if (
                  eventArgs.length > 1 &&
                  eventArgs[1] &&
                  eventArgs[1]._plasmic_state_init_
                ) {
                  return;
                }
              },
              onDeleteButtonClickStageChange: async (...eventArgs: any) => {
                generateStateOnChangeProp($state, [
                  "educationTile",
                  __plasmic_idx_0,
                  "deleteButtonClickStage"
                ]).apply(null, eventArgs);

                if (
                  eventArgs.length > 1 &&
                  eventArgs[1] &&
                  eventArgs[1]._plasmic_state_init_
                ) {
                  return;
                }
              },
              onDeleteButtonDisabledChange: async (...eventArgs: any) => {
                generateStateOnChangeProp($state, [
                  "educationTile",
                  __plasmic_idx_0,
                  "deleteButtonDisabled"
                ]).apply(null, eventArgs);

                if (
                  eventArgs.length > 1 &&
                  eventArgs[1] &&
                  eventArgs[1]._plasmic_state_init_
                ) {
                  return;
                }
              },
              onEducationalInstitutionInputValueChange: async (
                ...eventArgs: any
              ) => {
                generateStateOnChangeProp($state, [
                  "educationTile",
                  __plasmic_idx_0,
                  "educationalInstitutionInputValue"
                ]).apply(null, eventArgs);

                if (
                  eventArgs.length > 1 &&
                  eventArgs[1] &&
                  eventArgs[1]._plasmic_state_init_
                ) {
                  return;
                }
              },
              onEndDateInputValueChange: async (...eventArgs: any) => {
                generateStateOnChangeProp($state, [
                  "educationTile",
                  __plasmic_idx_0,
                  "endDateInputValue"
                ]).apply(null, eventArgs);

                if (
                  eventArgs.length > 1 &&
                  eventArgs[1] &&
                  eventArgs[1]._plasmic_state_init_
                ) {
                  return;
                }
              },
              onGraduatedCheckboxValueChange: async (...eventArgs: any) => {
                generateStateOnChangeProp($state, [
                  "educationTile",
                  __plasmic_idx_0,
                  "graduatedCheckboxValue"
                ]).apply(null, eventArgs);

                if (
                  eventArgs.length > 1 &&
                  eventArgs[1] &&
                  eventArgs[1]._plasmic_state_init_
                ) {
                  return;
                }
              },
              onLocationInputValueChange: async (...eventArgs: any) => {
                generateStateOnChangeProp($state, [
                  "educationTile",
                  __plasmic_idx_0,
                  "locationInputValue"
                ]).apply(null, eventArgs);

                if (
                  eventArgs.length > 1 &&
                  eventArgs[1] &&
                  eventArgs[1]._plasmic_state_init_
                ) {
                  return;
                }
              },
              onStartDateInputValueChange: async (...eventArgs: any) => {
                generateStateOnChangeProp($state, [
                  "educationTile",
                  __plasmic_idx_0,
                  "startDateInputValue"
                ]).apply(null, eventArgs);

                if (
                  eventArgs.length > 1 &&
                  eventArgs[1] &&
                  eventArgs[1]._plasmic_state_init_
                ) {
                  return;
                }
              },
              overview: hasVariant($state, "overviewGrid", "overviewBlock")
                ? true
                : undefined,
              startDateInputValue: generateStateValueProp($state, [
                "educationTile",
                __plasmic_idx_0,
                "startDateInputValue"
              ])
            };

            initializePlasmicStates(
              $state,
              [
                {
                  name: "educationTile[].degreeNameInputValue",
                  initFunc: ({ $props, $state, $queries }) =>
                    (() => {
                      try {
                        return currentItem.degree_name;
                      } catch (e) {
                        if (
                          e instanceof TypeError ||
                          e?.plasmicType === "PlasmicUndefinedDataError"
                        ) {
                          return undefined;
                        }
                        throw e;
                      }
                    })()
                },
                {
                  name: "educationTile[].locationInputValue",
                  initFunc: ({ $props, $state, $queries }) =>
                    (() => {
                      try {
                        return currentItem.location;
                      } catch (e) {
                        if (
                          e instanceof TypeError ||
                          e?.plasmicType === "PlasmicUndefinedDataError"
                        ) {
                          return undefined;
                        }
                        throw e;
                      }
                    })()
                },
                {
                  name: "educationTile[].startDateInputValue",
                  initFunc: ({ $props, $state, $queries }) =>
                    (() => {
                      try {
                        return currentItem.start_date
                          ? currentItem.start_date
                              .split("T")[0]
                              .split("-")
                              .join("-")
                          : null;
                      } catch (e) {
                        if (
                          e instanceof TypeError ||
                          e?.plasmicType === "PlasmicUndefinedDataError"
                        ) {
                          return undefined;
                        }
                        throw e;
                      }
                    })()
                },
                {
                  name: "educationTile[].endDateInputValue",
                  initFunc: ({ $props, $state, $queries }) =>
                    (() => {
                      try {
                        return currentItem.start_date
                          ? currentItem.end_date
                              .split("T")[0]
                              .split("-")
                              .join("-")
                          : null;
                      } catch (e) {
                        if (
                          e instanceof TypeError ||
                          e?.plasmicType === "PlasmicUndefinedDataError"
                        ) {
                          return undefined;
                        }
                        throw e;
                      }
                    })()
                },
                {
                  name: "educationTile[].graduatedCheckboxValue",
                  initFunc: ({ $props, $state, $queries }) =>
                    (() => {
                      try {
                        return currentItem.completed;
                      } catch (e) {
                        if (
                          e instanceof TypeError ||
                          e?.plasmicType === "PlasmicUndefinedDataError"
                        ) {
                          return true;
                        }
                        throw e;
                      }
                    })()
                },
                {
                  name: "educationTile[].deleteButtonDisabled",
                  initFunc: ({ $props, $state, $queries }) => undefined
                },
                {
                  name: "educationTile[].deleteButtonClickStage",
                  initFunc: ({ $props, $state, $queries }) => 0
                },
                {
                  name: "educationTile[].educationalInstitutionInputValue",
                  initFunc: ({ $props, $state, $queries }) =>
                    (() => {
                      try {
                        return currentItem.institution;
                      } catch (e) {
                        if (
                          e instanceof TypeError ||
                          e?.plasmicType === "PlasmicUndefinedDataError"
                        ) {
                          return undefined;
                        }
                        throw e;
                      }
                    })()
                }
              ],
              [__plasmic_idx_0]
            );
            return (
              <ProfileTileEducationTile
                data-plasmic-name={"educationTile"}
                data-plasmic-override={overrides.educationTile}
                {...child$Props}
              />
            );
          })();
        })}
      </div>
    </div>
  ) as React.ReactElement | null;
}

const PlasmicDescendants = {
  educationSection: [
    "educationSection",
    "profileSectionsProfileSectionHeading",
    "displayContainer",
    "educationTile"
  ],
  profileSectionsProfileSectionHeading: [
    "profileSectionsProfileSectionHeading"
  ],
  displayContainer: ["displayContainer", "educationTile"],
  educationTile: ["educationTile"]
} as const;
type NodeNameType = keyof typeof PlasmicDescendants;
type DescendantsType<T extends NodeNameType> =
  (typeof PlasmicDescendants)[T][number];
type NodeDefaultElementType = {
  educationSection: "div";
  profileSectionsProfileSectionHeading: typeof ProfileSectionsProfileSectionHeading;
  displayContainer: "div";
  educationTile: typeof ProfileTileEducationTile;
};

type ReservedPropsType = "variants" | "args" | "overrides";
type NodeOverridesType<T extends NodeNameType> = Pick<
  PlasmicProfileSectionsEducationBlock__OverridesType,
  DescendantsType<T>
>;
type NodeComponentProps<T extends NodeNameType> =
  // Explicitly specify variants, args, and overrides as objects
  {
    variants?: PlasmicProfileSectionsEducationBlock__VariantsArgs;
    args?: PlasmicProfileSectionsEducationBlock__ArgsType;
    overrides?: NodeOverridesType<T>;
  } & Omit< // Specify variants directly as props
    PlasmicProfileSectionsEducationBlock__VariantsArgs,
    ReservedPropsType
  > &
    /* Specify args directly as props*/ Omit<
      PlasmicProfileSectionsEducationBlock__ArgsType,
      ReservedPropsType
    > &
    /* Specify overrides for each element directly as props*/ Omit<
      NodeOverridesType<T>,
      ReservedPropsType | VariantPropType | ArgPropType
    > &
    /* Specify props for the root element*/ Omit<
      Partial<React.ComponentProps<NodeDefaultElementType[T]>>,
      ReservedPropsType | VariantPropType | ArgPropType | DescendantsType<T>
    >;

function makeNodeComponent<NodeName extends NodeNameType>(nodeName: NodeName) {
  type PropsType = NodeComponentProps<NodeName> & { key?: React.Key };
  const func = function <T extends PropsType>(
    props: T & StrictProps<T, PropsType>
  ) {
    const { variants, args, overrides } = React.useMemo(
      () =>
        deriveRenderOpts(props, {
          name: nodeName,
          descendantNames: PlasmicDescendants[nodeName],
          internalArgPropNames: PlasmicProfileSectionsEducationBlock__ArgProps,
          internalVariantPropNames:
            PlasmicProfileSectionsEducationBlock__VariantProps
        }),
      [props, nodeName]
    );
    return PlasmicProfileSectionsEducationBlock__RenderFunc({
      variants,
      args,
      overrides,
      forNode: nodeName
    });
  };
  if (nodeName === "educationSection") {
    func.displayName = "PlasmicProfileSectionsEducationBlock";
  } else {
    func.displayName = `PlasmicProfileSectionsEducationBlock.${nodeName}`;
  }
  return func;
}

export const PlasmicProfileSectionsEducationBlock = Object.assign(
  // Top-level PlasmicProfileSectionsEducationBlock renders the root element
  makeNodeComponent("educationSection"),
  {
    // Helper components rendering sub-elements
    profileSectionsProfileSectionHeading: makeNodeComponent(
      "profileSectionsProfileSectionHeading"
    ),
    displayContainer: makeNodeComponent("displayContainer"),
    educationTile: makeNodeComponent("educationTile"),

    // Metadata about props expected for PlasmicProfileSectionsEducationBlock
    internalVariantProps: PlasmicProfileSectionsEducationBlock__VariantProps,
    internalArgProps: PlasmicProfileSectionsEducationBlock__ArgProps
  }
);

export default PlasmicProfileSectionsEducationBlock;
/* prettier-ignore-end */
